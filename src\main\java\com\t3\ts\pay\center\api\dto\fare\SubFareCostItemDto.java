package com.t3.ts.pay.center.api.dto.fare;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/23 17:59
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubFareCostItemDto implements Serializable {
    /**
     * 子项项目名称
     */
    private String subItem;
    /**
     * 子项value
     */
    private String value;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 颜色
     */
    private boolean color;

    /**
     * 备注，备用字段
     */
    private String[] remarks;

    public SubFareCostItemDto(String subItem, String value, Integer sort, boolean color) {
        this.subItem = subItem;
        this.value = value;
        this.sort = sort;
        this.color = color;
    }
}
