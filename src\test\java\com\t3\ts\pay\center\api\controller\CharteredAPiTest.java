package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.CharteredPaymentBusiness;
import com.t3.ts.pay.center.api.dto.chartered.PayTradeCharteredReq;
import com.t3.ts.result.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest(classes = PayCenterApiServer.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class CharteredAPiTest {

    @Autowired
    private CharteredPaymentBusiness paymentBusiness;

    @Test
    public void cashierDeskProTest(){
        PayTradeCharteredReq req = new PayTradeCharteredReq();
        req.setRoutePlanUuid("100000564509704t3go0000351386099");
        Response pro = paymentBusiness.cashierDeskPro(req);
        System.out.println(pro.getData());
    }
}
