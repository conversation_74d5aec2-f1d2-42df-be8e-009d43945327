package com.t3.ts.pay.center.api.dto.trade;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: hujiande
 * @Date: 2022/5/6 10:45
 */
@Getter
@Setter
public class SecretFreeStatusVo {

    /**
     * 支付宝免密状态  0:未开通  1:已开通
     */
    private Integer aliPay;

    /**
     * 微信免密状态
     */
    private Integer wxPay;

    /**
     * 一网通免密状态
     */
    private Integer netcomPay;

    /**
     * 银联免密状态
     */
    private Integer unionPay;

    private Integer zhima;

    /**
     * 商城状态
     */
    private Integer zhimaMall;

    /**
     * 是否展示 微信、支付宝签约免密滑框
     */
    private boolean showNoPassword = false;

}
