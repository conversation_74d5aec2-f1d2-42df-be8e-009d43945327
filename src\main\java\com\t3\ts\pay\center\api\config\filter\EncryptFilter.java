package com.t3.ts.pay.center.api.config.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.util.AESUtil;
import com.t3.ts.pay.center.api.util.RSAUtils;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Describer: 报文解密
 *
 * <AUTHOR>
 * 2022/10/9 17:24
 */
@WebFilter(urlPatterns = "/*")
@Slf4j
@Order(0)
public class EncryptFilter implements Filter {

    /**
     * 请求返回结果
     */
    private static final String ERR_MSG_SIGN_ERROR = "请更新至最新APP版本，造成不变请谅解！";
    /**
     * 登录过期，请重新登录
     */
    public static final Integer ERR_CODE_SIGN_ERROR = 50001;

    private static final String SIGN = "t3Sign";

    /**
     * rsa 私钥
     */
    @Value("${sign.key:null}")
    private String privateKey;

    /**
     * sign签名拦截开关
     */
    @Value("${sign.filter.empty:false}")
    private Boolean filterEmpty;
    @Value("${sign.filter.emptyMini:true}")
    private Boolean filterEmptyMini;

    /**
     * sign签名拦截地址
     */
    @Value("${sign.filter.url:[]}")
    private List<String> filterUrls;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse,
                         FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        //对请求地址进行判断，如果不在请求拦截当中，直接放过
        if (CollectionUtils.isEmpty(filterUrls) || !filterUrls.contains(request.getRequestURI())) {
            //直接放过
            filterChain.doFilter(request, response);
            return;
        }

        WrappedRequest myRequestWrapper = request instanceof WrappedRequest ? (WrappedRequest) request :
                new WrappedRequest(request);

        String t3Sign = myRequestWrapper.getHeader(SIGN);
        log.info("uri:{} EncryptFilter t3Sign: {}", request.getRequestURI(), t3Sign);
        //没有t3Sign 直接阻断
        if (StringUtils.isEmpty(t3Sign)) {
            this.emptySignCheck(response, filterChain, myRequestWrapper);
            return;
        }

        //有t3Sign,进行加解密处理
        this.signCheck(t3Sign, myRequestWrapper, filterChain, response);
    }

    /**
     * sign签名为空，判断 开关关闭，直接放行，开关打开，阻断
     *
     * @param response         响应提
     * @param filterChain      过滤链接
     * @param myRequestWrapper 请求
     * @throws IOException      异常
     * @throws ServletException 异常
     */
    public void emptySignCheck(HttpServletResponse response, FilterChain filterChain,
                               WrappedRequest myRequestWrapper) throws IOException, ServletException {
        if (filterEmpty) {
            //直接阻断
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter()
                    .write(JSON.toJSONString(Response.createError(ERR_MSG_SIGN_ERROR, ERR_CODE_SIGN_ERROR)));
            log.error("sign convert error for sign check:{}", myRequestWrapper.getBody());
            return;
        }
        //判断 h5 小程序 是否直接阻断
        if (filterEmptyMini) {
            String grayVersion = myRequestWrapper.getHeader("grayVersion");
            boolean b = StringUtils.isBlank(grayVersion) || (!grayVersion.startsWith(CommonNumConst.STR_GRAV_A)
                    && !grayVersion.startsWith(CommonNumConst.STR_GRAV_I));
            if (b) {
                //非安卓 ios端请求，直接阻断
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter()
                        .write(JSON.toJSONString(Response.createError(ERR_MSG_SIGN_ERROR, ERR_CODE_SIGN_ERROR)));
                log.error("sign convert error for sign check app:{}", myRequestWrapper.getBody());
                return;
            }
        }
        filterChain.doFilter(myRequestWrapper, response);
    }

    /**
     * 签名校验
     *
     * @param t3Sign           签名
     * @param myRequestWrapper 请求对象
     * @param filterChain      过滤链
     * @param response         返回结果
     * @throws IOException io异常
     */
    public void signCheck(String t3Sign, WrappedRequest myRequestWrapper, FilterChain filterChain,
                          HttpServletResponse response) throws IOException {
        try {
            //解析出ase秘钥，为空直接阻断
            String aseKey = RSAUtils.decrypt(t3Sign, privateKey);
            if (StringUtils.isEmpty(aseKey)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter()
                        .write(JSON.toJSONString(Response.createError(ERR_MSG_SIGN_ERROR, ERR_CODE_SIGN_ERROR)));
                log.error("sign convert error for rsa decrypt :{}", myRequestWrapper.getBody());
                return;
            }

            //进行解密 body 主体
            String body = myRequestWrapper.getBody();
            JSONObject json = JSONObject.parseObject(body);
            String bodyContent = (String) json.get("content");
            log.info("EncryptFilter content: {}", bodyContent);
            String message = AESUtil.decryptByECB(bodyContent, aseKey);
            if (StringUtils.isEmpty(message)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter()
                        .write(JSON.toJSONString(Response.createError(ERR_MSG_SIGN_ERROR, ERR_CODE_SIGN_ERROR)));
                log.error("sign convert error for aes decrypt:{}", myRequestWrapper.getBody());
                return;
            }

            //正常解析出来，直接重置body,流程通过
            myRequestWrapper.setBody(message);
            //重新
            filterChain.doFilter(myRequestWrapper, response);
        } catch (Exception e) {
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter()
                    .write(JSON.toJSONString(Response.createError(ERR_MSG_SIGN_ERROR, ERR_CODE_SIGN_ERROR)));
            log.error("sign convert error for other:{}", myRequestWrapper.getBody(), e);
        }
    }
}
