package com.t3.ts.pay.center.api.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class PayStatusQueryReqV2 {
    /**
     * 查询类型
     */
    private Integer type;
    /**
     * 行程订单uuid
     */
    @NotBlank(message = "結算uuid不能为空")
    @ApiModelProperty(value = "結算uuid", required = true)
    private String settlementID;

    private String orderUuid;
    /**
     * 用户uuid
     */
    private String userIds;

    /**
     * 预付款类型 1：企业
     */
    private String enterprise;

    /**
     * 是否需要订单详细信息 true/false
     */
    private String needDetail;

}
