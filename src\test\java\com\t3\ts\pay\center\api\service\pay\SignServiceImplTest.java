package com.t3.ts.pay.center.api.service.pay;

import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.account.center.service.ThirdSignService;
import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:07
 */
public class SignServiceImplTest {
    @Mock
    PayHandlerFactory factory;
    @Mock
    ThirdSignService thirdSignService;
    @Mock
    AccountSignService accountSignService;
    @Mock
    Logger log;
    @InjectMocks
    SignServiceImpl signServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOpenSecretFree() throws Exception {
        Response<?> result = signServiceImpl.openSecretFree(new SecretFreeReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCloseSecretFree() throws Exception {
        Response<?> result = signServiceImpl.closeSecretFree(new SecretFreeReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQuerySignStatus() throws Exception {
        Response<?> result = signServiceImpl.querySignStatus("userId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testPriority() throws Exception {
        Response<?> result = signServiceImpl.priority(new SecretFreeReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQuerySignStatusByChannel() throws Exception {
        Response<?> result = signServiceImpl.querySignStatusByChannel(new PayChannelReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQuerySignList() throws Exception {
        Response<?> result = signServiceImpl.querySignList("userUid");
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
