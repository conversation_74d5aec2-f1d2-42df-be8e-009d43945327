
package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.center.api.constants.CommonNumConst;

import java.math.BigDecimal;

/**
 * 数据转换
 *
 * <AUTHOR>
 */
public final class DataTransfer {
    private DataTransfer() {
    }

    /**
     * 数据转换
     *
     * @param fare 参数
     * @return 出参
     */
    public static BigDecimal getBigDecimal(Object fare) {
        return null == fare ? BigDecimal.ZERO
                : (new BigDecimal(String.valueOf(fare))).setScale(CommonNumConst.NUM_2, CommonNumConst.NUM_4);
    }

    /**
     * 数据转换
     *
     * @param fare 参数
     * @return 出参
     */
    public static Double getDouble(Object fare) {
        return null == fare ? 0.0D : Double.valueOf(String.valueOf(fare));
    }

    /**
     * 数据转换
     *
     * @param obj 参数
     * @return 出参
     */
    public static String toString(Object obj) {
        return obj == null ? null : obj.toString();
    }
}
