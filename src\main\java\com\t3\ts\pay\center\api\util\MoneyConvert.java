package com.t3.ts.pay.center.api.util;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * Description: 金额转换
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/8/23/0023 15:10
 */
public class MoneyConvert {

    /**
     * int或零转换成单位元的bigDecimal
     *
     * @param money 钱
     * @return {@link BigDecimal}
     */
    public static BigDecimal intOrNullConvertBig(Integer money) {
        return BigDecimal.valueOf(Objects.isNull(money) ? 0 : money)
                .setScale(Constants.N_2, BigDecimal.ROUND_DOWN)
                .divide(new BigDecimal(Constants.N_100), BigDecimal.ROUND_DOWN);
    }

    /**
     * Long或零转换成单位元的bigDecimal
     *
     * @param money 钱
     * @return {@link BigDecimal}
     */
    public static BigDecimal longOrNullConvertBig(Long money) {
        return BigDecimal.valueOf(Objects.isNull(money) ? 0 : money)
                .setScale(Constants.N_2, BigDecimal.ROUND_DOWN)
                .divide(new BigDecimal(Constants.N_100), BigDecimal.ROUND_DOWN);
    }

}
