package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 司机支付配置
 *
 * <AUTHOR>
 */
@Data
@ApiModel("司机支付配置")
public class DriverPayConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接口公钥
     */
    @ApiModelProperty(value = "公钥")
    private String payV2Key;


}
