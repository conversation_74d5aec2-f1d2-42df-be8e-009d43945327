package com.t3.ts.pay.center.api.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 10:30
 *
 * @author: ccq
 * @Date: 2020/6/16 17:35
 * @Description: 发票行程单
 */
@Data
@ApiModel(value = "发票行程单")
public class InvoiceRouteV3Vo implements Serializable {
    private static final long serialVersionUID = 7836010216074402119L;
    /**
     * 行程id
     */
    @ApiModelProperty(value = "行程id")
    private String routeUuid;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 起点
     */
    @ApiModelProperty(value = "起点")
    private String originAddress;
    /**
     * 终点
     */
    @ApiModelProperty(value = "终点")
    private String destAddress;
    /**
     * 出发时间
     */
    @ApiModelProperty(value = "出发时间")
    private Date deparTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;
    /**
     * 附加费用（不可开票费用=附加服务费用+高速费+路桥费+停车费+其他费）
     */
    @ApiModelProperty(value = "附加费用（不可开票费用=附加服务费用+高速费+路桥费+停车费+其他费）")
    private BigDecimal additionalMoney;
    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private String billMoney;

    /**
     * 支付信息
     */
    @ApiModelProperty(value = "支付信息")
    private List<PayInfo> payInfoList;
    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    @ApiModelProperty(value = "运力类型(1 T3运力,2 东风运力,3 一汽运力)")
    private Integer transportType;

    /**
     * 发票主体代码
     */
    @ApiModelProperty(value = "发票主体代码")
    private Integer invoiceSubjectCode;
    /**
     * 发票来源（0 平台开票）
     */
    @ApiModelProperty(value = "发票来源（0 平台开票）")
    private Integer invoiceSource;

    @ApiModelProperty(value = "业务线划分 1 出租车; 2专享; 4快享; 5顺风车; 10包车; 14企业用车")
    private Integer productLine;

    /**
     * 开票主体
     */
    private String invoiceSubjectName;

    /**
     * 开票内容
     */
    private String invoiceContent;

}
