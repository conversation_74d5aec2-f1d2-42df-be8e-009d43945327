package com.t3.ts.pay.center.api.util;

import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:01
 */
public class AESUtilTest {

    @Test
    public void testDecryptByECB() throws Exception {
        String result = AESUtil.decryptByECB("data", "key");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
