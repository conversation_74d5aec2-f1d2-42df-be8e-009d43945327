package com.t3.ts.pay.center.api.enums;

import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-15 23:01
 * @des: 支付通道枚举
 */
public enum PayChannelEnum {

    /**
     *
     */
    ALIPAY(EnumPayOrderChannel.ALIPAY.getCode(), "支付宝", "aliPayServiceImpl"),
    ALIPAY_H5(EnumPayOrderChannel.ALIPAY_H5.getCode(), "支付宝h5", "aliH5PayServiceImpl"),
    WEIXIN(EnumPayOrderChannel.WEIXIN.getCode(), "微信", "wxPayServiceImpl"),
    WEIXIN_H5(EnumPayOrderChannel.WEIXIN_H5.getCode(), "微信h5", "wxH5PayServiceImpl"),
    NETCOM(EnumPayOrderChannel.NETCOM.getCode(), "一网通", "netcomPayServiceImpl"),
    NETCOM_MINI(EnumPayOrderChannel.NETCOM_MINI_PAY.getCode(), "一网通小程序", "netcomMiniPayServiceImpl"),
    BALANCE(EnumPayOrderChannel.BALANCE.getCode(), "余额", "balanceServiceImpl"),
    UNIONPAY(EnumPayOrderChannel.UNIONPAY.getCode(), "银联支付", "unionPayServiceImpl"),
    WECHATMINIPROGRAM(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode(), "微信小程序支付", "wxAppletPayServiceImpl"),
    ALIMINIPROGRAM(EnumPayOrderChannel.ALIPAY_MINI.getCode(), "支付宝小程序支付", "aliAppletPayServiceImpl"),
    CMB_AGGREGATE_WX_MINI_PAY(EnumPayOrderChannel.CMB_AGGREGATE_WX_MINI_PAY.getCode(), "聚合支付(微信小程序支付)",
            "cmbAggregatePayServiceImpl"),
    CMB_AGGREGATE_WX_UNIFIED_PAY(EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode(), "聚合支付(微信统一支付)",
            "CmbAggregateUnifiedPayServiceImpl"),
    CMB_AGGREGATE_ALI_APP_PAY(EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode(), "聚合支付(支付宝APP支付)",
            "cmbAggregateAliPayServiceImpl"),
    UNION_QUICK_PASS_PAY(EnumPayOrderChannel.UNION_QUICK_PASS_PAY.getCode(), "云闪付",
            "unionQuickPassPayServiceImpl"),

    UNION_OMNI_PAY(EnumPayOrderChannel.UNION_JS_PAY.getCode(), "银联二维码统一下单",
            "unionJsPayServiceImpl"),
    CMBLIFE(EnumPayOrderChannel.CMBLIFE.getCode(), "掌上生活", "cmblifePayServiceImpl"),
    DOUYIN_MINI_PAY(EnumPayOrderChannel.DOUYIN_MINI_PAY.getCode(), "抖音小程序",
            "douYinMiniPayServiceImpl"),
    BESTPAY_AGGREGATE_WX_UNIFIED(EnumPayOrderChannel.BESTPAY_AGGREGATE_WX.getCode(), "翼支付聚合(微信统一支付)",
            "CmbAggregateUnifiedPayServiceImpl"),
    ;

    private final Integer payType;
    private final String payTypeName;
    private final String payChannelBean;

    PayChannelEnum(Integer payType, String payTypeName, String payChannelBean) {
        this.payType = payType;
        this.payTypeName = payTypeName;
        this.payChannelBean = payChannelBean;
    }

    public static PayChannelEnum getPayChannelEnumByPayType(Integer payType) {
        if (null == payType) {
            return null;
        }
        for (PayChannelEnum payEnum : PayChannelEnum.values()) {
            if (payEnum.getPayType().equals(payType)) {
                return payEnum;
            }
        }
        return null;
    }

    /**
     * 得到第三支付通道枚举
     *
     * @param payTypeList 支付类型列表
     * @return {@link PayChannelEnum}
     */
    public static PayChannelEnum getThirdPayChannelEnum(List<Integer> payTypeList) {
        for (Integer payType : payTypeList) {
            if (matchAnyThirdPay(payType, PayChannelEnum.WEIXIN, PayChannelEnum.ALIPAY, PayChannelEnum.NETCOM,
                    PayChannelEnum.UNIONPAY)) {
                return getPayChannelEnumByPayType(payType);
            }
        }
        return null;
    }

    /**
     * 匹配任何第三支付
     *
     * @param payType         支付类型
     * @param payChannelEnums 支付通道枚举
     * @return boolean
     */
    public static boolean matchAnyThirdPay(Integer payType, PayChannelEnum... payChannelEnums) {
        for (PayChannelEnum payChannelEnum : payChannelEnums) {
            if (payChannelEnum.getPayType().intValue() == payType) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public Integer getPayType() {
        return payType;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public String getPayChannelBean() {
        return payChannelBean;
    }
}
