spring:
  application:
    name: pay-center-api
  #  cloud:
  #    consul:
  #      host: 127.0.0.1
  #      port: 8500
  #      discovery:
  #        healthCheckInterval: 5s
  #        service-name: ${spring.application.name}
  #        health-check-url: http://${spring.cloud.client.ip-address}:${server.port}/healthCheck
  #        instanceId: ${spring.application.name}:${spring.cloud.client.ip-address}
  #        prefer-ip-address: true
  redis:
    database: 0
    host: r-6el31b5bbf944bf4.redis.rds.ops.t3go.com
    port: 6379
    password: AR5AsKylvSdq8KIY
    timeout: 0
    jedis:
      pool:
        max-idle: 10
        min-idle: 10
        max-wait: 2001
        max-active: 15
server:
  port: 20618
dubbo:
  application:
    name: pay-center-api
  protocol:
    port: -1
    name: dubbo
  registry:
    address: zookeeper://***********:2181?backup=***********:2181,***********:2181

  consumer:
    cache: false
    check: false
    timeout: 5000

swagger:
  enable: true

platform:
  switch:
    #是否开启立即支付 FALSE--非立即支付（默认值） TRUE--立即支付
    isPayInstant: false
    # 是否是招行聚合非生产环境
    cmbAggEnvUat: true
    # 是否支付招行聚合支付
    supportCmbAgg: true
    # 招行聚合支付的白名单
    cmbAggWhiteList: "1"
    # 招行聚合支付的百分比0-127
    cmbAggPercent: 100
    cmbAggNewPayUrl: pages/newPay/index?
    # app是否展示校验三方支付方式必选提示-降级开关
    thirdPayTypeToast: true
    # app是否展示校验三方支付方式必选提示信息
    thirdPayTypeToastMsg: "本订单含服务费或券套餐，需勾选第三方进行支付"

callWaitConfig:
  # 展示正计时时间（单位：秒）
  showTime: 300
  # 实时单呼叫等待最大超时时间（单位：秒）
  realTimeMaxWaitTime: 600
  # 预约单呼叫等待最大超时时间（单位：秒）
  appointMaxWaitTime: 900
  # 同时呼叫追加呼叫等待实际(单位:秒)
  thirdRingMaxWaitTime: 120

# 小程序
wechatMiniProgram:
  appId: wxe241a1d8464bc578
  appSecret: 85e43f959ed3983043a43512d550458d

#各中台SLB地址
slb:
  route: http://***********:8082/route-gateway-api
  gis: http://***********:8082/gis-api
  mall: http://***********:8082/mall-gateway-api
  marketing: http://***********:8082/marketing-api
  travelconfig: http://***********:8082/operation-gateway

#微信支付分
wechatPayPoints:
  enabled: true
  btnTitle: 先乘后付（下车后自动付款）
  btnSubTitle: 微信支付分 | 550分及以上信用优享
  btnIcon: http://devbucket.oss-cn-chongqing-t3go-d01-a.res.t3go.com/files/20210408155657696/T3_ADMIN/20210408155657696.png

logging:
  level:
    root: info
    org.apache.zookeeper.ClientCnxn: error
    jdbc.audit: error
    jdbc.resultset: error
    jdbc.sqlonly: error
    com.t3.ts.cache.redis: error
    com.t3.ts.kafka.consumer: error
