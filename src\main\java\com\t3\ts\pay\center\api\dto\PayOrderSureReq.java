package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 商品详情入参
 *
 * <AUTHOR>
 * @version 1.0.0 2019-11-21
 */
@Data
public class PayOrderSureReq implements java.io.Serializable {

    /**
     * 商品编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderCode;


    /**
     * 兼容历史单渠道支付，多渠道时候这里保留三方渠道
     * 支付渠道 现金支付渠道，现金支付渠道，0,”未知” 1,”支付宝” ；
     * 2,”微信”；3,”一网通”，4,”余额”，5,”优惠券”，6,”线下支付”，7,”营销或者客服操作”，8,”银联支付”;24,支付宝H5；25,
     * 微信H5；26,微信_JSAPI;28,支付宝_JSAPI
     */
    private Integer payOrderChannel = 0;

    /**
     * 支付渠道 现金支付渠道，现金支付渠道，0,”未知” 1,”支付宝” ；
     * 2,”微信”；3,”一网通”，4,”余额”，5,”优惠券”，6,”线下支付”，7,”营销或者客服操作”，8,”银联支付”;24,支付宝H5；25,
     * 微信H5；26,微信_JSAPI;28,支付宝_JSAPI
     */
    @ApiModelProperty(value = "支付渠道列表")
    private List<Integer> payChannels;

    /**
     * 支付宝  用户付款中途退出返回商户网站的地址
     */
    private String quitUrl;

    /**
     * 浏览器扫码微信  WAP网站URL地址
     */
    private String wapUrl;
    /**
     * 浏览器扫码微信  WAP 网站名
     */
    private String wapName;

    /**
     * 微信或者支付宝code-微信或者支付宝
     */
    private String code;

    private String userId;
    private Integer accountType;
    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;

    private String openId;

    private Integer dataSource;


    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 外网IP
     */
    private String outIp;

    /**
     * 来源
     * 注释同 {@link com.t3.ts.pay.center.api.dto.trade.RechargeReq.subSource}  字段注释
     */
    private String subSource;

    /**
     * 预付款 prePay、取消付款 cancel 、立即付款  payNow
     */
    private String payTypeName;
}

