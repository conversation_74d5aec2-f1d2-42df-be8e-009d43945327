package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.UnionPayConsumeDto;
import com.t3.ts.pay.remote.dto.UnionPayOpenDto;
import com.t3.ts.pay.remote.service.UnionPayService;
import com.t3.ts.result.Response;
import java.util.Objects;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 银联支付
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("unionPayServiceImpl")
public class UnionPayServiceImpl implements PayChannelService {

    @DubboReference
    private UnionPayService unionPayService;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.UNIONPAY.getCode());
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        //免密支付
        if (paymentInfoBo.isNoSecret()) {
            UnionPayConsumeDto dto = new UnionPayConsumeDto();
            dto.setOrderId(paymentResp.getMsg());
            dto.setUserId(paymentInfoBo.getPassengerUuid());
            dto.setTrId(paymentInfoBo.getPassengerMobile());
            Response consume = unionPayService.consume(dto);
            rechargePayVo.setSdk(consume.getMsg());
            if (!consume.isSuccess()) {
                rechargePayVo.setCode(NumConstants.STR_500);
                rechargePayVo.setSdk("银联免密支付失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        } else {
            // 未开通银联免密支付,调用开通免密并支付接口
            UnionPayOpenDto dto = new UnionPayOpenDto();
            dto.setOrderId(paymentResp.getMsg());
            dto.setUserId(paymentInfoBo.getPassengerUuid());
            dto.setTrId(paymentInfoBo.getPassengerMobile());
            Response openAndConsumeInfo = unionPayService.getOpenAndConsumeInfo(dto);
            rechargePayVo.setSdk(String.valueOf(openAndConsumeInfo.getData()));
            if (!openAndConsumeInfo.isSuccess() || Objects.isNull(openAndConsumeInfo.getData())) {
                rechargePayVo.setCode(NumConstants.STR_500);
                rechargePayVo.setSdk("获取银联支付串失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        }
        return Response.createSuccess(rechargePayVo);
    }
}
