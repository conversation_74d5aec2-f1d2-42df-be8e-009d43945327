package com.t3.ts.pay.center.api.business.common;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import org.apache.dubbo.config.annotation.DubboReference;
import com.commons.utils.Base64;
import com.t3.ts.channelmgr.center.service.PayChannelService;
import com.t3.ts.integrated.service.WechatUserInfoService;
import com.t3.ts.pay.center.api.business.wechat.Code2Session;
import com.t3.ts.pay.center.api.business.wechat.Code2SessionEnum;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.dto.Code2AccessToken;
import com.t3.ts.pay.center.api.enums.PayChannelEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;

import java.security.spec.AlgorithmParameterSpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_0;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_100;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.STR_GRAV_A;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.STR_GRAV_I;

/**
 * 微信
 *
 * <AUTHOR>
 * @date 2019-04-28
 */
@Component
public class CmbAggHelper {
    private static final Logger LOG = LoggerFactory.getLogger(CmbAggHelper.class);

    private static final String CHAR_NAME = "UTF-8";

    private static final String DEF_VERSION = "480";

    private static final String DEF_WX_VERSION_A = "530";

    private static final String DEF_WX_VERSION_I = "530";

    /**
     * 小程序 appid
     */
    @Value("${wechatMiniProgram.appId}")
    private String appId;

    @Value("${wechat.appId}")
    private String wxAppId;

    @Value("${wechat.appSecret}")
    private String wxAppSecret;
    /**
     * 小程序 appSecret
     */
    @Value("${wechatMiniProgram.appSecret}")
    private String appSecret;

    @DubboReference
    private WechatUserInfoService wechatUserInfoService;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private PayChannelService payChannelService;

    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;


    /**
     * 获取微信openid、unionid、session_key
     *
     * @param code 代码
     * @return {@link Code2Session}
     */
    public Code2Session code2Session(String code) {
        // 获取头像和昵称链接
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type"
                + "=authorization_code";
        try {
            url = StrUtil.format(url, appId, appSecret, code);
            Response<String> response = wechatUserInfoService.getThirdPartInfoByUrl(url);
            String returnMsg = response.getData();
            LOG.info("微信小程序code:{}，解密返回{}", code, returnMsg);

            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(returnMsg);
            if (null != jsonObject) {
                jsonObject.put("sessionKey", jsonObject.getString("session_key"));
                jsonObject.remove("session_key");
                return JSONUtil.toBean(jsonObject.toString(), Code2Session.class);
            }
            return null;
        } catch (Exception e) {
            LOG.error("微信code2Session接口调用失败");
        }
        return null;
    }

    /**
     * 获取微信openid、unionid、session_key
     *
     * @param code code
     * @return Code2AccessToken
     */
    public Code2AccessToken code2AccessToken(String code) {
        // 获取头像和昵称链接
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={}&secret={}&code={}&grant_type"
                + "=authorization_code";
        try {
            url = StrUtil.format(url, wxAppId, wxAppSecret, code);
            Response<String> response = wechatUserInfoService.getThirdPartInfoByUrl(url);
            String returnMsg = response.getData();
            LOG.info("微信code:{}，解密返回{}", code, returnMsg);
            return JSONUtil.toBean(returnMsg, Code2AccessToken.class);
        } catch (Exception e) {
            LOG.error("微信code2AccessToken接口调用失败", e);
        }

        return null;
    }

    /**
     * 获取微信聊天电话号码
     *
     * @param sessionKey    会话密钥
     * @param iv            4
     * @param encryptedData 加密的数据
     * @return {@link String}
     */
    public String getWeChatPhoneNumber(String sessionKey, String iv, String encryptedData) {
        String msg = weChatDecrypt(sessionKey, iv, encryptedData);
        if (StringUtils.isBlank(msg)) {
            return null;
        }
        JSONObject jsonObject = JSONUtil.parseObj(msg);
        return jsonObject.getStr("purePhoneNumber");
    }

    /**
     * 解密微信用户信息
     *
     * @param sessionkey sessionkey
     * @param ivStr      第四str
     * @param encDataStr str enc数据
     * @return {@link String}
     * @date 2019年05月08日
     */
    public String weChatDecrypt(String sessionkey, String ivStr, String encDataStr) {
        try {
            byte[] encData = Base64.decode(encDataStr);
            byte[] iv = Base64.decode(ivStr);
            byte[] key = Base64.decode(sessionkey);
            AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
            // 躲避sonar检测误报
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding ".trim());
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            return new String(cipher.doFinal(encData), CHAR_NAME);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 获取微信登录code
     *
     * @param code 代码
     * @return {@link Response<String>}
     */
    public Response<String> getWMPOpenId(String code) {
        // 小程序支付方式
        if (StringUtils.isBlank(code)) {
            return Response.createError(Code2SessionEnum.INVALID);
        }

        Code2Session code2Session = code2Session(code);
        if (ObjectUtil.isNull(code2Session) || StringUtils.isBlank(code2Session.getOpenid())) {
            return Response.createError(Code2SessionEnum.INVALID);
        }
        return Response.createSuccess("微信小程序用户信息解析成功", code2Session.getOpenid());
    }

    /**
     * 得到wx开放id
     *
     * @param openId      代码
     * @param code        代码
     * @param paywayEnums payway枚举
     * @return {@link Response<String>}
     * @param:
     * @return:
     * @Description: 获取微信小程序用户openid
     * @Author: zmr
     * @Date: 2020/5/19 14:40
     */
    public Response<String> getWxOpenId(String openId, String code, PaywayEnum[] paywayEnums) {
        if (BooleanUtil.isFalse(isWxPay(paywayEnums))) {
            return Response.createSuccess();
        }
        // 如果前端传了openId 就直接返回
        if (StringUtils.isNotEmpty(openId)) {
            return Response.createSuccess("获取成功", openId);
        }
        // 小程序支付方式
        if (StringUtils.isBlank(code)) {
            return Response.createError(Code2SessionEnum.INVALID);
        }

        Code2AccessToken code2AccessToken = code2AccessToken(code);
        if (ObjectUtil.isNull(code2AccessToken) || StringUtils.isBlank(code2AccessToken.getOpenid())) {
            return Response.createError(Code2SessionEnum.getEnumByCode(code2AccessToken.getErrcode()));
        }
        return Response.createSuccess("微信用户信息解析成功", code2AccessToken.getOpenid());
    }

    /**
     * 换成聚合支付类型
     *
     * @param payTypeList    支付类型列表
     * @param aggPayTypeList 端上支持的聚合支付渠道
     * @param passenger      乘客
     * @param paymentDto     paymentDto
     */
    public void convertAggChannel(List<Integer> payTypeList, List<Integer> aggPayTypeList, String passenger,
                                  PaymentDto paymentDto) {
        Response<Set<String>> demoteChannelResp = null;
        try {
            // 这里防止异常影响主链路
            demoteChannelResp = payChannelService.getDemoteChannelSet();
        } catch (Exception e) {

        }
        // 配合招行聚合支付
        //微信聚合渠道转换
        if (switchConfig.getSupportCmbAgg() && payTypeList.contains(PayChannelEnum.WEIXIN.getPayType())
                && aggPayTypeList.contains(PayChannelEnum.WEIXIN.getPayType())
                && switchCmbAgg(passenger)
                && checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType())) {
            //如果是不支持免密支付，招行聚合支付替换微信支付
            if (checkAggregateWxUnifiedPay(aggPayTypeList, passenger, paymentDto)) {
                // 这里单独处理，渠道降级是从统一支付降级到微信直连
                if (checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_WX_UNIFIED_PAY.getPayType())) {
                    // 微信聚合小程序切换 统一下单
                    payTypeList.remove(PayChannelEnum.WEIXIN.getPayType());
                    payTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_UNIFIED_PAY.getPayType());
                    paymentDto.getExtendParams().put("t3MiniGray", Boolean.TRUE);
                }
            } else {
                // 微信小程序下单
                payTypeList.remove(PayChannelEnum.WEIXIN.getPayType());
                payTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType());
            }
        }
        //支付宝聚合渠道转换
        if (switchConfig.getSupportCmbAggAli() && payTypeList.contains(PayChannelEnum.ALIPAY.getPayType())
                && aggPayTypeList.contains(PayChannelEnum.ALIPAY.getPayType())
                && checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_ALI_APP_PAY.getPayType())) {
            //如果是不支持免密支付，招行聚合支付替换支付宝支付
            if (switchCmbAgg(passenger, switchConfig.getCmbAggAliBlackList(), switchConfig.getCmbAggAliWhiteList(),
                    switchConfig.getCmbAggAliPercent())) {
                payTypeList.remove(PayChannelEnum.ALIPAY.getPayType());
                payTypeList.add(PayChannelEnum.CMB_AGGREGATE_ALI_APP_PAY.getPayType());
            }
        }
    }

    /**
     * 换成聚合支付类型
     *
     * @param payTypeList    支付类型列表
     * @param aggPayTypeList 端上支持的聚合支付渠道
     * @param driverUuid     司机
     * @param paymentDto     paymentDto
     */
    public void convertAggChannelForDriver(List<Integer> payTypeList, List<Integer> aggPayTypeList, String driverUuid,
                                           PaymentDto paymentDto) {
        Response<Set<String>> demoteChannelResp = null;
        try {
            // 这里防止异常影响主链路
            demoteChannelResp = payChannelService.getDemoteChannelSet();
        } catch (Exception e) {

        }
        // 配合招行聚合支付
        //微信聚合渠道转换
        if (switchConfig.getDriverSupportCmbAgg() && payTypeList.contains(PayChannelEnum.WEIXIN.getPayType())
                && aggPayTypeList.contains(PayChannelEnum.WEIXIN.getPayType())
                && switchCmbAggForDriver(driverUuid)
                && checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType())) {
            //如果是不支持免密支付，招行聚合支付替换微信支付
            if (checkAggregateWxUnifiedPay(aggPayTypeList, driverUuid, paymentDto)) {
                // 这里单独处理，渠道降级是从统一支付降级到微信直连
                if (checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_WX_UNIFIED_PAY.getPayType())) {
                    // 微信聚合小程序切换 统一下单
                    payTypeList.remove(PayChannelEnum.WEIXIN.getPayType());
                    payTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_UNIFIED_PAY.getPayType());
                    paymentDto.getExtendParams().put("t3MiniGray", Boolean.TRUE);
                }
            } else {
                // 微信小程序下单
                payTypeList.remove(PayChannelEnum.WEIXIN.getPayType());
                payTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType());
            }
        }
        //支付宝聚合渠道转换
        if (switchConfig.getDriverSupportCmbAggAli() && payTypeList.contains(PayChannelEnum.ALIPAY.getPayType())
                && aggPayTypeList.contains(PayChannelEnum.ALIPAY.getPayType())
                && checkChannelDemote(demoteChannelResp, PayChannelEnum.CMB_AGGREGATE_ALI_APP_PAY.getPayType())) {
            //如果是不支持免密支付，招行聚合支付替换支付宝支付
            if (switchCmbAgg(driverUuid, switchConfig.getDriverCmbAggAliBlackList(), switchConfig.getDriverCmbAggAliWhiteList(),
                    switchConfig.getDriverCmbAggAliPercent())) {
                payTypeList.remove(PayChannelEnum.ALIPAY.getPayType());
                payTypeList.add(PayChannelEnum.CMB_AGGREGATE_ALI_APP_PAY.getPayType());
            }
        }
    }

    /**
     * 微信聚合小程序切换 统一下单
     *
     * @param aggPayTypeList aggPayTypeList
     * @param passenger      passenger
     * @param paymentDto     paymentDto
     * @return boolean
     */
    private boolean checkAggregateWxUnifiedPay(List<Integer> aggPayTypeList, String passenger, PaymentDto paymentDto) {
        return aggPayTypeList.contains(PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType())
                && switchCmbAggT3Mini(passenger)
                && ObjectUtil.isNotNull(paymentDto);
    }

    /**
     * 判断聚合渠道是否降级
     *
     * @param demoteChannelResp demoteChannelResp
     * @param payChannel        payChannel
     * @return boolean
     */
    private boolean checkChannelDemote(Response<Set<String>> demoteChannelResp, Integer payChannel) {
        if (demoteChannelResp == null || CollectionUtils.isEmpty(demoteChannelResp.getData())) {
            return true;
        }
        for (String demoteChannel : demoteChannelResp.getData()) {
            if (demoteChannel.equals(payChannel.toString())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 聚合支付
     *
     * @param passengerUuid 乘客id
     * @return q
     */
    private boolean switchCmbAgg(String passengerUuid) {
        if (switchConfig.getCmbAggBlackList().contains(passengerUuid)) {
            return false;
        }
        if (switchConfig.getCmbAggWhiteList().contains(passengerUuid)) {
            return true;
        }
        int hashCode = passengerUuid.hashCode();
        if (hashCode == Integer.MIN_VALUE) {
            hashCode = Integer.MAX_VALUE;
        }
        int hashCodeAbs = Math.abs(hashCode);
        Integer index = hashCodeAbs % NUM_100;
        if (index <= switchConfig.getCmbAggPercent()) {
            return true;
        }
        return false;
    }

    /**
     * 聚合支付
     *
     * @param driverUuid 乘客id
     * @return q
     */
    private boolean switchCmbAggForDriver(String driverUuid) {
        if (switchConfig.getDriverCmbAggBlackList().contains(driverUuid)) {
            return false;
        }
        if (switchConfig.getDriverCmbAggWhiteList().contains(driverUuid)) {
            return true;
        }
        int hashCode = driverUuid.hashCode();
        if (hashCode == Integer.MIN_VALUE) {
            hashCode = Integer.MAX_VALUE;
        }
        int hashCodeAbs = Math.abs(hashCode);
        Integer index = hashCodeAbs % NUM_100;
        if (index <= switchConfig.getDriverCmbAggPercent()) {
            return true;
        }
        return false;
    }

    /**
     * 聚合支付 跳转t3小程序 配置项
     *
     * @param passengerUuid 乘客id
     * @return true | false
     */
    public boolean switchCmbAggT3Mini(String passengerUuid) {
        if (switchConfig.getCmbAggT3MiniBlackList().contains(passengerUuid)) {
            return false;
        }
        if (switchConfig.getCmbAggT3MiniWhiteList().contains(passengerUuid)) {
            return true;
        }
        int hashCode = passengerUuid.hashCode();
        if (hashCode == Integer.MIN_VALUE) {
            hashCode = Integer.MAX_VALUE;
        }
        int hashCodeAbs = Math.abs(hashCode);
        int index = hashCodeAbs % NUM_100;
        return index <= switchConfig.getCmbAggT3MiniPercent();
    }

    /**
     * 聚合支付开关
     *
     * @param passengerUuid 乘客uuid
     * @param blackList     黑名单
     * @param whiteList     白名单
     * @param percent       放量百分比
     * @return boolean
     */
    private boolean switchCmbAgg(String passengerUuid, List<String> blackList, List<String> whiteList, int percent) {
        if (null != blackList && blackList.contains(passengerUuid)) {
            return false;
        }
        if (null != whiteList && whiteList.contains(passengerUuid)) {
            return true;
        }
        int hashCode = passengerUuid.hashCode();
        if (hashCode == Integer.MIN_VALUE) {
            hashCode = Integer.MAX_VALUE;
        }
        int hashCodeAbs = Math.abs(hashCode);
        Integer index = hashCodeAbs % NUM_100;
        if (index <= percent) {
            return true;
        }
        return false;
    }


    /**
     * 识别端上能支持的聚合支付的渠道
     * 新增方法默认支持微信聚合支付
     *
     * @param grayBuild    APP版本号
     * @param grayVersion  APP版本号 -- 来自不同开发者的版本号
     * @param payAndSign   支付并签约
     * @param source       来源
     * @param payOrderType 类型
     * @return List<Integer>
     */
    public List<Integer> getCmbAggPayTypeList(String grayBuild, String grayVersion, String payAndSign, int source,
                                              int payOrderType, List<Integer> payTypeList, String userId) {
        List<Integer> aggPayTypeList = new ArrayList<>();
        if (source != CommonNumConst.NUM_101) {
            aggPayTypeList.add(PayChannelEnum.WEIXIN.getPayType());
        }
        if (compareGrayBuild(grayBuild, DEF_VERSION)) {
            int payAndSignInt = StringUtils.isNotBlank(payAndSign) ? Integer.parseInt(payAndSign) : NUM_0;
            if (null == payAndSign || NUM_0 == payAndSignInt) {
                //高于、等于指定版本号则认为支持支付宝聚合支付 && 不是签约并支付
                aggPayTypeList.add(PayChannelEnum.ALIPAY.getPayType());
            }
        }
        // 需要安卓版本才可以 出租车扫码赚钱付 不支持
        if (compareGrayBuild(grayVersion, grayBuild, DEF_WX_VERSION_A, DEF_WX_VERSION_I)
                && EnumPayOrderType.DRIVER_RECOMMEND_PASSENGER_PAY_ROUTE.getCode() != payOrderType) {
            int payAndSignInt = StringUtils.isNotBlank(payAndSign) ? Integer.parseInt(payAndSign) : NUM_0;
            if (null == payAndSign || NUM_0 == payAndSignInt) {
                //高于、等于指定版本号则认为支持聚合小程序微信下单 转成 统一下单
                aggPayTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType());
            }
        }
        return aggPayTypeList;
    }

    /**
     * @param grayVersion   端
     * @param grayBuild     版本号
     * @param defWxVersionA 支持的安卓版本号
     * @param defWxVersionI 支持的ios版本号
     * @return boolean
     */
    private boolean compareGrayBuild(String grayVersion, String grayBuild, String defWxVersionA, String defWxVersionI) {
        if (StringUtils.isBlank(grayVersion)) {
            return false;
        }
        return (grayVersion.startsWith(STR_GRAV_A) && compareGrayBuild(grayBuild, defWxVersionA))
                || (grayVersion.startsWith(STR_GRAV_I) && compareGrayBuild(grayBuild, defWxVersionI));
    }

    /**
     * 判断是否支持机型
     *
     * @param grayVersion 支持版本
     * @param supportSys  支持系统
     * @return boolean
     */
    public boolean checkSystemSupport(String grayVersion, List<String> supportSys) {
        if (StringUtils.isNotBlank(grayVersion)) {
            for (String str : supportSys) {
                if (grayVersion.startsWith(str)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 是否当前版本是否大于等于指定版本
     *
     * @param b1 待比对版本
     * @param b2 指定版本
     * @return boolean true:大于等于 false:小于
     */
    public boolean compareGrayBuild(String b1, String b2) {
        Integer grayBuild = 0;
        if (StringUtils.isNotBlank(b1)) {
            grayBuild = Integer.parseInt(b1);
        }
        if (grayBuild >= Integer.parseInt(b2)) {
            return true;
        }
        return false;
    }

    /**
     * wx支付
     *
     * @param paywayEnums payway枚举
     * @return boolean
     * @param:
     * @return:
     * @Description: 判断是否是微信内支付-true是 false 否
     * @Author: zmr
     * @Date: 2020/5/19 14:40
     */
    public boolean isWxPay(PaywayEnum[] paywayEnums) {
        return Arrays.asList(paywayEnums).contains(PaywayEnum.WEIXIN_JSAPI);
    }

    /**
     * 是否是聚合支付
     *
     * @param paywayEnums paywayEnums
     * @return boolean
     */
    public boolean isCmbAggPay(PaywayEnum[] paywayEnums) {
        return Arrays.stream(paywayEnums).anyMatch(paywayEnum ->
                Arrays.asList("CMB_AGGREGATE_WX_MINI_PAY", "CMB_AGGREGATE_ALI_APP_PAY", "CMB_AGGREGATE_WX_UNIFIED_PAY")
                        .contains(paywayEnum.getCode()));
    }

    /**
     * 判断是否支持 跳转T3自有小程序
     *
     * @param grayBuild grayBuild
     * @return boolean
     */
    public boolean supportCmbAggT3Mini(String grayBuild) {
        return compareGrayBuild(grayBuild, DEF_WX_VERSION_A);
    }
}
