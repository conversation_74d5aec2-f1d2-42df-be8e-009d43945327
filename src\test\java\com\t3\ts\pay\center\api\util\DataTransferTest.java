package com.t3.ts.pay.center.api.util;

import java.math.BigDecimal;
import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:04
 */
public class DataTransferTest {

    @Test
    public void testGetBigDecimal() throws Exception {
        BigDecimal result = DataTransfer.getBigDecimal("fare");
        Assert.assertEquals(new BigDecimal(0), result);
    }

    @Test
    public void testGetDouble() throws Exception {
        Double result = DataTransfer.getDouble("fare");
        Assert.assertEquals(Double.valueOf(0), result);
    }

    @Test
    public void testToString() throws Exception {
        String result = DataTransfer.toString("obj");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
