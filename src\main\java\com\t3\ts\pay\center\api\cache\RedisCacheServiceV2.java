package com.t3.ts.pay.center.api.cache;

import com.t3.ts.cache.t3.type.ExpireListService;
import com.t3.ts.cache.t3.type.ExpireStringService;
import com.t3.ts.cache.t3.type.LockService;
import com.t3.ts.cache.t3.type.StringService;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <Description> 升级T3redis缓存，请使用当前类替代RedisCacheService类<br>
 *
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2020/9/3 <br>
 * @see RedisCacheService <br>
 */

@Slf4j
@Component
public class RedisCacheServiceV2 {

    @Resource
    private T3CacheFactory factory;

    /**
     * 到期字符串
     *
     * @return {@link ExpireStringService}
     */
    private ExpireStringService expireString() {
        return factory.getExpireStringService();
    }

    /**
     * 到期名单
     *
     * @return {@link ExpireListService}
     */
    private ExpireListService expireList() {
        return factory.getExpireListService();
    }

    /**
     * 锁
     *
     * @return {@link LockService}
     */
    private LockService lock() {
        return factory.getLockService();
    }

    /**
     * 字符串
     *
     * @return {@link StringService}
     */
    private StringService string() {
        return factory.getStringService();
    }

    /**
     * 设置数据
     *
     * @param key  键 不能为null
     * @param val  值
     * @param time 过期时间数(秒)
     * @return boolean
     */
    public boolean setValue(String key, Object val, Integer time) {
        try {
            // 不修改过期时间
            return expireString().setExpireValue(key, val, time);

        } catch (Exception e) {
            log.error("设置Redis key:{} value:{} 发生异常:{}", key, val, e.getMessage());
        }
        return false;
    }

    /**
     * 判断key是否存在
     *
     * @param key 键 不能为null
     * @return {@link Boolean}
     */
    public Boolean containsKey(String key) {
        try {
            return expireString().containsKey(key);
        } catch (Exception e) {
            log.error("判断Redis key:{} 是否存在, 发生异常:{}", key, e.getMessage());
        }
        return false;
    }

    /**
     * 设置数据
     *
     * @param key 键 不能为null
     * @param val 值
     * @return boolean
     * @Des 设值不修改过期时间
     */
    public boolean setValueWithRemainingExpire(String key, Object val) {
        try {
            return expireString().setValueWithRemainingExpire(key, val);

        } catch (Exception e) {
            log.error("设置Redis key:{} value:{} 发生异常:{}", key, val, e.getMessage());
        }
        return false;
    }

    /**
     * 获取数据
     * @param key   键 不能为null
     * @param <T>  This describes the type parameter
     * @param clazz clazz
     * @return 数据
     */
    public <T> T getValue(String key, Class<T> clazz) {
        try {
            return expireString().getValue(key, clazz);
        } catch (Exception e) {
            log.error("获取Redis key:{} 发生异常:{}", key, e.getMessage());
        }
        return null;
    }

    /**
     * 获取数据
     *
     * @param key   键 不能为null
     * @param <T>  This describes the type parameter
     * @param clazz clazz
     * @return 数据
     */
    public <T> List<T> getListValue(String key, Class<T> clazz) {
        try {
            return expireList().getListValue(key, 0L, clazz);
        } catch (Exception e) {
            log.error("获取Redis key:{} 发生异常:{}", key, e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     * @return boolean
     */
    public boolean delete(String... key) {
        try {
            return expireString().delete(key);
        } catch (Exception e) {
            log.error("删除 Redis key:{} 发生异常:{}", key, e.getMessage());
        }
        return false;
    }

    /**
     * Description: redis分布式锁的获取<br>
     *
     * @param key <br>
     * @return java.lang.Boolean <br>
     * <AUTHOR> <br>
     * @taskId <br>
     */
    public Boolean getLock(String key) {
        try {
            return lock().lock(key);
        } catch (Exception e) {
            log.error("获取 Redis锁 key:{} 发生异常:{}", key, e.getMessage());
        }
        return false;
    }

    /**
     * Description: redis分布式锁的解锁<br>
     *
     * @param key <br>
     * @return java.lang.Boolean <br>
     * <AUTHOR> <br>
     * @taskId <br>
     */
    public Boolean releaseLock(String key) {
        try {
            return lock().unLock(key);
        } catch (Exception e) {
            log.error("释放 Redis锁 key:{} 发生异常:{}", key, e.getMessage());
        }
        return false;
    }

    /**
     * 设置步进数据
     *
     * @param key  键 不能为null
     * @param incr 步进值
     * @return {@link Long}
     */
    public Long setValueIncr(String key, long incr) {
        try {
            // 不修改过期时间
            return string().setValueIncr(key, incr);

        } catch (Exception e) {
            log.error("设置步进Redis key:{} 步进value:{} 发生异常:{}", key, incr, e.getMessage());
        }
        return null;
    }

    /**
     * 设置值增加
     *
     * @param redisKey 复述,关键
     * @param incr     增加
     * @param seconds  秒
     * @return {@link Long}
     */
    public Long setValueIncr(String redisKey, long incr, int seconds) {
        try {
            return expireString().setExpireValueIncr(redisKey, incr, seconds);
        } catch (Exception e) {
            log.error("RedisComponent.setValueIncr >>> failed : ", e);
        }
        return null;
    }

}
