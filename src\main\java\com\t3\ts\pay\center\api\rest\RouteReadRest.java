package com.t3.ts.pay.center.api.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.RoutePrePayDetailDto;
import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanDetailReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.vo.RouteOnGoingOrderVo;
import com.t3.ts.result.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/12 19:24
 * @description:
 */
@Component
public class RouteReadRest extends BaseRest {

    @Resource
    private SlbConfig slbConfig;

    /**
     * 出行费用明细
     *
     * @param routeFareItemReq 参数
     * @return 出参
     */
    public Response getRouteFareItems(RouteFareItemReq routeFareItemReq) {
        String postHttp = sendPostHttp(slbConfig.getRoute() + "/api/passenger/read/v1/getRouteFareItems",
                JSONObject.toJSONString(routeFareItemReq));
        if (StringUtils.isNotBlank(postHttp)) {
            Response response = JSONObject.parseObject(postHttp, Response.class);
            return response;
        }
        return null;
    }

    /**
     * @param routePlanUuidReq 行程计划Uuid
     * @return Response 出参
     * @throws
     * @Description: 查询预付款变更行程
     * <AUTHOR>
     * @date 2021/9/8 10:51
     */
    public Response getEditRoutePointByCache(RoutePlanUuidReq routePlanUuidReq) {

        String postHttp = sendPostHttp(slbConfig.getRoute() + "/api/passenger/read/v1/route/getEditRoutePointByCache",
                JSONObject.toJSONString(routePlanUuidReq));
        if (StringUtils.isNotBlank(postHttp)) {
            Response response = JSONObject.parseObject(postHttp, Response.class);
            return response;
        }
        return null;
    }


    /**
     * 查询进行中的订单
     * Arrays.asList("0", "1", "2", "3", "4", "5", "6")
     *
     * @param userId      用户id
     * @param routeStatus 行程状态
     * @return Response
     */
    public Response<List<RouteOnGoingOrderVo>> getOnGoingOrder(String userId, List<String> routeStatus) {
        String url = "/api/passenger/read/v1/route/onGoingOrder";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("passengerUuid", userId);
        jsonObject.put("routeStatusList", routeStatus);
        String response = sendPostHttp(slbConfig.getRoute() + url, jsonObject.toJSONString());
        JSONObject object = JSON.parseObject(response);
        return Response.createSuccess("", JSON.parseArray(object.getString("data"), RouteOnGoingOrderVo.class));
    }

    /**
     * 查询预付款详情
     *
     * @param advanceSerial String
     * @param requirementId String
     * @return Response<RoutePrePayDetailDto>
     */
    public Response<RoutePrePayDetailDto> getRoutePrePayDetail(String advanceSerial, String requirementId) {
        String url = "/trip/facade/solution/passenger/require/queryPrepayInfo";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("advanceSerial", advanceSerial);
        jsonObject.put("requirementId", requirementId);
        String response = sendPostHttp(slbConfig.getSolutionGateway() + url, jsonObject.toJSONString());
        JSONObject object = JSON.parseObject(response);
        return Response.createSuccess(JSON.parseObject(object.getString("data"), RoutePrePayDetailDto.class));
    }

}
