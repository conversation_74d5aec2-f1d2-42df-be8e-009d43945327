package com.t3.ts.pay.center.api.business.query.paystatus.handler;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: 支付状态查询工厂服务
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 17:11
 */
@Component
public class PayStatusQueryHandlerFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<String, PayStatusHandler> PAY_SERVICE_MAP = new HashMap<>(8);

    /**
     * 得到处理程序
     *
     * @param type 类型
     * @return {@link PayStatusHandler}
     */
    public PayStatusHandler getHandler(String type) {
        return PAY_SERVICE_MAP.get(type);
    }

    @Override
    public void afterPropertiesSet() {
        appContext.getBeansOfType(PayStatusHandler.class).values().forEach(t -> PAY_SERVICE_MAP.put(t.getType(), t));
    }

    private ApplicationContext appContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        this.appContext = context;
    }
}
