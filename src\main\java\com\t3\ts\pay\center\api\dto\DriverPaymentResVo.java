package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 收银台VO
 *
 * <AUTHOR>
 * @date 2022/06/16
 */
@Data
@ApiModel("支付出参")
public class DriverPaymentResVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 秘钥串
     */
    @ApiModelProperty(value = "秘钥串")
    private String secretKey;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态")
    private Integer status;

    /**
     * 支付方式 1: 支付宝; 2:微信; 49:聚合支付(支付宝); 47:聚合支付(微信)
     */
    @ApiModelProperty(value = "支付方式")
    private Integer payType;

    /**
     * 招行聚合appId
     */
    @ApiModelProperty(value = "招行聚合appId")
    private String cmbMiniAppId;

}
