package com.t3.ts.pay.center.api.util;

import com.t3.ts.account.center.dto.AccountCompanyDto;
import com.t3.ts.account.center.dto.AccountDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ConstantsTest.java
 * @Description TODO
 * @createTime 2020年11月24日 19:19:00
 */
public class AccountUtilsTest {

    @Mock
    Logger log;
    @InjectMocks
    AccountUtils accountUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCalBalanceSum() throws Exception {
        try {
            AccountDto account = new AccountDto();
            AccountUtils.calBalanceSum(account);

            AccountUtils.calChargeSubsidySum(account);

            AccountCompanyDto company = new AccountCompanyDto();
            AccountUtils.getAvailableQuota(company);

            AccountUtils.calChargeSubsidySum(account);

            AccountUtils.getDecimalString(new BigDecimal(1));

            AccountUtils.getIntegralNum(1, "1");

            AccountUtils.intToBigDecimal(1);

            Assert.assertTrue(true);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void testGetMapParamFromForm() throws Exception {
        try {
            Map<String, Object> result = HttpRequestUtil.getMapParamFromForm(null);
            Assert.assertTrue(true);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme