package com.t3.ts.pay.center.api.config.interceptor;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HttpTraceLogInterceptorTest.java
 * @Description TODO
 * @createTime 2020年11月24日 19:19:00
 */
public class HttpTraceLogInterceptorTest {
    @Mock
    ThreadLocal<Long> THREAD_LOCAL_TIME;
    @Mock
    Logger log;
    @InjectMocks
    HttpTraceLogInterceptor httpTraceLogInterceptor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPreHandle() throws Exception {
        boolean result = httpTraceLogInterceptor.preHandle(null, null, "handler");
        Assert.assertEquals(true, result);
    }

    @Test
    public void testAfterCompletion() throws Exception {
        httpTraceLogInterceptor.afterCompletion(null, null, "handler", null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme