package com.t3.ts.pay.center.api.dto.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2019-11-13 14:10
 * @des: 充值支付返回对象
 */
@Data
public class RechargePayVo {

    public RechargePayVo() {
    }

    public RechargePayVo(Integer payType, boolean noSecret, String sdk) {
        this.payType = payType;
        this.noSecret = noSecret;
        this.sdk = sdk;
    }

    /**
     * 第三方支付sdk字符串
     */
    private String sdk;

    /**
     * 是否免密支付
     */
    private boolean noSecret = false;

    /**
     * 支付方式
     *
     * @see com.t3.ts.pay.remote.constants.EnumPayOrderChannel
     */
    private Integer payType;

    /**
     * 结算Id
     */
    private String settlementID;

    /**
     * 招行聚合支付的时候，需要用到的小程序id
     */
    private String cmbMiniAppId;

    /**
     * orderNo
     */
    private String orderNo;
}
