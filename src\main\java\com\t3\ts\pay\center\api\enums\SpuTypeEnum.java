package com.t3.ts.pay.center.api.enums;

import com.t3.ts.enums.BaseEnumInterface;
import com.t3.ts.settlement.centre.enums.BizType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单商品类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SpuTypeEnum implements BaseEnumInterface {
    COUPON(1, "优惠券", true, "coupon", -1),
    CARD(2, "礼品卡", false, "card", BizType.SHOPPING_GIFT_CARD.getType()),
    GOODS(3, "实物", true, "goods", BizType.SHOPPING_GOODS.getType()),
    CODE(4, "兑换码", true, "code", 30),
    REWARD_CENTER_CARD(5, "礼品卡(奖励中心)", false, "rewardCenterCard", -1),
    PAY_MEMBER(6, "付费会员", false, "payMember", BizType.SHOPPING_CDK.getType()),
    RECHARGE(7, "充值", false, "RECHARGE", BizType.CASH_RECHARGE.getType()),
    INTEREST_CARD(8, "权益卡", true, "interestCard", BizType.INTEREST_CARD.getType()),
    BIG_LUCKY_CARD(9, "大福卡", true, "bigLuckyCard", BizType.SHOPPING_DAFU.getType()),

    LIFE_SERVICE(10, "生活服务", true, "lifeService", BizType.LIFE_SERVICE.getType()),
    OUT_INTERESTS(11, "外部权益", true, "outInterests", -1),
    NO_COMMISSION(12, "免佣卡", true, "noCommission", 134),

    DRIVER_NO_COMMISSION(13, "司机免佣卡", true, "driverNoCommission", 141),
    DRIVER_COUPON(14, "司机充电优惠券", true, "driverCoupon", 142),
    DRIVER_RECHARGE_GIFT_CARD(15, "司机充电卡", true, "DriverRechargeGiftCard", 143),
    DRIVER_NO_COMMISSION_INTEREST(16, "司机免佣卡权益", true, "driverNoCommission", 141),
    DRIVER_FLOW_ACCELERATOR_CARD(17, "流水加速卡", true, "flowAccelerationCard", 141),
    ;


    private final Integer code;
    private final String msg;
    private final boolean commodityRefund;
    private final String abbreviations;
    private final Integer bizType;

    /**
     * @param code code
     * @return boolean
     */
    public static boolean commodityRefund(Integer code) {
        SpuTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            SpuTypeEnum item = var1[var3];
            if (item.getCode().intValue() == code) {
                return item.commodityRefund;
            }
        }
        return false;
    }

    /**
     * @param code code
     * @return SpuTypeEnum
     */
    public static SpuTypeEnum match(Integer code) {
        SpuTypeEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            SpuTypeEnum item = var1[var3];
            if (item.getCode().intValue() == code) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否是优惠券商品
     *
     * @param spuType spuType
     * @return boolean
     */
    public static boolean isCoupon(Integer spuType) {
        return SpuTypeEnum.COUPON.getCode().equals(spuType);
    }

    /**
     * 是否是礼品卡商品
     *
     * @param spuType spuType
     * @return boolean
     */
    public static boolean isCard(Integer spuType) {
        return SpuTypeEnum.CARD.getCode().equals(spuType);
    }

    /**
     * 是否是兑换码商品
     *
     * @param spuType spuType
     * @return boolean
     */
    public static boolean isCode(Integer spuType) {
        return SpuTypeEnum.CODE.getCode().equals(spuType);
    }

    /**
     * isGoods
     *
     * @param spuType spuType
     * @return boolean
     */
    public static boolean isGoods(Integer spuType) {
        return SpuTypeEnum.GOODS.getCode().equals(spuType);
    }
}
