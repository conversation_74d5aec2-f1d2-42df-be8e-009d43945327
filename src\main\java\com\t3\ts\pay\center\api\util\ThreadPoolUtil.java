package com.t3.ts.pay.center.api.util;



import com.t3.ts.pay.center.api.constants.CommonNumConst;


import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_10240;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_4;


/**
 * <AUTHOR>
 * @date 2020/7/16 20:58
 * @description:
 */
public final class ThreadPoolUtil {

    private static final ThreadPoolUtil THREAD_POOL_REPO = new ThreadPoolUtil();
    private final int nThreads = Runtime.getRuntime().availableProcessors() * NUM_4;

    private final ExecutorService executorService = new ThreadPoolMdcUtil.ThreadPoolExecutorMdcWrapper(
            "common-app-api-threadPool", nThreads, nThreads, CommonNumConst.NUM_30, TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(NUM_10240));

    /**
     * 线程池跑龙套
     */
    private ThreadPoolUtil() {
    }

    /**
     * 获得实例
     *
     * @return {@link ThreadPoolUtil}
     */
    public static ThreadPoolUtil getInstance() {
        return THREAD_POOL_REPO;
    }

    /**
     * 可运行对象
     *
     * @param runnable 可运行的
     */
    public void execute(Runnable runnable) {
        executorService.execute(runnable);
    }

    /**
     * 执行异步
     *
     * @param <T>  This describes my type parameter
     * @param task 任务
     * @return {@link Future<T>}
     */
    public <T> Future<T> execAsync(Callable<T> task) {
        return executorService.submit(task);
    }
}
