package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022.03.03 14:41
 */
@Data
public class RequisitionDto {
    /**
     * 充值结算id
     */
    private String settlementId;
    /**
     * 行程id
     */
    private String routePlanUuid;
    /**
     * 申请单id
     */
    private String requisitionUuid;
    /**
     * 用车状态 0:待审批;1:审批驳回;2:超时未审批;3:已完成;4:取消;5:派单中;6:派单成功;7:派单失败
     */
    private Integer status;
    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;
    /**
     * 冻结金额
     */
    private BigDecimal actualMoney;
    /**
     * 总金额
     */
    private BigDecimal totalMoney;
    /**
     * 是否弹个人支付弹窗
     */
    private Boolean isPopUpsPay;
    /**
     * 是否弹收银台
     */
    private Boolean isPopUpsCashierDesk;
    /**
     * 流水号
     */
    private String advanceSerial;
    /**
     * 优惠券抵扣金额
     */
    private BigDecimal couponPrice;

    /**
     * 冻结的优惠券couponId列表
     */
    private List<String> couponIdList;

    /**
     * 待支付订单数量
     */
    private Integer waitForPaymentOrderCount;

    /**
     * 是否都是企业订单
     */
    private Boolean isAllEnterpriseOrder;

    /**
     * 预估里程（公里）
     */
    private Double planTrip;
    /**
     * 预估时长（分钟）
     */
    private Integer planDuration;

    /**
     * 冒泡id
     */
    private String bubblingId;

    /**
     * 终点区域编码
     */
    private String destAreaCode;

    /**
     *
     */
    private String passengerUuid;

}
