package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: 商城发票信息表
 *
 * <AUTHOR> <EMAIL>
 * Date:     2020/10/20/0020 10:20
 */
@Data
public class InvoiceMallDetailDto implements Serializable {

    private static final long serialVersionUID = -3397942164919861427L;
    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private String orderNo;
    /**
     * 城市名
     */
    @ApiModelProperty(value = "城市名")
    private String city;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userUuid;
    /**
     * 完单时间
     */
    @ApiModelProperty(value = "完单时间")
    private Date completeTime;
    /**
     * 业务类型 0积分商城 7 商城礼品卡
     */
    @ApiModelProperty(value = "业务类型 0积分商城 7 商城礼品卡")
    private Integer bizType;
    /**
     * 可开票金额
     */
    @ApiModelProperty(value = "可开票金额")
    private BigDecimal amount;
    /**
     * 开票 状态（1：有效，2：作废）
     */
    @ApiModelProperty(value = "开开票 状态（1：有效，2：作废）")
    private Integer invoiceStatus;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String skuName;
    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer skuNum;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
