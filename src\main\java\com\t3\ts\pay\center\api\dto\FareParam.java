package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FareParam.java
 * @Description TODO
 * @createTime 2021年06月11日 14:40:00
 */
@Data
public class FareParam {

    // 合计费用
    private BigDecimal totalFare = BigDecimal.ZERO;
    // 待支付金额
    private BigDecimal actualFare = BigDecimal.ZERO;
    // 省心打可抵扣金额
    private BigDecimal unWorryCanPay = BigDecimal.ZERO;
    // 优惠券可抵扣金额
    private BigDecimal couponCanPay = BigDecimal.ZERO;
    // 权益卡可抵扣金额
    private BigDecimal privilegeCanPay = BigDecimal.ZERO;
    // 附加费
    private BigDecimal additionalFee = BigDecimal.ZERO;
    // 待支付订单费
    private BigDecimal orderFareToPay = BigDecimal.ZERO;
    //钱包总打车金余额
    private BigDecimal giftCurrency = BigDecimal.ZERO;
    //礼品卡余额
    private BigDecimal giftCard = BigDecimal.ZERO;
    // 钱包余额
    private BigDecimal balanceTotal = BigDecimal.ZERO;
    // 省心打已支付
    private BigDecimal unWarriedArrivePay = BigDecimal.ZERO;
    // 可用于支付余额
    private BigDecimal availableBalance = BigDecimal.ZERO;
    //打车金可支付金额
    private BigDecimal giftMoneyCanPay = BigDecimal.ZERO;
    //礼品卡可支付金额
    private BigDecimal giftCardCanPay = BigDecimal.ZERO;
    //充值本金可支付金额
    private BigDecimal rechargeCanPay = BigDecimal.ZERO;
    //充值赠金可支付金额
    private BigDecimal rechargeGiftCanPay = BigDecimal.ZERO;
    //企业礼品卡可支付金额
    private BigDecimal companyGiftCardCanPay = BigDecimal.ZERO;
    // 礼品卡是否支持附加费支付
    private Boolean giftCardCanPayService = Boolean.FALSE;
    private Boolean giftCardCanPayFestival = Boolean.FALSE;
    private Boolean giftCardCanPayCrossCity = Boolean.FALSE;
    private Boolean giftCardCanPayDispatch = Boolean.FALSE;
    // 积分已抵扣
    private BigDecimal integralPayed = BigDecimal.ZERO;
    // 已预付
    private BigDecimal prPayed = BigDecimal.ZERO;
    // 优惠券已抵扣
    private BigDecimal couponPay = BigDecimal.ZERO;
    // 权益卡已抵扣金额
    private BigDecimal privilegePay = BigDecimal.ZERO;
    // 余额已支付
    private BigDecimal balancePay = BigDecimal.ZERO;
    //充值已支付金额
    private BigDecimal rechargeCashPay = BigDecimal.ZERO;
    //充值赠金已支付金额
    private BigDecimal rechargeGiftPay = BigDecimal.ZERO;
    //打车金已支付金额
    private BigDecimal giftCashPay = BigDecimal.ZERO;
    //企业礼品卡已支付金额
    private BigDecimal companyGiftCardPayed = BigDecimal.ZERO;
    //预付款已支付金额
    private BigDecimal preRechargePay = BigDecimal.ZERO;
    //取消费
    private BigDecimal cancelFare = BigDecimal.ZERO;
    //节日费,这个只是待支付的钱
    private BigDecimal festivalFee = BigDecimal.ZERO;
    private BigDecimal crossCityFee = BigDecimal.ZERO;
    private BigDecimal dispatchFee = BigDecimal.ZERO;
    private BigDecimal lostReturnFee = BigDecimal.ZERO;
    private BigDecimal compensationFare = BigDecimal.ZERO;

    /**
     * 这个才是支付总费用
     */
    private BigDecimal serviceFareTotal = BigDecimal.ZERO;
    private BigDecimal festivalFareTotal = BigDecimal.ZERO;
    private BigDecimal crossCityFareTotal = BigDecimal.ZERO;
    private BigDecimal dispatchFareTotal = BigDecimal.ZERO;
    private BigDecimal lostReturnFareTotal = BigDecimal.ZERO;
    private BigDecimal compensationFareTotal = BigDecimal.ZERO;
    private BigDecimal passengerChoiceFareTotal = BigDecimal.ZERO;
    /**
     * 购买券包活动费用
     */
    private BigDecimal couponActivityFee = BigDecimal.ZERO;

    /**
     * 企业支付金额
     */
    private BigDecimal enterprisePay = BigDecimal.ZERO;
    /**
     * 企业优惠券已经抵扣的金额
     */
    private BigDecimal companyCouponAmount = BigDecimal.ZERO;
    /**
     * 业务线
     */
    private Integer expandBizLine;
    /**
     * 产品线
     */
    private Integer productLine;
    /**
     * 资产已支付 包括充值本金+充值赠金+打车金+礼品卡
     */
    private BigDecimal assetPay = BigDecimal.ZERO;
    /**
     * 充值本金
     */
    private BigDecimal rechargeCash = BigDecimal.ZERO;
    /**
     * 充值赠金
     */
    private BigDecimal rechargeGift = BigDecimal.ZERO;
    /**
     * 打车金
     */
    private BigDecimal giftCash = BigDecimal.ZERO;
    /***
     * 企业礼品卡余额
     */
    private BigDecimal companyGiftCard = BigDecimal.ZERO;
    /**
     * 充值余额标识（充值本金+充值赠金） =1 标识余额>1,
     */
    private Integer rechargeFlag = 0;
    /**
     * cannotUseCouponFlag=true 表示现金余额不支持叠加优惠券的场景
     */
    private Boolean cannotUseCouponFlag = Boolean.FALSE;
    /**
     * cannotUsePrivilegeFlag = true 表示 企业礼品卡不支持叠加权益卡
     */
    private Boolean cannotUsePrivilegeFlag = Boolean.FALSE;
    /**
     * couponNotUserType 0 表示现金余额不支持叠加优惠券的场景；1 表示企业礼品卡不支持叠加优惠券 的场景；3 表示现金余额和企业礼品卡不支持叠加优惠券的场景
     */
    private Integer couponNotUserType = 0;
    /**
     * 是否支持个人支付 true 标识支持
     */
    private Boolean isSupportPersonRecharge = Boolean.FALSE;
}
