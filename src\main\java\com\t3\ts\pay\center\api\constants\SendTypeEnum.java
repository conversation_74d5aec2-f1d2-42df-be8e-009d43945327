package com.t3.ts.pay.center.api.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/4/26 11:23
 */
@Getter
public enum SendTypeEnum {
    //发送类型(1>发送类型(1 发送电子发票和行程单;2 仅发送发票;3 仅发送行程单)
    INVOICE_AND_ROUTE(1, "发送电子发票和行程单"),
    INVOICE(2, "仅发送发票"),
    ROUTE(3, "仅发送行程单");

    /**
     * 类型代码
     */
    private Integer type;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 实名认证枚举构造
     *
     * @param type 类型代码
     * @param name 类型名称
     */
    SendTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

}
