package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.util.List;

/**
 * Description: 简单三方支付流程
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/28/0028 19:43
 */
@Data
public class SimpleThirdReq {

    /**
     * 执行类，类型标志
     */
    private String type;
    /**
     * 微信小程序支付时code
     */
    private String wxCode;
    /**
     * 支付宝小程序code
     */
    private String code;
    /**
     * 报文 json字符串
     */
    private String message;
    /**
     * 支付方式
     */
    private List<Integer> payChannelList;
    /**
     * 订单Id
     */
    private String orderId;
    /**
     * 业务类型 settlement-center 里的 BizType
     */
    private Integer bizType;

    /**
     * 支付宝  用户付款中途退出返回商户网站的地址
     */
    private String quitUrl;

    private String payReturnUrl;
}
