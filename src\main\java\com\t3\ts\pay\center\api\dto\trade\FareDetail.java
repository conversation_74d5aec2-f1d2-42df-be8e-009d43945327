package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:51
 * @des 1.0
 */

@Data
public class FareDetail {

    // 附加费
    private BigDecimal serviceFare;

    private BigDecimal festivalFare;

    private BigDecimal crossCityFare;
    private BigDecimal dispatchFare;
    private BigDecimal lostReturnFare;
    private BigDecimal compensationFare;

    // 订单费用
    private BigDecimal orderFare;

    // 行程取消费
    private String cancelFare;

    // 剩余应付金额
    private BigDecimal remainPay;

    // 行程总费用
    private String totalFare;
    /**
     * 这个才是支付总费用
     */
    private BigDecimal serviceFareTotal = BigDecimal.ZERO;
    private BigDecimal festivalFareTotal = BigDecimal.ZERO;
    private BigDecimal crossCityFareTotal = BigDecimal.ZERO;
    private BigDecimal dispatchFareTotal = BigDecimal.ZERO;
    private BigDecimal lostReturnFareTotal = BigDecimal.ZERO;
    private BigDecimal compensationFareTotal = BigDecimal.ZERO;
    private BigDecimal passengerChoiceFareTotal = BigDecimal.ZERO;
}
