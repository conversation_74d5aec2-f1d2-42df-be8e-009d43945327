package com.t3.ts.pay.center.api.service.impl.invoice;

import com.t3.ts.invoice.center.service.PassengerInvoiceService;
import com.t3.ts.invoice.center.service.RouteInvoiceService;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.pay.center.api.dto.vo.PassengerInvoiceDetailVo;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.web.client.RestTemplate;

import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 13:59
 */
public class MallInvoiceServiceImplTest {
    @Mock
    RouteInvoiceService routeInvoiceService;
    @Mock
    PassengerInvoiceService passengerInvoiceService;
    @Mock
    RestTemplate restTemplate;
    @Mock
    SlbConfig slbConfig;
    @Mock
    Logger logger;
    @InjectMocks
    MallInvoiceServiceImpl mallInvoiceServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryMallBilling() throws Exception {
        Response<PageResult<MallInvoiceVo>> result =
                mallInvoiceServiceImpl.queryMallBilling(new InvoiceRoutReq(), "passengerUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testSubmitMallBill() throws Exception {
        when(slbConfig.getPay()).thenReturn("getPayResponse");

        Response<?> result = mallInvoiceServiceImpl.submitMallBill(new InvoiceMallSubmitReq(), "passengerUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetInvoiceInfoAll() throws Exception {
        Response<PassengerInvoiceDetailVo> result = mallInvoiceServiceImpl.getInvoiceInfoAll(new InvoiceReq());
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
