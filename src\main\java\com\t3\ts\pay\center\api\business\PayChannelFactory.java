package com.t3.ts.pay.center.api.business;

import org.apache.dubbo.config.annotation.DubboReference;
import com.t3.ts.channelmgr.center.dto.PayChannelDto;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.enums.PayChannelEnum;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date: 2019-11-15 22:43
 * @des: 支付通道工程类
 */
@Slf4j
@Component
public class PayChannelFactory implements ApplicationContextAware {

    @DubboReference
    private com.t3.ts.channelmgr.center.service.PayChannelService payChannelService;
    /**
     * 支付通道实现类
     */
    private Map<String, PayChannelService> payChannelServiceMap = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        payChannelServiceMap = applicationContext.getBeansOfType(PayChannelService.class);
        log.info("PayChannelFactory load-channelService : {}", payChannelServiceMap);
    }

    /**
     * 获支付信息
     *
     * @param payType       类型
     * @param paymentInfoBo 入参
     * @return 出参
     */
    public Response<RechargePayBo> paymentInfo(Integer payType, PaymentInfoBo paymentInfoBo) {
        //根据支付方式获取服务实现bean名称
        PayChannelEnum payEnum = PayChannelEnum.getPayChannelEnumByPayType(payType);
        if (null == payEnum) {
            return Response.createError(ResultErrorEnum.UN_SUPPOURT_PAY_TYPE);
        }
        //根据服务实现bean名称获取服务实例
        PayChannelService paymentService = payChannelServiceMap.get(payEnum.getPayChannelBean());
        Response<RechargePayBo> response = paymentService.paymentInfo(paymentInfoBo);
        // 判断如果失败了，则触发渠道降级
        if (!response.isSuccess()) {
            try {
                PayChannelDto dto = new PayChannelDto();
                dto.setPayChannel(payEnum.getPayType());
                dto.setRecordStatus(NumConstant.NUM_2);
                payChannelService.setChannelDemoteCache(dto);
            } catch (Exception e) {
                log.warn("发送渠道降级缓存失败，服务降级处理", e);
            }
        }
        return response;
    }
}
