package com.t3.ts.pay.center.api.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestOperations;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Description DELL
 * @Date 15:22 2020/8/26
 **/
@Slf4j
public final class HttpClientUtil {

    private HttpClientUtil() {
    }

    private static final String ERROR = "$$$$$$$$$$$$$$$$$$$$$$$$$$$";

    /**
     * 发送post请求
     *
     * @param restOperations 请求模板
     * @param url            链接
     * @param body           请求body
     * @param responseType   响应数据转换类型
     * @param <T>            类型
     * @param <P>            泛型
     * @return 结果
     */
    public static <T, P> T post(RestOperations restOperations, String url, P body, Class<T> responseType) {
        return post(restOperations, url, body, responseType, false);
    }

    /**
     * 发送post请求
     *
     * @param restOperations 请求模板
     * @param url            链接
     * @param body           请求body
     * @param responseType   响应数据转换类型
     * @param <T>            类型
     * @param <P>            泛型
     * @return 结果
     */
    public static <T, P> List<T> postList(RestOperations restOperations, String url, P body, Class<T> responseType) {
        return postList(restOperations, url, body, responseType, false);
    }

    /**
     * 发送post请求
     *
     * @param restOperations     请求模板
     * @param url                链接
     * @param body               请求body
     * @param responseType       响应数据转换类型
     * @param parameterAppendUrl true 参数拼接在url里
     * @param <T>                类型
     * @param <P>                泛型
     * @return 结果
     */
    public static <T, P> T post(RestOperations restOperations, String url, P body, Class<T> responseType,
                                boolean parameterAppendUrl) {
        String request = buildParameter(body, parameterAppendUrl);
        if (Objects.equals(request, ERROR)) {
            return null;
        }
        Response response = send(restOperations, url, request, parameterAppendUrl);
        if (Objects.isNull(response) || BooleanUtil.isFalse(response.isSuccess())) {
            return null;
        }
        if (null == response.getData()) {
            return JSON.parseObject(JSON.toJSONString(""), responseType);
        }
        return JSON.parseObject(JSON.toJSONString(response.getData()), responseType);
    }

    /**
     * 发送post请求
     *
     * @param restOperations     请求模板
     * @param url                链接
     * @param body               请求body
     * @param responseType       响应数据转换类型
     * @param parameterAppendUrl true 参数拼接在url里
     * @param <T>                类型
     * @param <P>                泛型
     * @return 结果
     */
    public static <T, P> List<T> postList(RestOperations restOperations, String url, P body, Class<T> responseType,
                                          boolean parameterAppendUrl) {
        String request = buildParameter(body, parameterAppendUrl);
        if (Objects.equals(request, ERROR)) {
            return Collections.emptyList();
        }
        Response response = send(restOperations, url, request, parameterAppendUrl);
        if (Objects.isNull(response) || BooleanUtil.isFalse(response.isSuccess())
                || Objects.isNull(response.getData())) {
            log.warn("Post fail! response code is fail! message:{}", JSONUtil.toJsonStr(response));
            return Collections.emptyList();
        }
        return JSON.parseArray(JSON.toJSONString(response.getData()), responseType);
    }

    /**
     * 发送数据
     *
     * @param restOperations     请求模板
     * @param url                url
     * @param request            参数
     * @param parameterAppendUrl 参数是否需要拼接到url
     * @return 结果
     */
    private static Response send(RestOperations restOperations, String url, String request,
                                 boolean parameterAppendUrl) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> httpEntity = BooleanUtil.isFalse(parameterAppendUrl)
                ? new HttpEntity<>(request, httpHeaders) : new HttpEntity<>(httpHeaders);
        //拼接参数到url
        if (parameterAppendUrl) {
            url = newUrl(url, request);
        }
        Response response;
        try {
            response = restOperations.postForObject(url, httpEntity, Response.class);
            log.info("traceId={}, send-url={}, request={}, response={}",
                    MDC.get("traceId"), url, JSON.toJSONString(httpEntity),
                    JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("traceId={}, 调用地址异常, url={}, parameter={}, error-message={}",
                    MDC.get("traceId"), url, request, e.getLocalizedMessage());
            log.error("调用地址异常", e);
            return null;
        }
        return response;
    }

    /**
     * 组装参数
     *
     * @param body               请求消息体
     * @param parameterAppendUrl true 参数拼接在url里
     * @param <P>                泛型
     * @return 结果
     */
    private static <P> String buildParameter(P body, boolean parameterAppendUrl) {
        if (Objects.isNull(body)) {
            return "";
        }
        if (BooleanUtil.isFalse(parameterAppendUrl)) {
            try {
                return JSON.toJSONString(body);
            } catch (Exception e) {
                log.warn("参数格式化异常", e);
                return ERROR;
            }
        }
        if (BeanUtils.isPrimitive(body.getClass()) || Objects.equals(body.getClass(), Object.class)) {
            return String.valueOf(body);
        }
        if (body instanceof Map) {
            return convertMap((Map) body);
        }
        return convertForBean(body);
    }

    /**
     * 判断基本数据类型
     *
     * @param clazz 类型
     * @return 结果
     */
    public static boolean isPrimitive(Class clazz) {
        if (Objects.isNull(clazz)) {
            return false;
        }
        if (clazz.isPrimitive()) {
            return true;
        }
        return Character.class.equals(clazz) || Boolean.class.equals(clazz) || Byte.class.equals(clazz)
                || Short.class.equals(clazz) || Integer.class.equals(clazz) || Long.class.equals(clazz)
                || Float.class.equals(clazz) || Double.class.equals(clazz);
    }

    /**
     * map属性解析
     *
     * @param map Map
     * @return 结果
     */
    private static String convertMap(Map map) {
        StringBuilder parameter = new StringBuilder();
        AtomicInteger hasError = new AtomicInteger(0);
        map.forEach((k, v) -> {
            if (parameter.length() > 0) {
                parameter.append("&");
            }
            try {
                parameter.append(k).append("=").append(URLEncoder.encode(convert(v), CharsetUtil.UTF_8));
            } catch (UnsupportedEncodingException e) {
                log.error("参数解析异常", e);
                hasError.incrementAndGet();
            }
        });
        return hasError.get() > 0 ? ERROR : parameter.toString();
    }

    /**
     * bean 对象属性解析
     *
     * @param bean 对象
     * @return 结果
     */
    private static String convertForBean(Object bean) {
        Field[] fields = BeanUtils.getFieldClass(bean.getClass());
        if (ArrayUtil.isEmpty(fields)) {
            return "";
        }
        StringBuilder parameter = new StringBuilder();
        try {
            for (Field field : fields) {
                if (parameter.length() > 0) {
                    parameter.append("&");
                }
                parameter.append(field.getName()).append("=")
                        .append(URLEncoder.encode(convert(ReflectUtil.getFieldValue(bean, field)), CharsetUtil.UTF_8));
            }
            return parameter.toString();
        } catch (UnsupportedEncodingException e) {
            log.error("参数解析异常", e);
            return ERROR;
        }
    }

    /**
     * 类型转换
     *
     * @param parameter 类型
     * @return 结果
     */
    private static String convert(Object parameter) {
        if (Objects.isNull(parameter)) {
            return "null";
        }
        if (parameter.getClass().isArray() || parameter instanceof Collection) {
            return JSON.toJSONString(parameter);
        }
        if (parameter instanceof Date) {
            return String.valueOf(((Date) parameter).getTime());
        }
        return String.valueOf(parameter);
    }

    /**
     * 新url
     *
     * @param oldUrl  旧url
     * @param request 参数
     * @return 新url
     */
    private static String newUrl(String oldUrl, String request) {
        int index = oldUrl.indexOf('?');
        if (index < 0) {
            return oldUrl.concat("?").concat(request);
        }
        boolean flag = BooleanUtil.isFalse(oldUrl.endsWith("&"));
        if (flag) {
            oldUrl = oldUrl.concat("&");
        }
        oldUrl = oldUrl.concat(request);
        return oldUrl;
    }

}
