package com.t3.ts.pay.center.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 迁移 by ivy .2021/09/17 14:20
 *
 * @Author: hupo
 * @Date: 2019.9.26
 * @Description: 发票结算数据
 */
@Data
public class InvoiceSettlementDataBo implements Serializable {

    private static final long serialVersionUID = -2740555262602499250L;

    /**
     * 余额现金部分金额
     */
    private BigDecimal balanceCash;
    /**
     * 余额赠送币部分金额
     */
    private BigDecimal balanceGift;
    /**
     * 余额礼品卡部分金额
     */
    private BigDecimal balanceGiftCard;
    /**
     * 余额出行币部分金额
     */
    private BigDecimal balanceTravel;
    /**
     * 第三方支付金额
     */
    private BigDecimal thirdAmount;
    /**
     * 支付渠道(枚举:第三方、线下支付)
     */
    private String content;
    /**
     * 行程总费用（订单费用+附加费等-营销费用优惠-司机减免费用）
     */
    private BigDecimal totalFare;
    /**
     * 三方支付明细
     */
    private String thirdDetail;
    /**
     * 礼品卡支付
     */
    private BigDecimal giftCard;
    /**
     * 积分支付
     */
    private BigDecimal integral;
    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    private Integer typeEnt;

    /**
     * 新建实例
     *
     * @return {@link InvoiceSettlementDataBo}
     */
    public static InvoiceSettlementDataBo newInstance() {
        return new InvoiceSettlementDataBo();
    }
}
