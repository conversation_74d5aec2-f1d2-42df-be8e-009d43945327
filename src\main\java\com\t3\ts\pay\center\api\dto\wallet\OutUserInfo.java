package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class OutUserInfo {

    @NotBlank(message = "姓名不能为空")
    private String userName;

    @NotBlank(message = "身份证号不能为空")
    private String idCardNo;

    @NotBlank(message = "请填写正确的手机号")
    @Length(min = 11, max = 11, message = "请填写正确的手机号")
    private String mobile;

    @NotBlank(message = "验证码不能为空")
    private String verifyCode;
}
