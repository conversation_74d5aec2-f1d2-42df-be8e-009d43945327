package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.common.BindCardErrorCheckBiz;
import com.t3.ts.pay.center.api.dto.wallet.OutUserInfo;
import com.t3.ts.pay.center.api.dto.wallet.OutWithdrawOrder;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import com.t3.ts.withdraw.center.remote.dto.UserAccountOutDto;
import com.t3.ts.withdraw.center.remote.dto.WithdrawRecordOutDto;
import com.t3.ts.withdraw.center.remote.dto.sms.SmsVerifyCodeReq;
import com.t3.ts.withdraw.center.remote.service.OutWithdrawService;
import com.t3.ts.withdraw.center.remote.service.WithdrawConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@Api(tags = "外部链接api")
@Slf4j
@RestController
@RequestMapping("/notoken/out/api")
public class OutController {

    @DubboReference
    private OutWithdrawService outWithdrawService;

    @DubboReference
    private WithdrawConfigService withdrawConfigService;

    @Autowired
    private BindCardErrorCheckBiz bindCardErrorCheckBiz;

    @ApiOperation(value = "查询约约司机账户信息", notes = "查询约约司机账户信息")
    @PostMapping("/queryUserAccountOutInfo")
    @Deprecated
    public Response<UserAccountOutDto> queryUserAccountOutInfo(@RequestBody OutUserInfo req) {
        if (StringUtils.isBlank(req.getIdCardNo())) {
            return Response.createError("身份证号不存在");
        }
        UserAccountOutDto queryParam = new UserAccountOutDto();
        queryParam.setIdCardNo(req.getIdCardNo());
        Response<UserAccountOutDto> userAccountRes = outWithdrawService.getUserAccount(queryParam);
        if (null == userAccountRes || !userAccountRes.isSuccess() || null == userAccountRes.getData()) {
            return Response.createError("身份证号与特选车主信息不一致");
        }
        UserAccountOutDto userAccountOutDto = userAccountRes.getData();
        if (StringUtils.isBlank(userAccountOutDto.getAccountName())) {
            userAccountOutDto.setAccountName(userAccountOutDto.getUserName());
        }
        return Response.createSuccess(userAccountOutDto);
    }

    /**
     * 约约司机获取短信验证码
     *
     * @param req SmsVerifyCodeReq
     * @return {@link Response <String>}
     */
    @ApiOperation(value = "约约司机获取短信验证码", notes = "约约司机获取短信验证码")
    @PostMapping("/getSmsVerifyCodeForOut")
    public Response<String> getSmsVerifyCodeForOut(@RequestBody OutUserInfo req) {
        if (StringUtils.isBlank(req.getMobile())) {
            return Response.createError("获取验证码失败：手机号不存在");
        }
        SmsVerifyCodeReq smsVerifyCodeReq = new SmsVerifyCodeReq();
        smsVerifyCodeReq.setMobile(req.getMobile());
        smsVerifyCodeReq.setUserId(req.getIdCardNo());
        smsVerifyCodeReq.setTmepIdx("04");
        return withdrawConfigService.sendSmsVerifyCode(smsVerifyCodeReq);
    }

    /**
     * 身份信息确认
     *
     * @param req OutUserInfo
     * @return {@link Response <String>}
     */
    @ApiOperation(value = "身份信息确认", notes = "身份信息确认")
    @PostMapping("/confirmUserInfo")
    public Response<String> confirmUserInfo(@RequestBody @Validated OutUserInfo req) {
        //校验验证码
        SmsVerifyCodeReq smsReq = new SmsVerifyCodeReq();
        smsReq.setMobile(req.getMobile());
        smsReq.setUserId(req.getIdCardNo());
        smsReq.setTmepIdx("04");
        smsReq.setVerifyCode(req.getVerifyCode());
        Response<String> checkResponse = withdrawConfigService.checkSmsVerifyCode(smsReq);
        if (!checkResponse.isSuccess()) {
            return Response.createError(checkResponse.getMsg());
        }
        UserAccountOutDto queryParam = new UserAccountOutDto();
        queryParam.setIdCardNo(req.getIdCardNo());
        Response<UserAccountOutDto> accountRes = outWithdrawService.getUserAccount(queryParam);
        if (null == accountRes || !accountRes.isSuccess() || null == accountRes.getData()) {
            return Response.createError("身份证号与特选车主信息不一致");
        }
        UserAccountOutDto userAccountOutDto = accountRes.getData();
        if (!req.getUserName().equals(userAccountOutDto.getUserName())) {
            return Response.createError("姓名与特选车主信息不一致");
        }
        if (!req.getMobile().equals(userAccountOutDto.getPhone())) {
            return Response.createError("手机号与特选车主信息不一致");
        }
        return Response.createSuccess("确认成功",bindCardErrorCheckBiz.getConfirmToken(req.getIdCardNo()));
    }

    /**
     * 约约司机提现
     *
     * @param req OutWithdrawOrder
     * @return {@link Response}
     */
    @ApiOperation(value = "约约司机提现", notes = "约约司机提现")
    @PostMapping("/saveOutWithdraw")
    public Response saveOutWithdraw(@RequestBody @Validated OutWithdrawOrder req) {
        //校验身份标识
        String idCardNo = bindCardErrorCheckBiz.getIdCardNoByConfirmToken(req.getConfirmToken());
        if (StringUtils.isBlank(idCardNo)) {
            return Response.createError("身份标识失效");
        }
        UserAccountOutDto queryParam = new UserAccountOutDto();
        queryParam.setIdCardNo(idCardNo);
        Response<UserAccountOutDto> accountRes = outWithdrawService.getUserAccount(queryParam);
        if (null == accountRes || !accountRes.isSuccess() || null == accountRes.getData()) {
            return Response.createError("身份证号与特选车主信息不一致");
        }
        WithdrawRecordOutDto withdrawRecordOutDto = new WithdrawRecordOutDto();
        withdrawRecordOutDto.setAccountId(accountRes.getData().getId());
        BigDecimal totalAmt = req.getPlatformAmount().add(req.getAwardAmount());
        if (totalAmt.compareTo(BigDecimal.ZERO) <= 0) {
            return Response.createError("提现金额必须大于0");
        }
        withdrawRecordOutDto.setWithdrawAmount(totalAmt);
        withdrawRecordOutDto.setAccountName(req.getAccountName());
        withdrawRecordOutDto.setAlipayNo(req.getAlipayNo());
        return outWithdrawService.saveOutWithdrawRecord(withdrawRecordOutDto);
    }

    /**
     * 约约司机查询提现记录
     *
     * @param req OutWithdrawOrder
     * @return {@link Response}
     */
    @ApiOperation(value = "约约司机查询提现记录", notes = "约约司机查询提现记录")
    @PostMapping("/queryOutWithdrawRecord")
    public Response queryOutWithdrawRecord(@RequestBody OutWithdrawOrder req) {
        if (StringUtils.isBlank(req.getConfirmToken())) {
            return Response.createError("身份标识不能为空");
        }
        //校验身份标识
        String idCardNo = bindCardErrorCheckBiz.getIdCardNoByConfirmToken(req.getConfirmToken());
        if (StringUtils.isBlank(idCardNo)) {
            return Response.createError("身份标识失效");
        }
        UserAccountOutDto queryParam = new UserAccountOutDto();
        queryParam.setIdCardNo(idCardNo);
        Response<UserAccountOutDto> accountRes = outWithdrawService.getUserAccount(queryParam);
        if (null == accountRes || !accountRes.isSuccess() || null == accountRes.getData()) {
            return Response.createError("身份证号与特选车主信息不一致");
        }
        // 180天之内是否有提现记录
        WithdrawRecordOutDto queryWithdrawRecord = new WithdrawRecordOutDto();
        queryWithdrawRecord.setAccountId(accountRes.getData().getId());
        return outWithdrawService.queryWithdrawRecord(queryWithdrawRecord);
    }
}
