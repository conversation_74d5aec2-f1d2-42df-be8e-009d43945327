package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: ccq
 * @Date: 2020/6/17 15:31
 * @Description: T3运力发票信息
 */
@Data
@ApiModel
public class T3Transport implements Serializable {
    private static final long serialVersionUID = -6980102711045966665L;
    /**
     * 订单的Id,行程开票未order表,充值开票为actual_flow表
     */
    @ApiModelProperty(value = "订单的Id", required = true)
    private String orderUuid;
    /**
     * 订单的Id跟开票主体code对应关系
     */
    @ApiModelProperty(value = "订单的Id", required = true)
    private List<OrderToCode> orderToCodes;
    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    @ApiModelProperty(value = "运力类型(1 T3运力,2 东风运力,3 一汽运力)", required = true)
    private Integer transportType;
    /**
     * 金额
     **/
    @ApiModelProperty(value = "金额", required = true)
    private BigDecimal money;
}
