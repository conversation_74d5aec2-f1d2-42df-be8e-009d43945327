package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.privilege.PrivilegeBusiness;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.result.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: PrivilegeApi
 * @Package com.t3.ts.pay.center.api.controller
 * @date 2022/3/31 9:15
 */
@RestController
@Slf4j
@RequestMapping("/api/privilege")
@Api("Privilege-Api")
public class PrivilegeApi extends BaseApi {
    @Autowired
    private PrivilegeBusiness privilegeBusiness;
    /**
     * 获取权益卡列表
     *
     * @param routePlanUuidReq 获取免单卡请求
     * @param request     请求
     * @return {@link Response}
     */
    @PostMapping("/v1/queryCanUsePrivilege")
    @ApiOperation(value = "获取免单卡列表", notes = "获取免单卡列表")
    public Response queryCanUsePrivilege(@RequestBody @Validated RoutePlanUuidReq routePlanUuidReq,
                                                   HttpServletRequest request) {
        routePlanUuidReq.setGrayVersion(getGrayVersion(request));
        return privilegeBusiness.queryCanUsePrivilege(routePlanUuidReq, getUserUid(request));
    }
}
