package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PayConstants;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.common.constant.settle.SettleStatusEnum;
import com.t3.ts.settlement.centre.enums.BizType;
import org.apache.dubbo.config.annotation.DubboReference;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: CommonPayHandler
 * @Package com.t3.ts.pay.center.api.service.pay
 * @date 2022/8/17 15:13
 */
@Slf4j
@Component
public class CommonPayHandler extends AbstractSimpleThirdPayHandler implements PayHandler {
    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private PayChannelFactory payChannelFactory;

    @Override
    public String getType() {
        return "common";
    }

    /**
     * 参数校验
     *
     * @param context 上下文
     */
    @Override
    public void checkParam(PayContext context) {
        if (StringUtils.isEmpty(context.getUserId()) || StringUtils.isEmpty(context.getOrderId())
                || null == context.getBizType()) {
            throw new IllegalArgumentException("用户|类型不能为空");
        }
        context.getExtendParam().put("inner_wxOpenId", context.getWxCode());
    }

    @Override
    public Integer getBizType(PayContext context) {
        return context.getBizType();
    }

    @Override
    public void checkSettlement(PayContext context) {
        SettlementGeneralDto dto = new SettlementGeneralDto();
        dto.setBizTypes(getBizTypeList(context));
        dto.setOrderUuid(context.getOrderId());
        final Response<List<SettlementGeneralDto>> listResponse = settlementGeneralService.selectSettlement(dto);
        if (!listResponse.isSuccess() || CollectionUtil.isEmpty(listResponse.getData())) {
            throw new IllegalStateException("结算单不存在");
        }
        List<SettlementGeneralDto> generalDtos = listResponse.getData().stream()
                .filter(dto1 -> dto1.getOrderStatus() < SettleStatusEnum.SETTLED.getCode())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(generalDtos)) {
            throw new IllegalStateException("结算单已结算");
        }
        context.getExtendParam().put("inner_bizId", generalDtos.get(0).getOrderUuid());
        context.getExtendParam().put("inner_settleId", generalDtos.get(0).getSettleUuid());
        context.getExtendParam().put("bizType", generalDtos.get(0).getBizType());
        context.setSettleId(generalDtos.get(0).getSettleUuid());
    }

    /**
     * 获取业务类型
     * @param context 上下文
     * @return {@link List}<{@link Integer}>
     */
    public List<Integer> getBizTypeList(PayContext context) {
        Integer bizType = context.getBizType();
        if (Objects.isNull(bizType)) {
            return null;
        }
        if (BizType.contains(bizType, BizType.DRIVER_PRE_PAY)) {
            return CollectionUtil.newArrayList(BizType.DRIVER_PRE_PAY.getType(), BizType.PASSENGER_PRE_CHARGE.getType());
        }
        if (BizType.contains(bizType, BizType.DRIVER_CHARGING_PAY)) {
            return CollectionUtil.newArrayList(BizType.DRIVER_CHARGING_PAY.getType(),
                    BizType.PASSENGER_CHARGE.getType());
        }
        return CollectionUtil.newArrayList(bizType);
    }
    /**
     * route by pay channel
     *
     * @param payInfo 支付信息
     * @param context 上下文
     */
    @Override
    public void routePayChannel(Response<String> payInfo, PayContext context) {

        if (!PayUtils.payRoutingSuccess(payInfo)
                && !payInfo.getCode().equals(NumConstants.NUM_202)
                && !cmbAggHelper.isCmbAggPay(PayWayConvert.getPayWayEnum(context.getPayChannelList()))) {
            RechargePayBo rechargePayBo = new RechargePayBo();
            rechargePayBo.setSdk(payInfo.getData());
            rechargePayBo.setPayType(context.getPayChannelList().get(0));
            context.getExtendParam().put("output_response", Response.createSuccess(rechargePayBo));
            return;
        }

        int payType = PayUtils.getRealChannelRouting(payInfo,context.getPayChannelList());
        log.info("CommonPayHandler 获取支付路由后的支付类型 payType={}", payType);
        //是否免密支付
        boolean isNoSecret = PayConstants.NO_SEC_PAY.equals(String.valueOf(payInfo.getCode()));
        //内部传递对象
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret).paymentResp(payInfo).passengerUuid(context.getUserId()).build();
        //支付工厂模式调用
        Response<RechargePayBo> response = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        context.getExtendParam().put("output_response", response);
    }

    /**
     * process return message
     *
     * @param context 上下文
     * @return {@link Response<RechargePayBo>}
     */
    @Override
    public Response<JSONObject> processResponse(PayContext context) {
        Response<?> response = (Response<?>) context.getExtendParam().get("output_response");
        if (!response.isSuccess() || Objects.isNull(response.getData())) {
            return Response.createError(response.getMsg());
        }
        RechargePayBo rechargePayBo = (RechargePayBo) response.getData();
        rechargePayBo.setSettlementId(context.getSettleId());
        super.packageAggAliPayBo(rechargePayBo);
        return Response.createSuccess("获取支付信息成功", JSON.parseObject(JSON.toJSONString(rechargePayBo)));
    }
}
