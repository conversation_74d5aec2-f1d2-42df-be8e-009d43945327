/**
 * <AUTHOR>
 * @date ：Created in 2021/4/26 10:57
 * @description：
 */

package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

import static com.t3.ts.pay.center.api.constants.NumConstants.NUM_10;

/**
 * <AUTHOR>
 * @date 2021/4/26 10:57
 */
@Getter
@Setter
public class InvoiceHistoryReq implements Serializable {
    /**
     * 当前页
     */
    @Min(value = 1)
    @ApiModelProperty(value = "当前页")
    private Integer currPage = 1;

    /**
     * 每页数量
     */
    @Max(value = 10)
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize = NUM_10;

    @NotNull(message = "发票类型列表")
    @ApiModelProperty(value = "发票类型列表(1.出行服务 2.商城)")
    private List<Integer> invoiceClassList;

}
