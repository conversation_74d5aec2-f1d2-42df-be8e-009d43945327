package com.t3.ts.pay.center.api.service.impl.invoice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.invoice.center.dto.InvoiceMallDataDto;
import com.t3.ts.invoice.center.dto.InvoiceMallDto;
import com.t3.ts.invoice.center.dto.PassengerInvoiceDto;
import com.t3.ts.invoice.center.form.route.InvoiceMallQueryParam;
import com.t3.ts.invoice.center.service.PassengerInvoiceService;
import com.t3.ts.invoice.center.service.RouteInvoiceService;
import com.t3.ts.pay.center.api.bo.InvoiceMallDataBo;
import com.t3.ts.pay.center.api.bo.PassengerInvoiceBo;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceGroupMallDto;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitItem;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.invoice.MallInvoicePageReqDto;
import com.t3.ts.pay.center.api.dto.invoice.MallInvoiceResDto;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceItemVo;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.pay.center.api.dto.vo.PassengerInvoiceDetailVo;
import com.t3.ts.pay.center.api.enums.InvoiceClassEnum;
import com.t3.ts.pay.center.api.service.invoice.MallInvoiceService;
import com.t3.ts.pay.center.api.util.HttpClientUtil;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.utils.StringUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_2;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_3;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_5;

/**
 * @Author: ivy
 * @Date: 2021/10/11 19:20
 * @Description: 商城开票
 */
@Service
public class MallInvoiceServiceImpl implements MallInvoiceService {

    @DubboReference
    private RouteInvoiceService routeInvoiceService;

    @DubboReference
    private PassengerInvoiceService passengerInvoiceService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SlbConfig slbConfig;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String BILLING_INVOICE_GROUP = "/api/invoice/v1/syn/billingByGroup";

    /**
     * 商城开票
     *
     * @param req           请求类
     * @param passengerUuid 乘客id
     * @return {@link PageResult<MallInvoiceVo>}
     */
    @Override
    public Response<PageResult<MallInvoiceVo>> queryMallBilling(InvoiceRoutReq req, String passengerUuid) {
        MallInvoicePageReqDto mallInvoicePageReqDto = initParam(req, passengerUuid);
        // 查询商城可开票列表
        InvoiceMallQueryParam param = BeanUtils.propertiesCopy(mallInvoicePageReqDto, InvoiceMallQueryParam.class);
        Response<PageResult<InvoiceMallDataDto>> response = routeInvoiceService.getMallInvoicePageList(param);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg(), response.getCode());
        }
        if (ObjectUtil.isNull(response.getData())) {
            PageResult<MallInvoiceVo> pageResult = new PageResult<>(new ArrayList<>(), 0,
                    req.getPageSize(), req.getCurrPage());
            return Response.createSuccess(pageResult);
        }
        List<MallInvoiceVo> listVo = new ArrayList<>();
        PageResult pageResultResp = JSON.parseObject(JSON.toJSONString(response.getData()), PageResult.class);
        List<JSONObject> resultList = pageResultResp.getList();
        int totalCount = pageResultResp.getTotalCount();
        if (!CollectionUtils.isEmpty(resultList)) {
            for (JSONObject jsonObj : resultList) {
                MallInvoiceResDto dto = JSONObject.parseObject(jsonObj.toJSONString(), MallInvoiceResDto.class);
                if (dto.getInvoiceAmount() == null || dto.getInvoiceAmount().compareTo(new BigDecimal(0)) == 0) {
                    totalCount--;
                    continue;
                }
                MallInvoiceVo vo = BeanUtils.propertiesCopy(dto, MallInvoiceVo.class);
                vo.setTimeStamp(dto.getCreateTime().getTime());
                listVo.add(vo);
            }
        }
        PageResult<MallInvoiceVo> pageResult = new PageResult<>(listVo, totalCount,
                pageResultResp.getPageSize(), pageResultResp.getCurrPage());
        return Response.createSuccess(pageResult);
    }

    /**
     * 提交商城开票
     *
     * @param req           入参
     * @param passengerUuid 乘客uuid
     * @return {@link Response}
     */
    public Response<?> submitMallBill(InvoiceMallSubmitReq req, String passengerUuid) {
        if (CollectionUtils.isEmpty(req.getMallList())) {
            return Response.createError(ResultErrorEnum.MISSING_PARAMETER_OR_PARAMETER_IS_EMPTY);
        }
        if (ObjectUtil.isNull(req.getBillType())) {
            // 默认1 开票
            req.setBillType(PassengerConstants.BILLTYPE_COMMON);
        }
        // 重新开票
        if (PassengerConstants.BILLTYPE_REDONE.equals(req.getBillType())) {
            Response<?> checkRes = checkRedoneMallBill(req);
            if (!checkRes.isSuccess()) {
                return Response.createError(checkRes.getMsg());
            }
        }

        PassengerInvoiceBo passengerInvoiceBo = initParamOfSubmitMallBill(req);
        passengerInvoiceBo.setPassengerUuid(passengerUuid);
        if (CollectionUtil.isEmpty(passengerInvoiceBo.getMallList())) {
            return Response.createSuccess();
        }
        // 分组
        List<PassengerInvoiceBo> passengerInvoiceDtos = new ArrayList<>();
        Map<String, List<InvoiceMallDataBo>> collect = passengerInvoiceBo.getMallList().stream()
                .collect(Collectors.groupingBy(InvoiceMallDataBo::getGroupKey));
        for (List<InvoiceMallDataBo> invoiceMallDataBos : collect.values()) {
            PassengerInvoiceBo passengerInvoiceBoTemp =
                    BeanUtils.propertiesCopy(passengerInvoiceBo, PassengerInvoiceBo.class);
            passengerInvoiceBoTemp.setMallList(invoiceMallDataBos);
            InvoiceMallDataBo invoiceMallDataBo = invoiceMallDataBos.get(0);
            if (Objects.nonNull(invoiceMallDataBo)) {
                passengerInvoiceBoTemp.setInvoiceSubjectCode(invoiceMallDataBo.getInvoiceSubjectCode());
                passengerInvoiceBoTemp.setInvoiceClass(invoiceMallDataBo.getInvoiceClass());
                passengerInvoiceDtos.add(passengerInvoiceBoTemp);
            }
        }
        InvoiceGroupMallDto invoiceGroupDto = new InvoiceGroupMallDto();
        invoiceGroupDto.setPassengerInvoiceDtos(passengerInvoiceDtos);
        invoiceGroupDto.setCallBackOpenApi(false);
        try {
            logger.info("billingByGroup请求商城开票入参--{}", JSON.toJSONString(invoiceGroupDto));
            List<PassengerInvoiceDto> passengerInvoiceDto =
                    HttpClientUtil.postList(restTemplate,
                            slbConfig.getPay()
                                    + BILLING_INVOICE_GROUP,
                            invoiceGroupDto,
                            PassengerInvoiceDto.class);
            logger.info("billingByGroup接口返回商城开票入参--{}, 返回 {}", JSON.toJSONString(invoiceGroupDto),
                    JSON.toJSONString(passengerInvoiceDto));
            if (CollectionUtil.isNotEmpty(passengerInvoiceDto)) {
                //修改行程为已开票
                return Response.createSuccess("开票成功");
            }
            return Response.createError("开票失败,请稍后重试");
        } catch (Exception e) {
            logger.error("invoiceBilling param={} error:", JSON.toJSONString(invoiceGroupDto), e);
            return Response.createError("开票出错");
        }
    }

    /**
     * 初始化入参
     *
     * @param req           入参
     * @param passengerUuid 乘客uuid
     * @return {@link MallInvoicePageReqDto}
     */
    private MallInvoicePageReqDto initParam(InvoiceRoutReq req, String passengerUuid) {
        MallInvoicePageReqDto mallInvoicePageReqDto = new MallInvoicePageReqDto();
        if (ObjectUtil.isNotNull(req.getBizType())) {
            mallInvoicePageReqDto.setBizType(req.getBizType());
        }

        mallInvoicePageReqDto.setInvoiceStatusList(Lists.newArrayList(0));
        if (StringUtils.isNotBlank(req.getStartDay())) {
            DateTime beginTime = DateUtil.beginOfDay(DateUtil.parse(req.getStartDay(), "yyyyMMdd"));
            mallInvoicePageReqDto.setStartTime(beginTime);
        }
        if (StringUtils.isNotBlank(req.getEndDay())) {
            DateTime endTime = DateUtil.endOfDay(DateUtil.parse(req.getEndDay(), "yyyyMMdd"));
            mallInvoicePageReqDto.setEndTime(endTime);
        }
        mallInvoicePageReqDto.setUserUuid(passengerUuid);
        mallInvoicePageReqDto.setInvoiceClass(InvoiceClassEnum.MALL.getType());
        mallInvoicePageReqDto.setInvoiceSubjectCode(NUM_3);
        mallInvoicePageReqDto.setCurrPage(req.getCurrPage() == null ? 1 : req.getCurrPage());
        mallInvoicePageReqDto.setPageSize(
                null == req.getPageSize() || req.getPageSize() > CommonNumConst.NUM_200 ? CommonNumConst.NUM_20
                        : req.getPageSize());
        mallInvoicePageReqDto.setOrderNo(req.getOrderNo());
        return mallInvoicePageReqDto;
    }

    /**
     * 重开发票校验 及作废原发票
     *
     * @param req 入参
     * @return {@link Response}
     */
    private Response<?> checkRedoneMallBill(InvoiceMallSubmitReq req) {
        Response checkResponse = Response.createSuccess();
        if (StringUtils.isBlank(req.getUuid())) {
            checkResponse = Response.createError("发票id不能为空");
        }
        // 判断发票状态
        InvoiceReq invoiceReq = new InvoiceReq();
        invoiceReq.setUuid(req.getUuid());
        Response<PassengerInvoiceDetailVo> invoiceInfo = getInvoiceInfoAll(invoiceReq);
        if (!invoiceInfo.isSuccess() || ObjectUtil.isNull(invoiceInfo.getData())) {
            return Response.createError("查询发票详情失败");
        }
        // 查看发票抬头是否变更
        if (StringUtils.isNotBlank(req.getHeader())
                && StringUtils.equals(req.getHeader(), invoiceInfo.getData().getHeader())) {
            return Response.createError("提交失败，开票信息未变更");
        }
        if (Integer.valueOf(NUM_5).equals(invoiceInfo.getData().getStatus())) {
            if (Integer.valueOf(NUM_1).equals(invoiceInfo.getData().getModifyStatus())) {
                return Response.createError("提交失败，当前发票无法重开");
            }
            // 重新开票,先作废原发票
            Response<String> response =
                    passengerInvoiceService.invalidInvoice(req.getUuid(), req.getApplySource(), null, null);
            if (!response.isSuccess()) {
                return Response.createError("重新开票失败,请稍后重试");
            }
        }
        return checkResponse;
    }

    /**
     * 获取发票详情 支持出行发票和商城发票
     *
     * @param invoiceReq 入参
     * @return {@link Response<PassengerInvoiceDetailVo>}
     */
    public Response<PassengerInvoiceDetailVo> getInvoiceInfoAll(InvoiceReq invoiceReq) {
        PassengerInvoiceDto passengerInvoiceDto = new PassengerInvoiceDto();
        passengerInvoiceDto.setUuid(invoiceReq.getUuid());
        // update by ivy .2021/09/18 修改为dubbo方式
        Response<PassengerInvoiceDto> response =
                passengerInvoiceService.invoiceAllDetail(passengerInvoiceDto);
        if (!response.getSuccess() || null == response.getData()) {
            return Response.createError("查询商城发票详情失败");
        }
        PassengerInvoiceDto data = response.getData();
        PassengerInvoiceDetailVo vo = new PassengerInvoiceDetailVo();
        BeanUtil.copyProperties(data, vo, "mallList");
        boolean b = (InvoiceClassEnum.MALL.getType().equals(data.getInvoiceClass())
                || InvoiceClassEnum.CHARGING_FEE.getType().equals(data.getInvoiceClass()))
                && !CollectionUtils.isEmpty(data.getMallList());
        if (b) {
            formatMallData(vo, data.getMallList());
        }
        return Response.createSuccess(vo);
    }

    /**
     * 提交商城发票初始化参数
     *
     * @param req 入参
     * @return {@link PassengerInvoiceBo}
     */
    private PassengerInvoiceBo initParamOfSubmitMallBill(InvoiceMallSubmitReq req) {
        PassengerInvoiceBo passengerInvoice = BeanUtils.propertiesCopy(req, PassengerInvoiceBo.class);
        if (!CollectionUtils.isEmpty(req.getMallList())) {
            List<InvoiceMallDataBo> mallList = new ArrayList<>();
            for (InvoiceMallSubmitItem invoiceMallSubmitItem : req.getMallList()) {
                InvoiceMallDataBo dto = new InvoiceMallDataBo();
                dto.setBizType(invoiceMallSubmitItem.getBizType());
                dto.setCity(invoiceMallSubmitItem.getCityName());
                dto.setAmount(invoiceMallSubmitItem.getInvoiceAmount());
                dto.setOrderNo(invoiceMallSubmitItem.getOrderNo());
                dto.setSkuName(invoiceMallSubmitItem.getSkuName());
                dto.setSkuNum(invoiceMallSubmitItem.getSkuNum());
                dto.setCreateTime(new Date());
                if (ObjectUtil.isNotNull(invoiceMallSubmitItem.getCompleteTime())) {
                    dto.setCompleteTime(new Date(invoiceMallSubmitItem.getCompleteTime()));
                }
                Integer invoiceClass = invoiceMallSubmitItem.getInvoiceClass();
                Integer invoiceSubjectCode = invoiceMallSubmitItem.getInvoiceSubjectCode();
                dto.setInvoiceClass(invoiceClass);
                dto.setInvoiceSubjectCode(invoiceSubjectCode);
                dto.setGroupKey(String.join("_", Objects.nonNull(invoiceClass) ? String.valueOf(invoiceClass) : "",
                        Objects.nonNull(invoiceSubjectCode) ? String.valueOf(invoiceSubjectCode) : ""));
                mallList.add(dto);
            }
            passengerInvoice.setMallList(mallList);
        }
        passengerInvoice.setInvoiceClass(NUM_2);
        passengerInvoice.setApplySource(1);
        // 商城只开电子发票
        passengerInvoice.setSendType(NUM_2);
        return passengerInvoice;
    }

    /**
     * 设置购物中心数据格式
     *
     * @param vo       返回对象
     * @param mallList 列表
     */
    private void formatMallData(PassengerInvoiceDetailVo vo, List<InvoiceMallDto> mallList) {
        vo.setMallOrderNum(mallList.size());
        Date startDate = mallList.get(0).getCreateTime();
        Date endDate = mallList.get(0).getCreateTime();
        List<MallInvoiceItemVo> mallVoList = new ArrayList<>();
        for (InvoiceMallDto dto : mallList) {
            MallInvoiceItemVo mallInvoiceVo = new MallInvoiceItemVo();
            mallInvoiceVo.setOrderNo(dto.getOrderNo());
            mallInvoiceVo.setCityName(dto.getCity());
            mallInvoiceVo.setAmount(dto.getAmount());
            mallInvoiceVo.setBizType(dto.getBizType());
            mallInvoiceVo.setCreateTime(dto.getCreateTime().getTime());
            mallInvoiceVo.setName(dto.getSkuName());
            mallInvoiceVo.setQuantity(dto.getSkuNum());
            mallInvoiceVo.setInvoiceClass(vo.getInvoiceClass());
            mallInvoiceVo.setInvoiceSubjectCode(vo.getInvoiceSubjectCode());
            mallVoList.add(mallInvoiceVo);
            if (ObjectUtil.isNotNull(dto.getCreateTime()) && dto.getCreateTime().compareTo(startDate) < 0) {
                startDate = dto.getCreateTime();
            }
            if (ObjectUtil.isNotNull(dto.getCreateTime()) && dto.getCreateTime().compareTo(endDate) > 0) {
                endDate = dto.getCreateTime();
            }
        }
        vo.setMallList(mallVoList);
        vo.setMallStartDate(startDate.getTime());
        vo.setMallEndDate(endDate.getTime());
    }
}
