package com.t3.ts.pay.center.api.dto.route;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Author:   liuss
 * Date:     2020/7/20 22:40
 * Description: 费用详情信息（出行转储）
 */
@Data
public class RouteFareItemsDto {

    String routePlanUuid;

    //额外费用
    BigDecimal otherFare;

    //路桥费
    BigDecimal roadBridgeFare;

    //高速费
    BigDecimal highwayFare;

    //司机减免金额
    Double driverReliefFare;

    //停车费
    BigDecimal parkingFare;

    //调整价格
    BigDecimal adjustFare;

    //付加服务费
    BigDecimal serviceFare;

    /**
     * 计费规则快照
     */
    private String fareModelJson;

    /**
     * 基础补足费
     */
    private BigDecimal highPremiumFare;

    //订单总金额 （订单金额）(包含企业用车一口价，共用一个字段) 行程总费用（=司机收入=各费用之和*高峰溢价率+附加费等+（后台）调整价格）
    BigDecimal totalFare;

    //订单费用
    BigDecimal orderFare;

    //起步费
    BigDecimal startFare;

    //折扣费用（增加折扣明细）
    BigDecimal discountFare;

    //等待费用
    BigDecimal waitFare;

    //超出里程费
    BigDecimal overTripFare;

    //超出时长费
    BigDecimal overTimeFare;

    //夜间费
    BigDecimal nightFare;

    //长途费
    BigDecimal haulBackFare;

    //会员折让费
    BigDecimal memberDiscountFare;

    // 员工折扣
    BigDecimal employeeDiscountFare;

    // 会员折让费类型 1 员工专享
    Integer memberDiscountType;

    //渠道折让费
    BigDecimal channelDiscountFare;

    //取消费
    BigDecimal cancelFare;

    //是否预付
    Boolean isPrePay;

    //计价方式
    Integer fareMethod;

    //折扣上限 (快享 有这个展示 ，对于其他的展示需要确认)
    BigDecimal cellingFare;

    //起步里程
    BigDecimal startTrip;

    //起步时长
    Integer startDuration;

    //超出里程
    Double beyondTrip;

    //超出时间
    Integer beyondTimeLength;

    //回空里程--远途里程
    Double haulBackTrip;

    //夜间里程
    Double nightTrip;

    //等待时长
    Integer waitDuration;

    //折扣费率
    Double highPremiumRate;

    //---------------出租车添加-----------------------

    //费用是否变更（0否，1是）
    private Integer fareChange;

    //燃油附加费
    private BigDecimal fuelExtraCharge;

    /**
     * 渠道直减
     */
    private BigDecimal channelDirectMinus;

    //时长、里程费用明细
    private RouteFareDetailDto routeFareDetail;

    /**
     * 流水明细
     */
    private BigDecimal expenseFare;
}
