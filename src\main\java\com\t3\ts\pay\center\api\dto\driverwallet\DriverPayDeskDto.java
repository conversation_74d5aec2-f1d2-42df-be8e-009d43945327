package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class DriverPayDeskDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结算单类型 对应
     * 结算单类型枚举 com.t3.ts.settlement.centre.enums.BizType
     */
    private Integer settlementBizType;

    /**
     * 支付单类型 对应
     * 支付单枚举 com.t3.ts.pay.remote.constants.EnumPayOrderType
     */
    private Integer payOrderType;

    /**
     * 业务订单ID
     */
    private String orderUuid;


    /**
     * 是否云闪付
     */
    private Boolean quickPass=true;


    /**
     * 支付渠道列表
     */
    @ApiModelProperty(value = "支付渠道列表")
    private List<Integer> payChannels;

}
