package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.CharteredPaymentBusiness;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.dto.chartered.AdvanceChargeReq;
import com.t3.ts.pay.center.api.dto.chartered.PayTradeCharteredReq;
import com.t3.ts.pay.center.api.dto.chartered.RoutePaymentReq;
import com.t3.ts.pay.center.api.dto.vo.RechargePayVo;
import com.t3.ts.result.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 包车相关支付接口
 *
 * <AUTHOR>
 * @date 2021/9/15 10:46
 */
@RestController
@Slf4j
@RequestMapping("/api/payment/v1/chartered")
@Api("Chartered-Api")
public class CharteredApi extends BaseApi {

    private final CharteredPaymentBusiness charteredPaymentBusiness;

    /**
     * @param charteredPaymentBusiness 包车相关的乘客支付充值逻辑
     */
    public CharteredApi(CharteredPaymentBusiness charteredPaymentBusiness) {
        this.charteredPaymentBusiness = charteredPaymentBusiness;
    }

    /**
     * 收银台详情-包车
     *
     * @param req req
     * @return Response
     */
    @PostMapping("/cashierDeskPro")
    @ApiOperation(value = "收银台", notes = "收银台")
    public Response cashierDeskPro(@RequestBody PayTradeCharteredReq req) {
        return charteredPaymentBusiness.cashierDeskPro(req);
    }

    /**
     * @param routePaymentReq 支付车费入参
     * @return com.t3.ts.result.Response<com.t3.ts.chartered.controller.payment.vo.RechargePayVo>
     * @author: qul
     * @Description: 支付车费
     * @CreateDate:
     */
    @PostMapping("/routePay")
    @ApiOperation(value = "支付车费", notes = "支付车费")
    public Response<RechargePayVo> routePay(@RequestBody @Validated RoutePaymentReq routePaymentReq) {
        return charteredPaymentBusiness.routePay(routePaymentReq, RequestContextHelper.getPassengerUuid(),
                RequestContextHelper.getPassengerMobile());
    }

    /**
     * @param advanceChargeReq 入参
     * @return com.t3.ts.result.Response<com.t3.ts.pay.center.api.dto.vo.RechargePayVo>
     * @author: qul
     * @Description: 预付款支付接口
     * @CreateDate:
     */
    @PostMapping("/advancePayment")
    @ApiOperation(value = "预付款", notes = "预付款")
    public Response<RechargePayVo> advancePayment(@RequestBody @Validated AdvanceChargeReq advanceChargeReq) {
        return charteredPaymentBusiness.advanceCharge(advanceChargeReq, RequestContextHelper.getPassengerUuid(),
                RequestContextHelper.getPassengerMobile());
    }
}
