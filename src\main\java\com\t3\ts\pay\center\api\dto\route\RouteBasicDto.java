package com.t3.ts.pay.center.api.dto.route;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/13 14:23
 * @description:
 */
@Data
public class RouteBasicDto implements Serializable {

    private static final long serialVersionUID = -1386279477375145385L;

    /**
     * 行程计划Id
     */
    private String routePlanId = "";

    /**
     * 行程状态
     */
    private String routeStatus = "";

    /**
     * 订单模块类型（1:出租车,2：专车，3：拼车，4：快车）
     */
    private Integer typeModule = 0;

    /**
     * 1实时单  2 预约单
     */
    private Integer typeTime = 0;

    /**
     * 行程类型（本人/代客）：1 本人；2 代客
     */
    private Integer typeSelf;

    /**
     * 车架号
     */
    private String vin = "";

    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    private Integer typeEnt;

    /**
     * 订单行程类型（1：用车;2日租;3半日租;4接机;5送机;6接站;7送站）
     */
    private Integer typeTrip;

    /**
     * 订单来源
     */
    private Integer source;

    /**
     * 产品线   4:快享 2:专享 5:顺风车  10:包车  11:包车-专车 12:包车-快车 13:企业用车-快享
     */
    private Integer productLine;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 派单过程中状态：0-正常；1-改派中；2-改派失败；3-改派成功；4-指派完成
     */
    private Integer processStatus;

    /**
     * 出发时间
     **/
    private Date deparTime;

    /**
     * 城市id
     */
    private String cityUuid;

    /**
     * 司机uuid
     */
    private String driverUuid;

    /**
     * 区域Code
     */
    private String areaCode;

    /**
     * 计价方式
     */
    private Integer fareMethod;

    /**
     * 订单子状态
     */
    private Integer subStatus;

}
