package com.t3.ts.pay.center.api.bo;

import com.t3.ts.invoice.center.enums.InvoiceStatusEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * @Author: ivy
 * @Date: 2021/09/17 10:00
 */
@Getter
@Setter
public class PassengerInvoiceBo implements Serializable {

    private static final long serialVersionUID = 1097430386469345362L;
    /**
     * uuid
     **/
    private String uuid;

    /**
     * 乘客UUID
     **/
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 发票类型（1按行程2按金额）
     **/
    private Integer type;

    /**
     * 支付方式(0.无（电子发票）,1.到付,2.寄付)
     **/
    private Integer payType;

    /**
     * 抬头类型（1：企业单位2：个人/非企业单位）
     */
    private Integer headerType;

    /**
     * 发票抬头
     **/
    private String header;

    /**
     * 金额
     **/
    private BigDecimal money;

    /**
     * 发票内容
     **/
    private String content;

    /**
     * 收件人
     **/
    private String recipient;

    /**
     * 联系电话
     **/
    private String mobile;

    /**
     * 所在地区
     **/
    private String area;

    /**
     * 详细地址
     **/
    private String detailAddress;

    /**
     * 备注
     **/
    private String remark;

    /**
     * 物流订单号
     **/
    private String logisticsOrderNo;

    /**
     * 发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）、6作废中、7作废成功、8作废失败)
     **/
    private Integer status;

    /**
     * 创建时间
     **/
    private Date createTime;

    /****/
    private Date updateTime;

    /**
     * 修改者
     **/
    private String updater;

    /**
     * 订单的Id,行程开票未order表,充值开票为actual_flow表
     */
    private String orderUuid;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 开票类型(1.电子发票,2.纸质发票)
     */
    private Integer invoiceType;

    /**
     * 接收发票的邮箱
     */
    private String email;

    /**
     * 电子发票JPG路径
     */
    private String invoiceUrl;

    /**
     * 电子发票PDF路径
     */
    private String pdfUrl;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerTel;

    /**
     * 开户账号
     */
    private String openingAccount;

    /**
     * 开户银行
     */
    private String openingBank;

    /**
     * 税号
     */
    private String taxNum;

    /**
     * 行程开始时间
     */
    private Date startDate;

    /**
     * 行程结束时间
     */
    private Date endDate;
    /**
     * 行程数量
     */
    private Integer itineraryNum;

    /**
     * 行程列表
     */
    private List<InvoiceRouteBo> itineraryList;

    /**
     * 商城订单列表
     */
    private List<InvoiceMallDataBo> mallList;

    /**
     * 寄送发票日期
     */
    private Date expressDate;

    /**
     * 申请来源(1:app申请 2:客服申请)
     */
    private Integer applySource;
    /**
     * 业务类型(1:开具发票 2:发票作废)
     */
    private Integer bizType;
    /**
     * 发票作废时间
     */
    private Date invalidDate;
    /**
     * 废票路径
     */
    private String invalidInvoiceUrl;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 申请人工号  -- 客服操作必填
     */
    private String agent;

    /******以下为新增字段*******/

    /**
     * 申请人姓名   -- 客服操作必填
     */
    private String agentName;

    /**
     * 行程来源（1 T3，7 高德）
     */
    private Integer sourceCode;

    /**
     * 发票类别(1 出行服务,2 商城)
     */
    private Integer invoiceClass;


    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    private Integer transportType;
    /**
     * 电子发票发送类型 1：发送发票和行程单 2：仅发送发票 3：仅发送行程单 4：不发送邮件
     */
    private Integer sendType;
    /**
     * 运营商uuid
     */
    private String agentCompanyUuid;
    /**
     * 运营商uuidList
     */
    private List<String> agentCompanyUuidList;
    /**
     * 供应商名称
     */
    private String agentCompanyName;
    /**
     * 发票主体id
     */
    private Integer invoiceSubjectCode;
    /**
     * 分组id
     */
    private String groupId;
    /**
     * 开票失败原因
     */
    private String failReason;

    /**
     * 是否可变更发票状态（0 可修改，1 不可修改）
     */
    private Integer modifyStatus;
    /**
     * 不可修改原因
     */
    private String immutableReason;
    /**
     * 税率
     */
    private BigDecimal rate;

    private static final int NUM_2 = 2;
    private static final int NUM_5 = 5;
    private static final int NUM_7 = 7;


    public String getImmutableReason() {
        return immutableReason;
    }

    public void setImmutableReason(String immutableReason) {
        this.immutableReason = immutableReason;
    }

    /**
     * @return {@link String}
     */
    public String getTypeDisplay() {
        if (null == getType()) {
            return null;
        }
        return getType() == 1 ? "行程开票" : "购卡开票";
    }

    /**
     * @return {@link String}
     */
    public String getInvoiceTypeDisplay() {
        if (null == getInvoiceType()) {
            return null;
        }
        return getInvoiceType() == 1 ? "电子发票" : "纸质发票";
    }

    /**
     * @return {@link String}
     */
    public String getBillingTime() {
        if (null == getCreateTime()) {
            return null;
        }
        return DateFormatUtils.format(getCreateTime(), "yyyy-MM-dd");
    }

    /**
     * @return {@link String}
     */
    public String getStatusDisplay() {
        if (null == getStatus()) {
            return null;
        }
        return InvoiceStatusEnum.getDesc(getStatus());
    }

    @Override
    public String toString() {
        return "PassengerInvoiceDto{"
                + "uuid='" + uuid + '\''
                + ", passengerUuid='" + passengerUuid + '\''
                + ", passengerMobile='" + passengerMobile + '\''
                + ", type=" + type
                + ", payType=" + payType
                + ", headerType=" + headerType
                + ", header='" + header + '\''
                + ", money=" + money
                + ", content='" + content + '\''
                + ", recipient='" + recipient + '\''
                + ", mobile='" + mobile + '\''
                + ", area='" + area + '\''
                + ", detailAddress='" + detailAddress + '\''
                + ", remark='" + remark + '\''
                + ", logisticsOrderNo='" + logisticsOrderNo + '\'' + ", status=" + status
                + ", createTime=" + createTime + ", updateTime=" + updateTime
                + ", updater='" + updater + '\'' + ", orderUuid='" + orderUuid + '\''
                + ", logisticsCompany='" + logisticsCompany + '\''
                + ", invoiceNo='" + invoiceNo + '\'' + ", invoiceType=" + invoiceType
                + ", email='" + email + '\'' + ", invoiceUrl='" + invoiceUrl + '\''
                + ", registerAddress='" + registerAddress + '\'' + ", registerTel='" + registerTel + '\''
                + ", openingAccount='" + openingAccount + '\'' + ", openingBank='" + openingBank + '\''
                + ", taxNum='" + taxNum + '\'' + ", startDate=" + startDate + ", endDate=" + endDate
                + ", itineraryNum=" + itineraryNum + ", itineraryList=" + itineraryList
                + ", expressDate=" + expressDate + ", applySource=" + applySource + ", bizType=" + bizType
                + ", agent=" + agent + ", invoiceCode=" + invoiceCode + ", sourceCode="
                + sourceCode + ", modifyStatus=" + modifyStatus + ", ImmutableReason=" + immutableReason
                + ", transportType=" + transportType
                + '}';
    }

}
