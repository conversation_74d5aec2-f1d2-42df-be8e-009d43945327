package com.t3.ts.pay.center.api.config.filter;

import com.t3.ts.pay.center.api.util.CommonUtils;
import com.t3.ts.pay.common.util.JSONLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.Filter;
import org.apache.dubbo.rpc.Invocation;
import org.apache.dubbo.rpc.Invoker;
import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.apache.dubbo.rpc.service.GenericService;

/**
 * <AUTHOR>
 */
@Slf4j
@Activate(group = {CommonConstants.CONSUMER})
public class ConsumerLogFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        //打印入参日志
        String serviceName = invocation.getInvoker().getInterface().getName();
        String methodName = invocation.getMethodName();
        long startTime = System.currentTimeMillis();
        //执行接口调用逻辑
        Result result = invoker.invoke(invocation);
        long elapsed = System.currentTimeMillis() - startTime;
        //如果发生异常 则打印异常日志
        if (result.hasException() && invoker.getInterface() != GenericService.class) {
            log.error("InterfaceName={}.{}, request:{}, exception:{} ", serviceName, methodName,
                    JSONLogUtils.getLogObjString(invocation.getArguments(), CommonUtils.getRequestFilter()),
                            result.getException());
        } else {
            //打印响应日志
            log.info("InterfaceName={}.{}, request:{}, response:{}, SpendTime:{}ms ", serviceName,
                    methodName, JSONLogUtils.getLogObjString(invocation.getArguments(), CommonUtils.getRequestFilter()),
                    JSONLogUtils.getLogString(result.getValue(), CommonUtils.getRequestFilter()), elapsed);
        }
        //返回结果响应结果
        return result;
    }
}
