package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 发票行程单dto
 */
@ApiModel
@Data
public class InvoiceRouteDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行程id
     */
    @ApiModelProperty(value = "行程id")
    private String routeUuid;
    /**
     * 发票id
     */
    @ApiModelProperty(value = "发票id")
    private String invoiceUuid;
    /**
     * 乘客id
     */
    @ApiModelProperty(value = "乘客id")
    private String passengerUuid;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    /**
     * 订单模块类型（1：出租车，2：专享，3：拼车，4：快享，5：顺风车，6：骑手，7：搬家）
     */
    @ApiModelProperty(value = "订单模块类型")
    private Integer typeModule;
    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;
    /**
     * 出发时间
     */
    @ApiModelProperty(value = "出发时间")
    private Date deparTime;
    /**
     * 起点
     */
    @ApiModelProperty(value = "起点")
    private String originAddress;
    /**
     * 终点
     **/
    @ApiModelProperty(value = "终点")
    private String destAddress;
    /**
     * 里程
     */
    @ApiModelProperty(value = "里程")
    private Double trip;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payMoney;
    /**
     * 附加费用（不可开票费用=附加服务费用+高速费+路桥费+停车费+其他费）
     */
    @ApiModelProperty(value = "附加费用")
    private BigDecimal additionalMoney;

    /**
     * 状态（1：有效，2：作废）
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    @ApiModelProperty(value = "行程类型（个人/企业）：1 个人；2 企业")
    private Integer typeEnt;

    /**
     * add 2020年2月19日17:26:58
     * 行程结束时间
     */
    @ApiModelProperty(value = "行程结束时间")
    private Date arriveTime;
    /**
     * 行程结束时间
     */
    @ApiModelProperty(value = "开票金额(不包括附加费)")
    private BigDecimal amount;
    /**
     * 订单行程类型(1.用车 8 包车)
     */
    @ApiModelProperty(value = "订单行程类型")
    private Integer typeTrip;

    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    @ApiModelProperty(value = "运力类型(1 T3运力,2 东风运力,3 一汽运力)")
    private Integer transportType;
    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费")
    private BigDecimal serviceAmount;
}
