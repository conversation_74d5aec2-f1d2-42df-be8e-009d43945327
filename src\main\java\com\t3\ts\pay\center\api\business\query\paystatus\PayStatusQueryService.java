package com.t3.ts.pay.center.api.business.query.paystatus;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.result.Response;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 17:23
 */
public interface PayStatusQueryService {

    /**
     * 进行查询
     *
     * @param context 上下文
     * @return {@link Response <String>}
     */
    Response<JSONObject> doQuery(QueryStatusContext context);

}
