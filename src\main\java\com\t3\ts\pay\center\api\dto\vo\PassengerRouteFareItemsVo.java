package com.t3.ts.pay.center.api.dto.vo;

import com.t3.ts.pay.center.api.dto.route.RouteFareItemsDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Author:   liuss
 * Date:     2020/7/20 23:01
 * Description: 乘客订单费用详情
 */
@Data
public class PassengerRouteFareItemsVo extends RouteFareItemsDto implements Serializable {

    //实际计费金额
    BigDecimal actPaid;

    //实际支付金额
    BigDecimal actualFare;

    //优惠券金额
    BigDecimal conponFare;

    //预付费用
    BigDecimal prePayFare;

    //客服退款
    BigDecimal refundFare;

    //客服折让费
    BigDecimal callDiscountFare;

    //预退款
    BigDecimal refundPrePay;

    //余额已支付
    BigDecimal accountCash;

    //订单取消费用
    BigDecimal cancelFare;
    /**
     * 企业折扣
     */
    private BigDecimal firmDiscountFare;
    /**
     * 企业直减
     */
    private BigDecimal firmMinusFare;

    /**
     * 一口价车费
     */
    private BigDecimal onePriceCarFare;

    /**
     * 平台调价
     */
    private BigDecimal advanceDutyAdjustFare = BigDecimal.ZERO;

}
