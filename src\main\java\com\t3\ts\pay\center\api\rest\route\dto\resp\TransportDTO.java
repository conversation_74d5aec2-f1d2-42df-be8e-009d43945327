package com.t3.ts.pay.center.api.rest.route.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * ligq
 *
 * 行程信息接口出参
 */
@Data
public class TransportDTO implements Serializable {
    private static final long serialVersionUID = -8375008505252866790L;
    /**
     * 司机ID
     */
    private String driverUuid;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车型等级
     */
    private Integer vehicleLevel;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 是否新能源车
     */
    private Integer isElectricCar;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 车辆ID
     */
    private String carUuid;
}
