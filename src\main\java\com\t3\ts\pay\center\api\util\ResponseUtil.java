package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.remote.exception.PayExceptionCode;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * HttpServletResponse工具类
 *
 * <AUTHOR>
 * @date 19 /7/7
 */
@Slf4j
public class ResponseUtil {

    /**
     * Write success response.
     *
     * @param response the response
     * @param result   the result
     */
    public static void writeSuccessResponse(HttpServletResponse response, Object result) {
        try {
            response.setStatus(Constants.NUMBER_200);
            response.setHeader("content-type", "application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            out.write(getSuccessMessage(result));
        } catch (IOException e) {
            log.error("ResponseUtil.writeResponse error", e);
            response.setStatus(Constants.NUMBER_500);
        }
    }

    /**
     * Write fail response.
     *
     * @param response      the response
     * @param exceptionCode the exception code
     */
    public static void writeFailResponse(HttpServletResponse response, PayExceptionCode exceptionCode) {
        writeFailResponse(response, exceptionCode.getCode(), exceptionCode.getMessage());
    }

    /**
     * Write fail response.
     *
     * @param response     the response
     * @param errorCode    the error code
     * @param errorMessage the error message
     */
    public static void writeFailResponse(HttpServletResponse response, Integer errorCode, String errorMessage) {
        try {
            response.setStatus(Constants.NUMBER_200);
            response.setHeader("content-type", "application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            out.write(getFailMessage(errorCode, errorMessage));
        } catch (IOException e) {
            log.error("ResponseUtil.writeResponse error", e);
            response.setStatus(Constants.NUMBER_500);
        }
    }

    /**
     * Gets success message.
     *
     * @param data the data
     * @return the success message
     */
    public static String getSuccessMessage(Object data) {
        JSONObject result = new JSONObject();
        result.put("code", "200");
        result.put("success", "true");
        if (data instanceof String) {
            result.put("data", data);
        } else {
            result.put("data", JSON.parseObject(JSON.toJSONString(data)));
        }
        result.put("msg", "获取成功");
        return result.toString();
    }

    /**
     * Gets fail message.
     *
     * @param errorCode the error code
     * @param message   the message
     * @return the fail message
     */
    public static String getFailMessage(Integer errorCode, String message) {
        JSONObject result = new JSONObject();
        result.put("code", NumConstants.STR_500);
        result.put("success", "false");
        result.put("errorCode", errorCode);
        result.put("msg", message);
        return result.toString();
    }
}
