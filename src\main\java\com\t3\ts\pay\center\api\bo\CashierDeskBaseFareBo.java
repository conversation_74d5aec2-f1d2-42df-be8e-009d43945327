package com.t3.ts.pay.center.api.bo;

import com.t3.ts.pay.center.api.dto.trade.CouponVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收银台基础费用
 *
 * <AUTHOR>
 * @date 2020.11.5
 */
@Data
public class CashierDeskBaseFareBo {

    /**
     * 用户钱包余额（赠币+礼品卡）
     */
    private BigDecimal balanceTotal;
    /**
     * 附加费
     */
    private BigDecimal additionalFee;
    /**
     * 剩余应付金额
     */
    private BigDecimal remainPay;
    /**
     * 当前支付总费用
     */
    private BigDecimal amountPayed;
    /**
     * 总费用
     */
    private BigDecimal totalFare;
    /**
     * 预付款已支付
     */
    private BigDecimal prPayed;
    /**
     * 优惠券已支付-包车用
     */
    private BigDecimal couponPay;
    /**
     * 余额已支付
     */
    private BigDecimal balancePay;
    /**
     * 积分已抵扣
     */
    private BigDecimal integralPayed;
    /**
     * 礼品卡是否支持附加费支付
     */
    private Boolean giftCardCanPayService;
    /**
     * 优惠券抵扣信息-包车用
     */
    private CouponVo couponVo;
    /**
     * 余额可支付金额
     */
    private BigDecimal balancePayable;
    /**
     * 企业支付金额
     */
    private BigDecimal enterprisePay;
    /**
     * 企业优惠券已经抵扣的金额
     */
    private BigDecimal companyCouponAmount;
}
