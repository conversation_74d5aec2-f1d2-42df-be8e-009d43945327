package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2022-02-22 10:50
 * @des:
 */
@Data
public class RechargePayDto implements Serializable {
    // 支付方式
    private Integer payOrderChannel;

    // 支付宝支付
    private String alipaySdk;
    // 微信支付
    private String wxPaySdk;
    // 一网通支付
    private String cmbConenet;
    // 银联支付
    private String unionSdk;

    //  (微信、支付宝、一网通、银联 ， 0： 未开通 1： 已开通)
    private Integer isNoSecretPayment;

    private String msg;

    private Boolean isSuccess;

    private String sdk;
}
