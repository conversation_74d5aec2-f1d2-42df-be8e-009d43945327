package com.t3.ts.pay.center.api.enums;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:03
 * @description:
 */
public enum RouteStatus {
    //状态码定义
    ROUTE_0("0", "约车中"),
    ROUTE_1("1", "预约中"),
    ROUTE_2("2", "接乘中"),
    ROUTE_3("3", "已抵达接乘地"),
    ROUTE_4("4", "载乘中"),
    ROUTE_5("5", "已抵达目的地"),
    ROUTE_6("6", "待支付"),
    ROUTE_7("7", "待评价"),
    ROUTE_8("8", "行程结束"),
    ROUTE_9("9", "行程取消");
    // 成员变量
    private String status;
    private String statusName;

    // 构造方法
    RouteStatus(String status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    /**
     * @param status 1
     * @return {@link RouteStatus}
     */
    public static RouteStatus getByCode(String status) {
        for (RouteStatus routeStatus : values()) {
            if (routeStatus.getStatus().equals(status)) {
                return routeStatus;
            }
        }
        return null;
    }

    //覆盖方法
    @Override
    public String toString() {
        return this.status + "_" + this.statusName;
    }

    public String getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }
}
