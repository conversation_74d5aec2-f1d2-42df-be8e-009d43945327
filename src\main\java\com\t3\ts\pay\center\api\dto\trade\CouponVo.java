package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/27 17:05
 * @des 1.0
 */

@Data
public class CouponVo {

    // 优惠券券数量
    private Integer couponCount;

    // 优惠券券id
    private String couponId;

    // 优惠券券可抵扣金额
    private BigDecimal decutionAmount;

    // 第三方渠道
    private String thirdChannel;

    private List<String> thirdChannelList;
    /**
     * cannotUseCouponFlag=true 表示现金余额不支持叠加优惠券的场景
     */
    private Boolean cannotUseCouponFlag = Boolean.FALSE;
    /**
     * 提示文案：现金余额不支持叠加优惠券的场景  现金余额不支持叠加优惠券
     */
    private String cannotUseCouponMsg;
    /**
     * 可以使用的终端 1. app专享 8 微信小程序 0 通用
     */
    private String itemSubType;

    /**
     * 展示文案
     */
    private String itemSubTitle;


    /**
     * 展示文案: 本券更优惠，可撤销膨胀
     */
    private String expandedInfo;

}
