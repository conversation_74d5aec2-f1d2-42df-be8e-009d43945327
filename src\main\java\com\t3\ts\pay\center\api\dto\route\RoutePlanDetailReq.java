package com.t3.ts.pay.center.api.dto.route;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Author:   liuss
 * Date:     2020/7/16 17:12
 * Description: 订单详情查询入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoutePlanDetailReq implements Serializable {

    private static final long serialVersionUID = -7951535403187928779L;

    /**
     * 行程id
     */
    private String routePlanUuid;

    /**
     * 1:行程主要信息 {@link ReadingRoutePlanDto}
     * 2:状态信息 {@link com.t3.ts.api.route.dto.ReadingRouteStateDto}
     * 3:途经点信息{@link com.t3.ts.api.route.dto.ReadingRoutePassingPointDto}
     * 4.取消信息 {@link com.t3.ts.api.route.dto.ReadingRouteCancelDto}
     * 5.详细信息 {@link com.t3.ts.api.route.dto.ReadingRouteDetailDto}
     * 6:起点 {@link com.t3.ts.api.route.dto.ReadingRoutePassingPointDto}
     * 7:终点 {@link com.t3.ts.api.route.dto.ReadingRoutePassingPointDto}
     * 8.变更点信息 {@link com.t3.ts.api.route.dto.ReadingRoutePassingPointDto}
     * 9:规划路径 {@link com.t3.ts.api.route.dto.ReadingRouteSegmentDto}
     * 10：扩展字段 {@link com.t3.ts.api.route.dto.ReadingRouteExtendDto}
     * 11：评价信息 {@link com.t3.ts.api.route.dto.ReadingRouteEvaluateDto}
     * 12：航班信息 {@link com.t3.ts.api.route.dto.ReadingRouteTrafficDto}
     * 13： HBase信息(包含东风一汽运力标识,顺风车相关) {@link com.t3.ts.api.route.dto.ReadingHBaseInfoDto}
     */
    private List<Integer> queryParam;

    /**
     * <Description> 构造器<br>
     *
     * <AUTHOR>
     * @version 1.0<br>
     * @taskId <br>
     * @CreateDate 2020/8/4 <br>
     */
    public RoutePlanDetailReq(String routePlanUuid) {
        this.routePlanUuid = routePlanUuid;
    }
}
