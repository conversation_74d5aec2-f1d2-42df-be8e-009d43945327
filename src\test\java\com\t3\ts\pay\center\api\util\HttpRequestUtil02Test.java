package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/28 17:03
 */
public class HttpRequestUtil02Test {
    @Mock
    Logger log;
    @InjectMocks
    HttpRequestUtil httpRequestUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetJSONParamFromJson() throws Exception {
        try {
            JSONObject result = HttpRequestUtil.getJSONParamFromJson(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetMapParamFromForm() throws Exception {
        try {
            Map<String, Object> result = HttpRequestUtil.getMapParamFromForm(null);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testParseUa() throws Exception {
        try {
            Integer result = HttpRequestUtil.parseUa(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetChannel() throws Exception {
        try {
            Integer result = HttpRequestUtil.getChannel("micromessenger");
            result = HttpRequestUtil.getChannel("alipayclient");
            result = HttpRequestUtil.getChannel("111");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme