package com.t3.ts.pay.center.api.rest.route;

import com.t3.ts.pay.center.api.dto.RouteInfoDto;

/**
 * 出行详情客户端
 *
 * <AUTHOR>
 * @date 2023/3/6 17:58
 */
public interface RouteDetailClient {
    /**
     * 根据行程ID查询行程详情
     * @param routePlanUuid 行程UUID
     * @return 行程详情
     */
    RouteInfoDto getRouteInfo(String routePlanUuid);

    /**
     * 降级
     * @param routePlanUuid 行程id
     * @return 详情
     */
    RouteInfoDto getRouteInfoWeak(String routePlanUuid);

    /**
     * 根据行程ID查询行程详情
     * @param routePlanUuid 行程UUID
     * @return 行程详情
     */
    RouteInfoDto getRouteInfoWithFareWeak(String routePlanUuid);
}
