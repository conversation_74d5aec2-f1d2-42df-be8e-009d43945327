package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.bo.PayDeskInfoBo;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.dto.AdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.result.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-15 21:34
 * @des: 行程支付相关接口类
 */
public interface RoutePayService {

    /**
     * 获取预付款（充值）支付信息
     *
     * @param advanceRechargeDTO 参数
     * @return RechargePayBo 出参
     */
    Response<RechargePayBo> advanceRecharge(AdvanceRechargeDTO advanceRechargeDTO);

    /**
     * 获取收银台信息
     *
     * @param routePlanUuid 行程uuid
     * @param passengerUuid 乘客uuid
     * @return 出参
     */
    Response<PayDeskInfoBo> getPayDeskInfo(String routePlanUuid, String passengerUuid);

    /**
     * 发起行程支付
     *
     * @param routePayDTO 入参
     * @return RechargePayBo 出参
     */
    Response<RechargePayBo> launchRoutePay(RoutePayDTO routePayDTO);

    /**
     * 获取支付结果
     *
     * @param payType       支付类型
     * @param paymentInfoBo 支付参数
     * @return 支付结果
     */
    Response<RechargePayBo> getPayResponse(int payType, PaymentInfoBo paymentInfoBo);


    /**
     * fullPaymentDto
     *
     * @param paymentDto        paymentDto
     * @param payTypeList       payTypeList
     * @param openId            openId
     * @param aliAuthCode       aliAuthCode
     * @param availableIntegral availableIntegral
     * @return 出参
     */
    Response<PaymentDto> fullPaymentDto(PaymentDto paymentDto, List<Integer> payTypeList, String openId,
                                               String aliAuthCode, String availableIntegral);
}
