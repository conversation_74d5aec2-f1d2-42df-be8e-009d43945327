package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.*;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_10;


/**
 * @Author: ivy
 * @Date: 2019-03-02
 * @Description: 请求REQ
 */
@ApiModel
public class InvoiceReq implements Serializable {

    private static final long serialVersionUID = 8326079494002313455L;
    /**
     * 发票id
     */
    @ApiModelProperty(value = "发票id")
    private String uuid;

    /**
     * 发票类型（1按行程2按金额）
     **/
    @ApiModelProperty(value = "发票类型（1按行程2按金额）")
    private Integer type;

    /**
     * 发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）)
     **/
    @ApiModelProperty(value = "发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）)")
    private Integer status;


    @Min(value = 1)
    private Integer currPage = 1;

    @Max(value = 10)
    private Integer pageSize = NUM_10;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCurrPage() {
        return currPage;
    }

    public void setCurrPage(Integer currPage) {
        this.currPage = currPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
