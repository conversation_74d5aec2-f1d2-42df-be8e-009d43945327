package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.pay.center.api.dto.vo.BookPayWayVo;
import com.t3.ts.pay.center.api.dto.vo.PayWayVo;
import com.t3.ts.pay.remote.dto.DriverPayDetail;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class DriverPayDeskVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 司机ID
     */
    private String userId;

    /**
     * 订单uuid
     */
    private String orderUuid;

    /**
     * 订单总金额
     */
    private BigDecimal orderFare;

    /**
     * 充电-保险费（汽车着火之类）
     */
    private BigDecimal insureFare = BigDecimal.ZERO;

    /**
     * 已支付金额
     */
    private BigDecimal orderFarePayed;

    /**
     * 剩余待支付金额
     */
    private BigDecimal remainPay;

    /**
     * 三方支付明细
     */
    private List<DriverPayDetail> payDetail;

    /**
     * 三方支付渠道列表
     */
    List<PayWayVo> payWayVoList;

    /**
     * 可支付余额渠道列表
     */
    List<BookPayWayVo> bookPayWayVoList;

    /**
     * 账本余额不足提示用户增加三方支付渠道描述
     */
    private String thirdPayTypeToastMsg;

    /**
     * 支付方式组合满足继续支付条件，可以继续支付
     * 1、余额足够
     * 2、勾选的三方支付渠道
     */
    private boolean canPay = true;

    /**
     * 需要查询协议
     */
    private boolean needProtocol = false;

}
