package com.t3.ts.pay.center.api.config.filter;

import org.apache.dubbo.rpc.Result;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ProviderLogFilterTest.java
 * @Description TODO
 * @createTime 2020年11月24日 19:18:00
 */
public class ProviderLogFilterTest {
    @Mock
    Logger log;
    @InjectMocks
    ProviderLogFilter providerLogFilter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testInvoke() throws Exception {
        Result result = providerLogFilter.invoke(null, null);
        Assert.assertEquals(null, result);
    }
}

//Generated with love by <PERSON><PERSON>e :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme