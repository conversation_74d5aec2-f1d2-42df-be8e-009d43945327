package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/5/10 15:45
 * @description 收银台数出参
 */
@Data
public class PayDeskVo {

    /**
     * 行程id
     */
    private String orderUuid;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 车牌
     */
    private String plateNum;
    /**
     * 钱包余额
     */
    private BigDecimal balance;
    /**
     * 总费用
     */
    private BigDecimal totalFare;
    /**
     * 折扣价格
     */
    private BigDecimal discountFare;
    /**
     * 待支付费用
     */
    private BigDecimal actualFare;
    /**
     * 余额已支付费用
     */
    private BigDecimal amountPayed;
    /**
     * 优惠已抵扣
     */
    private BigDecimal couponPayed;
    /**
     * 优惠卷id
     */
    private String couponId;
    /**
     * 优惠价
     */
    private BigDecimal couponMoney;
    /**
     * 优惠卷数量
     */
    private Integer couponCount;

    private Boolean isPrePay;

    private BigDecimal hasPrePay;
    /**
     * 附加费
     */
    private BigDecimal additionalFee;

    private BigDecimal festivalFee;
    private BigDecimal crossCityFee;
    private BigDecimal dispatchFee;
    private BigDecimal lostReturnFee;
    private BigDecimal compensationFare;
    /**
     * 赠送币
     */
    private BigDecimal giftCurrency;
    /**
     * 取消费
     */
    private BigDecimal cancelFee;
    /**
     * 超时取消费
     */
    private BigDecimal timeOutFee;

    /**
     * 余额可支付费用
     */
    private BigDecimal availableBalance;

    /**
     * 礼品卡是否支持附加费支付
     */
    private Boolean giftCardCanPayService;
    /**
     * 是否可以积分抵扣
     */
    private Boolean integralDeductFlag;
    /**
     * 可用积分
     */
    private BigDecimal availableIntegral;
    /**
     * 可抵扣费用（设置默认值，防止某些特殊场景下空指针异常）
     */
    private BigDecimal deductCost = BigDecimal.ZERO;
    /**
     * 积分已抵扣费用
     */
    private BigDecimal integralPayed;
    /**
     * 积分抵扣限制提示
     */
    private Map<String, Object> integralMsg;
    /**
     * 新版节分抵扣限制提示
     */
    private Map<String, Object> integralMessage;

    private String orgCode;

}

