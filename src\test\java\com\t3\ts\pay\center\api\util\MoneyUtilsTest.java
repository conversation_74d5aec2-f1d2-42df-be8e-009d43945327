package com.t3.ts.pay.center.api.util;

import java.math.BigDecimal;
import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:02
 */
public class MoneyUtilsTest {

    @Test
    public void testFenToYuan() throws Exception {
        BigDecimal result = MoneyUtils.fenToYuan(new BigDecimal(0));
        Assert.assertEquals(new BigDecimal(0), result);
    }

    @Test
    public void testFenToYuan2() throws Exception {
        BigDecimal result = MoneyUtils.fenToYuan(Integer.valueOf(0));
        Assert.assertEquals(new BigDecimal(0), result);
    }

    @Test
    public void testIntToBigDecimal() throws Exception {
        BigDecimal result = MoneyUtils.intToBigDecimal(0);
        Assert.assertEquals(new BigDecimal(0), result);
    }

    @Test
    public void testToIntAmount() throws Exception {
        Integer result = MoneyUtils.toIntAmount(new BigDecimal(0));
        Assert.assertEquals(Integer.valueOf(0), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
