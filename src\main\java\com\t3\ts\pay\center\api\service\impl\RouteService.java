package com.t3.ts.pay.center.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.service.AccountUnifiedService;
import com.t3.ts.pay.center.api.util.Constants;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.pay.center.api.util.ResponseUtil;
import com.t3.ts.pay.remote.exception.PayException;
import com.t3.ts.pay.remote.exception.PayExceptionCode;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.service.SettlementUnifiedService;
import com.t3.ts.utils.StringUtils;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL> Date:     2020/5/14, 0014 14:01
 */
@Component
@Slf4j
public class RouteService {

    /**
     * The Unified service.
     */
    @DubboReference
    private UnifiedService unifiedService;
    /**
     * The Account unified service.
     */
    @DubboReference
    private AccountUnifiedService accountUnifiedService;
    /**
     * The Settlement unified service.
     */
    @DubboReference
    private SettlementUnifiedService settlementUnifiedService;

    /**
     * Route.
     *
     * @param request  the request
     * @param response the response
     */
    public void route(HttpServletRequest request, HttpServletResponse response) {
        JSONObject params = HttpRequestUtil.getJSONParamFromJson(request);
        String method = params.getString("method");
        checkMethod(method);
        doRoute(request, response, params, method);
    }

    /**
     * 做的路线
     *
     * @param request  请求
     * @param response 响应
     * @param params   参数个数
     * @param method   方法
     */
    public void doRoute(HttpServletRequest request, HttpServletResponse response, JSONObject params, String method) {
        if (method.contains(Constants.PAY_CENTER_TRADE_PREFIX) || method.contains(Constants.PAY_CENTER_PREFIX)) {
            com.t3.ts.pay.remote.dto.UnifiedDto unifiedDto = new com.t3.ts.pay.remote.dto.UnifiedDto();
            unifiedDto.setSceneType(method);
            unifiedDto.setChannel(HttpRequestUtil.parseUa(request));
            unifiedDto.setExtendParam(params.getString("content"));
            Response handle = unifiedService.handle(unifiedDto);
            if (Boolean.TRUE.equals(handle.getSuccess())) {
                ResponseUtil.writeSuccessResponse(response, handle.getData());
            } else {
                ResponseUtil.writeFailResponse(response, handle.getCode(), handle.getMsg());
            }
        } else if (method.contains(Constants.ACCOUNT_CENTER_PREFIX)) {
            com.t3.ts.account.center.dto.UnifiedDto unifiedDto = new com.t3.ts.account.center.dto.UnifiedDto();
            unifiedDto.setSceneType(method);
            unifiedDto.setExtendParam(params.getString("content"));
            Response handle = accountUnifiedService.handle(unifiedDto);
            if (Boolean.TRUE.equals(handle.getSuccess())) {
                ResponseUtil.writeSuccessResponse(response, handle.getData());
            } else {
                ResponseUtil.writeFailResponse(response, handle.getCode(), handle.getMsg());
            }
        } else if (method.contains(Constants.SETTLEMENT_CENTER_PREFIX)) {
            com.t3.ts.settlement.centre.dto.UnifiedDto unifiedDto = new com.t3.ts.settlement.centre.dto.UnifiedDto();
            unifiedDto.setSceneType(method);
            unifiedDto.setExtendParam(params.getString("content"));
            Response handle = settlementUnifiedService.handle(unifiedDto);
            if (Boolean.TRUE.equals(handle.getSuccess())) {
                ResponseUtil.writeSuccessResponse(response, handle.getData());
            } else {
                ResponseUtil.writeFailResponse(response, handle.getCode(), handle.getMsg());
            }
        } else {
            throw new PayException(PayExceptionCode.SERVICE_NOT_SUPPORT);
        }
    }

    /**
     * Check method.
     *
     * @param method the method
     */
    public void checkMethod(String method) {
        if (StringUtils.isEmpty(method)) {
            throw new PayException(PayExceptionCode.PARAM_CHECK_FAIL);
        }
    }
}
