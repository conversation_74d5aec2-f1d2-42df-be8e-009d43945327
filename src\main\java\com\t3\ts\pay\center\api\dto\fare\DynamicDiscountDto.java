package com.t3.ts.pay.center.api.dto.fare;

import com.t3.ts.pay.center.api.constants.NumConstants;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:08
 * @description:
 */
@Data
public class DynamicDiscountDto implements Serializable {

    /**
     * 折扣率
     */
    private BigDecimal finalRate;

    /**
     * 折扣上限
     */
    private BigDecimal finalUpperLimitFare = new BigDecimal(NumConstants.NUM_9999);

    /**
     * 车型  车型等级   1经济型   2舒适型  3行政型  4商务型  5尊贵型
     **/
    private String carLevel;

    List<DynamicDiscountItemDto> dynamicDiscountItemDtos = new ArrayList<>();

    public void calFinalDiscount() {

        List<DynamicDiscountItemDto> sortDynamicDiscountItemDtos
                = dynamicDiscountItemDtos.stream().sorted((u1, u2) -> u2.getSort().compareTo(u1.getSort())).collect(
                Collectors.toList());

        if (sortDynamicDiscountItemDtos.size() > 0
                && Integer.valueOf(NumConstants.NUM_3).equals(sortDynamicDiscountItemDtos.get(0)
                .getDynamicDiscountType())) {
            finalRate = sortDynamicDiscountItemDtos.get(0).getRate();
            finalUpperLimitFare = sortDynamicDiscountItemDtos.get(0).getUpperLimitFare() == null
                    ? new BigDecimal(NumConstants.NUM_9999)
                    : sortDynamicDiscountItemDtos.get(0).getUpperLimitFare();
        } else if (sortDynamicDiscountItemDtos.size() > 1 && Integer.valueOf(NumConstants.NUM_2)
                .equals(sortDynamicDiscountItemDtos.get(0)
                        .getDynamicDiscountType())) {
            finalRate =
                    sortDynamicDiscountItemDtos.get(0).getRate().multiply(sortDynamicDiscountItemDtos.get(1).getRate());
            finalUpperLimitFare = sortDynamicDiscountItemDtos.get(1).getUpperLimitFare() == null
                    ? new BigDecimal(NumConstants.NUM_9999) : sortDynamicDiscountItemDtos.get(1).getUpperLimitFare();
        } else if (sortDynamicDiscountItemDtos.size() > 0 && Integer.valueOf(1)
                .equals(sortDynamicDiscountItemDtos.get(0)
                        .getDynamicDiscountType())) {
            finalRate = sortDynamicDiscountItemDtos.get(0).getRate();
            finalUpperLimitFare = sortDynamicDiscountItemDtos.get(0).getUpperLimitFare() == null
                    ? new BigDecimal(NumConstants.NUM_9999) : sortDynamicDiscountItemDtos.get(0).getUpperLimitFare();
        } else {
            finalRate = BigDecimal.valueOf(1);
            finalUpperLimitFare = new BigDecimal(NumConstants.NUM_9999);
        }

    }

}
