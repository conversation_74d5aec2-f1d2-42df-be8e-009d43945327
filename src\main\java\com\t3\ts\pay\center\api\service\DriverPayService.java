package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.dto.DriverChannelListResVo;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskDto;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskVo;
import com.t3.ts.pay.center.api.dto.trade.PayDeskVoV4;
import com.t3.ts.pay.remote.dto.GeneralSettlePayDto;
import com.t3.ts.result.Response;

import javax.servlet.http.HttpServletRequest;

/**
 * Description: 支付入口
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:12
 */
public interface DriverPayService {


    /**
     * 司机收银台
     * @param dto 请求参数
     * @param request 请求参数
     * @return {@link Response<PayDeskVoV4>}
     */
    Response<DriverPayDeskVo> deskInfo(DriverPayDeskDto dto, HttpServletRequest request);


    /**
     * 查支付渠道
     * @param request request
     * @return DriverChannelListResVo
     */
    DriverChannelListResVo queryDriverChannelList(DriverPayDeskDto driverPayDeskDto,HttpServletRequest request);


    /**
     * 获取通用泛出行支付配置
     * @param settlementBizType settlementBizType
     * @param payOrderType payOrderType
     * @return GeneralSettlePayDto
     */
    GeneralSettlePayDto getGeneralSettlePayDto(Integer settlementBizType, Integer payOrderType);
}
