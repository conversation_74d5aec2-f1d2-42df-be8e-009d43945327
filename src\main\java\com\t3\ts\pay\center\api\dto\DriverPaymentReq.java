package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 收银台Req
 *
 * <AUTHOR>
 * @date 2022/06/16
 */
@Data
@ApiModel("支付入参")
public class DriverPaymentReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @NotBlank(message = "orderId不能为空")
    @ApiModelProperty(value = "订单id")
    private String orderId;
    /**
     * 支付渠道
     * 后续改用 payChannels
     */
    @Deprecated
    @ApiModelProperty(value = "支付渠道")
    private Integer payChannel;

    /**
     * 支付渠道列表
     */
    @ApiModelProperty(value = "支付渠道")
    private List<Integer> payChannels;

    /**
     * 业务类型 支付的 PayOrderType
     */
    @NotNull(message = "bizType不能为空")
    @ApiModelProperty(value = "支付业务类型 PayOrderType")
    private Integer bizType;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 司机uuid 内部生成
     */
    private String driverUuid;

    /**
     * 支付类型 内部生成
     */
    private List<Integer> payTypeList;
    /**
     * 支付类型 内部生成
     */
    private boolean notNeedPay;
}
