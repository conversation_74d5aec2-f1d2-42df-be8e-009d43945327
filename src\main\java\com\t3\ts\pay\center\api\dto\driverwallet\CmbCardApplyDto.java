package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/9/9 11:39
 */
@ApiModel
@Data
public class CmbCardApplyDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "用户id不能为空")
    private String userId;

    @NotNull(message = "用户类型不能为空")
    private Integer accountType;

    @NotBlank(message = "身份证号不能为空")
    private String idCard;

    private String name;
}
