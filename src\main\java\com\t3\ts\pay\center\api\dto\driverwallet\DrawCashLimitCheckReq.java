package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2023/10/31
 */
@Getter
@Setter
@ApiModel("提现校验-请求参数")
public class DrawCashLimitCheckReq {
    @ApiModelProperty(value = "提款金额")
    @NotNull(message = "提现金额不能为空")
    private String withdrawAmount;
}
