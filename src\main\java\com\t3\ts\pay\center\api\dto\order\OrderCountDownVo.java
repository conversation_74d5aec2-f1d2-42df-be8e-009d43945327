package com.t3.ts.pay.center.api.dto.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.ToString;

import java.io.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-05-15 21:31
 * @des: 订单倒计时缓存对象
 */
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderCountDownVo implements Serializable {

    /**
     * 订单uuid
     */
    private String orderUuid;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 订单时效类型（1：实时，2：预约）
     */
    private Integer typeTime;
    /**
     * 倒计时时间/秒
     */
    private Long countdownTime;

    public OrderCountDownVo() {
    }

    public OrderCountDownVo(String orderUuid, Date createTime, Integer typeTime) {
        this.orderUuid = orderUuid;
        this.createTime = createTime;
        this.typeTime = typeTime;
    }

    public String getOrderUuid() {
        return orderUuid;
    }

    public void setOrderUuid(String orderUuid) {
        this.orderUuid = orderUuid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getTypeTime() {
        return typeTime;
    }

    public void setTypeTime(Integer typeTime) {
        this.typeTime = typeTime;
    }

    public Long getCountdownTime() {
        return countdownTime;
    }

    public void setCountdownTime(Long countdownTime) {
        this.countdownTime = countdownTime;
    }

}
