package com.t3.ts.pay.center.api.service.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PayConstants;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.OfflineCompanyDto;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 企业充值支付处理器
 *
 * <AUTHOR>
 * @date 2022/12/09
 */
@Slf4j
@Component
public class CompanyRechargePayHandler extends AbstractSimpleThirdPayHandler implements PayHandler {

    @Autowired
    private PayChannelFactory payChannelFactory;

    @Autowired
    private CmbAggHelper cmbAggHelper;

    @DubboReference
    private SettlementRechargeService settlementRechargeService;

    /**
     * getType
     *
     * @return String
     */
    @Override
    public String getType() {
        return String.valueOf(EnumPayOrderType.COMPANY_RECHARGE_ONLINE.getCode());
    }

    /**
     * getBizType
     *
     * @param context PayContext
     * @return Integer
     */
    @Override
    public Integer getBizType(PayContext context) {
        return EnumPayOrderType.COMPANY_RECHARGE_ONLINE.getCode();
    }

    /**
     * checkSettlement
     *
     * @param context 上下文
     */
    @Override
    public void checkSettlement(PayContext context) {
        Response<OfflineCompanyDto> response = settlementRechargeService.getOfflineCompanyInfo(context.getOrderId());
        if (null == response || !response.isSuccess() || null == response.getData()) {
            throw new IllegalStateException("结算单不存在");
        }
        context.setUserId(response.getData().getCompanyUuid());
    }

    @Override
    public void checkParam(PayContext context) {
        if (StringUtils.isEmpty(context.getOrderId()) || StringUtils.isEmpty(context.getType())) {
            throw new IllegalArgumentException("orderId|type不能为空");
        }
        context.getExtendParam().put("inner_wxOpenId", context.getWxCode());
    }

    /**
     * createPaymentParam
     *
     * @param context 上下文
     * @return PaymentDto
     */
    @Override
    public PaymentDto createPaymentParam(PayContext context) {
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(super.getMappingBizType(getBizType(context)));
        paymentDto.setSettlementId(context.getOrderId());
        paymentDto.setPaywayEnums(PayWayConvert.getPayWayEnum(context.getPayChannelList()));
        Map<String, Object> params = new HashMap<>(NumConstants.NUM_8_INT);
        params.put("openId", context.getExtendParam().get("inner_wxOpenId"));
        params.put("code", context.getCode());
        paymentDto.setExtendParams(params);
        return paymentDto;
    }

    /**
     * route by pay channel
     *
     * @param payInfo 支付信息
     * @param context 上下文
     */
    @Override
    public void routePayChannel(Response<String> payInfo, PayContext context) {
        if (!PayUtils.payRoutingSuccess(payInfo)
                && !payInfo.getCode().equals(NumConstants.NUM_202)
                && !cmbAggHelper.isCmbAggPay(PayWayConvert.getPayWayEnum(context.getPayChannelList()))) {
            RechargePayBo rechargePayBo = new RechargePayBo();
            rechargePayBo.setSdk(payInfo.getData());
            rechargePayBo.setPayType(context.getPayChannelList().get(0));
            context.getExtendParam().put("output_response", Response.createSuccess(rechargePayBo));
            return;
        }

        int payType = PayUtils.getRealChannelRouting(payInfo,context.getPayChannelList());
        log.info("CompanyRechargePayHandler 获取支付路由后的支付类型 payType={}", payType);
        //是否免密支付
        boolean isNoSecret = PayConstants.NO_SEC_PAY.equals(String.valueOf(payInfo.getCode()));
        //内部传递对象
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret).paymentResp(payInfo).passengerUuid(context.getUserId()).build();
        //支付工厂模式调用
        Response<RechargePayBo> response = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        context.getExtendParam().put("output_response", response);
    }

    /**
     * process return message
     *
     * @param context 上下文
     * @return {@link Response<RechargePayBo>}
     */
    @Override
    public Response<JSONObject> processResponse(PayContext context) {
        Response<?> response = (Response<?>) context.getExtendParam().get("output_response");
        if (!response.isSuccess() || Objects.isNull(response.getData())) {
            return Response.createError(response.getMsg());
        }
        RechargePayBo rechargePayBo = (RechargePayBo) response.getData();
        rechargePayBo.setSettlementId(context.getOrderId());
        super.packageAggAliPayBo(rechargePayBo);
        return Response.createSuccess("获取支付信息成功", JSON.parseObject(JSON.toJSONString(rechargePayBo)));
    }
}
