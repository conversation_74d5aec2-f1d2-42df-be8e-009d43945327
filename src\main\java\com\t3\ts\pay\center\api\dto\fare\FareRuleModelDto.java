package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/5 20:41
 * @description:
 */
@Data
public class FareRuleModelDto implements Serializable {
    /**
     * uuid
     **/
    private String uuid;
    /**
     * 开通区域id open_area_config_uuid
     **/
    private String openAreaConfigUuid;
    /**
     * 业务id
     */
    private String businessUuid;
    /**
     * 城市
     **/

    private String cityUuid;
    /**
     * 车型
     **/
    private String carLevelUuid;
    /**
     * 指定司机费用
     **/
    private BigDecimal assignDriverFare;

    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 起步价
     **/
    private BigDecimal startFare;
    /**
     * 起步里程
     **/
    private BigDecimal startTrip;

    /**
     * 起步时长（分钟）
     **/
    private Integer startDuration;

    /**
     * 超出里程价格(元/公里)
     **/
    private BigDecimal beyondTripFare;

    /**
     * 超出时长费用（元/分钟）
     **/
    private BigDecimal beyondTimeFare;

    /**
     * 免费等待时间（超过这个时间才生效）
     **/
    private Integer freeWaitTime;

    /**
     * 超出等待时长费用（元/分钟）
     **/
    private BigDecimal beyondWaitFare;

    /**
     * 夜间开始时段(22:00-6:00)
     **/
    private String nightTimeStr;

    /**
     * 夜间里程费（元/分钟）
     **/
    private BigDecimal nightMinuteFare;

    /**
     * 夜间里程费（元/公里）
     **/
    private BigDecimal nightTripFare;

    /**
     * 回空里程（超过这个里程才生效）
     **/
    private BigDecimal haulBackTrip;

    /**
     * 回空里程费用（元/公里）
     **/
    private BigDecimal haulBackTripFare;

    /**
     * 状态 （1:启用  0:未启动， -1:失效，-2:已删除）
     **/
    private Integer status;

    /**
     * CURRENT_TIMESTAMP
     **/
    private Date createTime;

    /**
     * 修改时间
     **/
    private Date updateTime;

    /**
     * 修改者
     **/
    private String updater;

    private String creator;
    /**
     * 夜间时段开始时间
     **/
    private String nightStartTimeStr;
    /**
     * 夜间时段结束时间
     **/
    private String nightEndTimeStr;
    /**
     * 运价类型编码
     */
    private String fareType;

    /**
     * 运价类型说明
     */
    private String fareTypeNote;
    /**
     * 车辆等级代码
     */
    private Integer levelType;
    /**
     * 车型名称
     */
    private String levelName;
    /**
     * 城市名
     */
    private String city;
    /**
     * 起步时长（显示在页面上的，预约，实时为分钟数）
     **/
    private Integer startDurationText;

    /**
     * 乘客免费取消时间
     */
    private Integer freeCancelTime;
    /**
     * 司机可迟到时间
     */
    private Integer allowLateTime;

    /**
     * 区域uuid
     **/
    private String areaUuid;
    /**
     * 2 专车 4 快车
     */
    private Integer carType;
    /**
     * 城市编码
     */
    private String cityID;

    private String provinceID;
    /**
     * 省
     */
    private String province;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 订单时效性（1 实时订单、2 预约订单）
     */
    private Integer typeTime;

    /**
     * 当前折扣规则
     */
    private DiscountDTO discount;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 时段费
     */
    private List<TimeIntervalFareDTO> timeFare;
    /**
     * 里程费
     */
    private List<TimeIntervalFareDTO> tripFare;
    /**
     * 基础费用
     */
    private BaseFareDTO baseFare;

    private FirmDiscountDto firmDiscountDto;

    private String routeRuleContent;
    /**
     * 计费版本 0 旧版本 1 新版本
     */
    private Integer ruleType;
    /**
     * 行程费用
     */
    private BigDecimal orderFare;

    private DynamicDiscountDto dynamicDiscount;

    /**
     * DiscountTypeEnum
     * 会员折扣...
     */
    private MemberDiscountDto memberDiscountDto;

    /**
     * 渠道折扣
     */
    private ChannelDiscountDto channelDiscountDto;

    /**
     * 一口价json
     */
    private String onePriceJson;

    /**
     * 当前计费模式  1 普通计费模式  2 一口价计费模式   3  一口价转换后的普通计费模式
     */
    private Integer currentFareModel = 1;

    /**
     * 是否第三方渠道一口价订单
     *
     * @return boolean
     */
    public boolean thirdPartyOnePrice() {

        if (channelDiscountDto != null && channelDiscountDto.discountDetailAfterCreateOrder() != null) {
            return channelDiscountDto.discountDetailAfterCreateOrder().getChannelFareMethod() == 0;
        }
        return false;
    }

    public boolean thirdPartyOtherFarePreset() {

        if (!thirdPartyOnePrice()) {
            return false;
        }

        return channelDiscountDto.discountDetailAfterCreateOrder().getOtherFareMethod() == 1;
    }

    public BigDecimal thirdPartyOnePriceFare() {
        if (channelDiscountDto != null) {
            return channelDiscountDto.getOrderFare();
        }
        return null;
    }

    public void setThirdPartyOnePriceFare(BigDecimal fare) {
        if (channelDiscountDto != null) {
            channelDiscountDto.setOrderFare(fare);
        }
    }

    public MemberDiscountDto memberDiscount() {
        return memberDiscountDto;
    }

    public ChannelDiscountDto channelDiscount() {
        return channelDiscountDto;
    }

    public enum RuleType {
        oldRule(0, "旧版本"),
        newRule(1, "新版本");

        private Integer type;
        private String desc;

        RuleType(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public Integer getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

}
