package com.t3.ts.pay.center.api.dto.chartered;

import lombok.Data;

/**
 * <AUTHOR>
 * @dete 2020.10.26
 */
@Data
public class PayedDto {

    /**
     * 当前支付总费用
     */
    private String amountPayed;
    /**
     * 余额已支付--当前指赠币
     */
    private String balancePay;
    /**
     * 优惠券已支付
     */
    private String couponPay;
    /**
     * 预付款已支付
     */
    private String prPayed;
    /**
     * 礼品卡已抵扣
     */
    private String giftCardPayed;
    /**
     * 积分已抵扣
     */
    private String integralPayed;
    /**
     * 企业支付已经支付的总金额
     */
    private String companyPayedAmount;
    /**
     * 企业优惠券已经抵扣的金额
     */
    private String companyCouponAmount;

}
