package com.t3.ts.pay.center.api.cache;

import com.t3.ts.cache.redis.T3CacheService;
import com.t3.ts.cache.service.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date: 2019-02-19 14:27
 * @des: redis缓存服务类
 */
@Component
public class RedisCacheService {

    protected static Logger logger = LoggerFactory.getLogger(RedisCacheService.class);

    @Autowired
    private RedisService redisService;
    @Autowired
    private T3CacheService t3CacheService;

    /**
     * 设置数据
     *
     * @param key  键 不能为null
     * @param val  值
     * @param time 过期时间数
     * @param unit 过期时间单位
     * @return boolean
     */
    public boolean setValue(String key, Object val, int time, TimeUnit unit) {
        try {
            Boolean aBoolean = redisService.setExpireValue(key, val, (int) unit.toSeconds(time), false);
            return aBoolean;
        } catch (Exception e) {
            logger.error("redisService.setExpireValue Error", e);
        }
        return false;
    }


    /**
     * 获取数据
     *
     * @param key   键 不能为null
     * @param clazz clazz
     * @return 数据
     */
    public Object getValue(String key, Class clazz) {
        if (Objects.isNull(clazz)) {
            clazz = Object.class;
        }

        try {
            Object expireValue = redisService.getExpireValue(key, clazz);
            return expireValue;
        } catch (Exception e) {
            logger.error("redisService.getExpireValue Error", e);
        }
        return null;
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public void del(String key) {
        try {
            redisService.delete(key);
        } catch (Exception e) {
            logger.error("redisService.delete Error", e);
        }
    }
}


