package com.t3.ts.pay.center.api.service.impl.invoice;

import com.t3.ts.invoice.center.service.FrequentlyInvoiceHeaderService;
import com.t3.ts.invoice.center.service.PassengerInvoiceService;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceHeaderVO;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 13:58
 */
public class InvoiceHeaderServiceImplTest {
    @Mock
    FrequentlyInvoiceHeaderService frequentlyInvoiceHeaderService;
    @Mock
    PassengerInvoiceService passengerInvoiceService;
    @Mock
    SwitchConfig switchConfig;
    @Mock
    Logger logger;
    @InjectMocks
    InvoiceHeaderServiceImpl invoiceHeaderServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAddInvoiceHeader() throws Exception {
        Response<?> result = invoiceHeaderServiceImpl.addInvoiceHeader(new AddInvoiceHeaderReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDeleteInvoiceHeader() throws Exception {
        Response<?> result = invoiceHeaderServiceImpl.deleteInvoiceHeader(new DelInvoiceHeaderReq(), "passengerUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryInvoiceHeader() throws Exception {
        Response<?> result = invoiceHeaderServiceImpl.queryInvoiceHeader(new AddInvoiceHeaderReq(), "userUuid");
        Assert.assertEquals(null, result);
    }



    @Test
    public void testSaveOrUpdateInvoiceHeader() throws Exception {
        Response<?> result = invoiceHeaderServiceImpl.saveOrUpdateInvoiceHeader(new AddInvoiceHeaderReq(), "userUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryInvoiceHeader2() throws Exception {
        Response<InvoiceHeaderVO> result = invoiceHeaderServiceImpl.queryInvoiceHeader(Integer.valueOf(0), "userUid");
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
