package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.finance.center.dto.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class AccountSumFareItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("费用项编码列表")
    private List<Integer> fareItemList;
    @ApiModelProperty("日期2022-08-01")
    private Date startDate;
    @ApiModelProperty("日期2022-08-07")
    private Date endDate;
    @ApiModelProperty("汇总金额")
    private BigDecimal sumMoney;


}
