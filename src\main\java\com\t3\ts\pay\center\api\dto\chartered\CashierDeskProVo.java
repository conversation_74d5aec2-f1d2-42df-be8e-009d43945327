package com.t3.ts.pay.center.api.dto.chartered;

import com.t3.ts.pay.center.api.dto.vo.IntegralMsgVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收银台pro
 *
 * <AUTHOR>
 * @date 2020.10.22
 */
@Data
public class CashierDeskProVo {

    /**
     * 支付项
     * 合计费用  0.01元
     * 企业支付  0.01元
     * 动态折扣  0.01元
     * 取消费  0.01元
     * 超时等待费  0.01元
     * 已预付  0.01元
     * 优惠券  0.01元  可以跳转到优惠券列表界面
     * 优惠券已抵扣  -0.01元
     * 余额已支付  -0.01元
     * T币已抵扣  -0.01元
     * 还需支付  0.01元
     */
    private List<PayItem> payItemList;
    /**
     * 支付渠道
     */
    private List<Integer> payTypeList;

    /**
     * 行程id
     */
    private String routePlanUuid;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 车牌
     */
    private String plateNum;
    /**
     * 钱包余额
     */
    private String balanceTotal;

    /**
     * 待支付费用
     */
    private BigDecimal actualFare;
    /**
     * 最终需要支付费用
     */
    private BigDecimal finalFare;

    /**
     * 余额开关
     */
    private Boolean balanceFlag = Boolean.FALSE;
    /**
     * 是否预付支付尾款 1尾款 2普通
     */
    private Integer payDeskType;
    /**
     * 余额提示文案(附加费)
     * 1、不显示：没有附加费
     * 2、没有可用于支付的余额：当前账号余额包含赠送币、礼品卡，不可使用于支付，需通过第三方支付
     * 3、有可用支付的余额：当前账号余额中包含赠送币/礼品卡，余额支付xx.xx元，剩余费用需要通过第三方支付
     */
    private String balanceMsg;
    /**
     * 附加费
     */
    private BigDecimal additionalFee;
    /**
     * 可用余额
     */
    private BigDecimal availableBalance;
    /**
     * 是否积分抵扣
     */
    private Boolean integralDeductFlag;
    /**
     * 可用积分>0：打开开关
     */
    private BigDecimal availableIntegral;
    /**
     * 积分抵扣限制提示
     */
    private IntegralMsgVO integralMsg;
    /**
     * 是否使用券
     */
    private Boolean couponDeductFlag;

}
