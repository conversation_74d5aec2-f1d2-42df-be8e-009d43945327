package com.t3.ts.pay.center.api.dto.fare;

import com.t3.ts.pay.center.api.constants.NumConstants;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/8/12 17:27
 * @description:
 */
@Data
public class DiscountDTO implements Serializable {
    private String uuid;
    /**
     * 起始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 折扣率
     */
    private BigDecimal rate;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 订单时效(1实时,2预约)
     */
    private Integer typeTime;
    /**
     * 抵扣金额
     */
    private BigDecimal discountFare;
    /**
     * 版本号
     */
    private Integer versionNumber;
    /**
     * 折扣上限
     */
    private BigDecimal cellingFare = new BigDecimal(NumConstants.NUM_9999);
}
