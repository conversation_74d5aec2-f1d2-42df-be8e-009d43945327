package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.business.query.paystatus.PayStatusQueryService;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.PayStatusQueryReq;
import com.t3.ts.pay.center.api.dto.SimpleThirdReq;
import com.t3.ts.pay.center.api.service.PayService;
import com.t3.ts.pay.center.api.service.pay.PayContext;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.result.Response;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * Description: 感谢费支付
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/28/0028 13:46
 */
@RestController
@Slf4j
@RequestMapping("/api/payment")
public class SimpleThirdPayApi extends BaseApi {

    @Autowired
    private PayService payService;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private PayStatusQueryService payStatusQueryService;
    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;


    /**
     * 通用支付入口
     *
     * @param req     感谢费支付
     * @param request 参数
     * @return {@link Response<JSONObject>}
     */
    @PostMapping("/v1/simpleThirdPay")
    @ApiOperation(value = "通用支付入口", notes = "通用支付入口")
    public Response<JSONObject> simpleThirdPay(@RequestBody SimpleThirdReq req, HttpServletRequest request) {
        PayContext context = new PayContext();
        context.setUserId(getUserUid(request));
        context.setWxCode(req.getWxCode());
        context.setType(req.getType());
        context.setCode(req.getCode());
        context.setPayChannelList(req.getPayChannelList());
        if(!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(context.getUserId()).getData()){
        context.setAggPayChannelList(cmbAggHelper.getCmbAggPayTypeList(getGrayBuild(request),
                getGrayVersion(request), null, 0, EnumPayOrderType.THANKS.getCode(),
                req.getPayChannelList(),context.getUserId()));
        }
        context.setMessage(req.getMessage());
        context.setPassengerMobile(getUserMobile(request));
        context.setOrderId(req.getOrderId());
        context.setBizType(req.getBizType());

        context.setGrayBuild(HttpRequestUtil.getGrayBuild(request));
        context.setGrayVersion(HttpRequestUtil.getGrayVersion(request));
        context.setTerminal(PayUtils.getTerminal(context.getGrayVersion(),null));

        Map<String, Object> extendParam = new HashMap(NumConstants.NUM_16);
        extendParam.put("quit_url", req.getQuitUrl());
        extendParam.put("payReturnUrl", req.getPayReturnUrl());
        context.setExtendParam(extendParam);
        return payService.doPay(context);
    }

    /**
     * 支付状态查询
     *
     * @param query   查询请求
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/v1/simple/payStatusQuery")
    @ApiOperation(value = "查询订单支付状态", notes = "查询订单支付状态")
    public Response<JSONObject> payStatusQuery(@RequestBody @Valid PayStatusQueryReq query,
                                               HttpServletRequest request) {
        QueryStatusContext context = new QueryStatusContext();
        context.setType(query.getType());
        context.setSettleId(query.getSettleId());
        context.setOrderUuid(query.getOrderUuid());
        return payStatusQueryService.doQuery(context);
    }
}
