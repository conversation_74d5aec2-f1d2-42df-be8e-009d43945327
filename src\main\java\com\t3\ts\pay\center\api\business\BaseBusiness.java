package com.t3.ts.pay.center.api.business;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.t3.ts.gis.position.remote.service.GadAssistantService;
import com.t3.ts.operation.center.dto.amap.req.AMapCityQueryDTO;
import com.t3.ts.operation.center.dto.amap.res.AMapCityDto;
import com.t3.ts.operation.center.service.amap.AmapCityService;
import com.t3.ts.passenger.dto.PassengerInfoDto;
import com.t3.ts.passenger.service.PassengerService;
import com.t3.ts.pay.center.api.cache.ApiCacheKeyUtils;
import com.t3.ts.pay.center.api.cache.RedisCacheService;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.result.Response;
import com.t3.ts.travel.config.operate.dto.CityDto;
import com.t3.ts.travel.config.operate.service.CityService;
import com.t3.ts.utils.StringUtils;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_128;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_64;

/**
 * <AUTHOR>
 * @date 2019-04-25
 */
@Component
public class BaseBusiness {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseBusiness.class);

    @Resource
    private RedisCacheService redisCacheService;
    @DubboReference
    private GadAssistantService coordinateToAddress;
    @DubboReference
    private CityService cityService;
    @DubboReference
    private AmapCityService amapCityService;
    @DubboReference
    private PassengerService passengerService;
    /**
     * 高德城市code缓存（key:320100，value:025）
     */
    private LoadingCache<String, String> amapCityCodeCache =
            CacheBuilder.newBuilder()
                    //设置并发级别为:cpu核心数，并发级别是指可以同时写缓存的线程数
                    .concurrencyLevel(Runtime.getRuntime()
                            .availableProcessors())
                    //设置缓存容器的初始容量为10
                    .initialCapacity(NUM_64)
                    //设置缓存最大容量为100，超过100之后就会按照LRU最近虽少使用算法来移除缓存项
                    .maximumSize(NUM_128)
                    //是否需要统计缓存情况,该操作消耗一定的性能,生产环境应该去除
                    //.recordStats()
                    //设置写缓存后n秒钟过期
                    .expireAfterWrite(1, TimeUnit.DAYS)
                    //设置读写缓存后n秒钟过期,实际很少用到,类似于expireAfterWrite
                    //.expireAfterAccess(17, TimeUnit.SECONDS)
                    //只阻塞当前数据加载线程，其他线程返回旧值
                    //.refreshAfterWrite(13, TimeUnit.SECONDS)
                    //设置缓存的移除通知
                    .removalListener(notification -> LOGGER
                            .info("cityCodeCache removeCache key={} , value={} , cause={} ",
                                    notification.getKey(),
                                    notification.getValue(),
                                    notification.getCause()))
                    //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存
                    .build(
                            new CacheLoader<String, String>() {
                                @Override
                                public String load(
                                        String cityCode) {
                                    try {
                                        // 根据高德CityCode获取转换的城市信息
                                        AMapCityQueryDTO queryDTO = new AMapCityQueryDTO();
                                        queryDTO.setCityCode(
                                                cityCode);
                                        Response<AMapCityDto> amapCityRpcResponse = amapCityService
                                                .viewAMapCity(
                                                        queryDTO);
                                        if (amapCityRpcResponse
                                                .isSuccess()
                                                && !Objects
                                                .isNull(amapCityRpcResponse
                                                        .getData())) {
                                            String amapCityCode = amapCityRpcResponse
                                                    .getData()
                                                    .getAmapCityCode();
                                            return amapCityCode;
                                        }
                                    } catch (Exception e) {
                                        LOGGER.error(
                                                "viewAMapCity aMapCityCode={} error : ",
                                                cityCode, e);
                                    }
                                    return null;
                                }
                            }
                    );
    /**
     * 城市code缓存（key:025，value:320100）
     */
    private LoadingCache<String, AMapCityDto> cityCodeCache =
            CacheBuilder.newBuilder()
                    //设置并发级别为:cpu核心数，并发级别是指可以同时写缓存的线程数
                    .concurrencyLevel(Runtime.getRuntime()
                            .availableProcessors())
                    //设置缓存容器的初始容量为10
                    .initialCapacity(NUM_64)
                    //设置缓存最大容量为100，超过100之后就会按照LRU最近虽少使用算法来移除缓存项
                    .maximumSize(NUM_128)
                    //是否需要统计缓存情况,该操作消耗一定的性能,生产环境应该去除
                    //.recordStats()
                    //设置写缓存后n秒钟过期
                    .expireAfterWrite(1, TimeUnit.DAYS)
                    //设置读写缓存后n秒钟过期,实际很少用到,类似于expireAfterWrite
                    //.expireAfterAccess(17, TimeUnit.SECONDS)
                    //只阻塞当前数据加载线程，其他线程返回旧值
                    //.refreshAfterWrite(13, TimeUnit.SECONDS)
                    //设置缓存的移除通知
                    .removalListener(notification -> LOGGER
                            .info("cityCodeCache removeCache key={} , value={} , cause={} ",
                                    notification.getKey(),
                                    notification.getValue(),
                                    notification.getCause()))
                    //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存
                    .build(
                            new CacheLoader<String, AMapCityDto>() {
                                @Override
                                public AMapCityDto load(
                                        String amapCityCode) {
                                    try {
                                        // 根据高德CityCode获取转换的城市信息
                                        AMapCityQueryDTO queryDTO = new AMapCityQueryDTO();
                                        queryDTO.setAmapCityCode(
                                                amapCityCode);
                                        Response<AMapCityDto> amapCityRpcResponse = amapCityService
                                                .viewAMapCity(
                                                        queryDTO);
                                        if (amapCityRpcResponse
                                                .isSuccess()
                                                && !Objects
                                                .isNull(amapCityRpcResponse
                                                        .getData())) {
                                            return amapCityRpcResponse
                                                    .getData();
                                        }
                                    } catch (Exception e) {
                                        LOGGER.error(
                                                "viewAMapCity aMapCityCode={} error : ",
                                                amapCityCode, e);
                                    }
                                    return null;
                                }
                            }
                    );

    /**
     * 验证码输入错误次数
     *
     * @param mobile 移动
     * @return int
     */
    public int getCodeErrorCount(String mobile) {
        Object countObject = redisCacheService.getValue(ApiCacheKeyUtils.SMS_CODE_ERROR + mobile, Integer.class);
        if (countObject != null) {
            return (int) countObject;
        }
        return 0;
    }

    /**
     * 获取验证码发送次数
     *
     * @param mobile 手机号
     * @return 计算后的发送次数
     */
    public int getSendCountCount(String mobile) {
        Object countObject = redisCacheService
                .getValue(ApiCacheKeyUtils.SMS_CODE_COUNT + DateUtils.getDateFormat() + ":" + mobile, Integer.class);
        if (countObject != null) {
            return (int) countObject;
        }
        return 0;
    }

    /**
     * 根据adCode获取城市信息
     *
     * @param adCode 广告代码
     * @return {@link CityDto}
     */
    public CityDto getCityInfoByAreaCode(String adCode) {
        Response<CityDto> cityDtoResponse = cityService.getCityUuidByAdCode(adCode);
        if (BooleanUtil.isFalse(cityDtoResponse.isSuccess()) || ObjectUtil.isNull(cityDtoResponse.getData())) {
            return null;
        }
        return cityDtoResponse.getData();
    }


    /**
     * 同理得到城市代码缓存
     *
     * @return {@link LoadingCache <String, String>}
     */
    public LoadingCache<String, String> getAmapCityCodeCache() {
        return amapCityCodeCache;
    }

    /**
     * 设置城市代码缓存
     *
     * @param amapCityCodeCache 同理城市代码缓存
     */
    public void setAmapCityCodeCache(LoadingCache<String, String> amapCityCodeCache) {
        this.amapCityCodeCache = amapCityCodeCache;
    }

    /**
     * 得到城市代码缓存
     *
     * @return {@link LoadingCache <String,  AMapCityDto >}
     */
    public LoadingCache<String, AMapCityDto> getCityCodeCache() {
        return cityCodeCache;
    }

    /**
     * 设置城市代码缓存
     *
     * @param cityCodeCache 城市代码缓存
     */
    public void setCityCodeCache(LoadingCache<String, AMapCityDto> cityCodeCache) {
        this.cityCodeCache = cityCodeCache;
    }

    /**
     * 根据用户token获取用户信息
     *
     * @param userToken token
     * @return 用户信息
     */
    public PassengerInfoDto getPassengerInfoByToken(String userToken) {
        PassengerInfoDto passengerinfo = null;
        if (StringUtils.isNotBlank(userToken)) {
            Response<PassengerInfoDto> passenger = passengerService.findPassengerByToken(userToken);
            if (BooleanUtil.isTrue(passenger.isSuccess()) && !Objects.isNull(passenger.getData())) {
                passengerinfo = passenger.getData();
            }
        }
        return passengerinfo;
    }
}
