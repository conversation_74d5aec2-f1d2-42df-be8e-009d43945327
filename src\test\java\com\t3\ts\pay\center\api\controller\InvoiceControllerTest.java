package com.t3.ts.pay.center.api.controller;

import com.t3.ts.invoice.center.dto.PassengerInvoiceDto;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.bill.InvoiceSendReq;
import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceBillingDetail;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceForm;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceHistoryReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceQueryPageDto;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRelevantFormV3;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutDetailReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceHeaderVO;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceResVo;
import com.t3.ts.pay.center.api.service.invoice.InvoiceHeaderService;
import com.t3.ts.pay.center.api.service.invoice.MallInvoiceService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:00
 */
public class InvoiceControllerTest {
    @Mock
    InvoiceV3Business invoiceBusiness;
    @Mock
    InvoiceHeaderService invoiceHeaderService;
    @Mock
    MallInvoiceService mallInvoiceService;
    @Mock
    Logger log;
    @InjectMocks
    InvoiceController invoiceController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryMallBilling() throws Exception {
        when(mallInvoiceService.queryMallBilling(any(), anyString())).thenReturn(null);

        Response<PageResult<MallInvoiceVo>> result = invoiceController.queryMallBilling(new InvoiceRoutReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testSubmitMallBill() throws Exception {
        when(mallInvoiceService.submitMallBill(any(), anyString())).thenReturn(null);

        Response result = invoiceController.submitMallBill(new InvoiceMallSubmitReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryPageForCallCenter() throws Exception {
        when(invoiceBusiness.queryPageForCallCenter(any())).thenReturn(null);

        Response result = invoiceController.queryPageForCallCenter(new InvoiceQueryPageDto(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAddFrequentlyUsedHeaders() throws Exception {
        when(invoiceHeaderService.addInvoiceHeader(any())).thenReturn(null);

        Response<?> result = invoiceController.addFrequentlyUsedHeaders(new AddInvoiceHeaderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testUpdateFrequentlyUsedHeaders() throws Exception {
        when(invoiceHeaderService.saveOrUpdateInvoiceHeader(any(), anyString())).thenReturn(null);

        Response<?> result = invoiceController.updateFrequentlyUsedHeaders(new AddInvoiceHeaderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDeleteFrequentlyUsedHeaders() throws Exception {
        when(invoiceHeaderService.deleteInvoiceHeader(any(), anyString())).thenReturn(null);

        Response result = invoiceController.deleteFrequentlyUsedHeaders(new DelInvoiceHeaderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryFrequentlyUsedHeaders() throws Exception {

        Response<?> result = invoiceController.queryFrequentlyUsedHeaders(new AddInvoiceHeaderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryInvoiceHeader() throws Exception {
        when(invoiceHeaderService.queryInvoiceHeader(anyInt(), anyString())).thenReturn(null);

        Response<InvoiceHeaderVO> result = invoiceController.queryInvoiceHeader(new InvoiceHeaderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testInvoiceInfo() throws Exception {
        when(invoiceBusiness.getInvoiceInfoAll(any())).thenReturn(null);

        Response<?> result = invoiceController.invoiceInfo(new InvoiceReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBillingByRoute() throws Exception {
        when(invoiceBusiness.billingByRoute(any(), anyString())).thenReturn(null);

        Response<?> result = invoiceController.billingByRoute(new InvoiceRoutReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBillingDetailByRoute() throws Exception {
        when(invoiceBusiness.billingDetailByRoute(any(), anyString())).thenReturn(null);

        Response<InvoiceBillingDetail> result =
                invoiceController.billingDetailByRoute(new InvoiceRoutDetailReq(), null);
        Assert.assertEquals(null, result);
    }



    @Test
    public void testSendInvoice() throws Exception {
        when(invoiceBusiness.sendInvoiceAndRouteList(any())).thenReturn(null);

        Response result = invoiceController.sendInvoice(new InvoiceSendReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testSendRouteList() throws Exception {
        when(invoiceBusiness.sendRouteList(any(), anyString())).thenReturn(null);

        Response result = invoiceController.sendRouteList(new InvoiceSendReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryInvoiceHistoryList() throws Exception {
        when(invoiceBusiness.queryInvoiceHistoryList(any(), anyString())).thenReturn(null);

        Response<PageResult<PassengerInvoiceDto>> result =
                invoiceController.queryInvoiceHistoryList(new InvoiceHistoryReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBillingByRoute2() throws Exception {
        when(invoiceBusiness.invoiceCancellation(any())).thenReturn(null);

        Response result = invoiceController.billingByRoute(new InvoiceForm());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testInvoiceBilling() throws Exception {
        when(invoiceBusiness.checkOrderUuid(anyString(), any(), any(), anyString())).thenReturn(null);
        when(invoiceBusiness.invoiceBilling(any(), anyString())).thenReturn(null);

        Response<InvoiceResVo> result = invoiceController.invoiceBilling(new InvoiceRelevantFormV3(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetUserUid() throws Exception {
        String result = invoiceController.getUserUid(null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetDeviceFingerPrintToken() throws Exception {
        String result = InvoiceController.getDeviceFingerPrintToken(null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
