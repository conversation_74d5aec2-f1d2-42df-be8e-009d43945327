package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.common.monitor.MonitorIndexEnum;
import com.t3.ts.pay.common.monitor.MonitorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: chent
 * @Date: 2023/7/7 16:13
 */
@Component
@Slf4j
public class ErrorMsgAdapterUtil {

    /**
     * 收银台异常告警
     * @param req req
     */
    public void monitorPayDeskBusiness(PayTradeReq req) {
        log.info("上传【收银台】埋点错误信息:{}", JSONObject.toJSONString(req.getOrderUuid()));
        MonitorUtils.count("0001", "收银台异常", MonitorIndexEnum.PAY_CENTER_API);
    }
}
