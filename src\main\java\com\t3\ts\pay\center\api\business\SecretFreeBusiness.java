package com.t3.ts.pay.center.api.business;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.dto.AccountSignChannelDto;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.operation.center.dto.amap.res.AMapCityDto;
import com.t3.ts.passenger.center.dto.req.secretfree.SfGuidanceReq;
import com.t3.ts.passenger.center.dto.res.secretfree.SfGuidanceVo;
import com.t3.ts.passenger.center.service.secretfree.SfGuidanceConfigService;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.config.valueconfig.WechatPayPointsConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.common.PassengerPortrait;
import com.t3.ts.pay.center.api.dto.common.SfGuidance;
import com.t3.ts.pay.center.api.dto.vo.DataListVo;
import com.t3.ts.pay.center.api.dto.vo.SecretPayFreeVo;
import com.t3.ts.pay.center.api.rest.OperationRest;
import com.t3.ts.pay.common.constant.PayChannelEnum;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.common.http.CityCode;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * @Author: YJ
 * @Date: 2019/4/29 21:00
 * @Description: 乘客获取、关闭免密支付逻辑处理
 */
@Component
@Slf4j
public class SecretFreeBusiness {

    @Resource
    private BaseBusiness baseBusiness;
    @DubboReference
    private AccountSignService accountSignService;
    @Resource
    private OperationRest operationRest;
    @DubboReference
    private SfGuidanceConfigService sfGuidanceConfigService;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private WechatPayPointsConfig wechatPayPointsConfig;

    private static final String DEF_VERSION = "570";

    /**
     * 查询免密引导详情
     *
     * @param sfGuidance 参数
     * @return 出参
     */
    public Response<SfGuidanceVo> secretFreeGuidanceDetail(SfGuidance sfGuidance) {
        try {
            String cityCode = sfGuidance.getCityCode();
            String adCode = sfGuidance.getAdCode();
            String productLine = sfGuidance.getProductLine();
            String triggerNode = sfGuidance.getTriggerNode();
            // 我也不知道啥意思，预付款没有产品线，所以不校验了
            boolean flag = (StringUtils.isBlank(cityCode) && StringUtils.isBlank(adCode))
                    || (Boolean.FALSE.equals(sfGuidance.getPrePay()) && StringUtils.isBlank(productLine))
                    || StringUtils.isBlank(triggerNode);
            if (flag) {
                return Response.createSuccess("查询成功", new SfGuidanceVo());
            }
            AccountSignParamDto queryDto = new AccountSignParamDto();
            queryDto.setUserId(sfGuidance.getPassengerId());
            Response<List<AccountSignChannelDto>> response = accountSignService.getSignChannelStatus(queryDto);
            if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                return Response.createSuccess("查询成功", new SfGuidanceVo());
            }
            if (atLeastOneSign(response.getData())) {
                return Response.createSuccess("查询成功", new SfGuidanceVo());
            }

            if (Boolean.TRUE.equals(sfGuidance.getPrePay())) {
                // 预付款校验到渠道就可以了，不用校验是否首单
                SfGuidanceVo sfGuidanceVo = new SfGuidanceVo();
                sfGuidanceVo.setShowFlag(wechatPayPointsConfig.getPrePayEnabled());
                return Response.createSuccess("查询成功", sfGuidanceVo);
            }
            if (StringUtils.isNotBlank(sfGuidance.getAdCode())) {
                String convertCode = CityCode.convertCode(sfGuidance.getAdCode());
                sfGuidance.setCityCode(convertCode);
            } else {
                AMapCityDto aMapCityDto = baseBusiness.getCityCodeCache().get(sfGuidance.getCityCode());
                if (Objects.isNull(aMapCityDto)) {
                    return Response.createSuccess("查询成功", new SfGuidanceVo());
                }
                log.info("aMapCityDto:{}", aMapCityDto);
                sfGuidance.setCityCode(aMapCityDto.getCityCode());
            }

            // 判断是否已完成首单
            if (justPortrait(sfGuidance.getPhone())) {
                SfGuidanceReq sfGuidanceReq = BeanUtils.propertiesCopy(sfGuidance, SfGuidanceReq.class);
                return sfGuidanceConfigService.querySfGuidanceConfig(sfGuidanceReq);
            }

        } catch (Exception e) {
            log.error("secretFreeGuidanceDetail error", e);
        }
        return Response.createSuccess("查询成功", new SfGuidanceVo());
    }

    /**
     * 判断是否至少有一条签约成功记录
     *
     * @param list 签约记录
     * @return boolean
     */
    private boolean atLeastOneSign(List<AccountSignChannelDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (AccountSignChannelDto dto : list) {
            if (AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode() == dto.getDensityFree()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 查询是否完成首单
     *
     * @param phone 乘客手机号
     * @return 出参
     */
    private boolean justPortrait(String phone) {
        // 查询是否完成首单
        try {
            Response<PassengerPortrait> passengerPortraitResponse =
                    operationRest.passengerPortrait(phone);
            if (passengerPortraitResponse.isSuccess() && Objects.nonNull(passengerPortraitResponse.getData())) {
                PassengerPortrait passengerPortrait = passengerPortraitResponse.getData();
                String beFincntTotal = passengerPortrait.getBeFincntTotal();
                if (StringUtils.isNotBlank(beFincntTotal) && !NumConstants.STR_0.equals(beFincntTotal)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("justPortrait error", e);
        }
        return false;
    }

    /**
     * 查询渠道开通信息
     *
     * @param dto       dto
     * @param grayBuild grayBuild
     * @return {@link Response<DataListVo<SecretPayFreeVo>>}
     */
    public Response<DataListVo<SecretPayFreeVo>> getAllSignInfo(AccountSignParamDto dto, String grayBuild) {
        try {
            log.info("查询签约信息.request:{}.", JSON.toJSONString(dto));
            Response<List<AccountSignChannelDto>> response = accountSignService.getAllSignInfo(dto);
            if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                return Response.createError("查询余额签约失败");
            }

            ArrayList<SecretPayFreeVo> secretFreeVos = Lists.newArrayList();

            ArrayList<SecretPayFreeVo> secretFreeVosTemp1 = Lists.newArrayList();
            ArrayList<SecretPayFreeVo> secretFreeVosTemp2 = Lists.newArrayList();
            ArrayList<SecretPayFreeVo> secretFreeVosTemp3 = Lists.newArrayList();

            for (AccountSignChannelDto datum : response.getData()) {
                if (PayChannelEnum.ZHIMA.getCode() == datum.getPayChannel()) {
                    // 低于指定版本不显示
                    if (!cmbAggHelper.compareGrayBuild(grayBuild, DEF_VERSION)) {
                        continue;
                    }
                    // 展示开关关闭  用户未签约 不展示
                    if (!switchConfig.getZhimaShow() && NumConstants.NUM_1 != datum.getDensityFree()) {
                        continue;
                    }
                }
                if (PayChannelEnum.ZHIMA_MALL.getCode() == datum.getPayChannel()) {
                    // 低于指定版本不显示
                    if (!cmbAggHelper.compareGrayBuild(grayBuild, DEF_VERSION)) {
                        continue;
                    }
                    // 展示开关关闭  用户未签约 不展示
                    if (!switchConfig.getZhimaMallShow() && NumConstants.NUM_1 != datum.getDensityFree()) {
                        continue;
                    }
                }
                if (NumConstants.NUM_37 == datum.getPayChannel() && !switchConfig.getIntegral()) {
                    //T币隐藏 不展示
                    continue;
                }
                SecretPayFreeVo payFreeVo = new SecretPayFreeVo();
                payFreeVo.setPayChannel(datum.getPayChannel());
                payFreeVo.setStatus(datum.getDensityFree() == 1 ? 1 : 0);
                payFreeVo.setFisrtFlag(datum.getPriority() == 1);
                payFreeVo.setSort(getSortValue(datum.getPayChannel()));
                if (payFreeVo.isFisrtFlag()) {
                    secretFreeVosTemp1.add(payFreeVo);
                } else if (payFreeVo.getPayChannel() == NumConstants.NUM_4
                        || payFreeVo.getPayChannel() == NumConstants.NUM_37) {
                    secretFreeVosTemp3.add(payFreeVo);
                } else if (Integer.valueOf(1).equals(payFreeVo.getStatus())) {
                    secretFreeVosTemp2.add(payFreeVo);
                } else {
                    secretFreeVosTemp3.add(payFreeVo);
                }
            }
            secretFreeVosTemp2.sort(Comparator.comparing(SecretPayFreeVo::getSort));
            secretFreeVosTemp3.sort(Comparator.comparing(SecretPayFreeVo::getSort));
            //1、先按照乘客设置优先级排序
            //2、再按照渠道默认顺序排序
            secretFreeVos.addAll(secretFreeVosTemp1);
            secretFreeVos.addAll(secretFreeVosTemp2);
            secretFreeVos.addAll(secretFreeVosTemp3);
            reSort(secretFreeVos);
            return Response.createSuccess("查询成功", new DataListVo<>(secretFreeVos));
        } catch (Exception e) {
            log.error("查询余额签约.cause:{}", ExceptionUtils.getStackTrace(e));
            return Response.createError("查询余额签约失败");
        }
    }

    /**
     * 重新排序
     *
     * @param secretFreeVos 秘密免费vos
     */
    private void reSort(ArrayList<SecretPayFreeVo> secretFreeVos) {
        for (int i = 0, size = secretFreeVos.size(); i < size; i++) {
            secretFreeVos.get(i).setSort(i);
        }
    }

    /**
     * 缓存频道排序
     */
    private static final Map<Integer, Integer> CACHE_CHANNEL_SORT = new HashMap<>(CommonNumConst.NUM_16);

    static {
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_1, NumConstants.NUM_2);
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_2, NumConstants.NUM_5);
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_3, NumConstants.NUM_4);
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_8, NumConstants.NUM_3);
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_51, NumConstants.NUM_6);
        CACHE_CHANNEL_SORT.put(NumConstants.NUM_4, NumConstants.NUM_7);
    }

    /**
     * 获得的渠道顺序
     *
     * @param key 关键
     * @return int
     */
    private int getSortValue(Integer key) {
        Integer integer = CACHE_CHANNEL_SORT.get(key);
        return integer == null ? NumConstants.NUM_8_INT : integer;
    }

}
