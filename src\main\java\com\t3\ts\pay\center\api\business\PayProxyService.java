package com.t3.ts.pay.center.api.business;

import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.result.Response;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Description: 支付本地代理类，做统一处理
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/8/19/0019 14:32
 */
@Component
public class PayProxyService {
    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;

    /**
     * 代理支付方法
     *
     * @param paymentDto 付款dto
     * @return {@link Response<String>}
     */
    public Response<String> pay(PaymentDto paymentDto) {
        paymentDto.getExtendParams().put("riskDeviceToken", RequestContextHelper.getDeviceFingerPrintToken());
        paymentDto.getExtendParams().put("remoteIp", RequestContextHelper.getRemoteIp());
        paymentDto.getExtendParams().put("loginUserId", RequestContextHelper.getPassengerUuid());
        return unifiedPaymentFacade.pay(paymentDto);
    }
}
