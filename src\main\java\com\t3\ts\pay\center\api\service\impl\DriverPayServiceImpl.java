package com.t3.ts.pay.center.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.PayAccountBookDto;
import com.t3.ts.account.center.dto.PayAccountDto;
import com.t3.ts.account.center.dto.book.AccountBookFreezeDto;
import com.t3.ts.account.center.service.AccountBookService;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.channelmgr.center.constants.TerminalEnum;
import com.t3.ts.channelmgr.center.dto.PayChannelReqDto;
import com.t3.ts.channelmgr.center.dto.PayChannelResDto;
import com.t3.ts.channelmgr.center.service.PayChannelTakeOverService;
import com.t3.ts.context.ContextUtil;
import com.t3.ts.pay.center.api.business.MallPaymentBusiness;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.controller.BaseApi;
import com.t3.ts.pay.center.api.dto.ChargeOrderDto;
import com.t3.ts.pay.center.api.dto.DriverChannelListResVo;
import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskContext;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskDto;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskVo;
import com.t3.ts.pay.center.api.dto.vo.BookPayWayVo;
import com.t3.ts.pay.center.api.dto.vo.PayWayVo;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.rest.mall.RechargeCheckResp;
import com.t3.ts.pay.center.api.service.DriverPayService;
import com.t3.ts.pay.center.api.util.AppVersionCompareUtil;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.common.exception.BizExceptionUtil;
import com.t3.ts.pay.common.util.MoneyUtils;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.DriverFareDetail;
import com.t3.ts.pay.remote.dto.DriverPileFareDetail;
import com.t3.ts.pay.remote.dto.GeneralSettlePayDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.SettleType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 司机支付相关
 * 支付和收银台
 *
 * <AUTHOR>
 */

@Slf4j
@Service
public class DriverPayServiceImpl implements DriverPayService {


    /**
     * 配置的是否是全国
     */
    private static final String ALL_CITY = "0";

    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQueryFacade;
    @Autowired
    private MarketingRest marketingRest;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private MallPaymentBusiness mallPaymentBusiness;
    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @DubboReference
    private UnifiedService unifiedService;
    @DubboReference
    private PayChannelTakeOverService payChannelTakeOverService;
    @DubboReference
    private AccountBookService accountBookService;
    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;


    @Override
    public Response deskInfo(DriverPayDeskDto dto, HttpServletRequest request) {
        try {
            //检查入参
            checkParams(dto);
            //生成上下文
            DriverPayDeskContext context = new DriverPayDeskContext();
            context.setDriverPayDeskDto(dto);
            context.setDriverPayDeskVo(new DriverPayDeskVo());
            context.setUserId(ContextUtil.getUserId());
            context.setGrayVersion(HttpRequestUtil.getGrayVersion(request));

            DriverPayDeskVo driverPayDeskVo = context.getDriverPayDeskVo();
            //设置订单ID
            driverPayDeskVo.setOrderUuid(dto.getOrderUuid());
            //设置用户ID
            driverPayDeskVo.setUserId(context.getUserId());

            //查支付类型和结算类型配置关系，填充避免入参不足
            setGeneralSettlePayByBizType(dto);

            //判断业务类型查询业务单或者结算单，订单金额、保险费等
            setOrderFareByPayOrderType(dto, driverPayDeskVo, context.getUserId());

            //查询支付单，获得待支付金额
            setRemainPay(context);

            //设置可支付账本信息
            setBookPayWayVoList(context);

            //设置三方支付渠道（渠道和渠道优惠）
            driverPayDeskVo.setPayWayVoList(getPayWayVoList(dto, request));

            //支付的各种提示（如：只选了余额，但余额不足，则提示：钱包抵扣后还需支付10元，请再选择一种支付方式）
            setThirdPayTypeToastMsg(context);

            //设置用户协议（钱包代扣协议）
            driverPayDeskVo.setNeedProtocol(getNeedProtocol(context));

            //判断实付可以继续支付（如：只选了余额，但余额不足，则不能继续支付）
            driverPayDeskVo.setCanPay(getCanPay(context));

            return Response.createSuccess(driverPayDeskVo);
        } catch (BizException e) {
            log.error("查询司机收银台失败业务异常 dto={}", JSON.toJSONString(dto), e);
            return e.getRes();
        } catch (Exception e) {
            log.error("查询司机收银台失败未知异常 dto={}", JSON.toJSONString(dto), e);
        }
        return Response.createError();
    }

    private void checkParams(DriverPayDeskDto dto) {
        if (StringUtils.isEmpty(dto.getOrderUuid())) {
            BizExceptionUtil.create("订单ID不能为空");
        }
        if (dto.getPayOrderType() == null && dto.getSettlementBizType() == null) {
            BizExceptionUtil.create("支付单类型不能为空");
        }
    }

    /**
     * 判断当前支付配置是否允许继续支付
     * 
     * @return boolean true:允许支付 false:不允许支付
     *         当前逻辑主要校验账本余额与三方支付渠道的组合情况
     */
    private boolean getCanPay(DriverPayDeskContext context) {
        if (context.isChooseBookPay()
                && context.getChooseThirdPayChannel() == null
                && context.getThirdPayFare().compareTo(BigDecimal.ZERO) > 0
                ) {
            // 勾选余额，未勾选三方，但有需要三方支付的金额时候 false
            return false;
        }
        //其他不能支付继续的场景继续在这里添加

        //默认是true
        return true;
    }

    private boolean getNeedProtocol(DriverPayDeskContext context) {
        return context.isChooseBookPay();
    }

    /**
     * 获取三方支付渠道列表
     * 
     * @param dto 支付请求参数
     * @param request HTTP请求对象
     * @return PayWayVo 支付渠道信息列表
     *         当查询失败或无数据时返回null
     */
    private List<PayWayVo> getPayWayVoList(DriverPayDeskDto dto, HttpServletRequest request) {
        DriverChannelListResVo driverChannelListResVo = queryDriverChannelList(dto, request);
        if (driverChannelListResVo != null && CollectionUtils.isNotEmpty(driverChannelListResVo.getPayWayVoList())) {
            return driverChannelListResVo.getPayWayVoList();
        }
        return null;
    }

    private void setThirdPayTypeToastMsg(DriverPayDeskContext context) {
        DriverPayDeskDto dto = context.getDriverPayDeskDto();
        DriverPayDeskVo driverPayDeskVo = context.getDriverPayDeskVo();
        if (CollectionUtils.isEmpty(dto.getPayChannels())) {
            if(CollectionUtils.isNotEmpty(driverPayDeskVo.getBookPayWayVoList())
                && driverPayDeskVo.getBookPayWayVoList().get(0).isChoose()){
                Integer payChannel = driverPayDeskVo.getBookPayWayVoList().get(0).getPayChannel();
                dto.setPayChannels(Arrays.asList(payChannel));
            } else {
                return;
            }
        }
        //处理预付款/充电支付场景，构建支付渠道映射表
        if (dto.getSettlementBizType() != null
                && (dto.getSettlementBizType().equals(BizType.DRIVER_PRE_PAY.getType())
                || dto.getSettlementBizType().equals(BizType.DRIVER_CHARGING_PAY.getType()))) {
            Map<Integer, BookPayWayVo> bookPayWayVoMap = driverPayDeskVo.getBookPayWayVoList().stream()
                    .collect(Collectors.toMap(BookPayWayVo::getPayChannel, k1 -> k1, (k1, k2) -> k1));
            Map<Integer, PayWayVo> payWayVoMap = driverPayDeskVo.getPayWayVoList().stream()
                    .collect(Collectors.toMap(PayWayVo::getPayChannel, k1 -> k1, (k1, k2) -> k1));
            BigDecimal bookCanUseBalance = BigDecimal.ZERO;


            // 查冻结记录
            AccountBookFreezeDto accountBookFreezeDto = new AccountBookFreezeDto();
            accountBookFreezeDto.setUserId(driverPayDeskVo.getUserId());
            accountBookFreezeDto.setSceneUuid(driverPayDeskVo.getOrderUuid());
            accountBookFreezeDto.setBookType(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType());
            Response<PageResult<AccountBookFreezeDto>> accountBookFreeze = accountBookService.getAccountBookFreeze(accountBookFreezeDto);
            long totalFreezeFare=accountBookFreeze.getData().getList().stream().mapToLong(AccountBookFreezeDto::getFreezeAmount).sum();

            for (Integer payChannel : dto.getPayChannels()) {
                if (payWayVoMap.containsKey(payChannel)) {
                    context.setChooseThirdPayChannel(payWayVoMap.get(payChannel));
                }
                BookPayWayVo bookPayWayVo = bookPayWayVoMap.get(payChannel);
                if (bookPayWayVo == null) {
                    continue;
                }
                context.setChooseBookPay(true);
                bookCanUseBalance = bookCanUseBalance.add(bookPayWayVo.getAvailableBalance());
                //如果是充电卡要加上已经被冻结的充电卡金额
                if(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType() == bookPayWayVo.getBookType()){
                    bookCanUseBalance = bookCanUseBalance.add(MoneyUtils.fenToYuan(totalFreezeFare));
                }
            }
            if (!context.isChooseBookPay()) {
                //没有选择余额时候，不返回描述，前端会默认勾选三方渠道
                return;
            }
            BigDecimal bookCanUseBalance2 = driverPayDeskVo.getRemainPay();
            //如果是 预付款 充电卡可代付金额剔除充电保险费， 如果预付款已经支付，已经抵扣保险 ，充电卡用于支付 剩余充电订单
            if(dto.getSettlementBizType().equals(BizType.DRIVER_PRE_PAY.getType())){
                bookCanUseBalance2 = bookCanUseBalance2.subtract(driverPayDeskVo.getInsureFare());
            }
            bookCanUseBalance = bookCanUseBalance2.min(bookCanUseBalance);
            //计算剩余需第三方支付金额
            BigDecimal thirdRemainPay = driverPayDeskVo.getRemainPay().subtract(bookCanUseBalance);
            context.setBookPayFare(bookCanUseBalance);
            context.setThirdPayFare(thirdRemainPay);
            String thirdPayTypeToastMsg = "";
            if (thirdRemainPay.compareTo(BigDecimal.ZERO) <= 0) {
                //充电卡足够
                thirdPayTypeToastMsg = "";
            } else if (context.getChooseThirdPayChannel() == null && thirdRemainPay.compareTo(BigDecimal.ZERO) > 0) {
                //充电卡不足,未选三方支付时
                thirdPayTypeToastMsg = String.format(switchConfig.getThirdPayTypeToastMsgModel2(), thirdRemainPay.toPlainString());
            } else if (context.getChooseThirdPayChannel() != null && thirdRemainPay.compareTo(BigDecimal.ZERO) > 0) {
                //充电卡不足，选了三方支付时
                thirdPayTypeToastMsg = String.format(switchConfig.getThirdPayTypeToastMsgModel1(), thirdRemainPay.toPlainString());
            }

            driverPayDeskVo.setThirdPayTypeToastMsg(thirdPayTypeToastMsg);
        }

        //商城业务相关支付提示
        if (dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_FREE_COMMISSION_CARD_V2_PAY.getCode())
                || dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_CARD_PAY.getCode())
                || dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_COUPON_PAY.getCode())) {
            Map<Integer, BookPayWayVo> bookPayWayVoMap = driverPayDeskVo.getBookPayWayVoList().stream()
                    .collect(Collectors.toMap(BookPayWayVo::getPayChannel, k1 -> k1, (k1, k2) -> k1));
            Map<Integer, PayWayVo> payWayVoMap = driverPayDeskVo.getPayWayVoList().stream()
                    .collect(Collectors.toMap(PayWayVo::getPayChannel, k1 -> k1, (k1, k2) -> k1));
            //最后还是没有选择余额的时候，设置无提示，前端默认会勾选一个三方渠道
            if (CollectionUtils.isEmpty(dto.getPayChannels())) {
                return;
            }
            //账本可支付总金额
            BigDecimal bookCanUseBalance = BigDecimal.ZERO;
            for (Integer payChannel : dto.getPayChannels()) {
                if (payWayVoMap.containsKey(payChannel)) {
                    context.setChooseThirdPayChannel(payWayVoMap.get(payChannel));
                }
                BookPayWayVo bookPayWayVo = bookPayWayVoMap.get(payChannel);
                if (bookPayWayVo == null) {
                    continue;
                }
                context.setChooseBookPay(true);
                bookCanUseBalance = bookCanUseBalance.add(bookPayWayVo.getAvailableBalance());
            }
            if (!context.isChooseBookPay()) {
                //没有选择余额时候，不返回描述，前端会默认勾选三方渠道
                return;
            }
            //待支付金额中可用账本支付的金额
            BigDecimal bookCanUseBalance2 = driverPayDeskVo.getRemainPay();
            bookCanUseBalance = bookCanUseBalance2.min(bookCanUseBalance);
            //计算剩余需第三方支付金额
            BigDecimal thirdRemainPay = driverPayDeskVo.getRemainPay().subtract(bookCanUseBalance);
            context.setBookPayFare(bookCanUseBalance);
            context.setThirdPayFare(thirdRemainPay);
            String thirdPayTypeToastMsg = "";
            //余额足够
            if (thirdRemainPay.compareTo(BigDecimal.ZERO) <= 0) {
                thirdPayTypeToastMsg = String.format(switchConfig.getThirdPayTypeToastMsgModel3(),
                        bookCanUseBalance.toPlainString());
            } else if (context.getChooseThirdPayChannel() == null && thirdRemainPay.compareTo(BigDecimal.ZERO) > 0) {
                //余额不足,未选三方支付时
                thirdPayTypeToastMsg = String.format(switchConfig.getThirdPayTypeToastMsgModel4(),
                        thirdRemainPay.toPlainString());
            } else if (context.getChooseThirdPayChannel() != null && thirdRemainPay.compareTo(BigDecimal.ZERO) > 0) {
                //余额不足，选了三方支付时
                thirdPayTypeToastMsg = String.format(switchConfig.getThirdPayTypeToastMsgModel5(),
                        bookCanUseBalance.toPlainString(),
                        context.getChooseThirdPayChannel().getPayName(),
                        thirdRemainPay.toPlainString());
            }
            driverPayDeskVo.setThirdPayTypeToastMsg(thirdPayTypeToastMsg);
        }

    }

    /**
     * 设置通用结算支付配置
     * 
     * @param dto 支付请求参数
     */
    private void setGeneralSettlePayByBizType(DriverPayDeskDto dto) {
        GeneralSettlePayDto generalSettlePayDto = getGeneralSettlePayDto(dto.getSettlementBizType(), dto.getPayOrderType());
        if (generalSettlePayDto != null) {
            dto.setSettlementBizType(generalSettlePayDto.getBizType());
            dto.setPayOrderType(generalSettlePayDto.getPayOrderType());
        }
    }

    @Override
    public GeneralSettlePayDto getGeneralSettlePayDto(Integer settlementBizType, Integer payOrderType) {
        if(settlementBizType == null && payOrderType == null){
            BizExceptionUtil.create("参数异常");
        }
        GeneralSettlePayDto generalSettlePayDto = null;
        if (settlementBizType == null) {
            Response<GeneralSettlePayDto> configResp = unifiedService.getGeneralSettlePayByBizType(payOrderType,
                    CommonNumConst.NUM_2);
            generalSettlePayDto = configResp.getData();
        }else if (payOrderType == null) {
            Response<GeneralSettlePayDto> configResp = unifiedService.getGeneralSettlePayByBizType(settlementBizType,
                    CommonNumConst.NUM_1);
            generalSettlePayDto = configResp.getData();
        }
        return generalSettlePayDto;
    }

    /**
     * 设置可支付账本信息
     * 
     * @param context context
     */
    private void setBookPayWayVoList(DriverPayDeskContext context) {
        DriverPayDeskDto driverPayDeskDto = context.getDriverPayDeskDto();
        DriverPayDeskVo driverPayDeskVo = context.getDriverPayDeskVo();
        Response<PayAccountDto> acctResp = unifiedAccountFacade.getAccountBalance(context.getUserId(), PayAccountTypeEnum.DRIVER_TYPE);
        if (acctResp == null || !acctResp.isSuccess() || acctResp.getData() == null) {
            driverPayDeskVo.setBookPayWayVoList(new ArrayList<>());
            return;
        }
        List<BookPayWayVo> bookPayWayVoList = new ArrayList<>();
        List<PayAccountBookDto> books = acctResp.getData().getBooks();
        for (PayAccountBookDto book : books) {
            if (book.getBookType().equals(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType())
                    && book.getAvailableBalance() > 0
                    && driverPayDeskDto.getSettlementBizType() != null
                    && (driverPayDeskDto.getSettlementBizType().equals(BizType.DRIVER_PRE_PAY.getType())
                    || driverPayDeskDto.getSettlementBizType().equals(BizType.DRIVER_CHARGING_PAY.getType()))) {
                BookPayWayVo bookPayWayVo = new BookPayWayVo();
                bookPayWayVo.setIcon(switchConfig.getChargingElectricityCardIcon());
                bookPayWayVo.setBookType(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType());
                bookPayWayVo.setAvailableBalance(MoneyUtils.fenToYuan(book.getAvailableBalance()));
                bookPayWayVo.setBookDesc(String.format(switchConfig.getChargingElectricityCardBookDesc(),
                        bookPayWayVo.getAvailableBalance().toPlainString()));
                //账户余额可用性检查：查询礼品卡是否可用，默认不可用
                boolean chargingElectricityCardCanUse = marketingRest.querySupportCard(driverPayDeskDto.getOrderUuid());
                bookPayWayVo.setCanUse(chargingElectricityCardCanUse);
                if (!chargingElectricityCardCanUse) {
                    //不支持充电桩不支持充电卡时候，展示的文案
                    bookPayWayVo.setCanNotUseMsg(switchConfig.getChargingElectricityCardCanNotUseMsg());
                } else if(driverPayDeskVo.getInsureFare() != null && driverPayDeskVo.getInsureFare().compareTo(BigDecimal.ZERO) > 0){
                    bookPayWayVo.setInsureFareCanNotUseMsg(switchConfig.getInsureFareCanNotUseMsg());
                }
                bookPayWayVo.setPayChannel(EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode());
                //判断版本号，老版本选中 新版本 不选中
                if (AppVersionCompareUtil.compare(context.getGrayVersion(), AppVersionCompareUtil.VERSION2)) {
                    bookPayWayVo.setChoose(false);
                }
                bookPayWayVoList.add(bookPayWayVo);
            } else if (
                    switchConfig.isOpenBookPay()
                    && AppVersionCompareUtil.compare(context.getGrayVersion(), AppVersionCompareUtil.VERSION2)
                    && book.getBookType().equals(BookTypeEnum.DRIVER_SALARY.getType())
                    && book.getAvailableBalance() > 0
                    &&(driverPayDeskDto.getPayOrderType().equals(EnumPayOrderType.DRIVER_FREE_COMMISSION_CARD_V2_PAY.getCode())
                    || driverPayDeskDto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_CARD_PAY.getCode())
                    || driverPayDeskDto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_COUPON_PAY.getCode()))) {
                //司机薪酬账本
                BookPayWayVo bookPayWayVo = new BookPayWayVo();
                bookPayWayVo.setIcon(switchConfig.getAccountSalaryIcon());
                bookPayWayVo.setBookType(BookTypeEnum.DRIVER_SALARY.getType());
                bookPayWayVo.setAvailableBalance(MoneyUtils.fenToYuan(book.getAvailableBalance()));
                bookPayWayVo.setBookDesc(String.format(switchConfig.getAccountSalaryBookDesc(),
                        bookPayWayVo.getAvailableBalance().toPlainString()));
                bookPayWayVo.setPayChannel(EnumPayOrderChannel.PAY_ACCOUNT_SALARY.getCode());
                bookPayWayVo.setChoose(true);
                bookPayWayVoList.add(bookPayWayVo);
            }
            driverPayDeskVo.setBookPayWayVoList(bookPayWayVoList);
        }
    }

    /**
     * 查询并设置待支付金额
     * 
     * @param context 支付请求参数
     * @param context 收银台数据对象
     */
    private void setRemainPay(DriverPayDeskContext context) {
        DriverPayDeskDto dto = context.getDriverPayDeskDto();
        DriverPayDeskVo driverPayDeskVo = context.getDriverPayDeskVo();
        driverPayDeskVo.setRemainPay(driverPayDeskVo.getOrderFare());
        Response fareDetailResp = unifiedPaymentQueryFacade.queryPileFareDetail(dto.getOrderUuid());
        DriverPileFareDetail driverPileFareDetail = JSON.parseObject(JSON.toJSONString(fareDetailResp.getData()), DriverPileFareDetail.class);
        if (driverPileFareDetail != null) {
            DriverFareDetail driverFareDetail = driverPileFareDetail.getFareDetail();
            context.setPayDetail(driverFareDetail.getPayDetail());
            driverPayDeskVo.setOrderFare(driverFareDetail.getOrderFare());
            driverPayDeskVo.setRemainPay(driverFareDetail.getRemainPay());
            driverPayDeskVo.setOrderFarePayed(driverFareDetail.getOrderFarePayed());
        }
    }

    /**
     * 根据支付类型设置订单金额
     * 
     * @param dto 支付请求参数
     * @param driverPayDeskVo 收银台数据对象
     * @param userId 用户ID
     */
    private void setOrderFareByPayOrderType(DriverPayDeskDto dto, DriverPayDeskVo driverPayDeskVo, String userId) {
        if (dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_RECHARGE.getCode())
                || dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_CHARGING_PAY_AFTER.getCode())) {
            //查充电业务单
            ChargeOrderDto chargeOrderDto = marketingRest.queryChargingElectric(dto.getOrderUuid());
            driverPayDeskVo.setOrderFare(chargeOrderDto.getAdvanceMoney());
            if (chargeOrderDto.getInsureFare() != null) {
                driverPayDeskVo.setInsureFare(chargeOrderDto.getInsureFare());
            }
        } else if (dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_FREE_COMMISSION_CARD_V2_PAY.getCode())
                || dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_CARD_PAY.getCode())
                || dto.getPayOrderType().equals(EnumPayOrderType.DRIVER_ELECTRIC_COUPON_PAY.getCode())) {
            //查商城业务单
            PayOrderSureReq payOrderSureReq = new PayOrderSureReq();
            payOrderSureReq.setOrderCode(dto.getOrderUuid());
            payOrderSureReq.setUserId(userId);
            RechargeCheckResp mallOrder = mallPaymentBusiness.mallCheck(payOrderSureReq);
            BigDecimal payCash = mallOrder.getPayCash();
            driverPayDeskVo.setOrderFare(payCash);
        } else {
            //查结算单
            SettlementGeneralDto settlementGeneralDto = new SettlementGeneralDto();
            settlementGeneralDto.setOrderUuid(dto.getOrderUuid());
            settlementGeneralDto.setSettleType(SettleType.GENERAL_PAYMENT.getType());
            settlementGeneralDto.setBizType(dto.getSettlementBizType());
            Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(settlementGeneralDto);
            if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                SettlementGeneralDto settlementGeneralDtoR = response.getData().get(0);
                driverPayDeskVo.setOrderFare(settlementGeneralDtoR.getOrderFare());
            }
        }
    }


    /**
     * 查询司机可用的支付渠道列表
     * 
     * @param driverPayDeskDto 支付请求参数
     * @param request HTTP请求对象
     * @return DriverChannelListResVo 支付渠道响应对象
     *         当查询失败时抛出BizException
     * @throws BizException 业务异常，直接返回对应的错误响应
     */
    public DriverChannelListResVo queryDriverChannelList(DriverPayDeskDto driverPayDeskDto, HttpServletRequest request) {
        DriverChannelListResVo resVo = new DriverChannelListResVo();

        PayChannelReqDto payChannelReqDto = new PayChannelReqDto();
        // 显示云闪付
        payChannelReqDto.setQuickPass(true);
        Response<List<PayChannelResDto>> payChannelRes = payChannelTakeOverService.getPayChannelListForApp(payChannelReqDto);
        if (null == payChannelRes || !payChannelRes.isSuccess() || org.springframework.util.CollectionUtils.isEmpty(payChannelRes.getData())) {
            BizExceptionUtil.create(ResultErrorEnum.CHANNEL_LIST_ERROR.getCode(),ResultErrorEnum.CHANNEL_LIST_ERROR.getMsg());
        }
        String grayVersion = BaseApi.getGrayVersion(request);
        buildDriverChannelListResVo(driverPayDeskDto, payChannelRes.getData(), resVo, grayVersion);
        return resVo;
    }



    /**
     * buildDriverChannelListResVo
     *
     * @param dtoList List<PayChannelResDto>
     * @param resVo   DriverChannelListResVo
     * @param grayVersion   grayVersion
     */
    private void buildDriverChannelListResVo(DriverPayDeskDto driverPayDeskDto,
                                             List<PayChannelResDto> dtoList,
                                             DriverChannelListResVo resVo,
                                             String grayVersion) {
        List<PayWayVo> payWayVoList = new ArrayList<>();
        for (PayChannelResDto dto : dtoList) {
            if (!isSupportForDriverApp(dto.getPayChannel(), grayVersion)) {
                continue;
            }
            if (driverPayDeskDto != null
                    && switchConfig.isPayOrderTypeNotUseChannel(driverPayDeskDto.getPayOrderType(),
                    Lists.newArrayList(dto.getPayChannel()))) {
                continue;
            }
            PayWayVo payWayVo = new PayWayVo();
            payWayVo.setPayName(dto.getPayName());
            // 支付渠道标识
            payWayVo.setPayChannel(dto.getPayChannel());
            // 优惠文案
            if (!org.springframework.util.CollectionUtils.isEmpty(dto.getLabelList())) {
                List<String> labelList = Lists.newArrayList();
                payWayVo.setDiscountTags(labelList);
                dto.getLabelList().forEach(labelResDto -> {
                    //是否是开通引导免密配置
                    boolean isPayAndSign = null != labelResDto.getPayAndSign()
                            && NumConstants.NUM_1 == labelResDto.getPayAndSign();
                    //是否配置的是全国
                    boolean isAllCity = StringUtils.isNotBlank(labelResDto.getCityCodes())
                            && ALL_CITY.equals(labelResDto.getCityCodes());
                    //是否是配置的司机app
                    boolean isDriverApp = StringUtils.isNotBlank(labelResDto.getTerminalType())
                            && labelResDto.getTerminalType().contains(TerminalEnum.DRIVER_APP.getType());
                    // 需要在司机app上面展示的优惠文案
                    if (!isPayAndSign && isAllCity && isDriverApp) {
                        labelList.add(labelResDto.getLabelName());
                    }
                });
            }
            payWayVoList.add(payWayVo);
        }
        resVo.setPayWayVoList(payWayVoList);
    }



    /**
     * 司机收银台支持的渠道类型
     *
     * @param payChannel Integer
     * @param grayVersion grayVersion
     * @return true: 支付；false：不支持
     */
    private boolean isSupportForDriverApp(Integer payChannel, String grayVersion) {
        if (null == payChannel) {
            return false;
        }
        return EnumPayOrderChannel.ALIPAY.getCode() == payChannel
                || EnumPayOrderChannel.WEIXIN.getCode() == payChannel
                || EnumPayOrderChannel.NETCOM.getCode() == payChannel
                || (EnumPayOrderChannel.UNION_QUICK_PASS_PAY.getCode() == payChannel && canUseUnionPay(grayVersion));
    }

    /**
     * 判断是否可以使用银联云闪付
     * 
     * @param grayVersion 灰度版本号
     * @return true: 支持；false：不支持
     *         需要满足iOS/Android 2.9.0及以上版本要求
     */
    private boolean canUseUnionPay(String grayVersion) {
        if (StringUtils.isBlank(grayVersion)) {
            return false;
        }
        return AppVersionCompareUtil.compare(grayVersion, AppVersionCompareUtil.VERSION1);
    }


}
