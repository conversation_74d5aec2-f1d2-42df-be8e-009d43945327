package com.t3.ts.pay.center.api.business;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.constants.AccountTypeEnum;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.PayAccountBookDto;
import com.t3.ts.account.center.dto.PayAccountDto;
import com.t3.ts.account.center.dto.freeze.AccountFreezeDetailDto;
import com.t3.ts.account.center.dto.freeze.AccountFreezeQueryDto;
import com.t3.ts.account.center.service.AccountBookService;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.dto.wallet.AccountBalanceResVo;
import com.t3.ts.pay.center.api.util.MoneyUtils;
import com.t3.ts.result.Response;
import com.t3.ts.withdraw.center.enums.WithdrawOrderQueryType;
import com.t3.ts.withdraw.center.enums.WithdrawStatus;
import com.t3.ts.withdraw.center.remote.dto.QueryWithdrawOrderDto;
import com.t3.ts.withdraw.center.remote.dto.WithdrawOrderDto;
import com.t3.ts.withdraw.center.remote.service.WithdrawOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 *
 * @ClassName: DriverWalletBusiness
 * @Package com.t3.ts.pay.center.api.business
 * @version v1.0.0
 * <AUTHOR>
 * @date 2025/6/19 15:10
 */
@Slf4j
@Component
public class DriverWalletBusiness {

    @Autowired
    private SwitchConfig switchConfig;

    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;
    @DubboReference
    private WithdrawOrderService withdrawOrderService;

    @DubboReference
    private AccountBookService accountBookService;

    public AccountBalanceResVo queryAccountBalance(String userId, Integer accountType) {
        AccountBalanceResVo balanceResVo = new AccountBalanceResVo();
        // 提现提示语
        String tip = String.format(switchConfig.getDrawCashTip(), switchConfig.getDrawCashCntLimit());
        String prepareTip = switchConfig.getPrepareTip();
        balanceResVo.setPrepareTip(prepareTip);
        balanceResVo.setDrawCashTip(tip);
        Response<PayAccountDto> accountBalanceRes = unifiedAccountFacade
                .getAccountBalance(userId, PayAccountTypeEnum.getEnumByType(accountType));

        if (accountBalanceRes == null || !accountBalanceRes.isSuccess()
                || accountBalanceRes.getData() == null
                || CollectionUtils.isEmpty(accountBalanceRes.getData().getBooks())) {
            return balanceResVo;
        }
        PayAccountDto payAccountDto = accountBalanceRes.getData();
        //账本筛选
        builderNewDriverSalaryAccount(userId, accountType, payAccountDto);
        List<Integer> driverBookTypeList = getNewDriverBookTypeList(accountType);
        Long availableBalance = 0L;
        Long freezeBalance = 0L;
        Long totalBalance = 0L;
        Long prepareBalance = 0L;
        for (PayAccountBookDto bookInfo : payAccountDto.getBooks()) {
            if (driverBookTypeList.contains(Integer.valueOf(bookInfo.getBookType()))) {
                availableBalance = plusMoney(availableBalance, bookInfo.getAvailableBalance());
                freezeBalance = plusMoney(freezeBalance, bookInfo.getFreezeBalance());
                totalBalance = plusMoney(totalBalance, bookInfo.getTotalBalance());
                prepareBalance = plusMoney(prepareBalance, bookInfo.getPrepareBalance());
                balanceResVo.setBookStatus(null != bookInfo.getBookStatus() ? String.valueOf(bookInfo.getBookStatus()) : null);
            } else if (BookTypeEnum.EQUIPMENT_DEPOSIT_BOOK.getType() == bookInfo.getBookType()) {
                balanceResVo.setHardAwreCashDeposit(MoneyUtils.fenToYuan(bookInfo.getTotalBalance()));
            }
        }
        //出租车司机查询“提现中+核算中”金额
        if (AccountTypeEnum.TAXI_TYPE.getType() == accountType) {
            AccountFreezeQueryDto queryDto = new AccountFreezeQueryDto();
            queryDto.setAccountType(accountType);
            queryDto.setUserId(userId);
            Response<AccountFreezeQueryDto> accountFreezeRes = accountBookService.getAccountFreeze(queryDto);
            if (null != accountFreezeRes && accountFreezeRes.isSuccess() && null != accountFreezeRes.getData()) {
                AccountFreezeQueryDto accountFreezeQueryDto = accountFreezeRes.getData();
                List<AccountFreezeDetailDto> incomeFreezeList = accountFreezeQueryDto.getIncomeFreezeList();
                if (incomeFreezeList != null && incomeFreezeList.size() > 0) {
                    incomeFreezeList.sort(Comparator.comparing(AccountFreezeDetailDto::getUnfreezeDate));
                    //最近可解冻金额
                    balanceResVo.setUnfreezeDate(incomeFreezeList.get(0).getUnfreezeDate());
                    balanceResVo.setUnfreezeAmount(MoneyUtils.fenToYuan(incomeFreezeList.get(0).getFreezeAmount()));
                }
                //提现中
                balanceResVo.setWithdrawingBalance(MoneyUtils.fenToYuan(accountFreezeQueryDto.getWithdrawFreezeAmount()));
                //核算中
                balanceResVo.setAccountingBalance(MoneyUtils.fenToYuan(accountFreezeQueryDto.getIncomeFreezeAmount()));
                freezeBalance = subtractMoney(freezeBalance, accountFreezeQueryDto.getWithdrawFreezeAmount(),
                        accountFreezeQueryDto.getIncomeFreezeAmount());
            }
        }
        //提现中冻结金额
        Long withdrawFreezeAmt = queryWithdrawFreezeAmt(userId, accountType);
        freezeBalance = freezeBalance - withdrawFreezeAmt;
        totalBalance = totalBalance - withdrawFreezeAmt;
        balanceResVo.setWithdrawFreezeAmt(MoneyUtils.fenToYuan(withdrawFreezeAmt));
        balanceResVo.setFreezeBalance(MoneyUtils.fenToYuan(freezeBalance));
        balanceResVo.setAvailableBalance(MoneyUtils.fenToYuan(availableBalance));
        balanceResVo.setTotalBalance(MoneyUtils.fenToYuan(totalBalance));
        balanceResVo.setPrepareBalance(MoneyUtils.fenToYuan(prepareBalance));
        balanceResVo.setWithdrawalBalance(MoneyUtils.fenToYuan(payAccountDto.getWithdrawAmount()));
        balanceResVo.setRentAmount(MoneyUtils.fenToYuan(payAccountDto.getRentAmount()));
        return balanceResVo;
    }


    /**
     * 构建司机薪酬新账本信息
     *
     * @param userId        用户ID
     * @param accountType   账户类型
     * @param payAccountDto 支付账户dto响应
     */
    private void builderNewDriverSalaryAccount(String userId, int accountType,
                                               PayAccountDto payAccountDto) {
        //获取非司机薪酬老账本信息
        List<PayAccountBookDto> nomalBooks = payAccountDto.getBooks().stream()
                .filter(k -> !isOldBook(k))
                .collect(Collectors.toList());
        payAccountDto.setBooks(nomalBooks);

        //获取可提现金额  租金代扣金额
        WithdrawOrderDto withdrawOrderDto = new WithdrawOrderDto();
        withdrawOrderDto.setUserId(userId);
        withdrawOrderDto.setAccountType(accountType);
        Response<Integer> withdrawAmountResponse = withdrawOrderService.getWithdrawAmount(withdrawOrderDto);
        if (null != withdrawAmountResponse && withdrawAmountResponse.isSuccess()) {
            long withdrawAmount = withdrawAmountResponse.getData().longValue();
            long rentAmount = 0L;
            //T3司机 计算租金代扣金额
            if (PayAccountTypeEnum.DRIVER_TYPE.getType() == accountType) {
                Long vailableBalance = payAccountDto.getBooks().stream().filter(
                                k -> BookTypeEnum.DRIVER_SALARY.getType() == k.getBookType())
                        .map(PayAccountBookDto::getAvailableBalance).reduce(0L, Long::sum);
                if (withdrawAmount > 0) {
                    rentAmount = vailableBalance - withdrawAmount;
                } else {
                    rentAmount = vailableBalance < 0 ? 0 : vailableBalance;
                }
            }
            payAccountDto.setWithdrawAmount(withdrawAmount);
            payAccountDto.setRentAmount(rentAmount);
        }
    }

    /**
     * 是否是老钱包账本
     *
     * @param payAccountBookDto 支付账户书dto
     * @return boolean
     */
    private boolean isOldBook(PayAccountBookDto payAccountBookDto) {
        if (PayAccountTypeEnum.isContain(payAccountBookDto.getAccountType(), PayAccountTypeEnum.DRIVER_TYPE)) {
            return BookTypeEnum.isContain(payAccountBookDto.getBookType(), BookTypeEnum.DRIVER_SALARY_BOOK);
        }
        if (PayAccountTypeEnum.isContain(payAccountBookDto.getAccountType(), PayAccountTypeEnum.TAXI_TYPE)) {
            return BookTypeEnum.isContain(payAccountBookDto.getBookType(), BookTypeEnum.CASH_BOOK,
                    BookTypeEnum.TAXI_METER_BOOK);
        }
        return false;
    }


    /**
     * 获取司机的账本类型
     *
     * @return 非出租车司机为23，出租车司机为23,24
     */
    private List<Integer> getNewDriverBookTypeList(int accountType) {
        return Arrays.asList(BookTypeEnum.DRIVER_SALARY.getType(),
                BookTypeEnum.TAXI_DRIVER_CLEAR.getType());
    }



    /**
     * plusMoney
     *
     * @param a a
     * @param b b
     * @return Long
     */
    private Long plusMoney(Long a, Long b) {
        if (null == a) {
            a = 0L;
        }
        if (null == b) {
            b = 0L;
        }
        return a + b;
    }


    /**
     * subtractMoney
     *
     * @param values values
     * @return String
     */
    private Long subtractMoney(Long... values) {
        if (ArrayUtils.isEmpty(values)) {
            return 0L;
        }
        Long value = values[0];
        Long result = value == null ? 0 : value;
        for (int i = 1; i < values.length; i++) {
            value = values[i];
            if (value != null) {
                result = result - value;
            }
        }
        return result;
    }


    /**
     * 查询提现中的冻结金额
     *
     * @param userId      用户ID
     * @param accountType 账户类型
     * @return Long
     */
    private Long queryWithdrawFreezeAmt(String userId, int accountType) {
        QueryWithdrawOrderDto queryWithdrawOrderDto = new QueryWithdrawOrderDto();
        queryWithdrawOrderDto.setUserId(userId);
        queryWithdrawOrderDto.setAccountType(accountType);
        List<Integer> withdrawStatusList = Arrays.asList(WithdrawStatus.INIT.getCode(),
                WithdrawStatus.PAYING.getCode(), WithdrawStatus.REMITING.getCode(), WithdrawStatus.REMIT_FAIL.getCode(),
                WithdrawStatus.REMIT_SUCCESS.getCode());
        queryWithdrawOrderDto.setWithdrawStatusList(withdrawStatusList);
        Response res = withdrawOrderService.queryOrder(
                WithdrawOrderQueryType.QUERY_COUNT_AMOUNT.getCode(), queryWithdrawOrderDto);

        if (null == res || !res.isSuccess() || null == res.getData()) {
            return 0L;
        }
        return (Long) ((JSONObject) res.getData()).get("withdrawAmountTotal");
    }
}
