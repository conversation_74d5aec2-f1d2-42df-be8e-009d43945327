package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.EnterprisePaymentBusiness;
import com.t3.ts.pay.center.api.dto.DriverPaymentReq;
import com.t3.ts.pay.center.api.dto.PayStatusQueryReq;
import com.t3.ts.pay.center.api.dto.RoutePaymentReq;
import com.t3.ts.pay.center.api.dto.common.ChannelListReq;
import com.t3.ts.pay.center.api.dto.route.RouteOrderQueryReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.trade.MakeOrderReq;
import com.t3.ts.pay.center.api.dto.trade.PayStatusQueryReqV2;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.center.api.dto.trade.RechargeReq;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PassengerAPiTest {

    @Mock
    EnterprisePaymentBusiness enterprisePaymentBusiness;
    @Mock
    Logger log;
    @InjectMocks
    PassengerAPi passengerAPi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetPayChannelList() throws Exception {
        when(passengerAPi.getPayChannelList(any(), any())).thenReturn(null);

        Response result = passengerAPi.getPayChannelList(new ChannelListReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetPayDeskInfo4V3() throws Exception {
        when(passengerAPi.getPayDeskInfo4V3(any(), any())).thenReturn(null);

        Response result = passengerAPi.getPayDeskInfo4V3(new PayTradeReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testRoutePay() throws Exception {
        when(passengerAPi.routePay(any(), any())).thenReturn(null);

        Response result = passengerAPi.routePay(new RoutePaymentReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testPayStatusQuery4Special() throws Exception {
        when(passengerAPi.payStatusQuery4Special(any(), any())).thenReturn(null);

        Response result = passengerAPi.payStatusQuery4Special(new PayStatusQueryReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testRecharge() throws Exception {
        when(passengerAPi.recharge(any(), any())).thenReturn(null);

        Response result = passengerAPi.recharge(new RechargeReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testMakeOrder() throws Exception {
        when(passengerAPi.makeOrder(any(), any())).thenReturn(null);

        Response result = passengerAPi.makeOrder(new MakeOrderReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testPrePayStatusQuery() throws Exception {
        when(passengerAPi.prePayStatusQuery(any(), any())).thenReturn(null);

        Response result = passengerAPi.prePayStatusQuery(new PayStatusQueryReqV2(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCheckRoutePlanEdit() throws Exception {
        when(passengerAPi.checkRoutePlanEdit(any())).thenReturn(null);

        Response result = passengerAPi.checkRoutePlanEdit(new RoutePlanUuidReq(null));
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryRoutePlanUuid() throws Exception {
        when(passengerAPi.queryRoutePlanUuid(any(), any())).thenReturn(null);

        Response result = passengerAPi.queryRoutePlanUuid(new RouteOrderQueryReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryAliEcoRecycle() throws Exception {
        when(passengerAPi.queryAliEcoRecycle(any(), any())).thenReturn(null);

        Response result = passengerAPi.queryAliEcoRecycle(new RoutePlanUuidReq(null), null);
        Assert.assertEquals(null, result);
    }
}
