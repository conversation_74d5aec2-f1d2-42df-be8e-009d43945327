package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:19
 * @des 1.0
 */

@Data
public class PayDeskRes {

    /**
     * 已支付费用
     */
    private Payed payed;

    /**
     * 余额可支付明细
     */
    private BalanceCanPayDetail balanceCanPayDetail;

    /**
     * 余额
     */
    private Balance balance;

    /**
     * 费用明细
     */
    private FareDetail fareDetail;

    /**
     * 优惠券明细
     */
    private CouponDetail couponDetail;

    /**
     * 权益卡明细
     */
    private PrivilegeDetail privilegeDetail;

    /**
     * 省心打折扣明细
     */
    private UnWarriedArriveDetail unWarriedArriveDetail;

    private List<ChannelDeskDto> channelDeskDtoList;

    /**
     * 积分是否选中
     */
    private Boolean integralChecked = Boolean.FALSE;
    /**
     * 亲友代付标识
     */
    private PayForOtherDetail payForOtherDetail;
    /**
     * 行程相关信息
     */
    private RouteInfo routeInfo;
    /**
     * 优惠券数量
     */
    private String couponNum;
    /**
     * 是否开通了免密支付
     */
    private Boolean isAnyFree;
}
