package com.t3.ts.pay.center.api.dto.trade;

/**
 * 充值支付出参
 */
public class RechargePayVoV2 {
    // 支付宝支付
    private String alipay_sdk;
    // 微信支付
    private String wxPay_sdk;
    // 一网通支付
    private String cmb_conenet;
    // 支付方式
    private Integer payType;
    // 是否免密 (银联  0：已开通 1：未开通)  (一网通 0： 未开通 1： 已开通)
    private String isNoSecretPayment;
    // 银联支付
    private String union_sdk;
    private String code = "200";
    /**
     * 是否开启银联免密支付  0:开通   1:非开通
     */
    private String isOpenUnionPay = "1";
    private String msg;
    private String settlementID;

    public String getUnion_sdk() {
        return union_sdk;
    }

    public void setUnion_sdk(String union_sdk) {
        this.union_sdk = union_sdk;
    }

    public String getIsOpenUnionPay() {
        return isOpenUnionPay;
    }

    public void setIsOpenUnionPay(String isOpenUnionPay) {
        this.isOpenUnionPay = isOpenUnionPay;
    }

    public String getSettlementID() {
        return settlementID;
    }

    public void setSettlementID(String settlementID) {
        this.settlementID = settlementID;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsNoSecretPayment() {
        return isNoSecretPayment;
    }

    public void setIsNoSecretPayment(String isNoSecretPayment) {
        this.isNoSecretPayment = isNoSecretPayment;
    }

    public String getAlipay_sdk() {
        return alipay_sdk;
    }

    public void setAlipay_sdk(String alipay_sdk) {
        this.alipay_sdk = alipay_sdk;
    }

    public String getWxPay_sdk() {
        return wxPay_sdk;
    }

    public void setWxPay_sdk(String wxPay_sdk) {
        this.wxPay_sdk = wxPay_sdk;
    }

    public String getCmb_conenet() {
        return cmb_conenet;
    }

    public void setCmb_conenet(String cmb_conenet) {
        this.cmb_conenet = cmb_conenet;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }
}
