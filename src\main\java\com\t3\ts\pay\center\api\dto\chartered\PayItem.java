package com.t3.ts.pay.center.api.dto.chartered;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @dete 2020.10.26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayItem {
    /**
     * 支付项标题
     */
    private String itemTitle;

    /**
     * 金额
     */
    private String itemFee;

    /**
     * 是否加深颜色显示
     */
    private Boolean itemColorFlag;

    /**
     * 排序
     */
    private int sort;

    /**
     * 扩展
     */
    private Object extendData;

}
