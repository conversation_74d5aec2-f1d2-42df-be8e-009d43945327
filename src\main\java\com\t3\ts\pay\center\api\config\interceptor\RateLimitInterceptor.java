package com.t3.ts.pay.center.api.config.interceptor;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.RateLimiter;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 限流拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RateLimitInterceptor extends HandlerInterceptorAdapter {

    private final static ConcurrentHashMap<String, RateLimiter> rateLimiterMap = new ConcurrentHashMap<>();

    private final static ConcurrentHashMap<String, Double> oldRateLimitMap = new ConcurrentHashMap<>();

    @Value("#{${rate.rateLimitMap:{'/api/driver/wallet/getPayAccountAnalyseDetailPage':200,"
            + "'/api/driver/wallet/getPayAccountAnalyseSumByDay':200,"
            + "'/api/finance/payAccountAnalyseSum/v2/query/ratioTrend':200,"
            + "'/api/finance/payAccountAnalyseSum/v2/query/flowCount':200,"
            + "'/api/driver/wallet/payAccountAnalyseDwySum':200}}}")
    private Map<String, Double> newRateLimitMap;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        response.setCharacterEncoding("utf-8");
        String requestURI = request.getRequestURI();
        Double newLimitNum = newRateLimitMap.get(requestURI);
        Double oldLimitNum = oldRateLimitMap.get(requestURI);
        if (newLimitNum == null) {
            return true;
        }
        RateLimiter rateLimiter;
        if (newLimitNum.equals(oldLimitNum)) {
            log.info("限流拦截-使用旧 requestURI={}",requestURI);
            rateLimiter = rateLimiterMap.get(requestURI);
        } else {
            log.info("限流拦截-创建新的 requestURI={}, newRateLimitMap={},oldRateLimitMap={}",
                    requestURI, JSON.toJSONString(newRateLimitMap), JSON.toJSONString(oldRateLimitMap));
            rateLimiter = RateLimiter.create(newLimitNum);
            rateLimiterMap.put(requestURI, rateLimiter);
            oldRateLimitMap.put(requestURI,newLimitNum);
        }
        // 尝试获取令牌
        if (!rateLimiter.tryAcquire()) {
            log.warn("当前访问拥挤，请稍后重试 触发限流 requestURI={}, newRateLimitMap={},oldRateLimitMap={}",
                    requestURI, JSON.toJSONString(newRateLimitMap), JSON.toJSONString(oldRateLimitMap));
            outPrint(response.getWriter(), Response.createError("系统开小差了，请稍后重试！"));
            return false;
        }
        log.info("平安走过限流拦截 requestURI={}, newRateLimitMap={},oldRateLimitMap={}", requestURI,
                JSON.toJSONString(newRateLimitMap), JSON.toJSONString(oldRateLimitMap));
        return true;
    }
    private void outPrint(PrintWriter out, Response response) {
        out.println(JSON.toJSON(response));
        out.flush();
        out.close();
    }
}
