package com.t3.ts.pay.center.api.dto;

import lombok.Data;

/**
 * 预付款支付结果查询
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
public class PrepaymentStatusResVo {

    /**
     * 订单uuid（预付款流水号）
     */
    private String orderUuid;

    /**
     * 支付状态（1、支付中 2、支付完成）
     */
    private Integer payStatus;

    /**
     * 预付款文案
     */
    private String preMsg;

    /**
     * 免密扣款成功文案
     */
    private String paySucessMsg;

    /**
     * 是否开通三方免密签约支付 默认：false（未开通）
     */
    private Boolean isOpenNoSecret = false;
}
