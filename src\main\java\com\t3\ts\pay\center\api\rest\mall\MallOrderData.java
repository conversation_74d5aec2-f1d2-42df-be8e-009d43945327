package com.t3.ts.pay.center.api.rest.mall;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MallOrderData.java
 * @createTime 2021年07月14日 15:27:00
 */
@Data
public class MallOrderData implements Serializable {

    private static final long serialVersionUID = -6343660105576300600L;

    /**
     * buySpuNum
     */
    private Integer buySpuNum;

    /**
     * spuName
     */
    private String spuName;

    /**
     * 订单ID
     */
    private String orderCode;

    /**
     * spuCode
     */
    private String spuCode;

    /**
     * skuCode
     */
    private String skuCode;

    /**
     * payType
     */
    private Integer payType;

    /**
     * skuSellIntegral
     */
    private Integer skuSellIntegral;

    /**
     * 订单价格
     */
    private BigDecimal skuSellCash;

}
