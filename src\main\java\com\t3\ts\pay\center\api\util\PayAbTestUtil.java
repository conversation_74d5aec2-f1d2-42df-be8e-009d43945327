package com.t3.ts.pay.center.api.util;

import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.pay.center.api.config.valueconfig.AliPayPointsConfig;
import com.t3.ts.pay.center.api.config.valueconfig.WechatPayPointsConfig;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PayAbTestUtil.java
 * @Description TODO
 * @createTime 2021年11月16日 15:14:00
 */
@Component
@Slf4j
public class PayAbTestUtil {

    @Autowired
    private WechatPayPointsConfig wechatPayPointsConfig;
    @Autowired
    private AliPayPointsConfig aliPayPointsConfig;

    /**
     * abtest
     *
     * @param userId 乘客id
     * @return 是否被a到
     */
    public boolean wxPayAndSignAbTest(String userId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }

        log.info("payAndSignAliWhiteList:{}", wechatPayPointsConfig.getPayAndSignAliWhiteList());
        if (StringUtils.isNotBlank(wechatPayPointsConfig.getPayAndSignAliWhiteList())
                && wechatPayPointsConfig.getPayAndSignAliWhiteList().contains(userId)) {
            return true;
        }

        if (null != userId) {
            int hashCode = userId.hashCode();
            if (hashCode == Integer.MIN_VALUE) {
                hashCode = Integer.MAX_VALUE;
            }
            int hashCodeAbs = Math.abs(hashCode);
            Integer index = hashCodeAbs % NumConstant.NUM_100;
            if (index <= wechatPayPointsConfig.getPayAndSignAliPercent()) {
                return true;
            }
        }

        return false;
    }

    /**
     * abtest
     *
     * @param userId 乘客id
     * @return 是否被a到
     */
    public boolean aliPayAndSignAbTest(String userId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }

        log.info("payAndSignAliWhiteList:{}", aliPayPointsConfig.getPayAndSignAliWhiteList());
        if (StringUtils.isNotBlank(aliPayPointsConfig.getPayAndSignAliWhiteList())
                && aliPayPointsConfig.getPayAndSignAliWhiteList().contains(userId)) {
            return true;
        }
        if (null != userId) {
            int hashCode = userId.hashCode();
            if (hashCode == Integer.MIN_VALUE) {
                hashCode = Integer.MAX_VALUE;
            }
            int hashCodeAbs = Math.abs(hashCode);
            Integer index = hashCodeAbs % NumConstant.NUM_100;
            if (index <= aliPayPointsConfig.getPayAndSignAliPercent()) {
                return true;
            }
        }

        return false;
    }

}
