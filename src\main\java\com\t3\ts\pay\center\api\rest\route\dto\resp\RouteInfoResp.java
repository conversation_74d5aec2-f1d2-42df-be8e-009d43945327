package com.t3.ts.pay.center.api.rest.route.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ligq
 *
 * 行程信息接口出参
 */
@Data
public class RouteInfoResp implements Serializable {
    private static final long serialVersionUID = -8375008505252866790L;
    /**
     * 行程特征标签列表
     */
    private List<String> labels;
    /**
     * 主订单ID
     */
    private String combineRouteId;
    /**
     * 行程ID
     */
    private String routeId;

    /**
     * 行程状态信息
     */
    private RouteStateDTO routeStateDTO;

    /**
     * 运力信息
     */
    private TransportDTO transportDTO;

    /**
     * 行程业务模式
     */
    private BusinessDTO businessDTO;

    /**
     * 乘客费用信息
     */
    private PassengerFareDTO passengerFareDTO;

    /**
     * 行程计划模型
     */
    private TripPlanDTO tripPlanDTO;

    /**
     * 乘客信息
     */
    private PassengerDTO passengerDTO;

    /**
     * 扩展信息
     */
    private ExtendInfoDTO extendInfoDTO;
}
