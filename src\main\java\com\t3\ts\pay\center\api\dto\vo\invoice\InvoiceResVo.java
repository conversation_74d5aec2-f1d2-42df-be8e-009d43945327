package com.t3.ts.pay.center.api.dto.vo.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: com.t3.ts.app.api.third.invoice.vo.InvoiceResVo
 * @author: ccq
 * @Date: 2020/6/17 16:19
 * @Description: 开票结果Vo
 */
@ApiModel(value = "开票结果")
@Data
public class InvoiceResVo implements Serializable {
    private static final long serialVersionUID = -7151479854356976637L;

    /**
     * t3运力来源开票结果 1成功  0 失败
     */
    @ApiModelProperty(value = "东风运力来源开票结果 1成功 0 失败")
    private Integer t3InvoiceRes;

    /**
     * 东风运力来源开票结果 1成功 0 失败
     */
    @ApiModelProperty(value = "东风运力来源开票结果 1成功 0 失败")
    private Integer dfInvoiceRes;

    /**
     * 一汽运力来源开票结果 1 成功 0 失败
     */
    @ApiModelProperty(value = "东风运力来源开票结果 1成功 0 失败")
    private Integer fawInvoiceRes;
}
