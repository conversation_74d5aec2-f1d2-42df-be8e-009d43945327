package com.t3.ts.pay.center.api.business;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.AccountSignDtoV2;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.enums.common.BussiEnum;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.interactive.exception.ExceptionUtils;
import com.t3.ts.passenger.center.dto.RouteSerialDto;
import com.t3.ts.passenger.center.service.PassengerCacheService;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.AdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.PayTradeDetailDto;
import com.t3.ts.pay.center.api.dto.PrepaymentOrderDto;
import com.t3.ts.pay.center.api.dto.PrepaymentStatusResVo;
import com.t3.ts.pay.center.api.dto.RequisitionDto;
import com.t3.ts.pay.center.api.dto.RoutePrePayDetailDto;
import com.t3.ts.pay.center.api.dto.sign.SignButtonVo;
import com.t3.ts.pay.center.api.dto.trade.PayStatusQueryReqV2;
import com.t3.ts.pay.center.api.dto.trade.RechargeReq;
import com.t3.ts.pay.center.api.dto.vo.RechargePayVo;
import com.t3.ts.pay.center.api.exception.BizExceptionUtil;
import com.t3.ts.pay.center.api.rest.OrgExternalRest;
import com.t3.ts.pay.center.api.rest.PassengerFeignClient;
import com.t3.ts.pay.center.api.rest.RouteReadRest;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.center.api.service.RoutePayService;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.SecUtils;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderSubAccount;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.EnumSceneType;
import com.t3.ts.pay.remote.dto.PayOrderDto;
import com.t3.ts.pay.remote.dto.UnifiedDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_0;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.STR_203;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: PrePaymentBusiness
 * @Package com.t3.ts.pay.center.api.business
 * @Description: 乘客预付款支付充值逻辑处理
 * @date 2021/9/717:37
 */
@Slf4j
@Component
public class PrePaymentBusiness {

    private static final String MY_NJ_SDK_PREFIX = "znmh://pay?";

    @Autowired
    private CmbAggHelper cmbAggHelper;
    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;
    @DubboReference
    private UnifiedService unifiedService;
    @Resource
    private RouteBusiness routeBusiness;
    @DubboReference
    private PassengerCacheService passengerCacheService;
    @DubboReference
    private SettlementRechargeService settlementRechargeService;
    @Autowired
    private RoutePayService routePayService;
    @Resource
    private OrgExternalRest orgExternalRest;
    @Autowired
    private PassengerFeignClient passengerFeignClient;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private SecUtils secUtils;
    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQueryFacade;
    @DubboReference
    private AccountSignService accountSignService;
    @Resource
    private RouteReadRest routeReadRest;


    /**
     * 预付款支付-充值
     * 乘客充值
     *
     * @param rechargeReq 充电要求的事情
     * @param userUid     用户uid
     * @return {@link Response}
     */
    public Response recharge(RechargeReq rechargeReq, String userUid) {
        log.info("PrePaymentBusiness#rechargeReq:{},userUid:{}", JSONUtil.toJsonStr(rechargeReq), userUid);
        RechargePayVo rechargePayVo;
        AdvanceRechargeDTO advanceRechargeDTO = new AdvanceRechargeDTO();
        try {
            String passengerUuid;
            BigDecimal rechargeAmount;
            String prePayNo = rechargeReq.getPrePayNo();
            if (StrUtil.isNotBlank(rechargeReq.getEnterprise())
                    && "1".equals(rechargeReq.getEnterprise()) && switchConfig.getCompanyPrePayFlag()) {
                // 企业预付款 查询企业用车
                final RequisitionDto advanceInfo = orgExternalRest.getAdvanceInfo(rechargeReq.getPrePayNo());
                if (ObjectUtil.isNull(advanceInfo) || null == advanceInfo.getRechargeAmount()
                        || advanceInfo.getRechargeAmount().compareTo(BigDecimal.ZERO) == 0) {
                    BizExceptionUtil.create(ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getMsg(),
                            ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getCode());
                }
                rechargeAmount = advanceInfo.getRechargeAmount();
                advanceRechargeDTO.setSourceType(CommonNumConst.NUM_1);
                passengerUuid = advanceInfo.getPassengerUuid();
            } else {
                RoutePrePayDetailDto routePrePayDetailDto = queryRoutePrePeyInfo(rechargeReq);
                if (Objects.isNull(routePrePayDetailDto)) {
                    BizExceptionUtil.create(ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getMsg(),
                            ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getCode());
                }
                rechargeAmount = routePrePayDetailDto.getPrepayAmount();
                prePayNo = routePrePayDetailDto.getAdvanceSerial();
                passengerUuid = routePrePayDetailDto.getPassengerUuid();
                advanceRechargeDTO.setCityCode(routePrePayDetailDto.getCityCode());
            }
            if (StringUtils.isEmpty(prePayNo)) {
                BizExceptionUtil.create(ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getMsg(),
                        ResultErrorEnum.PRE_PAY_NO_TIMEOUT.getCode());
            }
            if (Objects.isNull(rechargeAmount)) {
                BizExceptionUtil.create(ResultErrorEnum.PRE_PAY_AMOUNT_TIMEOUT.getMsg(),
                        ResultErrorEnum.PRE_PAY_AMOUNT_TIMEOUT.getCode());
            }
            advanceRechargeDTO.setSource(rechargeReq.getSource());
            advanceRechargeDTO.setNoSecret(rechargeReq.getNoSecret());
            advanceRechargeDTO.setPassengerUuid(userUid);
            advanceRechargeDTO.setPassengerMobile(RequestContextHelper.getPassengerMobile());
            advanceRechargeDTO.setQuitUrl(rechargeReq.getQuit_url());
            if (switchConfig.getDiffUserSwitch()) {
                if (StringUtils.isNotBlank(passengerUuid) && !userUid.equals(passengerUuid)) {
                    log.info("预付款下单用户与支付用户不一致.下单用户:{}.支付用户:{}", passengerUuid, userUid);
                    if (switchConfig.getDiffUserReplace()) {
                        String mobile = passengerFeignClient.getPassengerMobile(passengerUuid);
                        advanceRechargeDTO.setPassengerUuid(passengerUuid);
                        advanceRechargeDTO.setPassengerMobile(mobile);
                    } else {
                        BizExceptionUtil.create(ResultErrorEnum.GET_RECHARGE_IFNO_ERROR.getMsg(),
                                ResultErrorEnum.GET_RECHARGE_IFNO_ERROR.getCode());
                    }
                }
            }
            advanceRechargeDTO.setPayType(rechargeReq.getRechargePayType());
            // 预付款流水号
            advanceRechargeDTO.setPrePayNo(prePayNo);
            // 充值金额
            advanceRechargeDTO.setRechargeAmount(rechargeAmount);
            advanceRechargeDTO.setPayAndSign(rechargeReq.getPayAndSign());
            //获取端上支付的聚合支付渠道
            List<Integer> cmbAggPayTypeList = cmbAggHelper.getCmbAggPayTypeList(rechargeReq.getGrayBuild(),
                    rechargeReq.getGrayVersion(), rechargeReq.getPayAndSign(), rechargeReq.getSource(),
                    EnumPayOrderType.RECHARGE.getCode(), Lists.newArrayList(rechargeReq.getRechargePayType()),
                    advanceRechargeDTO.getPassengerUuid());
            advanceRechargeDTO.setAggPayTypeList(cmbAggPayTypeList);
            if (rechargeReq.getRechargePayType() != null
                    && EnumPayOrderChannel.WECHATMINIPROGRAM.getCode() == rechargeReq.getRechargePayType()) {
                // 微信小程序支付，获取openid
                Response<String> wmpResponse = cmbAggHelper.getWMPOpenId(rechargeReq.getCode());
                if (BooleanUtil.isFalse(wmpResponse.isSuccess())) {
                    BizExceptionUtil.create(ResultErrorEnum.WX_MINI_USER_ERROR.getMsg(),
                            ResultErrorEnum.WX_MINI_USER_ERROR.getCode());
                }
                advanceRechargeDTO.setOpenId(wmpResponse.getData());
            }
            if (rechargeReq.getRechargePayType() != null
                    && EnumPayOrderChannel.ALIPAY_MINI.getCode() == rechargeReq.getRechargePayType()) {
                advanceRechargeDTO.setAliAuthCode(rechargeReq.getCode());
            }
            advanceRechargeDTO.setRoutePlanUuid(rechargeReq.getRoutePlanUuid());
            advanceRechargeDTO.setBizLineFlag(rechargeReq.getBizLineFlag());
            if (rechargeReq.getRechargePayType() == EnumPayOrderChannel.UNION_JS_PAY.getCode()) {
                advanceRechargeDTO.setAuthCode(rechargeReq.getCode());
                advanceRechargeDTO.setSubSource(rechargeReq.getSubSource());
            }

            advanceRechargeDTO.setTerminal(PayUtils.getTerminal(rechargeReq.getGrayVersion(), -1));

            advanceRechargeDTO.setGrayVersion(rechargeReq.getGrayVersion());
            advanceRechargeDTO.setGrayBuild(rechargeReq.getGrayBuild());
            advanceRechargeDTO.setUserIp(rechargeReq.getUserIp());

            Response<RechargePayBo> rechargePayResponse = routePayService.advanceRecharge(advanceRechargeDTO);
            if (!rechargePayResponse.isSuccess()) {
                BizExceptionUtil.create(rechargePayResponse.getMsg(), rechargePayResponse.getCode());
            }
            RechargePayBo rechargePayBo = rechargePayResponse.getData();
            //返回对象
            rechargePayVo = packageRechargeVo(rechargePayBo);
            packageMyNanJinPay(rechargeReq, rechargePayBo, passengerUuid);
            rechargePayVo.setSdk(rechargePayBo.getSdk());
            //记录用户签约免密滑块点击情况
            saveUserSignExtend(rechargeReq, passengerUuid);
        } catch (BizException e) {
            return Response.createError(e.getRes().getMsg(), e.getRes().getCode());
        }
        return Response.createSuccess("获取预付款支付信息成功", rechargePayVo);
    }

    /**
     * 适配我的南京支付
     *
     * @param rechargeReq   routePaymentReq
     * @param rechargePayBo rechargePayBo
     * @param userId        userId
     */
    private void packageMyNanJinPay(RechargeReq rechargeReq, RechargePayBo rechargePayBo, String userId) {
        // 只有渠道是 101- 我的南京  以及 支付方式是2的 才回转换
        if (CommonNumConst.NUM_101 == rechargeReq.getSource()
                && rechargeReq.getRechargePayType() == CommonNumConst.NUM_2) {
            try {
                // 查询待支付金额
                Response<PayOrderDto> payOrder = unifiedPaymentQueryFacade.getPayOrderByOrderNo(userId, rechargePayBo.getOrderNo());
                Integer payAmount = NUM_0;
                if (payOrder.getSuccess() && null != payOrder.getData()) {
                    payAmount = payOrder.getData().getSettleAmount();
                }
                // 组装我的南京需要的参数
                cn.hutool.json.JSONObject wxSdk = JSONUtil.parseObj(rechargePayBo.getSdk());
                String sdk = MY_NJ_SDK_PREFIX + "type=wxpay&sign=" + wxSdk.getStr("sign") + "&params="
                        + secUtils.getEncryptParams(wxSdk, rechargePayBo, payAmount);
                rechargePayBo.setSdk(sdk);
            } catch (Exception e) {
                log.info("PaymentBusiness.packageMyNanJinPay fail!");
            }
        }
    }

    /**
     * 查询出行预付款信息
     *
     * @param rechargeReq rechargeReq
     * @return RoutePrePayDetailDto
     */
    private RoutePrePayDetailDto queryRoutePrePeyInfo(RechargeReq rechargeReq) {
        RouteSerialDto routeDto = new RouteSerialDto();
        routeDto.setPrePayNo(rechargeReq.getPrePayNo());
        routeDto.setRoutePlanId(rechargeReq.getRoutePlanUuid());
        Response<RoutePrePayDetailDto> routePrePayRes =
                routeReadRest.getRoutePrePayDetail(rechargeReq.getPrePayNo(), rechargeReq.getRoutePlanUuid());

        if (null != routePrePayRes && routePrePayRes.isSuccess() && null != routePrePayRes.getData()) {
            return routePrePayRes.getData();
        }
        return null;
    }

    /**
     * 封装返回调通
     *
     * @param rechargePayBo 支付返回信息
     * @return RechargePayVo
     */
    private RechargePayVo packageRechargeVo(RechargePayBo rechargePayBo) {
        RechargePayVo rechargePayVo =
                new RechargePayVo(rechargePayBo.getPayType(), rechargePayBo.isNoSecret(), rechargePayBo.getSdk());
        rechargePayVo.setSettlementID(rechargePayBo.getSettlementId());
        rechargePayVo.setCmbMiniAppId(rechargePayBo.getCmbMiniAppId());
        // 支付宝聚合支付需要还原支付渠道
        if (null != rechargePayVo.getPayType()
                && EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode() == rechargePayBo.getPayType()) {
            rechargePayVo.setPayType(EnumPayOrderChannel.ALIPAY.getCode());
        }
        // 只有 203 标识灰度版本
        if (EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode() == rechargePayBo.getPayType()
                && STR_203.equals(rechargePayBo.getCode())) {
            //微信聚合支付跳转微信小程序有额外的落地页 需要在sdk上额外拼装参数
            try {
                String sdk = rechargePayVo.getSdk() + "%26scene%3D" + "ROUTE_PAY_PRE";
                rechargePayVo.setSdk(sdk);
                rechargePayVo.setCmbMiniAppId(switchConfig.getCmbAggT3MiniAppId());

            } catch (Exception e) {
                //查询出行异常则不作任何处理
                log.error("查询出行行程信息异常.cause:{}", ExceptionUtils.getFullStackTrace(e));
            }

        }
        // 抖音小程序 需返回抖音单号给端上
        if (null != rechargePayVo.getPayType()
                && EnumPayOrderChannel.DOUYIN_MINI_PAY.getCode() == rechargePayBo.getPayType()) {
            rechargePayVo.setOrderNo(rechargePayBo.getOrderNo());
        }
        return rechargePayVo;
    }


    /**
     * 支付情况查询
     *
     * @param queryReq 查询请求
     * @return {@link Response}
     */
    public Response prePayStatusQuery(PayStatusQueryReqV2 queryReq) {
        //6：预付款
        queryReq.setType(CommonNumConst.NUM_6);
        //支付测用orderUuid表示的结算uuid
        queryReq.setOrderUuid(queryReq.getSettlementID());
        UnifiedDto unifiedDto = new UnifiedDto();
        unifiedDto.setSceneType(EnumSceneType.QUERY_TRADE_RESULT_UNIFIED.getCode());
        unifiedDto.setExtendParam(JSON.toJSONString(queryReq));
        return unifiedService.handle(unifiedDto);
    }

    /**
     * 支付情况查询
     *
     * @param req 查询请求
     * @return {@link Response}
     */
    public Response<PrepaymentStatusResVo> prePayStatusQueryByOrderUuid(PayStatusQueryReqV2 req) {
        PrepaymentStatusResVo resVo = new PrepaymentStatusResVo();
        //需要详情信息
        req.setNeedDetail("true");
        UnifiedDto unifiedDto = new UnifiedDto();
        unifiedDto.setSceneType(EnumSceneType.QUERY_TRADE_RESULT.getCode());
        unifiedDto.setExtendParam(JSON.toJSONString(req));
        Response response = unifiedService.handle(unifiedDto);
        if (null == response || !response.isSuccess() || null == response.getData()) {
            return Response.createError(ResultErrorEnum.GET_RECHARGE_IFNO_ERROR);
        }
        PrepaymentOrderDto prepaymentOrderDto = JSON.parseObject((String) response.getData(), PrepaymentOrderDto.class);
        resVo.setOrderUuid(req.getOrderUuid());
        Integer payStatus = StringUtils.isNotBlank(prepaymentOrderDto.getTradeStatus())
                ? Integer.valueOf(prepaymentOrderDto.getTradeStatus()) : NumberConstants.NUMBER_0;
        resVo.setPayStatus(payStatus);
        //前端文案
        if (NumberConstants.NUMBER_2 == payStatus && isNoSecretChannel(prepaymentOrderDto)) {
            resVo.setPaySucessMsg(switchConfig.getPaySucessMsg());
        }
        resVo.setPreMsg(switchConfig.getPreMsg());
        resVo.setIsOpenNoSecret(isSignThirdChannel(req.getUserIds()));
        return Response.createSuccess(resVo);
    }

    /**
     * 预付款支付状态查询（变更行程）
     *
     * @param routePlanUuid 路线计划UUID
     * @return {@link Response}
     */
    public Response checkRoutePlanEdit(String routePlanUuid) {
        Response response = routeBusiness.getEditRoutePointByCache(routePlanUuid);
        if (!response.isSuccess()) {
            if (!BussiEnum.ERROR.getCode().equals(response.getErrCode())) {
                return response;
            }
            return Response.createError("失败", ResultErrorEnum.UNIFY_ERROR_CODE.getCode());
        }
        if (Objects.isNull(response.getData())) {
            return Response.createError("失败", ResultErrorEnum.UNIFY_ERROR_CODE.getCode());
        }
        return Response.createSuccess("成功");
    }

    /**
     * 是否签约三方渠道（去掉芝麻分）
     *
     * @param userId String
     * @return boolean
     */
    private boolean isSignThirdChannel(String userId) {
        //是否签约三方渠道
        boolean hasSignThirdChannel = false;
        AccountSignParamDto queryDto = new AccountSignParamDto();
        queryDto.setUserId(userId);
        queryDto.setAccountType(PayAccountTypeEnum.PASSENGER_TYPE.getType());
        queryDto.setDensityFree(AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode());
        Response<List<AccountSignDtoV2>> res = accountSignService.getSignContractList(queryDto);
        if (null == res || !res.isSuccess() || CollectionUtils.isEmpty(res.getData())) {
            return hasSignThirdChannel;
        }

        for (AccountSignDtoV2 accountSignDtoV2 : res.getData()) {
            if (null != accountSignDtoV2.getPayChannel() && isThirdChannel(accountSignDtoV2.getPayChannel())) {
                hasSignThirdChannel = true;
                break;
            }
        }
        return hasSignThirdChannel;
    }

    /**
     * 是否是免密渠道支付的
     *
     * @param prepaymentOrderDto PrepaymentOrderDto
     * @return boolean
     */
    private boolean isNoSecretChannel(PrepaymentOrderDto prepaymentOrderDto) {
        boolean isNoSecret = false;
        try {
            JSONObject detailJson = JSONObject.parseObject(prepaymentOrderDto.getDetail());
            JSONObject payStringJson = JSONObject.parseObject(detailJson.getString("pay"));
            if (null == payStringJson) {
                return isNoSecret;
            }
            String detail = payStringJson.getString("detail");
            if (StringUtils.isBlank(detail)) {
                return isNoSecret;
            }
            List<PayTradeDetailDto> payTradeDetailDtos = JSONObject.parseArray(detail, PayTradeDetailDto.class);
            if (CollectionUtils.isEmpty(payTradeDetailDtos)) {
                return isNoSecret;
            }
            for (PayTradeDetailDto payTradeDetailDto : payTradeDetailDtos) {
                if (null != payTradeDetailDto.getPayOrderSubAccount()
                        && (EnumPayOrderSubAccount.WX_NOPWD_PAY.getCode() == payTradeDetailDto.getPayOrderSubAccount()
                        || EnumPayOrderSubAccount.NETCOM_NOPWD_PAY.getCode() == payTradeDetailDto.getPayOrderSubAccount()
                        || EnumPayOrderSubAccount.UNION_NOPWD_PAY.getCode() == payTradeDetailDto.getPayOrderSubAccount()
                        || EnumPayOrderSubAccount.ALI_NOPWD_PAY.getCode() == payTradeDetailDto.getPayOrderSubAccount())) {
                    isNoSecret = true;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("parse josn error", e);
        }
        return isNoSecret;
    }

    /**
     * 是否是三方渠道
     *
     * @param payChannel int
     * @return boolean
     */
    private boolean isThirdChannel(int payChannel) {
        if (payChannel == EnumPayOrderChannel.ALIPAY.getCode()
                || payChannel == EnumPayOrderChannel.WEIXIN.getCode()
                || payChannel == EnumPayOrderChannel.NETCOM.getCode()
                || payChannel == EnumPayOrderChannel.UNIONPAY.getCode()) {
            return true;
        }
        return false;
    }

    /**
     * 记录用户签约免密滑块点击情况
     *
     * @param rechargeReq rechargeReq
     * @param userId      userId
     */
    private void saveUserSignExtend(RechargeReq rechargeReq, String userId) {
        if (null != rechargeReq.getSelWxSign()) {
            SignButtonVo vo = new SignButtonVo();
            vo.setSelSign(rechargeReq.getSelWxSign());
            vo.setOperateTime(new Date());

            JSONObject json = new JSONObject();
            json.put("identityType", "1");
            json.put("userId", userId);
            json.put("type", PassengerConstants.WXALREADYSIGN);
            json.put("info", vo);
            passengerFeignClient.saveUserExtend(json);
        }
    }
}
