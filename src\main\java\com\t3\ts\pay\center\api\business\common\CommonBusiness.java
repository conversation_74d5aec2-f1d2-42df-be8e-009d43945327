package com.t3.ts.pay.center.api.business.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.account.center.dto.AccountSignChannelDto;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.channelmgr.center.constants.TerminalEnum;
import com.t3.ts.channelmgr.center.dto.PayChannelReqDto;
import com.t3.ts.channelmgr.center.dto.PayChannelResDto;
import com.t3.ts.channelmgr.center.service.PayChannelTakeOverService;
import com.t3.ts.passenger.center.dto.res.secretfree.SfGuidanceVo;
import com.t3.ts.pay.center.api.business.SecretFreeBusiness;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.config.valueconfig.AliPayPointsConfig;
import com.t3.ts.pay.center.api.config.valueconfig.WechatPayPointsConfig;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.common.ChannelListReq;
import com.t3.ts.pay.center.api.dto.common.SfGuidance;
import com.t3.ts.pay.center.api.dto.route.RouteBasicDto;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.sign.SignButtonVo;
import com.t3.ts.pay.center.api.dto.vo.ChannelCouponNumVo;
import com.t3.ts.pay.center.api.dto.vo.ChannelNumVo;
import com.t3.ts.pay.center.api.dto.vo.DataListVo;
import com.t3.ts.pay.center.api.dto.vo.PayWayVo;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.rest.PassengerFeignClient;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.center.api.util.AccountUtils;
import com.t3.ts.pay.center.api.util.PayAbTestUtil;
import com.t3.ts.pay.center.api.util.ThreadPoolUtil;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.common.http.CityCode;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.UnifiedDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.enums.RoutePlanTypeEntEnum;
import com.t3.ts.settlement.finance.enums.PayChannelEnum;
import com.t3.ts.travel.config.operate.service.CityService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * 通用
 *
 * <AUTHOR>
 * @date 2019-04-28
 */
@Component
@Slf4j
public class CommonBusiness {


    @DubboReference(timeout = 1000)
    private PayChannelTakeOverService payChannelTakeOverService;

    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQuery;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private CityService cityService;
    @DubboReference
    private UnifiedService unifiedService;
    @Autowired
    private SecretFreeBusiness secretFreeBusiness;
    @Autowired
    private WechatPayPointsConfig wechatPayPointsConfig;
    @Autowired
    private AliPayPointsConfig aliPayPointsConfig;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;
    @DubboReference
    private AccountSignService accountSignService;
    @Autowired
    private PassengerFeignClient passengerFeignClient;

    @Autowired
    private PayAbTestUtil payAbTestUtil;
    @Autowired
    private MarketingRest marketingRest;

    /**
     * 获取乘客支付类型列表
     *
     * @param payChannelReqDto 是否支持云闪付
     * @return {@link Response}
     */
    public Response channelList(PayChannelReqDto payChannelReqDto) {
        boolean isQuickPass = payChannelReqDto.isQuickPass();
        Response<List<PayChannelResDto>> payChannelListRes = payChannelTakeOverService
                .getPayChannelListForApp(payChannelReqDto);
        if (!payChannelListRes.isSuccess() || CollectionUtils.isEmpty(payChannelListRes.getData())) {
            return Response.createError(ResultErrorEnum.CHANNEL_LIST_ERROR);
        }
        List<PayWayVo> payWayVolist = Lists.newArrayList();
        List<PayWayVo> recommendList = Lists.newArrayList();
        payChannelListRes.getData().forEach(payWayDto -> {
            boolean flag = (EnumPayOrderChannel.UNION_QUICK_PASS_PAY.getCode() == payWayDto.getPayChannel()
                    && !isQuickPass)
                    || (EnumPayOrderChannel.UNIONPAY.getCode() == payWayDto.getPayChannel() && isQuickPass);
            if (flag) {
                return;
            }
            PayWayVo payWayVo = new PayWayVo();
            // 支付渠道标识
            payWayVo.setPayChannel(payWayDto.getPayChannel());
            // 支付渠道名称
            payWayVo.setPayName(payWayDto.getPayName());
            // 是否推荐标识
            payWayVo.setRecommendFlag(payWayDto.getIsRecommend());
            // 优惠文案
            if (!CollectionUtils.isEmpty(payWayDto.getLabelList())) {
                List<String> labelList = Lists.newArrayList();
                payWayDto.getLabelList()
                        .forEach(payChannelLabelResDto -> labelList.add(payChannelLabelResDto.getLabelName()));
                payWayVo.setDiscountTags(labelList);
            }

            int recommendFlag = payWayVo.getRecommendFlag();
            if (NumConstants.NUM_1 == recommendFlag) {
                recommendList.add(payWayVo);
            } else {
                payWayVolist.add(payWayVo);
            }
        });

        List<PayWayVo> allList = Lists.newArrayList();
        allList.addAll(recommendList);
        allList.addAll(payWayVolist);

        return Response.createSuccess(new DataListVo<>(allList));
    }

    /**
     * 是否支持云闪付
     *
     * @param userId 用户id
     * @return boolean
     */
    private boolean isSupportQuickPass(String userId) {
        return AccountUtils.switchOpenByHash(userId,
                switchConfig.getQuickPassBlackList(), switchConfig.getQuickPassWhiteList(),
                switchConfig.getQuickPassPercent());
    }


    /**
     * 获取乘客支付类型列表
     *
     * @param channelListReq 频道列表请求
     * @param userUid        用户uid
     * @return {@link Response}
     * @throws ExecutionException   执行异常
     * @throws InterruptedException 中断异常
     */
    public Response<DataListVo> channelList(ChannelListReq channelListReq, String userUid) throws
            ExecutionException,
            InterruptedException {
        PayChannelReqDto payChannelReqDto = new PayChannelReqDto();
        //是否支持云闪付
        boolean isQuickPass = null != channelListReq.getQuickPass() && channelListReq.getQuickPass()
                && isSupportQuickPass(userUid);
        log.info("channelList isQuickPass:{}", isQuickPass);
        payChannelReqDto.setQuickPass(isQuickPass);
        String cityCode = StringUtils.isNotBlank(channelListReq.getAdCode())
                ? CityCode.convertCode(channelListReq.getAdCode()) : String.valueOf(NumConstant.NUM_0);
        payChannelReqDto.setCityCode(CityCode.convertCode(cityCode));
        if (StringUtils.isBlank(channelListReq.getOrderUuid())) {
            return channelList(payChannelReqDto);
        }
        if (StringUtils.isBlank(userUid)) {
            return Response.createError(ResultErrorEnum.LOGIN_TIMEOUT);
        }
        Integer lastPayType = channelListReq.getLastPayType();

        channelListReq.setUserId(userUid);
        channelListReq.setJourneyId(channelListReq.getOrderUuid());
        // 查询已签约列表
        List<Integer> signSuccessList = querySignSuccessList(userUid);
        // 支付宝签约情况查询
        PayWayVo aliWayVo = formatAliPoints(userUid, signSuccessList);
        payChannelReqDto.setAliPayAndSign(Boolean.TRUE.equals(aliWayVo.getShowAliPayAndSign())
                ? NumConstant.NUM_1 : NumConstant.NUM_0);
        payChannelReqDto.setOtherPayAndSign(NumConstant.NUM_0);
        // 微信支付分相关字段返回
        PayWayVo wxWayVo = formatWechatScore(channelListReq, userUid, signSuccessList);
        payChannelReqDto.setWxPayAndSign(Boolean.TRUE.equals(wxWayVo.getShowNoPassword())
                ? NumConstant.NUM_1 : NumConstant.NUM_0);
        // 一网通签约情况查询
        PayWayVo netComWayVo = formatNetComPoints(signSuccessList, channelListReq.getHasNetComApp());
        payChannelReqDto.setNetComPayAndSign(Boolean.TRUE.equals(netComWayVo.getShowNoPassword())
                ? NumConstant.NUM_1 : NumConstant.NUM_0);

        Future<Response<List<PayChannelResDto>>> getPayChannelListTask = ThreadPoolUtil.getInstance()
                .execAsync(getPayChannelList(payChannelReqDto));
        Future<Response<Boolean>> payDeskWeChatPayAvailableTask = ThreadPoolUtil.getInstance().execAsync(
                payDeskWeChatPayAvailable(userUid, channelListReq.getOrderUuid()));

        Response<List<PayChannelResDto>> payChannelListRes = getPayChannelListTask.get();
        if (!payChannelListRes.isSuccess() || CollectionUtils.isEmpty(payChannelListRes.getData())) {
            return Response.createError(ResultErrorEnum.CHANNEL_LIST_ERROR);
        }
        Response<Boolean> isWxOpenResp = payDeskWeChatPayAvailableTask.get();
        if (!isWxOpenResp.isSuccess() || Objects.isNull(isWxOpenResp.getData())) {
            return Response.createError(ResultErrorEnum.WX_CAN_USE_ERROR);
        }

        Boolean isWxOpen = isWxOpenResp.getData();
        // 需要置顶的支付方式
        AtomicReference<PayWayVo> firstPayWay = new AtomicReference<>();
        List<PayWayVo> payWayVolist = Lists.newArrayList();
        List<PayWayVo> recommendList = Lists.newArrayList();
        payChannelListRes.getData().stream().
                filter(payWayDto -> PayChannelEnum.WE_CHAT.getCode() != payWayDto.getPayChannel()
                        || !Boolean.FALSE.equals(isWxOpen)).forEach(payWayDto -> {
            boolean flag = (EnumPayOrderChannel.UNION_QUICK_PASS_PAY.getCode() == payWayDto.getPayChannel()
                    && !isQuickPass)
                    || (EnumPayOrderChannel.UNIONPAY.getCode() == payWayDto.getPayChannel() && isQuickPass);
            if (flag) {
                return;
            }
            PayWayVo payWayVo = new PayWayVo();
            // 支付渠道名称
            payWayVo.setPayName(payWayDto.getPayName());
            // 支付渠道标识
            payWayVo.setPayChannel(payWayDto.getPayChannel());
            // 是否推荐标识
            payWayVo.setRecommendFlag(payWayDto.getIsRecommend());
            payWayVo.setUseNewWxSign(channelListReq.getUseNewWxSign());
            // 预付不需要这个标识
            if (Boolean.TRUE.equals(channelListReq.getPrePay())) {
                payWayVo.setUseNewWxSign(true);
            }
            // 优惠文案
            if (!CollectionUtils.isEmpty(payWayDto.getLabelList())) {
                List<String> labelList = Lists.newArrayList();
                payWayVo.setDiscountTags(labelList);
                payWayDto.getLabelList().forEach(labelResDto -> {
                    // 需要在乘客app上面展示的优惠文案
                    if (StringUtils.isBlank(labelResDto.getTerminalType())
                            || labelResDto.getTerminalType().contains(TerminalEnum.PASSENGER_APP.getType())) {
                        labelList.add(labelResDto.getLabelName());
                    }
                });
            }
            // 微信支付分相关字段返回
            if (Integer.valueOf(PayChannelEnum.WE_CHAT.getCode()).equals(payWayDto.getPayChannel())) {
                payWayVo.setShowNoPassword(wxWayVo.getShowNoPassword());
                payWayVo.setNoPasswordIcon(wxWayVo.getNoPasswordIcon());
                payWayVo.setNoPasswordTitle(wxWayVo.getNoPasswordTitle());
                payWayVo.setNoPasswordSubTitle(wxWayVo.getNoPasswordSubTitle());
            }

            // 支付宝签约情况
            if (Integer.valueOf(PayChannelEnum.ALI_PAY.getCode()).equals(payWayDto.getPayChannel())) {
                payWayVo.setShowAliPayAndSign(aliWayVo.getShowAliPayAndSign());
                payWayVo.setNoPasswordIcon(aliWayVo.getNoPasswordIcon());
                payWayVo.setNoPasswordTitle(aliWayVo.getNoPasswordTitle());
                payWayVo.setNoPasswordSubTitle(aliWayVo.getNoPasswordSubTitle());
            }
            // 一网通签约情况
            if (Integer.valueOf(PayChannelEnum.NET_COM.getCode()).equals(payWayDto.getPayChannel())) {
                payWayVo.setShowNoPassword(netComWayVo.getShowNoPassword());
                payWayVo.setNoPasswordIcon(netComWayVo.getNoPasswordIcon());
                payWayVo.setNoPasswordTitle(netComWayVo.getNoPasswordTitle());
                payWayVo.setNoPasswordSubTitle(netComWayVo.getNoPasswordSubTitle());
            }

            if (null != lastPayType && lastPayType.equals(payWayVo.getPayChannel())) {
                firstPayWay.set(payWayVo);
            } else {
                int recommendFlag = payWayVo.getRecommendFlag();
                if (NumConstants.NUM_1 == recommendFlag) {
                    recommendList.add(payWayVo);
                } else {
                    payWayVolist.add(payWayVo);
                }
            }
        });

        List<PayWayVo> allList = Lists.newArrayList();
        if (null != firstPayWay.get()) {
            allList.add(firstPayWay.get());
        }
        allList.addAll(recommendList);
        allList.addAll(payWayVolist);

        // V3版本的支付列表 查询渠道券信息
        Map<String, ChannelNumVo> channelNumVoMap = getChannelPreferenceInfo(channelListReq);
        // 获取渠道支付优惠
        if (Objects.nonNull(channelNumVoMap)) {
            Map<String, ChannelNumVo> finalChannelNumVoMap = channelNumVoMap;
            allList.forEach(e -> {
                ChannelNumVo channelNumVo = finalChannelNumVoMap.get(String.valueOf(e.getPayChannel()));
                if (Objects.nonNull(channelNumVo) && StringUtils.isNotEmpty(channelNumVo.getNum()) && !StringUtils
                        .equals(channelNumVo.getNum(), NumConstants.STR_0)) {
                    e.setChannelPreferenceFlag(Boolean.TRUE);
                    e.setChannelPreferenceMsg("有" + channelNumVo.getNum() + "张" + e.getPayName() + "支付限定优惠券可使用");
                }
            });
        }
        DataListVo<Object> dataListVo = new DataListVo(allList);
        return Response.createSuccess(dataListVo);
    }

    /**
     * 获取微信支付分引导情况
     *
     * @param channelListReq channelListReq
     * @param userUid        userUid
     * @return PayWayVo
     */
    private PayWayVo formatWechatScore(ChannelListReq channelListReq, String userUid, List<Integer> signSuccessList) {
        PayWayVo wxWayVo = new PayWayVo();
        String amapCityCode = channelListReq.getCityCode();
        String adCode = channelListReq.getAdCode();
        wxWayVo.setUseNewWxSign(channelListReq.getUseNewWxSign());
        if (StringUtils.isNotBlank(amapCityCode) || StringUtils.isNotBlank(adCode)) {
            //获取用户群组
            List<String> userGroup = marketingRest.getUserGroupByPassengerUuid(userUid);

            SfGuidance sfGuidance = new SfGuidance();
            // 城市code转换
            sfGuidance.setCityCode(amapCityCode);
            sfGuidance.setAdCode(adCode);
            sfGuidance.setGroupIds(userGroup);
            sfGuidance.setPassengerId(userUid);
            if (StringUtils.isNotBlank(channelListReq.getExpandBizLine())) {
                sfGuidance.setProductLine(channelListReq.getExpandBizLine());
            } else {
                sfGuidance.setProductLine(channelListReq.getProductLine());
            }
            sfGuidance.setTriggerNode("4");
            sfGuidance.setPhone(RequestContextHelper.getPassengerMobile());
            log.info("channelList SfGuidance:{}", sfGuidance);
            sfGuidance.setPrePay(channelListReq.getPrePay());
            formatWechatPointsSfGuidance(wxWayVo, sfGuidance);
        } else {
            formatWechatPoints(wxWayVo, signSuccessList);
        }
        rebuildShowNoPassword(wxWayVo, userUid);
        return wxWayVo;
    }

    /**
     * 重建ShowNoPassword
     *
     * @param payWayVo 支付方式签证官
     * @param userUid  用户uid
     */
    private void rebuildShowNoPassword(PayWayVo payWayVo, String userUid) {
        Boolean useNewWxSign = Objects.nonNull(payWayVo.getUseNewWxSign()) && payWayVo.getUseNewWxSign();
        if (payWayVo.getShowNoPassword() && useNewWxSign) {
            SignButtonVo signButtonVo = getUserExtend(userUid);
            // 未签约+未取消：展示
            if (null == signButtonVo || null == signButtonVo.getSelSign()) {
                payWayVo.setShowNoPassword(Boolean.TRUE);
                return;
            }
            // 未签约+已取消：不展示
            payWayVo.setShowNoPassword(NumConstant.NUM_0 != signButtonVo.getSelSign());
        }
    }

    /**
     * 查询已签约列表
     * @param userUid
     * @return
     */
    public List<Integer> querySignSuccessList(String userUid) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(userUid);
            param.setDensityFree(AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode());
            Response<List<AccountSignChannelDto>> response = accountSignService.getSignChannelStatus(param);
            if (response.getSuccess() && !CollectionUtils.isEmpty(response.getData())) {
                return response.getData().stream().filter(item -> ObjectUtil.equal(item.getDensityFree(),
                                AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode()))
                        .map(AccountSignChannelDto::getPayChannel)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("SignService.querySignList fail!{}", ExceptionUtil.getMessage(e));
        }
        return new ArrayList<>();
    }

    /**
     * 支付宝返回字段补充
     *
     * @param userUid 用户uuid
     */
    private PayWayVo formatAliPoints(String userUid, List<Integer> signSuccessList) {
        PayWayVo aliWayVo = new PayWayVo();
        Boolean enabled = aliPayPointsConfig.getEnabled();
        if (!enabled) {
            return aliWayVo;
        }
        boolean abTest = payAbTestUtil.aliPayAndSignAbTest(userUid);
        log.info("formatAliPoints.getAliPayAndSignParam, userId:{},abTest:{}", userUid, abTest);
        if (!abTest) {
            return aliWayVo;
        }
        if (!signSuccessList.contains(PayChannelEnum.ALI_PAY.getCode())) {
            //未开通的情况 返回相关字段 端上展示支付宝及图标
            aliWayVo.setShowAliPayAndSign(true);
            aliWayVo.setNoPasswordIcon(aliPayPointsConfig.getBtnIcon());
            aliWayVo.setNoPasswordTitle(aliPayPointsConfig.getBtnTitle());
            aliWayVo.setNoPasswordSubTitle(aliPayPointsConfig.getBtnSubTitle());
        }
        return aliWayVo;
    }


    /**
     * 微信积分返回字段补充
     *
     * @param payWayVo 返回对象vo
     * @param signSuccessList  signSuccessList
     */
    private void formatWechatPoints(PayWayVo payWayVo, List<Integer> signSuccessList) {
        Boolean enabled = wechatPayPointsConfig.getEnabled();
        if (!enabled) {
            return;
        }
        if (!signSuccessList.contains(PayChannelEnum.WE_CHAT.getCode())) {
            //未开通的情况 返回相关字段 端上展示微信支付分相关文案及图标
            payWayVo.setShowNoPassword(true);
            payWayVo.setNoPasswordIcon(wechatPayPointsConfig.getBtnIcon());
            payWayVo.setNoPasswordTitle(wechatPayPointsConfig.getBtnTitle());
            payWayVo.setNoPasswordSubTitle(wechatPayPointsConfig.getBtnSubTitle());
        }
    }

    /**
     * 一网通返回字段补充
     *
     * @param signSuccessList signSuccessList
     */
    private PayWayVo formatNetComPoints(List<Integer> signSuccessList, Boolean hasNetComApp) {
        PayWayVo netComWayVo = new PayWayVo();
        Boolean enabled = switchConfig.getNetComEnabled();
        if (!enabled || Boolean.FALSE.equals(hasNetComApp)) {
            return netComWayVo;
        }
        if (!signSuccessList.contains(PayChannelEnum.NET_COM.getCode())) {
            //未开通的情况 返回相关字段 端上展示一网通及图标
            netComWayVo.setShowNoPassword(true);
            netComWayVo.setNoPasswordIcon(switchConfig.getNetComBtnIcon());
            netComWayVo.setNoPasswordTitle(switchConfig.getNetComBtnTitle());
            netComWayVo.setNoPasswordSubTitle(switchConfig.getNetComBtnSubTitle());
        }
        return netComWayVo;
    }
    /**
     * 获取用户扩展
     *
     * @param userUid 用户uid
     * @return {@link SignButtonVo}
     */
    private SignButtonVo getUserExtend(String userUid) {
        //获取用户签约点击情况
        JSONObject json = new JSONObject();
        json.put("identityType", "1");
        json.put("userId", userUid);
        json.put("typeList", Collections.singletonList(PassengerConstants.WXALREADYSIGN));
        JSONObject signObj = passengerFeignClient.getUserExtend(json);
        return Objects.isNull(signObj) ? null
                : JSONObject.parseObject(signObj.getString(PassengerConstants.WXALREADYSIGN), SignButtonVo.class);
    }

    /**
     * 获取免密引导配置
     *
     * @param payWayVo   参数
     * @param sfGuidance 参数
     */
    private void formatWechatPointsSfGuidance(PayWayVo payWayVo, SfGuidance sfGuidance) {
        Response<SfGuidanceVo> sfGuidanceVoResponse = secretFreeBusiness.secretFreeGuidanceDetail(sfGuidance);
        if (sfGuidanceVoResponse.isSuccess() && Objects.nonNull(sfGuidanceVoResponse.getData())
                && sfGuidanceVoResponse.getData().getShowFlag().equals(Boolean.TRUE)) {
            payWayVo.setShowNoPassword(true);
            payWayVo.setNoPasswordIcon(wechatPayPointsConfig.getBtnIcon());
            payWayVo.setNoPasswordTitle(wechatPayPointsConfig.getBtnTitle());
            payWayVo.setNoPasswordSubTitle(wechatPayPointsConfig.getBtnSubTitle());
        }
    }


    /**
     * 查询支付渠道优惠信息
     *
     * @param channelListReq 频道列表请求
     * @return {@link Map<String, ChannelNumVo>}
     */
    private Map<String, ChannelNumVo> getChannelPreferenceInfo(ChannelListReq channelListReq) {
        log.info("getChannelPreferenceInfo param:{}", channelListReq);

        if (null != channelListReq.getPrePay() && channelListReq.getPrePay()) {
            // 预付款收银台不加载渠道券
            return null;
        }

        if (StringUtils.isBlank(channelListReq.getCityCode()) && StringUtils.isBlank(channelListReq.getAdCode())) {
            RouteInfoDto routeInfo = routeDetailClient.getRouteInfoWeak(channelListReq.getOrderUuid());
            if (null != routeInfo) {
                // 过滤企业用车
                if (RoutePlanTypeEntEnum.COMPANY.getKey().equals(routeInfo.getTypeEnt())) {
                    return null;
                }
                String areaCode = routeInfo.getAreaCode();
                if (StringUtils.isNotBlank(areaCode)) {
                    String cityCode = CityCode.convertCode(areaCode);
                    channelListReq.setCityCode(cityCode);
                }
            }
        }
        // 优先使用
        if (StringUtils.isNotBlank(channelListReq.getAdCode())) {
            channelListReq.setCityCode(CityCode.convertCode(channelListReq.getAdCode()));
        }

        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.pay.query.coupon.channelCouponNum");
        dto.setExtendParam(JSONObject.toJSONString(channelListReq));
        Response response = unifiedService.handle(dto);
        if (Objects.nonNull(response) && Objects.nonNull(response.getData())) {
            ChannelCouponNumVo couponNumVo = JSON
                    .parseObject((String) response.getData(), ChannelCouponNumVo.class);
            if (Objects.nonNull(couponNumVo) && CollectionUtil.isNotEmpty(couponNumVo.getDetail())) {
                List<ChannelNumVo> detail = couponNumVo.getDetail();
                Map<String, ChannelNumVo> collect = detail.stream().collect(
                        Collectors.toMap(ChannelNumVo::getChannel, e -> e));
                return collect;
            }
        }
        return null;
    }


    /**
     * 判断微信支付渠道是否可用
     *
     * @param userUid   用户uid
     * @param orderUuid 订单uuid
     * @return {@link Callable<Response<Boolean>>}
     */
    private Callable<Response<Boolean>> payDeskWeChatPayAvailable(String userUid, String orderUuid) {
        return () -> unifiedPaymentQuery.payDeskWeChatPayAvailable(userUid, orderUuid);
    }


    /**
     * 获取所有支付渠道
     *
     * @param payChannelReqDto payChannelReqDto
     * @return {@link Callable <Response<List<PayChannelResDto>>>}
     */
    private Callable<Response<List<PayChannelResDto>>> getPayChannelList(PayChannelReqDto payChannelReqDto) {
        return () -> payChannelTakeOverService.getPayChannelListForApp(payChannelReqDto);
    }

}
