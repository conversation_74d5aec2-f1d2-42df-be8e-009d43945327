package com.t3.ts.pay.center.api.config;

import com.t3.ts.pay.center.api.config.interceptor.HttpTraceLogInterceptor;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PayCenterApiWebConfigTest.java
 * @Description TODO
 * @createTime 2020年11月24日 19:19:00
 */
public class PayCenterApiWebConfigTest {
    @Mock
    HttpTraceLogInterceptor httpTraceLogInterceptor;
    @InjectMocks
    PayCenterApiWebConfig payCenterApiWebConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAddInterceptors() throws Exception {
        payCenterApiWebConfig.addInterceptors(null);
    }
}

//Generated with love by Test<PERSON>e :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme