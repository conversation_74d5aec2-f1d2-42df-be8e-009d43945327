package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/22 14:07
 * @des 1.0
 */

@Data
public class PayItem implements Comparable<PayItem> {

    public PayItem(String itemTitle, String itemFee, Boolean itemColorFlag, int sort, Object extendData) {
        this.itemTitle = itemTitle;
        this.itemFee = itemFee;
        this.itemColorFlag = itemColorFlag;
        this.sort = sort;
        this.extendData = extendData;
    }

    public PayItem(String itemTitle, String itemFee, Boolean itemColorFlag, Integer itemType, int sort,
                   Object extendData) {
        this.itemTitle = itemTitle;
        this.itemFee = itemFee;
        this.itemColorFlag = itemColorFlag;
        this.itemType = itemType;
        this.sort = sort;
        this.extendData = extendData;
    }

    // 支付项标题
    private String itemTitle;

    // 副标题
    private String itemSubTitle;

    // 金额
    private String itemFee;

    // 是否加深颜色显示
    private Boolean itemColorFlag;

    // 支付类型  coupon:表示优惠券类型，unWarriedArrive: 表示省心打支付类型
    private Integer itemType;

    // 排序
    private int sort;

    // 扩展
    private Object extendData;

    @Override
    public int compareTo(PayItem o) {
        return this.getSort() - o.getSort();
    }
}
