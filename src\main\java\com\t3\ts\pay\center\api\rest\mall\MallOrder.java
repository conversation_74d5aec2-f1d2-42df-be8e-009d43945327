package com.t3.ts.pay.center.api.rest.mall;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MallOrder.java
 * @createTime 2021年07月14日 14:58:00
 */
@Data
@Builder
public class MallOrder implements Serializable {

    private static final long serialVersionUID = 4281165150058478455L;

    /**
     * 购买数量 等于 1 //必填
     */
    private Integer buyNum;

    /**
     * "cityCode:""//城市编码
     */
    private String cityCode;

    /**
     * "skuCode"：""//商品sku //必填
     */
    private String skuCode;

    /**
     * skuUuid
     */
    private String skuUuid;

    /**
     * "useFlag": 1 //是否使用 1: 使用 2:未使用 couponId 不为空时，必填
     */
    private Integer useFlag;

    /**
     * 使用优惠券的id
     */
    private String couponId;

    /**
     * 订单时间 couponId 不为空时，必填
     */
    private String orderTime;

    /**
     * 来源渠道 1：APP 2：微信
     */
    private Integer source;

    /**
     * 业务类型 3：搭售券
     */
    private Integer bizType;

    /**
     * 乘客信息
     */
    private PassengerInfoDTO passengerInfoDTO;

    /**
     * 新老标识
     */
    private String sellType;


}
