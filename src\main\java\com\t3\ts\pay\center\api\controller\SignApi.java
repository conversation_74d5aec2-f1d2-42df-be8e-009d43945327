package com.t3.ts.pay.center.api.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.t3.ts.account.center.dto.AccountSignDtoV2;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.pay.center.api.business.SecretFreeBusiness;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.common.SfGuidance;
import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.PaySignReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.pay.center.api.enums.SignTerminalEnum;
import com.t3.ts.pay.center.api.exception.BizExceptionEnum;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.service.SignService;
import com.t3.ts.pay.center.api.util.NetworkUtil;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.result.Response;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/pay/v2/sign")
public class SignApi extends BaseApi {
    @DubboReference
    private AccountSignService accountSignService;
    @Autowired
    private SecretFreeBusiness secretFreeBusiness;
    @Autowired
    private SignService signService;
    @Autowired
    private MarketingRest marketingRest;

    /**
     * 查询某渠道的签约
     *
     * @param dto     参数
     * @param request 参数
     * @return Response
     */
    @PostMapping("/getSignContract")
    @ApiOperation(value = "查询渠道签约", notes = "查询渠道签约")
    public Response getSignContract(@RequestBody AccountSignParamDto dto, HttpServletRequest request) {
        log.info("查询渠道签约.param:{}.", JSON.toJSONString(dto));
        try {
            dto.setUserId(getUserUid(request));
            Response response = accountSignService.getSignContract(dto);
            log.info("查询渠道签约.resp:{}.", JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("查询渠道签约.cause:{}", ExceptionUtils.getStackTrace(e));
            return Response.createError("查询渠道签约失败");
        }
    }

    /**
     * 查询某渠道的签约
     *
     * @param dto     参数
     * @param request 参数
     * @return Response
     */
    @PostMapping("/getAllSignInfo")
    @ApiOperation(value = "查询用户全渠道签约", notes = "查询用户全渠道签约")
    public Response getAllSignInfo(@RequestBody AccountSignParamDto dto, HttpServletRequest request) {
        try {
            dto.setUserId(getUserUid(request));
            return secretFreeBusiness.getAllSignInfo(dto, getGrayBuild(request));
        } catch (Exception e) {
            log.error("查询用户全渠道签约.cause:{}", ExceptionUtils.getStackTrace(e));
            return Response.createError("查询余额签约失败", BizExceptionEnum.SYSTEM_ERROR.getCode());
        }
    }

    /**
     * 保存渠道签约
     *
     * @param dto     参数
     * @param request 参数
     * @return Response
     */
    @PostMapping("/saveSignContract")
    @ApiOperation(value = "保存渠道签约", notes = "保存渠道签约")
    public Response saveSignContract(@RequestBody AccountSignDtoV2 dto, HttpServletRequest request) {
        log.info("保存渠道签约.param:{}.", JSON.toJSONString(dto));
        try {
            dto.setUserId(getUserUid(request));
            Response response = accountSignService.saveSignContract(dto);
            log.info("保存渠道签约.resp:{}.", JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("保存渠道签约.cause:{}", ExceptionUtils.getStackTrace(e));
            return Response.createError("保存渠道签约失败");
        }
    }

    /**
     * 获取无秘密字符串
     *
     * @param secretFreeReq 免密请求
     * @param request       请求
     * @return {@link Response}
     */
    @PostMapping("/pay/getSecretFreeString")
    @ApiOperation(value = "乘客获取开通免密支付串", notes = "乘客获取开通免密支付串")
    public Response<?> getSecretFreeString(@RequestBody SecretFreeReq secretFreeReq, HttpServletRequest request) {
        secretFreeReq.setUserId(getUserUid(request));
        secretFreeReq.setDeviceToken(getDeviceFingerPrintToken(request));
        secretFreeReq.setSignTerminal(getSignTerminal(request));
        secretFreeReq.setIp(NetworkUtil.getIpAddress(request));
        if (StrUtil.isBlank(secretFreeReq.getSecretFreeType())) {
            return Response.createError("参数异常,请检查");
        }
        String grayBuild = getGrayBuild(request);
        // 一网通低于1000版本报错
        if (!PayUtils.compareGrayBuild(grayBuild, NumConstants.STR_1000)
                && NumConstants.STR_3.equals(secretFreeReq.getSecretFreeType()) ) {
            return Response.createError(ResultErrorEnum.SIGN_VERSION_LOW);
        }
        return signService.openSecretFree(secretFreeReq);
    }

    /**
     * 保密保密
     *
     * @param secretFreeReq 免密请求
     * @param request       请求
     * @return {@link Response}
     */
    @PostMapping("/pay/closeSecretFree")
    @ApiOperation(value = "乘客关闭免密支付", notes = "乘客关闭免密支付")
    public Response<?> closeSecretFree(@RequestBody SecretFreeReq secretFreeReq, HttpServletRequest request) {
        secretFreeReq.setUserId(getUserUid(request));
        secretFreeReq.setDeviceToken(getDeviceFingerPrintToken(request));
        secretFreeReq.setSignTerminal(getSignTerminal(request));
        secretFreeReq.setIp(NetworkUtil.getIpAddress(request));
        return signService.closeSecretFree(secretFreeReq);
    }

    /**
     * 获得免密状态
     *
     * @param request 请求
     * @return {@link Response}
     * @throws Exception 例外
     */
    @PostMapping("/pay/getSecretFreeStatus")
    @ApiOperation(value = "乘客获取免密支付状态", notes = "乘客获取免密支付状态")
    public Response<?> querySignStatus(HttpServletRequest request) {
        return signService.querySignStatus(getUserUid(request));
    }

    /**
     * 按通道获取标志状态
     *
     * @param request 请求
     * @param req     绿色
     * @return {@link Response}
     * @throws Exception 例外
     */
    @PostMapping("/pay/getSignStatusByChannel")
    @ApiOperation(value = "单渠道签约结果查询", notes = "单渠道签约结果查询")
    public Response<?> getSignStatusByChannel(HttpServletRequest request, @RequestBody PayChannelReq req) {
        req.setUserId(getUserUid(request));
        return signService.querySignStatusByChannel(req);

    }

    /**
     * 按通道获取签约状态
     *
     * @param request 请求
     * @param req     绿色
     * @return {@link Response}
     * @throws Exception 例外
     */
    @PostMapping("/pay/getThirdSignStatus")
    @ApiOperation(value = "单渠道签约结果查询", notes = "单渠道签约结果查询")
    public Response<?> getThirdSignStatus(HttpServletRequest request, @RequestBody PaySignReq req) {
        req.setUserId(getUserUid(request));
        return signService.getThirdSignStatus(req);

    }

    /**
     * 优先
     *
     * @param secretFreeReq 免密请求
     * @param request       请求
     * @return {@link Response}
     */
    @PostMapping("/pay/priority")
    @ApiOperation(value = "设置或取消优先支付", notes = "设置或取消优先支付")
    public Response<?> priority(@RequestBody SecretFreeReq secretFreeReq, HttpServletRequest request) {
        secretFreeReq.setUserId(getUserUid(request));
        return signService.priority(secretFreeReq);
    }

    /**
     * 查询免密引导配置
     *
     * @param request 请求
     * @param req     绿色
     * @return {@link Response}
     * @throws Exception 例外
     */
    @PostMapping("/secretFreeGuidance/detail")
    @ApiOperation(value = "查询免密引导配置", notes = "查询免密引导配置")
    public Response<?> secretFreeGuidanceDetail(HttpServletRequest request, @RequestBody SfGuidance req) {
        req.setPassengerId(getUserUid(request));
        req.setGroupIds(marketingRest.getUserGroupByPassengerUuid(getUserUid(request)));
        req.setPhone(RequestContextHelper.getPassengerMobile());
        return secretFreeBusiness.secretFreeGuidanceDetail(req);

    }

    /**
     * 获取免密列表
     *
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/pay/getSecretFreeList")
    @ApiOperation(value = "获取免密支付列表", notes = "获取免密支付列表")
    public Response<?> getSecretFreeList(HttpServletRequest request) {
        return signService.querySignList(getUserUid(request));
    }

    /**
     * 获取签约终端
     *
     * @param request 请求头
     * @return String
     */
    private String getSignTerminal(HttpServletRequest request) {
        String grayVersion = getGrayVersion(request);
        String signTerminal = null;
        switch (grayVersion.charAt(0)) {
            case 'W':
                signTerminal = SignTerminalEnum.WECHAT.getCode();
                break;
            case 'P':
                if (grayVersion.startsWith(NumConstants.STRING_LOWERCASE_I)) {
                    signTerminal = SignTerminalEnum.IOS.getCode();
                } else if (grayVersion.startsWith(NumConstants.STRING_LOWERCASE_A)) {
                    signTerminal = SignTerminalEnum.ANDROID.getCode();
                } else if (grayVersion.startsWith(NumConstants.STRING_LOWERCASE_H)) {
                    signTerminal = SignTerminalEnum.HARMONY_OS.getCode();
                }
                break;
            case 'A':
                signTerminal = SignTerminalEnum.ZFB.getCode();
                break;
            case 'H':
                signTerminal = SignTerminalEnum.H5.getCode();
                break;
            default:
                break;
        }
        return signTerminal;
    }
}
