package com.t3.ts.pay.center.api.service.impl.invoice;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.t3.ts.invoice.center.dto.FrequentlyInvoiceHeaderDto;
import com.t3.ts.invoice.center.dto.FrequentlyInvoiceHeaderReq;
import com.t3.ts.invoice.center.dto.PassengerInvoiceHeaderDto;
import com.t3.ts.invoice.center.service.FrequentlyInvoiceHeaderService;
import com.t3.ts.invoice.center.service.PassengerInvoiceService;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.vo.InvoiceHeaderVo;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceHeaderVO;
import com.t3.ts.pay.center.api.enums.InvoiceHeaderTypeEnum;
import com.t3.ts.pay.center.api.service.invoice.InvoiceHeaderService;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_10;

/**
 * @Author: ivy
 * @Date: 2021/10/11 19:02
 * @Description: 常用抬头接口
 */
@Service
public class InvoiceHeaderServiceImpl implements InvoiceHeaderService {

    @DubboReference
    private FrequentlyInvoiceHeaderService frequentlyInvoiceHeaderService;

    @DubboReference
    private PassengerInvoiceService passengerInvoiceService;

    @Resource
    private SwitchConfig switchConfig;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 添加常用发票抬头
     *
     * @param req 入参
     * @return {@link Response}
     */
    @Override
    public Response<?> addInvoiceHeader(AddInvoiceHeaderReq req) {
        Response<?> check = checkParamOfAddInvoiceHeader(req);
        if (!check.isSuccess()) {
            return Response.createError(check.getMsg(), ResultErrorEnum.RPC_RETURN_ERROR.getCode());
        }
        FrequentlyInvoiceHeaderDto param = BeanUtils.propertiesCopy(req, FrequentlyInvoiceHeaderDto.class);
        param.setCreator(req.getUserUuid());
        Response<?> response = frequentlyInvoiceHeaderService.addFrequentlyInvoiceHeader(param);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg(), ResultErrorEnum.RPC_RETURN_ERROR.getCode());
        }
        return Response.createSuccess();
    }

    /**
     * 删除发票标题
     *
     * @param req           入参
     * @param passengerUuid 入参
     * @return {@link Response}
     */
    @Override
    public Response<?> deleteInvoiceHeader(DelInvoiceHeaderReq req, String passengerUuid) {
        if (StringUtils.isBlank(req.getUuid()) && CollectionUtils.isEmpty(req.getUuidList())) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR.getMsg(),
                    ResultErrorEnum.PARAM_NULL_ERROR.getCode());
        }
        FrequentlyInvoiceHeaderReq param = new FrequentlyInvoiceHeaderReq();
        param.setUpdater(passengerUuid);
        param.setUuid(req.getUuid());
        param.setUuidList(req.getUuidList());
        param.setUserUuid(passengerUuid);
        Response<?> response = frequentlyInvoiceHeaderService.invalidFrequentlyInvoiceHeaders(param);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg(), ResultErrorEnum.RPC_RETURN_ERROR.getCode());
        }
        return Response.createSuccess();
    }

    /**
     * 查询发票表头
     *
     * @param req      入参
     * @param userUuid 入参
     * @return {@link Response}
     */
    @Override
    public Response<?> queryInvoiceHeader(AddInvoiceHeaderReq req, String userUuid) {
        if (StringUtils.isBlank(userUuid)) {
            return Response.createError("无法获取乘客信息", ResultErrorEnum.RPC_RETURN_ERROR.getCode());
        }
        FrequentlyInvoiceHeaderReq param = new FrequentlyInvoiceHeaderReq();
        param.setUserUuid(userUuid);
        if (StringUtils.isNotBlank(req.getHeader())) {
            param.setHeader(req.getHeader());
        }
        if (ObjectUtil.isNotNull(req.getHeaderType())) {
            param.setHeaderType(req.getHeaderType());
        }
        Response<List<FrequentlyInvoiceHeaderDto>> response
                = frequentlyInvoiceHeaderService.queryFrequentlyInvoiceHeaderList(param);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg(), ResultErrorEnum.RPC_RETURN_ERROR.getCode());
        }
        List<InvoiceHeaderVo> vos = null;
        if (!CollectionUtils.isEmpty(response.getData())) {
            vos = BeanUtils.collectionCopy(response.getData(), InvoiceHeaderVo.class);
        }
        return Response.createSuccess(vos);
    }



    /**
     * @param req      情趣
     * @param userUuid 乘客id
     * @return {@link Response}
     */
    public Response<?> saveOrUpdateInvoiceHeader(AddInvoiceHeaderReq req, String userUuid) {
        // 税号判断
        if (InvoiceHeaderTypeEnum.TYPE_ENTERPRISE.getType().equals(req.getHeaderType())
                && StringUtils.isBlank(req.getTaxNum())) {
            return Response.createError("企业抬头税号不能为空",
                    ResultErrorEnum.PARAM_NULL_ERROR.getCode());
        }
        if (StringUtils.isBlank(userUuid)) {
            return Response.createError("未获得用户信息");
        }
        FrequentlyInvoiceHeaderDto param = BeanUtils.propertiesCopy(req, FrequentlyInvoiceHeaderDto.class);
        param.setUserUuid(userUuid);
        if (StringUtils.isBlank(req.getUuid())) {
            if (StringUtils.isBlank(req.getHeader()) || req.getHeaderType() == null) {
                return Response.createError("抬头、抬头类型不能为空",
                        ResultErrorEnum.PARAM_NULL_ERROR.getCode());
            }
            // uuid 为空 说明是新增
            FrequentlyInvoiceHeaderReq queryParam = new FrequentlyInvoiceHeaderReq();
            queryParam.setUserUuid(userUuid);
            queryParam.setHeader(req.getHeader());
            queryParam.setHeaderType(req.getHeaderType());
            Response<List<FrequentlyInvoiceHeaderDto>> resQuery
                    = frequentlyInvoiceHeaderService.queryFrequentlyInvoiceHeaderList(queryParam);
            // 不存在 uuid 为空 新增
            if (resQuery.isSuccess() && ObjectUtil.isNotNull(resQuery.getData())
                    && CollectionUtil.isEmpty(resQuery.getData())) {
                req.setUserUuid(userUuid);
                return addInvoiceHeader(req);
            } else {
                param.setUuid(resQuery.getData().get(0).getUuid());
            }
        }
        // 存在uuid  纯更新
        Response<?> response = frequentlyInvoiceHeaderService.updateFrequentlyInvoiceHeaders(param);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg(),
                    ResultErrorEnum.PARAM_NULL_ERROR.getCode());
        }
        return response;
    }

    /**
     * 查询一年内最近三次的开票成功发票抬头信息
     *
     * @param headerType 头类型
     * @param userUid    用户uid
     * @return {@link Response< InvoiceHeaderVO >}
     */
    public Response<InvoiceHeaderVO> queryInvoiceHeader(Integer headerType, String userUid) {

        if (StringUtils.isBlank(userUid)) {
            return Response.createError("获取用户信息失败");
        }

        Response<List<PassengerInvoiceHeaderDto>> listResponse = passengerInvoiceService
                .queryInvoiceHeaderByHeaderType(headerType, userUid);

        List<InvoiceHeaderVO> invoiceHeaderVOList = Lists.newArrayList();

        if (listResponse.isSuccess() && !CollectionUtils.isEmpty(listResponse.getData())) {
            invoiceHeaderVOList = BeanUtils.collectionCopy(listResponse.getData(), InvoiceHeaderVO.class);
        }

        return Response.createSuccess("查询成功", invoiceHeaderVOList);
    }

    /**
     * 检查添加发票抬头的参数
     *
     * @param req 入参
     * @return {@link Response}
     */
    private Response<?> checkParamOfAddInvoiceHeader(AddInvoiceHeaderReq req) {
        if (ObjectUtil.isNull(req.getHeaderType()) || StringUtils.isBlank(req.getHeader())) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR.getMsg(),
                    ResultErrorEnum.PARAM_NULL_ERROR.getCode());
        }
        FrequentlyInvoiceHeaderReq queryParam = new FrequentlyInvoiceHeaderReq();
        queryParam.setUserUuid(req.getUserUuid());
        Response<List<FrequentlyInvoiceHeaderDto>> resQuery
                = frequentlyInvoiceHeaderService.queryFrequentlyInvoiceHeaderList(queryParam);
        List<FrequentlyInvoiceHeaderDto> existList = new ArrayList<>();
        if (resQuery.isSuccess() && !CollectionUtils.isEmpty(resQuery.getData())) {
            if (resQuery.getData().size() >= NUM_10) {
                return Response.createError("常用抬头数量不能超过10条");
            }
            existList = resQuery.getData();
        }
        // 校验抬头税号是否重复
        if (!CollectionUtils.isEmpty(existList)) {
            // 个人校验发票抬头数量
            FrequentlyInvoiceHeaderDto invoiceHeaderDto;
            if (InvoiceHeaderTypeEnum.TYPE_PERSON.getType().equals(req.getHeaderType())) {
                invoiceHeaderDto = existList.stream().filter(item ->
                        req.getHeaderType().equals(item.getHeaderType())
                                && (req.getHeader().equals(item.getHeader()))
                ).findFirst().orElse(null);
            } else {
                invoiceHeaderDto = existList.stream().filter(item ->
                        req.getHeaderType().equals(item.getHeaderType())
                                &&
                                (req.getHeader().equals(item.getHeader()) || req.getTaxNum().equals(item.getTaxNum()))
                ).findFirst().orElse(null);
            }
            if (ObjectUtil.isNotNull(invoiceHeaderDto)) {
                return Response.createError("抬头或税号已存在");
            }
        }
        return Response.createSuccess();
    }
}
