/**
 * Copyright(C) 2018 Hangzhou yejin Technology Co., Ltd. All rights reserved.
 */
package com.t3.ts.pay.center.api.dto.vo;

import com.t3.ts.pay.center.api.dto.fare.FareCostItemDto;
import com.t3.ts.pay.center.api.dto.fare.FareModelRuleConcept;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/21 14:17
 * @description: 费用详情返回
 */
@Getter
@Setter
public class RouteFareDetailVo implements Serializable {

    private String routePlanUuid;
    /**
     * 总费用
     */
    private Double totalFare;
    /**
     * 订单费用
     */
    private Double orderFare;
    /**
     * 支付费用
     */
    private Double actPaid;
    /**
     * 实际支付金额
     */
    private Double actualFare;

    /**
     * 额外费用
     */
    private Double otherFare;

    /**
     * 取消费用
     */
    private Double cancelFare;

    // 费用项明细
    private List<FareCostItemDto> costItems;

    //支付项明细
    private List<FareCostItemDto> payItems;

    //支付项明细
    private List<FareCostItemDto> payWaterItems;

    //已预付明细
    private List<FareCostItemDto> advancePayItems;

    /**
     * 计费规则id
     */
    private String carModelsLevelUuid;
    /**
     * 优惠卷数量
     */
    private Integer useCouponCount;
    /**
     * 新增费用
     */
    private Double addFare;

    private Double latestOrderFare;

    private String payType;

    /**
     * 超出里程（公里）
     **/
    private Double beyondTrip;
    /**
     * 超出里程费
     **/
    private Double overTripFare;

    /**
     * 起步时长（毫秒）
     **/
    private Integer beyondTimeLength;
    /**
     * 超出时长费用
     **/
    private Double overTimeFare;

    private Integer doingTime;

    /**
     * 城市uuid
     */
    private String cityUuid;

    /**
     * 折扣费
     */
    private Double discountFare;

    /**
     * 司机减免费
     */
    private Double driverReliefFare;

    /**
     * 是否预付
     */
    private Boolean isPrePay;

    /**
     * 预付金额
     */
    private Double prePayFare;

    /**
     * 退款费用（包车预付款大于应付金额时展示）
     */
    private Double drawBackFare;

    /**
     * 退款状态（2:退款中  3:退款完成）
     */
    private Integer drawBackStatus;

    /**
     * 待退款金额
     */
    private BigDecimal stayRefundFare;

    /**
     * 未支付，待支付金额
     */
    private BigDecimal unActPaid;

    private Integer fareMethod = 1;

    private FareModelRuleConcept fareModelRuleConcept;

//    /**
//     * 给司机端返回的扣除动态折扣费用 totalFare-discountFare
//     */
//    private BigDecimal reduceDiscountFare;

    /**
     * 企业支付费用
     */
    private Double companyPay;

    /**
     * 等待费用
     */
    private Double waitFare;

    /**
     * 预付款退款费用
     */
    private Double refundPrePayFare;

    /**
     * 订单模块类型（1:出租车,2：专车，3：拼车，4：快车）
     */
    private Integer typeModule = 0;

    /**
     * 1实时单 2 预约单
     */
    private Integer typeTime = 0;

    /**
     * 区域Code
     */
    private String areaCode;

    /**
     * 车架号
     */
    private String vin;

    /**
     * 司机预估收益
     */
    private BigDecimal forecastEarningsFare = null;

    /**
     * 司机流水（司机端用）
     */
    private BigDecimal expenseFare = BigDecimal.ZERO;

}
