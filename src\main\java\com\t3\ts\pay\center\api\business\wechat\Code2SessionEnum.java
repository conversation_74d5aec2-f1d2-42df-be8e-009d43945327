package com.t3.ts.pay.center.api.business.wechat;

import com.t3.ts.enums.BaseEnumInterface;

/**
 * <AUTHOR>
 * @date 2020/3/6 16:27
 * @des 1.0
 */
public enum Code2SessionEnum implements BaseEnumInterface {

    /**
     * 系统繁忙
     */
    BUSY(-1, "系统繁忙，此时请开发者稍候再试"),

    /**
     * 请求成功
     */
    SUCCESS(0, "请求成功"),
    /**
     * 未知
     */
    NONE(40163, "未知 无效"),
    /**
     * 无效
     */
    INVALID(40029, "code 无效"),
    /**
     * 频率限制
     */
    EXCEPTION(45011, "频率限制，每个用户每分钟100次");

    private final int code;
    private final String msg;

    Code2SessionEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 通过编码获取枚举
     *
     * @param code 代码
     * @return {@link Code2SessionEnum}
     */
    public static Code2SessionEnum getEnumByCode(int code) {
        Code2SessionEnum[] values = Code2SessionEnum.values();
        for (Code2SessionEnum value : values) {
            if (Integer.valueOf(code).equals(value.getCode())) {
                return value;
            }
        }

        return null;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
