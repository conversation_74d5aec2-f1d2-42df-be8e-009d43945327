package com.t3.ts.pay.center.api.business.privilege;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.enums.common.BussiEnum;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.remote.service.AccountPrivilegeService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementRouteDTO;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.service.SettlementRouteService;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.utils.StringUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: PrivilegeBusiness
 * @Package com.t3.ts.pay.center.api.business.privilege
 * @date 2022/3/31 9:25
 */
@Slf4j
@Component
public class PrivilegeBusiness {
    @DubboReference
    private SettlementRouteService settlementService;
    @DubboReference
    private SrOrderService srOrderService;
    @DubboReference
    private AccountPrivilegeService accountPrivilegeService;

    /**
     * 查询权益卡列表
     *
     * @param routePlan routePlan
     * @param userId    userId
     * @return {@link Response}
     */
    public Response queryCanUsePrivilege(RoutePlanUuidReq routePlan, String userId) {
        String routePlanUuid = routePlan.getRoutePlanUuid();
        String grayVersion = routePlan.getGrayVersion();
        log.info("PrivilegeBusiness.queryCanUsePrivilege routePlanUuid:{}, userId:{}", routePlanUuid, userId);
        try {
            SettlementRouteDTO routeSettlementDTO;
            if (StringUtils.isNotEmpty(routePlanUuid)) {
                Response<SettlementRouteDTO> response =
                        settlementService.getSettlementRouteByPlanId(routePlanUuid);
                if (null == response || !response.isSuccess() || null == response.getData()
                        || StringUtils.isEmpty(response.getData().getUuid())) {
                    Response<SrOrderDto> srOrderDtoResponse = srOrderService.querySrOrderByRoutePlanId(routePlanUuid);

                    if (null == srOrderDtoResponse || !srOrderDtoResponse.isSuccess()
                            || null == srOrderDtoResponse.getData()) {
                        throw new BizException("结算单不存在", BussiEnum.ERROR.getCode(), null);
                    }

                    SrOrderDto srOrderDto = srOrderDtoResponse.getData();

                    routeSettlementDTO = new SettlementRouteDTO();
                    routeSettlementDTO.setAdjustOrderFare(srOrderDto.getOrderFare());
                    routeSettlementDTO.setDriverReliefFare(srOrderDto.getDriverReliefFare());
                    routeSettlementDTO.setUuid(srOrderDto.getUuid());
                    routeSettlementDTO.setSource(srOrderDto.getSource());
                    routeSettlementDTO.setRoutePlanUuid(srOrderDto.getRoutePlanUuid());
                    routeSettlementDTO.setTypeModule(srOrderDto.getTypeModule());
                    routeSettlementDTO.setTypeTrip(srOrderDto.getTypeTrip());
                    routeSettlementDTO.setTypeVehicle(srOrderDto.getTypeVehicle());
                    routeSettlementDTO.setStartRailIds(srOrderDto.getStartRailIds());
                    routeSettlementDTO.setEndRailIds(srOrderDto.getEndRailIds());
                    routeSettlementDTO.setCreateTime(srOrderDto.getCreateTime());
                    routeSettlementDTO.setExpandBizLine(srOrderDto.getExpandBizLine());
                    routeSettlementDTO.setAdjustFestivalFare(srOrderDto.getFestivalFare());
                    routeSettlementDTO.setAdjustCrossCityFare(srOrderDto.getCrossCityFare());
                    routeSettlementDTO.setAdjustDispatchFare(srOrderDto.getDispatchFare());
                    routeSettlementDTO.setAdjustLostReturnFare(srOrderDto.getLostReturnFare());
                    routeSettlementDTO.setAdjustCompensationFare(srOrderDto.getCompensationFare());
                    routeSettlementDTO.setTypeTime(srOrderDto.getTypeTime());
                    routeSettlementDTO.setOrderFare(srOrderDto.getOrderFare());
                    routeSettlementDTO.setCancelFare(srOrderDto.getCancelFare());
                } else {
                    routeSettlementDTO = response.getData();
                }
                log.info("PayDeskInfoServiceImpl.getUserCanUseCoupon, find routeSettlementDTO{}",
                        JSONObject.toJSONString(routeSettlementDTO));

                Map<String, Object> params = new HashMap<>(NumConstant.NUM_16);
                params.put("userId", StringUtils.isEmpty(userId) ? routeSettlementDTO.getPassengerUuid() : userId);
                // 订单类型 1：实时  2：预约
                params.put("orderType", routeSettlementDTO.getTypeTime());
                params.put("sceneCode", CommonNumConst.PAY001);
                // 业务线
                params.put("bizLine", routeSettlementDTO.getExpandBizLine());
                // 产品线
                params.put("bizType", routeSettlementDTO.getTypeModule());
                // 车型
                params.put("carType", routeSettlementDTO.getTypeVehicle());
                params.put("journeyId", routeSettlementDTO.getRoutePlanUuid());
                params.put("orderAmount", routeSettlementDTO.getOrderFare());
                params.put("cancelAmount", routeSettlementDTO.getCancelFare());
                params.put("useChannel", NUM_1);
                // 渠道来源
                if (StringUtils.isNotBlank(grayVersion)) {
                    if (grayVersion.startsWith(CommonNumConst.WX) || grayVersion.startsWith(CommonNumConst.AX)) {
                        params.put("useChannel", CommonNumConst.NUM_2);
                    }
                }
                return accountPrivilegeService.queryCanUsePrivilege(params);
            }
        } catch (Exception e) {
            log.warn("queryCanUsePrivilege error", e);
            throw new BizException("", BussiEnum.ERROR.getCode(), null);
        }
        return null;
    }
}
