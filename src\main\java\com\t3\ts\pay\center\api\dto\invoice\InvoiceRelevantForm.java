package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: ccq
 * @Date: 2020/6/16 15:49
 * @Description: 开票相关入参
 */
@Data
@ApiModel(value = "开票相关入参")
public class InvoiceRelevantForm implements Serializable {
    private static final long serialVersionUID = 4339345106908840050L;

    /**
     * 申请来源(1:app申请 2:客服申请)--- 客服申请
     */
    @ApiModelProperty(value = "申请来源(1:app申请 2:客服申请)--- 客服申请")
    private Integer applySource = 1;

    /**
     * 行程来源（1 T3，7 高德）必填
     */
    @ApiModelProperty(value = "行程来源（1 T3，7 高德）", required = true)
    private Integer sourceCode = 1;
    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    @ApiModelProperty(value = "运力类型(1 T3运力,2 东风运力,3 一汽运力)", required = true)
    private Integer transportType;

    /**
     * 东风运力
     */
    @ApiModelProperty(value = "东风运力")
    private DFTransport dfTransport;

    /**
     * 一汽运力
     */
    @ApiModelProperty(value = "一汽运力")
    private FAWTransport fawTransport;

    /**
     * T3运力
     */
    @ApiModelProperty(value = "T3运力")
    private T3Transport t3Transport;

    /**
     * 发票id
     */
    @ApiModelProperty(value = "发票id")
    private String uuid;

    /**
     * 乘客手机号
     */
    @ApiModelProperty(value = "乘客手机号")
    private String passengerMobile;

    /**
     * 发票类型（1按行程2按金额）
     **/
    @ApiModelProperty(value = "发票类型（1按行程2按金额）")
    private Integer type;

    /**
     * 支付方式(0.无（电子发票）,1.到付,2.寄付)
     **/
    @ApiModelProperty(value = "支付方式(0.无（电子发票）,1.到付,2.寄付)")
    private Integer payType;

    /**
     * 抬头类型（1：企业单位2：个人/非企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位2：个人/非企业单位）")
    private Integer headerType;

    /**
     * 发票抬头
     **/
    @ApiModelProperty(value = "发票抬头")
    private String header;

    /**
     * 金额
     **/
    @ApiModelProperty(value = "金额")
    private BigDecimal money;

    /**
     * 发票内容
     **/
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 收件人
     **/
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 联系电话
     **/
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 所在地区
     **/
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     **/
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    /**
     * 备注
     **/
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 物流订单号
     **/
    @ApiModelProperty(value = "物流订单号")
    private String logisticsOrderNo;

    /**
     * 发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）)
     **/
    @ApiModelProperty(value = "发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）)")
    private Integer status;

    /**
     * 订单的Id,行程开票未order表,充值开票为actual_flow表
     */
    @ApiModelProperty(value = "订单的Id")
    private String orderUuid;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 开票类型(1.电子发票,2.纸质发票)
     */
    @ApiModelProperty(value = "开票类型(1.电子发票,2.纸质发票)")
    private Integer invoiceType;

    /**
     * 接收发票的邮箱
     */
    @ApiModelProperty(value = "接收发票的邮箱")
    @Pattern(regexp = "^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$",
             message = "请输入正确的邮箱")
    private String email;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String registerAddress;

    /**
     * 注册电话
     */
    @ApiModelProperty(value = "注册电话")
    private String registerTel;

    /**
     * 开户账号
     */
    @ApiModelProperty(value = "开户账号")
    private String openingAccount;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String taxNum;

    /**
     * 开票类型 1:开票  2:重新开票
     */
    @ApiModelProperty(value = " 开票类型 1:开票  2:重新开票")
    private Integer billType;


}
