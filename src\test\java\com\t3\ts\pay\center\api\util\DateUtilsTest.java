package com.t3.ts.pay.center.api.util;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:02
 */
public class DateUtilsTest {
    @Mock
    Logger LOG;
    @InjectMocks
    DateUtils dateUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testStrDateTimeString() throws Exception {
        String result = DateUtils.strDateTimeString("date");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testStrToDateFormat() throws Exception {
        String result = DateUtils.strToDateFormat("date");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testFormat() throws Exception {
        Date result = DateUtils.format("strDate", "f");
        Assert.assertEquals(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), result);
    }

    @Test
    public void testGetDateFormat() throws Exception {
        String result = DateUtils.getDateFormat();
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetTimeFormat() throws Exception {
        String result = DateUtils.getTimeFormat();
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetDateTimeFormat() throws Exception {
        String result = DateUtils.getDateTimeFormat();
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetMonthFormat() throws Exception {
        String result = DateUtils.getMonthFormat();
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetDateFormat2() throws Exception {
        String result = DateUtils.getDateFormat(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetTimeFormat2() throws Exception {
        String result = DateUtils.getTimeFormat(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetDateTimeFormat2() throws Exception {
        String result = DateUtils.getDateTimeFormat(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetMonthFormat2() throws Exception {
        String result = DateUtils.getMonthFormat(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetDateTimeLong() throws Exception {
        Long result = DateUtils.getDateTimeLong("date", "time");
        Assert.assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testStringParseDate() throws Exception {
        String result = DateUtils.stringParseDate("date", "pattern");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testTranslateDate() throws Exception {
        String result = DateUtils.translateDate(Long.valueOf(1));
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetMilliSecond() throws Exception {
        Long result = DateUtils.getMilliSecond(Integer.valueOf(0));
        Assert.assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testGetMillisecondsNextEarlyMorning() throws Exception {
        int result = DateUtils.getMillisecondsNextEarlyMorning();
        Assert.assertEquals(0, result);
    }

    @Test
    public void testGetDateAfterHalfHour() throws Exception {
        Long result = DateUtils.getDateAfterHalfHour(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(),
                Integer.valueOf(0));
        Assert.assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testLongOfTwoDate() throws Exception {
        Integer result = DateUtils.longOfTwoDate(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(),
                new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), "formart");
        Assert.assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testIsEffectiveDate() throws Exception {
        boolean result = DateUtils
                .isEffectiveDate(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), "beginTime1",
                        "endTime1");
        Assert.assertEquals(true, result);
    }

    @Test
    public void testGetDateAddMonth() throws Exception {
        Date result = DateUtils.getDateAddMonth(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), 0);
        Assert.assertEquals(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), result);
    }

    @Test
    public void testIsBeforeAppointedTime() throws Exception {
        boolean result = DateUtils
                .isBeforeAppointedTime(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(),
                        Integer.valueOf(0));
        Assert.assertEquals(true, result);
    }

    @Test
    public void testObtainMinutes() throws Exception {
        String result = DateUtils.obtainMinutes(Integer.valueOf(0), Integer.valueOf(0));
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetMillsecBeforeMoment() throws Exception {
        long result = DateUtils.getMillsecBeforeMoment(0, 0, 0, 0);
        Assert.assertEquals(0L, result);
    }

    @Test
    public void testGetMillisecBetweenDate() throws Exception {
        long result = DateUtils
                .getMillisecBetweenDate(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(),
                        new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime());
        Assert.assertEquals(0L, result);
    }

    @Test
    public void testGetMoment() throws Exception {
        Date result = DateUtils.getMoment(0, 0, 0, 0);
        Assert.assertEquals(new GregorianCalendar(2022, Calendar.NOVEMBER, 7, 14, 2).getTime(), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
