package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Getter
@Setter
@ApiModel("绑定银行卡-请求参数")
public class CardBindReq {
    @ApiModelProperty(value = "银行卡号")
    private String cardNo;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行卡类型，1储蓄卡，2信用卡")
    private String cardType;

    @ApiModelProperty(value = "银行卡预留手机号")
    @NotNull(message = "手机号不能为空")
    @Length(min = 11, max = 11, message = "请填写正确的手机号")
    private String mobile;

    @ApiModelProperty(value = "协议编码")
    private String agreementCode;

    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    @ApiModelProperty(value = "短信验证码")
    private String verifyCode;

    @ApiModelProperty(value = "银行卡对应的户名(黑户司机使用，正常司机使用系统存储的姓名)")
    private String name;

}
