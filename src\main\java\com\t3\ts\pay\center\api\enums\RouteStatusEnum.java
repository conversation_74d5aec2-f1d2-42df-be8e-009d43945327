package com.t3.ts.pay.center.api.enums;

import com.t3.ts.enums.order.SubStatus;
import com.t3.ts.enums.order.TypeTime;

/**
 * <AUTHOR>
 * @date 2020/9/2 10:05
 * @description:
 */
public enum RouteStatusEnum {

    APPOINT(1, "预约中"),

    PROCESS(2, "进行中"),

    WAIT_PAY(3, "待支付"),

    WAIT_EVALUATION(4, "待评价"),

    CANCEL(5, "取消"),

    FINISH(6, "完成"),

    CANCEL_WAIT_PAY(7, "有责取消待支付"),

    CANCEL_FINISH_PAY(8, "有责取消支付完成");

    private Integer code;

    private String desc;

    RouteStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RouteStatusEnum getRouteStatus(TypeTime typeTime, SubStatus subStatus) {
        if (null == typeTime || null == subStatus) {
            return null;
        }
        RouteSubStatusEnum subStatusEnum = RouteSubStatusEnum.getRouteSubStatus(subStatus);
        if (null == subStatusEnum) {
            return null;
        }
        return subStatusEnum.getRouteStatus(typeTime);
    }

    public static RouteStatusEnum getRouteStatus(Integer typeTimeKey, Integer subStatusKey) {
        if (null == typeTimeKey || null == subStatusKey) {
            return null;
        }
        RouteSubStatusEnum subStatusEnum = RouteSubStatusEnum.getRouteSubStatus(subStatusKey);
        if (null == subStatusEnum) {
            return null;
        }
        for (TypeTime typeTime : TypeTime.values()) {
            if (typeTimeKey.equals(typeTime.key)) {
                return subStatusEnum.getRouteStatus(typeTime);
            }
        }
        return null;
    }

    public static Integer getRouteStatusCode(TypeTime typeTime, SubStatus subStatus) {
        RouteStatusEnum statusEnum = getRouteStatus(typeTime, subStatus);
        if (null == statusEnum) {
            return null;
        }
        return statusEnum.getCode();
    }

    public static Integer getRouteStatusCode(Integer typeTime, Integer subStatus) {
        if (null == typeTime || null == subStatus) {
            return null;
        }
        RouteStatusEnum statusEnum = getRouteStatus(typeTime, subStatus);
        if (null == statusEnum) {
            return null;
        }
        return statusEnum.getCode();
    }
}

enum RouteSubStatusEnum implements IRouteStatus {

    WAIT(SubStatus.ORDER_STATUS_WAIT) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            if (typeTime.equals(TypeTime.APPOINT)) {
                return RouteStatusEnum.APPOINT;
            }
            return RouteStatusEnum.PROCESS;
        }
    },
    CONFIRM(SubStatus.ORDER_STATUS_CONFIRM) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            if (typeTime.equals(TypeTime.APPOINT)) {
                return RouteStatusEnum.APPOINT;
            }
            return RouteStatusEnum.PROCESS;
        }
    },
    GO(SubStatus.ORDER_STATUS_GO) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },
    ARRIVE(SubStatus.ORDER_STATUS_ARRIVE) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },
    START(SubStatus.ORDER_STATUS_START) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },
    ROUTE_CONFIRM(SubStatus.ORDER_STATUS_ROUTE_CONFIRM) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },
    ROUTE_EDIT_ROUTE(SubStatus.ORDER_STATUS_ROUTE_EDIT_ROUTE) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },
    ARRIVED(SubStatus.ORDER_STATUS_ARRIVED) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.PROCESS;
        }
    },

    FARE_CONFIRM(SubStatus.ORDER_STATUS_FARE_CONFIRM) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.WAIT_PAY;
        }
    },
    PASSENGER_CANCEL_WAIT_PAY(SubStatus.ORDER_STATUS_PASSENGER_CANCEL_WAIT_PAY) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.CANCEL_WAIT_PAY;
        }
    },

    COMPLETE(SubStatus.ORDER_STATUS_COMPLETE) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.WAIT_EVALUATION;
        }
    },
    PASSENGER_CANCEL_COMPLETE(SubStatus.ORDER_STATUS_PASSENGER_CANCEL_COMPLETE) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.CANCEL_FINISH_PAY;
        }
    },

    COMMENTED(SubStatus.ORDER_STATUS_COMMENTED) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.WAIT_EVALUATION;
        }
    },
    SECOND_COMMENTED(SubStatus.ORDER_STATUS_SECOND_COMMENTED) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.WAIT_EVALUATION;
        }
    },

    PASSENGER_CANCEL(SubStatus.ORDER_STATUS_PASSENGER_CANCEL) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.CANCEL;
        }
    },
    PASSENGER_CANCEL_CONFIRMED(SubStatus.ORDER_STATUS_PASSENGER_CANCEL_CONFIRMED) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.CANCEL;
        }
    },
    PASSENGER_CANCEL_ARRIVED(SubStatus.ORDER_STATUS_PASSENGER_CANCEL_ARRIVED) {
        @Override
        public RouteStatusEnum getRouteStatus(TypeTime typeTime) {
            return RouteStatusEnum.CANCEL;
        }
    };

    private SubStatus subStatus;

    public SubStatus getSubStatus() {
        return subStatus;
    }

    RouteSubStatusEnum(SubStatus subStatus) {
        this.subStatus = subStatus;
    }

    /**
     * @Description 根据SubStatus获取RouteSubStatusEnum
     * @param subStatus  subStatus
     * @return RouteSubStatusEnum
     */
    public static RouteSubStatusEnum getRouteSubStatus(SubStatus subStatus) {
        if (null == subStatus) {
            return null;
        }
        return getRouteSubStatus(subStatus.key);
    }

    /**
     * @Description 根据SubStatus的Key值获取RouteSubStatusEnum
     * @param subStatusKey subStatusKey
     * @return RouteSubStatusEnum
     */
    public static RouteSubStatusEnum getRouteSubStatus(Integer subStatusKey) {
        if (null == subStatusKey) {
            return null;
        }
        for (RouteSubStatusEnum statusEnum : values()) {
            if (subStatusKey.equals(statusEnum.subStatus.key)) {
                return statusEnum;
            }
        }
        return null;
    }
}

interface IRouteStatus {
    RouteStatusEnum getRouteStatus(TypeTime typeTime);
}
