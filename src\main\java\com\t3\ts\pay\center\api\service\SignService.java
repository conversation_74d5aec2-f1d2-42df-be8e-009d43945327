package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.PaySignReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.result.Response;

/**
 * Description: 支付入口
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:12
 */
public interface SignService {

    /**
     * 签约
     *
     * @param req secretFreeReq
     * @return Response
     */
    Response<?> openSecretFree(SecretFreeReq req);

    /**
     * 解约
     *
     * @param req secretFreeReq
     * @return Response
     */
    Response<?> closeSecretFree(SecretFreeReq req);

    /**
     * 查询签约状态
     *
     * @param userId userId
     * @return Response
     */
    Response<?> querySignStatus(String userId);

    /**
     * 设置优先级
     *
     * @param req secretFreeReq
     * @return Response
     */
    Response<?> priority(SecretFreeReq req);

    /**
     * 查询签约状态
     *
     * @param req req
     * @return Response
     */
    Response<?> querySignStatusByChannel(PayChannelReq req);

    /**
     * 查询签约状态
     *
     * @param req req
     * @return Response
     */
    Response<?> getThirdSignStatus(PaySignReq req);

    /**
     * 获取免密支付列表
     *
     * @param userUid userUid
     * @return Response
     */
    Response<?> querySignList(String userUid);
}
