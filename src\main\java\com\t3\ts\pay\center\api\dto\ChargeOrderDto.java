package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 接收充电订单
 *
 * <AUTHOR>
 */
@Data
@ApiModel("运营商充电订单")
public class ChargeOrderDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderSeq;


    /**
     * 结算单id
     */
    private String payNo;

    /**
     * 结算单id 后付流程结算单
     */
    private String payTwoNo;

    /**
     * 三方运营商id
     */
    private String isp;

    /**
     * 三方运营服务商id
     */
    private String operatorId;

    /**
     * 充电设备接口编码
     */
    private String connectorId;


    /**
     * 二维码内容
     */
    private String qrCode;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 司机id
     */
    private String driverUuid;

    /**
     * vin
     */
    private String vin;

    /**
     * 车牌
     */
    private String licensePlate;

    /**
     * 订单状态 0.待支付（待付预付款）1.待充电（预付款已付）  2.充电中 3 已完成  4.已取消 5.预付尾款
     */
    private Integer orderStatus;


    /**
     * 充电设备接口状态1.空闲  2.占用（未充电） 3 占用（充电中） 4.占用（预约锁定）255.故障',
     */
    private Integer connectorStatus;


    /**
     * 充电站ID
     */
    private String stationId;

    /**
     * 充电站名称
     */
    private String stationName;

    /**
     * 国家
     */
    private String countryCode;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 区域code
     */
    private String areaCode;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 停止充电失败原因 0:无 1:接收失败
     */
    private Integer failReason;

    /**
     * 开始充电时间
     */
    private String startTime;

    /**
     * 结束充电时间
     */
    private String endTime;

    /**
     * A相电流
     */
    private Float currentA;

    /**
     * B相电流
     */
    private Float currentB;

    /**
     * C相电流
     */
    private Float currentC;

    /**
     * A相电压
     */
    private Float voltageA;

    /**
     * B相电压
     */
    private Float voltageB;

    /**
     * C相电压
     */
    private Float voltageC;

    /**
     * 电池剩余电量
     */
    private Float soc;

    /**
     * 启动充电失败原因 0:无；1:此设备不存在；2:此设备离线：3～99:自定义90:车辆未备案91:该车辆充电中
     */
    private Integer identCode;

    /**
     * 累计充电量
     */
    private Float totalPower;

    /**
     * 总电费
     */
    private BigDecimal totalElecMoney;

    /**
     * 总服务费
     */
    private BigDecimal totalSeviceMoney;

    /**
     * 累计总金额
     */
    private BigDecimal totalMoney;

    /**
     * 预付款
     */
    private BigDecimal advanceMoney;

    /**
     * 保险费
     */
    private BigDecimal insureFare;

    /**
     * 应退款金额
     */
    private BigDecimal shouldRefundMoney;

    /**
     * 实际退款金额
     */
    private BigDecimal refundMoney;

    /**
     * 退款状态 0:无 1.退款中 2.已退款
     */
    private Integer refundStatus;


    /**
     * 结算支付金额（司机支付金额、订单金额）
     */
    private BigDecimal payMoney;

    /**
     * 提成佣金
     */
    private BigDecimal commissionMoney;

    /**
     * t3实际提成佣金
     */
    private BigDecimal actualCommissionMoney;

    /**
     * 司机实付金额
     */
    private BigDecimal driverActualMoney;

    /**
     * 司机服务费（总服务费+ 提成佣金）
     */
    private BigDecimal driverServiceMoney;

    /**
     * 0.正常  1.超预付款   2.未抽成佣金（退款 < 佣金）
     */
    private Integer errMoneyType;

    /**
     * 充电结束原因 0:用户手动停止充电 1:客户归属地运营商平台停止充电 2：BMS停止充电；3：充电机设备故障；4：连接器断开 5-99自定义
     */
    private Integer stopReason;

    /**
     * 时段数N 范围：0～32
     */
    private Integer sumPeriod;


    /**
     * 充电百比例
     */
    private Float chargingRatio;

    /**
     * 预计充满还需的时间（分钟）向上取整
     */
    private String fullTime;

    /**
     * 运营商
     */
    private String agentUuid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 逻辑删除标志 1 正常 0 逻辑删除
     */
    private Integer isDel;

    /**
     * 支付类型：0.预付款 1.钱包后付款
     */
    private Integer payType;

    /**
     * 是否发起充电(0:未 1:发起)
     */
    private Integer chargeStart;

}
