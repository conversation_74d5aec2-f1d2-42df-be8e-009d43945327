
package com.t3.ts.pay.center.api.config.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.gson.GsonBuilder;
import com.t3.ts.pay.center.api.util.CommonUtils;
import com.t3.ts.pay.common.util.JSONLogUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HttpTraceLogInterceptor.java
 * @Description TODO
 * @createTime 2020年11月20日 10:00:00
 */
@Component
@Slf4j
public class HttpTraceLogInterceptor implements HandlerInterceptor {

    /**
     * The constant HEADER_TOKEN.
     */
    public static final String HEADER_TOKEN = "token";

    public static final String TRACE_ID = "traceId";
    public static final String HEADER_GRAYVERSION = "grayversion";


//    private static final ThreadLocal<Long> THREAD_LOCAL_TIME = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
//        THREAD_LOCAL_TIME.set(System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
//        String path = request.getRequestURI();
//        String[] ignorePathArray = {"/healthCheck"};
//        if (!ArrayUtils.contains(ignorePathArray, path)
//                && !StringUtils.equals("multipart/form-data", request.getContentType())) {
//            HttpTraceLog traceLog = new HttpTraceLog();
//            traceLog.setHeaderMap(getHeader(request));
//            traceLog.setParameterMap(request.getParameterMap());
//            traceLog.setRequestBody(SpringUtils.getRequestContent(request));
//            traceLog.setResponseBody(getResponseBody(response));
//            log.info("reuqestMethod={} , requestUri={} >>> httpStatus={} , spendTime={} ms ,{}",
//                    request.getMethod(), path, response.getStatus(),
//                    (System.currentTimeMillis() - THREAD_LOCAL_TIME.get()), traceLog);
//            THREAD_LOCAL_TIME.remove();
//        }
        getHeaders();
    }


    /**
     * Gets response body.
     *
     * @param response the response
     * @return the response body
     */
    private String getResponseBody(HttpServletResponse response) {
        ContentCachingResponseWrapper wrapper =
                WebUtils.getNativeResponse(response, ContentCachingResponseWrapper.class);
        if (null != wrapper) {
            try {
                return IOUtils.toString(wrapper.getContentAsByteArray(), wrapper.getCharacterEncoding());
            } catch (IOException e) {
                log.warn("getResponseBody Exception >>> ", e);
            }
        }
        return "";
    }


    /**
     * Gets request body.
     *
     * @param request the request
     * @return the request body
     */
    private String getRequestBody(HttpServletRequest request) {
        ContentCachingRequestWrapper wrapper = WebUtils.getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (wrapper != null) {
            try {
                String requestStr = IOUtils.toString(wrapper.getContentAsByteArray(), wrapper.getCharacterEncoding());
                return JSONLogUtils.getLogJSONString(requestStr, CommonUtils.getRequestFilter());
            } catch (IOException e) {
                log.warn("getRequestBody Exception >>> ", e);
            }
        }
        return "";
    }


    /**
     * Gets header.
     *
     * @param request the request
     * @return the header
     */
    private Map<String, String> getHeader(HttpServletRequest request) {
        Map<String, String> headerMap = Maps.newHashMap();
        ContentCachingRequestWrapper wrapper = WebUtils.getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (null != wrapper) {
            Enumeration headerNames = wrapper.getHeaderNames();
            if (null == headerNames) {
                return headerMap;
            }
            headerMap.put(HEADER_TOKEN, wrapper.getHeader(HEADER_TOKEN));
        }
        return headerMap;
    }

    /**
     * 得到头
     */
    private void getHeaders() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Enumeration<String> headerNames = request.getHeaderNames();
        JSONObject json = new JSONObject();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                String values = request.getHeader(name);
                json.put(name, values);
            }
        }
        log.info("HttpTraceLogInterceptor.getHeaders header:{}", json.toJSONString());
    }


    /**
     * The type Http trace log.
     */
    @Data
    private static class HttpTraceLog {
        /**
         * The Parameter map.
         */
        private Map<String, String[]> parameterMap;
        /**
         * The Header map.
         */
        private Map<String, String> headerMap;
        /**
         * The Request body.
         */
        private String requestBody;
        /**
         * The Response body.
         */
        private String responseBody;

        @Override
        public String toString() {
            return "HttpTraceLog{"
                    + "parameterMap=" + new GsonBuilder().create().toJson(parameterMap)
                    + ", headerMap=" + headerMap
                    + ", requestBody='" + requestBody + '\''
                    + ", responseBody='" + responseBody + '\''
                    + '}';
        }
    }

}
