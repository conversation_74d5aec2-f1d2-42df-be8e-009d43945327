package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import org.springframework.stereotype.Component;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("aliAppletPayServiceImpl")
public class AliAppletPayServiceImpl implements PayChannelService {

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.ALIPAY_MINI.getCode());

        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            rechargePayVo.setCode(NumConstants.STR_500);
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
        }
        rechargePayVo.setSdk(String.valueOf(paymentResp.getData()));
        return Response.createSuccess(rechargePayVo);

    }
}
