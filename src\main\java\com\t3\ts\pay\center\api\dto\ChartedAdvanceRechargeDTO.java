package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2019-11-15 22:04
 * @des: 预付款充值传输对象
 */
@Data
public class ChartedAdvanceRechargeDTO {
    private Integer payType;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 预付款流水号
     */
    private String prePayNo;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 小程序openid
     */
    private String openId;

    /**
     * 支付类型 1 聚合支付
     */
    private Integer adaPayType;

    /**
     * 支付宝授权码
     */
    private String aliAuthCode;
}
