package com.t3.ts.pay.center.api.dto.chartered;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <b>
 * PayDeskContentDTO
 * 2020/6/4
 * 收银台业务入参
 * </b>
 *
 * <AUTHOR>
 */
@ApiModel("包车收银台业务")
@Getter
@Setter
public class PayDeskContentDTO implements Serializable {
    private static final long serialVersionUID = 6638149748058057873L;

    /**
     * 行程ID
     */
    @ApiModelProperty("行程ID")
    private String journeyId;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String userId;

    /**
     * 城市代码
     */
    @ApiModelProperty("城市代码")
    private String cityGuid;
}
