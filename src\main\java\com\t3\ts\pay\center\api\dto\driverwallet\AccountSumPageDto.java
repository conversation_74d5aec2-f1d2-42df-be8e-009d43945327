package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.finance.center.dto.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


@Data
public class AccountSumPageDto extends Page implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("1收支维度 2费用项")
    private Integer bizType;
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("账户类型,3:t3司机,4:出租车司机")
    private Integer accountType;
    @ApiModelProperty("1日2周3月")
    @NotNull(message = "日期维度不能为空")
    private Integer dateType;
    @ApiModelProperty("日期2022-08-01")
    private Date startDate;
    @ApiModelProperty("日期2022-08-07")
    private Date endDate;
    @ApiModelProperty("费用大类,10000订单收入、20000任务奖励等")
    private String fareClassify;
    @ApiModelProperty("收支类型,1收入,2支出,3收支")
    private Integer changedType;
    private Integer queryWithdraw = 0;
    @NotNull(message = "统计时间不能为空")
    private String yearMonth;

}
