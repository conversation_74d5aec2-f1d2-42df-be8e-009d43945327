package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/20 23:17
 * @description:
 */
@Data
public class FareCostItemDto implements Serializable {
    /**
     * 费用项目
     */
    private String item;
    /**
     * 费用
     */
    private String cost;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 是否抵扣金额
     */
    private Boolean isDeductible;
    /**
     * 颜色
     */
    private boolean color;

    private Integer addBaseFare;

    /**
     * 费用子项
     */
    private List<SubFareCostItemDto> subFareCostItem;

    private String remark;

    public FareCostItemDto() {
    }

    public FareCostItemDto(String item, String cost, Integer sort, Boolean isDeductible, boolean color,
                           Integer addBaseFare) {
        this.item = item;
        this.cost = cost;
        this.sort = sort;
        this.isDeductible = isDeductible;
        this.color = color;
        this.addBaseFare = addBaseFare;
    }

    public FareCostItemDto(String item, String cost, Integer sort, Boolean isDeductible, boolean color, String remark) {
        this.item = item;
        this.cost = cost;
        this.sort = sort;
        this.isDeductible = isDeductible;
        this.color = color;
        this.remark = remark;
    }

    public FareCostItemDto(String item, String cost, Integer sort, Boolean isDeductible, boolean color) {
        this.item = item;
        this.cost = cost;
        this.sort = sort;
        this.isDeductible = isDeductible;
        this.color = color;
    }

    public FareCostItemDto(String item, String cost) {
        this.item = item;
        this.cost = cost;
    }
}
