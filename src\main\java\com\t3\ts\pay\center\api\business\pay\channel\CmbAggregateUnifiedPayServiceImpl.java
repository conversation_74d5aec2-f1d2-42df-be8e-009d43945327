package com.t3.ts.pay.center.api.business.pay.channel;

import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 结果参数拼装
 * @createTime 2021年11月22日 14:12:00
 */
@Service("CmbAggregateUnifiedPayServiceImpl")
public class CmbAggregateUnifiedPayServiceImpl implements PayChannelService {

    @Autowired
    private SwitchConfig switchConfig;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode());
        rechargePayVo.setSdk(JSONUtil.toJsonStr(paymentResp.getData()));
        rechargePayVo.setCode(paymentResp.getCode().toString());
        rechargePayVo.setCmbMiniAppId(switchConfig.getCmbAggT3MiniAppId());
        return Response.createSuccess(rechargePayVo);
    }
}
