/**
 * <AUTHOR>
 * @date ：Created in 2021/4/27 15:09
 * @description：
 */

package com.t3.ts.pay.center.api.dto.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.*;

/**
 * 迁移 by ivy .2021/09/17 14:35
 *
 * @Author: qul
 * @Date: 2021/4/27 15:09
 */
@Getter
@Setter
public class InvoiceHeaderVo implements Serializable {

    private static final long serialVersionUID = 2008229294490541037L;

    /**
     * 唯一标识
     */
    private String uuid;

    /**
     * 抬头类型（1：企业单位2：个人/非企业单位）
     */
    private Integer headerType;

    /**
     * 发票抬头
     */
    private String header;

    /**
     * 税号
     */
    private String taxNum;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerTel;

    /**
     * 开户银行
     */
    private String openingBank;

    /**
     * 开户银行账号
     */
    private String bankAccount;

    /**
     * 是否默认抬头 0否 1是
     */
    private Integer defaultFlag;

    /**
     * 邮箱
     */
    private String email;
}
