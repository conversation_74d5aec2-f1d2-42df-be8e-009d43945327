package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.math.BigDecimal;

/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * @Author: qul
 * @Date:  2021/4/25 16:35
 */
@Getter
@Setter
public class InvoiceMallSubmitItem implements Serializable {

    private static final long serialVersionUID = -4281169852154464547L;

    /**
     * 订单ID
     */
    private String orderNo;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 可开票金额
     */
    private BigDecimal invoiceAmount;
    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品数量
     */
    private Integer skuNum;

    private Integer bizType;

    /**
     * 创建时间 时间戳
     */
    private Long createTime;

    private Integer invoiceClass;

    private Integer invoiceSubjectCode;

    private Long completeTime;
}
