package com.t3.ts.pay.center.api.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.FareItemConfigDto;
import com.t3.ts.account.center.dto.flow.PayAccountFlowsDto;
import com.t3.ts.account.center.dto.flow.PayAccountFlowsQueryPageDto;
import com.t3.ts.account.center.service.FareItemConfigService;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.adapter.center.constants.PayCodeEnum;
import com.t3.ts.adapter.center.dto.AdaptiveRequest;
import com.t3.ts.adapter.center.dto.AdaptiveResponse;
import com.t3.ts.adapter.center.service.AdaptiveCoreService;
import com.t3.ts.finance.center.dto.DictDto;
import com.t3.ts.finance.center.service.DictService;
import com.t3.ts.finance.center.service.PayAccountAnalyseSumService;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.integrated.dto.security.BankCardAuthenticationReqDto;
import com.t3.ts.integrated.dto.security.BankCardAuthenticationRespDto;
import com.t3.ts.kz.data.center.fp.dto.agreement.AgreementUnSignResDto;
import com.t3.ts.kz.data.center.fp.req.agreement.AgreementSignReqDto;
import com.t3.ts.kz.data.center.fp.req.agreement.AgreementUnSignReqDto;
import com.t3.ts.kz.data.center.fp.service.agreement.DriverAgreementService;
import com.t3.ts.operation.center.service.agreement.AgreementSignService;
import com.t3.ts.pay.center.api.business.DriverWalletBusiness;
import com.t3.ts.pay.center.api.business.common.BindCardErrorCheckBiz;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.RedisKeyConstant;
import com.t3.ts.pay.center.api.dto.DriverFareClassifyResVo;
import com.t3.ts.pay.center.api.dto.DriverFareClassifyVo;
import com.t3.ts.pay.center.api.dto.driverwallet.*;
import com.t3.ts.pay.center.api.dto.wallet.*;
import com.t3.ts.pay.center.api.enums.BindCardErrMsgEnum;
import com.t3.ts.pay.center.api.rest.OpenApiOcrRest;
import com.t3.ts.pay.center.api.service.DriverWalletDwySumService;
import com.t3.ts.pay.center.api.util.AccountNewUtils;
import com.t3.ts.pay.center.api.util.AccountUtils;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.pay.center.api.util.LockUtils;
import com.t3.ts.pay.center.api.util.MoneyUtils;
import com.t3.ts.pay.common.http.driver.dto.res.DriverResDto;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.remote.dto.BankCardDto;
import com.t3.ts.pay.remote.dto.BankCardResponse;
import com.t3.ts.pay.remote.service.BankCardService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.order.GeneralItemDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.GeneralItemService;
import com.t3.ts.utils.StringUtils;
import com.t3.ts.withdraw.center.enums.WithdrawCardType;
import com.t3.ts.withdraw.center.enums.WithdrawOrderQueryType;
import com.t3.ts.withdraw.center.enums.WithdrawStatus;
import com.t3.ts.withdraw.center.remote.dto.*;
import com.t3.ts.withdraw.center.remote.dto.sms.SmsVerifyCodeReq;
import com.t3.ts.withdraw.center.remote.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Describer: 司机钱包
 *
 * <AUTHOR>
 * 2022/11/3 11:59
 */
@Api(tags = "司机钱包API")
@Slf4j
@RestController
@RequestMapping("/api/driver/wallet")
public class DriverWalletController extends BaseApi {

    private static final Integer FARE_ITEM = 800101;

    private static final Integer[] NOT_MATCH_FARE = {800101, 900101, 900102};

    private static final Integer[] NOT_MATCH_FARE_PAYOUT = {800101, 900101, 900102};

    private static final Integer[] NOT_MATCH_FARE_FREEZE = {110101, 110102, 120101, 120102};

    private static final String SUCCESS_CODE = "00";

    private static final int HITCH_DRIVER_AGREEMENT = 36;

    private static final String BEIJING_CITY = "110100";

    private static final Cache<String, String> cache =
            CacheBuilder.newBuilder().expireAfterWrite(600, TimeUnit.SECONDS) // 设置写入后的过期时间，600 秒
                    .build();

    @Value("${driverWallet.conf.test:false}")
    private Boolean isTest;

    @DubboReference
    private WithdrawConfigService withdrawConfigService;
    @DubboReference
    private BindCardService bindCardService;
    @DubboReference
    private WithdrawOrderService withdrawOrderService;

    @Autowired
    private SwitchConfig switchConfig;

    @DubboReference
    private FareItemConfigService fareItemConfigService;

    @DubboReference
    private OutWithdrawService outWithdrawService;

    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;


    @DubboReference
    private AgreementSignService agreementSignService;
    @DubboReference
    private DriverAgreementService driverAgreementService;

    @DubboReference
    private com.t3.ts.kz.data.center.fp.service.agreement.AgreementSignService passengerAgreementSignService;

    @DubboReference
    private BankCardService bankCardService;

    @DubboReference
    private WithdrawBankService withdrawBankService;
    /**
     * The Pay account analyse sum service.
     */
    @DubboReference
    private PayAccountAnalyseSumService payAccountAnalyseSumService;
    @DubboReference
    private DictService dictService;
    @Autowired
    private DriverWalletDwySumService driverWalletDwySumService;
    @Autowired
    private BindCardErrorCheckBiz bindCardErrorCheckBiz;
    @Autowired
    private OpenApiOcrRest openApiOcrRest;
    @DubboReference
    private AdaptiveCoreService adaptiveCoreService;
    @Autowired
    private AccountNewUtils accountNewUtils;
    @Autowired
    private DriverWalletBusiness driverWalletBusiness;
    @DubboReference
    private GeneralItemService generalItemService;
    @Autowired
    private LockUtils lockUtils;

    /**
     * 获取短信验证码
     *
     * @param req SmsVerifyCodeReq
     * @return {@link Response <String>}
     */
    @ApiOperation(value = "获取短信验证码", notes = "获取短信验证码")
    @PostMapping("/getSmsVerifyCode")
    public Response<String> getSmsVerifyCode(@RequestBody SmsVerifyCodeReq req) {
        String mobile = req.getMobile();
        // 没有传手机号时，用账户对应手机号
        if (StringUtils.isBlank(mobile)) {
            mobile = RequestContextHelper.getDriverMobile();
        }
        if (StringUtils.isBlank(mobile)) {
            return Response.createError("获取验证码失败：手机号不存在");
        }
        DriverResDto driver = RequestContextHelper.getLoginDriver();
        if (null != driver && StringUtils.isNotBlank(driver.getCityCode()) && BEIJING_CITY.equals(driver.getCityCode())) {
            req.setMsgSource("mt");
        }
        req.setMobile(mobile);
        req.setUserId(RequestContextHelper.getDriverUuid());
        return withdrawConfigService.sendSmsVerifyCode(req);
    }

    /**
     * 校验验证码并提现
     *
     * @param req DrawCashReq
     * @return {@link Response}
     */
    @ApiOperation(value = "校验验证码并提现", notes = "校验验证码并提现")
    @PostMapping("/drawCash")
    public Response drawCash(@RequestBody @Validated DrawCashReq req) {
        try {
            String mobile = RequestContextHelper.getDriverMobile();
            String userId = RequestContextHelper.getDriverUuid();
            Integer accountType = accountNewUtils.getAccountTypeE(userId);
            long drawAmount = AccountUtils.toIntAmount(new BigDecimal(req.getWithdrawAmount()));
            String driverName = RequestContextHelper.getDriverName();


            //校验验证码
            SmsVerifyCodeReq smsReq = new SmsVerifyCodeReq();
            smsReq.setMobile(mobile);
            smsReq.setUserId(userId);
            smsReq.setTmepIdx("03");
            smsReq.setWithdrawAmount(new BigDecimal(req.getWithdrawAmount()));
            smsReq.setVerifyCode(req.getVerifyCode());
            Response<String> checkResponse = withdrawConfigService.checkSmsVerifyCode(smsReq);
            if (!checkResponse.isSuccess()) {
                return Response.createError(checkResponse.getMsg());
            }


            GeneralItemDto dto = new GeneralItemDto();
            dto.setUserId(userId);
            dto.setBizType(BizType.DRIVER_EARLY_RELEASE_CARD.getType());
            Response response = generalItemService.checkLastResult(dto);
            // 提现加速中，请稍后重试
            if (null != response && !response.isSuccess()) {
                return Response.createError("正在全力以赴的加速中，请重试一下哦！");
            }

        //提现
        WithdrawOrderDto reqDto = new WithdrawOrderDto();
        reqDto.setUserId(userId);
        reqDto.setUserName(driverName);
        reqDto.setAccountType(accountType);
        reqDto.setWithdrawAmount(drawAmount);
        // 只设置提现卡类型，顺风车司机为二清提现
        if (PayAccountTypeEnum.PASSENGER_TYPE.getType() == accountType) {
            reqDto.setCardType(WithdrawCardType.CMB_CLEAR.getType());
        } else {
            reqDto.setCardType(WithdrawCardType.DEBIT_CARD.getType());
        }
        Response saveOrderRes = withdrawOrderService.saveOrderWithSplit(reqDto);

            if (!saveOrderRes.isSuccess()) {
                return Response.createError("提现申请失败:" + saveOrderRes.getMsg());
            }
            saveOrderRes.setData(new Date());
            return saveOrderRes;
        } catch (Exception e) {
            log.error("drawCash error,information:", e);
            return Response.createError("提现处理失败，请重新提交申请");
        }
    }

    @ApiOperation(value = "获取提现申请提交后提示信息", notes = "获取提现申请提交后提示信息")
    @PostMapping("/drawSubmitMsg")
    public Response drawSubmitMsg() {
        String driverUuid = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(driverUuid);
        return withdrawConfigService.queryWithdrawSubmitConfigMsg(accountType, driverUuid);
    }

    @ApiOperation(value = "日进斗金开关接口", notes = "日进斗金开关接口")
    @PostMapping(value = "/getRecommendPassengerFlag")
    public Response<DriverFareClassifyResVo> getRecommendPassengerFlag(HttpServletRequest request) {
        DriverFareClassifyResVo resVo = new DriverFareClassifyResVo();
        resVo.setRecommendPassengerFlag(switchConfig.getRecommendPassengerFlag());
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        WithdrawRuleConfigDto queryDto = new WithdrawRuleConfigDto();
        queryDto.setRuleType(NumberConstants.NUMBER_1);
        queryDto.setConfigStatus(NumberConstants.NUMBER_1);
        queryDto.setAccountType(accountType);
        Response<List<WithdrawRuleConfigDto>> response = withdrawConfigService.queryWithdrawRuleList(queryDto);
        if (null == response || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            return Response.createSuccess(resVo);
        }
        WithdrawRuleConfigDto configDto = response.getData().get(0);
        resVo.setQuestionDesc1(String.format(switchConfig.getQuestionDesc1(),
                configDto.getWithdrawTimeStart(), configDto.getWithdrawTimeEnd()));
        resVo.setQuestionDesc2(String.format(switchConfig.getQuestionDesc2(),
                MoneyUtils.fenToYuan(configDto.getWithdrawMaxAmount()),
                MoneyUtils.fenToYuan(configDto.getWithdrawWeekMaxAmount())));
        resVo.setQuestionDesc3(String.format(switchConfig.getQuestionDesc3(), configDto.getWithdrawMaxCount()));
        return Response.createSuccess(resVo);
    }

    @ApiOperation(value = "费用项列表接口", notes = "费用项列表接口")
    @PostMapping(value = "/getFareItmeList")
    public Response<DriverFareClassifyVo> getClassifyList(@RequestBody DriverFareClassifyVo req) {
        DriverFareClassifyVo resVo = new DriverFareClassifyVo();
        FareItemConfigDto queryParam = new FareItemConfigDto();
        queryParam.setClassify(req.getClassifyCode());
        Response<List<FareItemConfigDto>> response = fareItemConfigService.queryFareItemConfig(queryParam);
        if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
            List<FareItemConfigDto> configDtos = response.getData();
            List<Integer> fareItemList = new ArrayList<>();
            configDtos.forEach(configDto -> {
                if (configDto.getClassify().equals(req.getClassifyCode())) {
                    fareItemList.add(configDto.getFareItem());
                }
            });
            resVo.setFareItemList(fareItemList);
        }
        resVo.setClassifyCode(req.getClassifyCode());
        return Response.createSuccess(resVo);
    }

    /**
     * 查询账户余额 NEW
     *
     * @return {@link Response<AccountBalanceResVo>}
     */
    @ApiOperation(value = "查询账户余额", notes = "查询账户余额")
    @PostMapping("/queryNewAccountBalance")
    public Response<AccountBalanceResVo> queryNewAccountBalance() {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        AccountBalanceResVo balanceResVo = driverWalletBusiness.queryAccountBalance(userId, accountType);
        return Response.createSuccess(balanceResVo);
    }

    /**
     * 收支分析明细
     * 作废了，后续使用  /v2/query/flowList  这个接口 司机收入明细
     * @param req AccountFlowReqVo
     * @return {@link Response<PageResult<AccountFlowResVo>>}
     */
    @Deprecated
    @ApiOperation(value = "收支分析明细", notes = "收支分析明细")
    @PostMapping("/queryAccountAnalyseDetail")
    public Response<PageResult<AccountFlowResVo>> queryAccountAnalyseDetail(@RequestBody AccountFlowReqVo req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        PayAccountFlowsQueryPageDto queryPageDto = new PayAccountFlowsQueryPageDto();
        try {
            if (req.getStartDate() != null && req.getEndDate() != null) {
                queryPageDto.setStartDate(req.getStartDate());
                queryPageDto.setEndDate(req.getEndDate());
            } else {
                Date date = DateUtils.parseDate(req.getYearMonthDay(), DateUtils.FORMAT_DATE_1);
                queryPageDto.setStartDate(DateUtils.getDayStartTimeNew(date));
                queryPageDto.setEndDate(DateUtils.getDayEndTime(date));
            }

        } catch (Exception e) {
            log.error("queryAccountAnalyseDetail_dateParseError" + e.getMessage());
            return Response.createError("按日汇总入参日期错误");
        }
        queryPageDto.setUserId(userId);
        queryPageDto.setAccountType(accountType);
        queryPageDto.setBookTypeList(getNewDriverBookTypeList(accountType));
        if (req.getCurrPage() != null && req.getCurrPage() > 0
                && req.getPageSize() != null && req.getPageSize() > 0) {
            queryPageDto.setCurrPage(req.getCurrPage());
            queryPageDto.setPageSize(req.getPageSize());
        } else {
            queryPageDto.setCurrPage(NumConstants.NUM_1);
            queryPageDto.setPageSize(NumConstants.NUM_200_INT);
        }

        queryPageDto.setFareItemList(req.getFareItemList());
        if (req.getVisitType().equals(NumConstants.NUM_1)) {
            queryPageDto.setNotMatchFareItemList(Collections.singletonList(FARE_ITEM));
        } else if (req.getVisitType().equals(NumConstants.NUM_2)) {
            queryPageDto.setNotMatchFareItemList(Arrays.asList(NOT_MATCH_FARE));
        } else {
            log.info("queryAccountAnalyseDetail_notMatchFareItemList is empty");
        }
        queryPageDto.setChangedType(req.getChangedType());
        if (null == req.getChangedType()) {
            //变更类型为收入和支出
            queryPageDto.setChangedTypeList(Arrays.asList(NumConstants.NUM_1, NumConstants.NUM_2));
        } else if (req.getChangedType() == NumConstant.NUM_1 || req.getChangedType() == NumConstant.NUM_2) {
            //默认过滤 释放、提现
            List<Integer> notMatchFareItemList = Arrays.asList(NOT_MATCH_FARE_PAYOUT);
            if (!CollectionUtils.isEmpty(queryPageDto.getFareItemList())) {
                // 指定查提现的时候，不过滤提现
                notMatchFareItemList = Arrays.stream(NOT_MATCH_FARE_PAYOUT)
                        .filter(a -> !queryPageDto.getFareItemList().contains(a))
                        .collect(Collectors.toList());
            }
            queryPageDto.setNotMatchFareItemList(notMatchFareItemList);
        }
        queryPageDto.setNeedCount(Boolean.FALSE);
        Response<PageResult<PayAccountFlowsDto>> flowsRes = unifiedAccountFacade.getPayAccountFlowListPage(queryPageDto);
        //出租车司机 && 订单流水  区分出租车、网约车
        if (null == flowsRes || !flowsRes.isSuccess()
                || flowsRes.getData() == null || CollectionUtils.isEmpty(flowsRes.getData().getList())) {
            log.warn("query account flow list is empty.");
            return Response.createSuccess();
        }
        PageResult<PayAccountFlowsDto> pageResult1 = flowsRes.getData();
        List<AccountFlowResVo> resVos = new ArrayList<>();
        pageResult1.getList().forEach(item -> {
            AccountFlowResVo resVo = new AccountFlowResVo();
            BeanUtil.copyProperties(item, resVo);
            //金额单位转换
            resVo.setAfterBalance(MoneyUtils.fenToYuan(item.getAfterBalance()));
            resVo.setBeforeBalance(MoneyUtils.fenToYuan(item.getBeforeBalance()));
            resVo.setChangedBalance(MoneyUtils.fenToYuan(item.getChangedBalance()));
            resVo.setAfterPrepareBalance(MoneyUtils.fenToYuan(item.getAfterPrepareBalance()));
            resVo.setAfterAvailableBalance(MoneyUtils.fenToYuan(item.getAfterAvailableBalance()));
            resVo.setAfterFreezeBalance(MoneyUtils.fenToYuan(item.getAfterFreezeBalance()));
            //补充业务时间归属日期
            resVo.setDateStr(DateUtil.format(item.getBizTime(), "yyyy-MM-dd"));
            resVos.add(resVo);
        });
        try {
            //查 t_settlement_common_es 补全其他参数
            driverWalletDwySumService.completionDriverSettlement(resVos);
        } catch (Exception e) {
            log.error("elastic search 查询失败", e);
        }
        PageResult<AccountFlowResVo> pageResult2 = new PageResult<>();
        pageResult2.setTotalCount(pageResult1.getTotalCount());
        pageResult2.setPageSize(pageResult1.getPageSize());
        pageResult2.setCurrPage(pageResult1.getCurrPage());
        pageResult2.setHasMore(pageResult1.isHasMore());
        pageResult2.setList(resVos);
        return Response.createSuccess(pageResult2);
    }

    /**
     * 查询冻结记录明细
     *
     * @param req FreezeRecordReq
     * @return {@link Response<PageResult< FreezeRecordResVo >>}
     */
    @ApiOperation(value = "查询冻结记录明细", notes = "查询冻结记录明细")
    @PostMapping("/queryFreezeRecordePage")
    public Response<PageResult<FreezeRecordResVo>> queryFreezeRecordePage(@RequestBody FreezeRecordReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        PayAccountFlowsQueryPageDto queryPageDto = new PayAccountFlowsQueryPageDto();
        queryPageDto.setUserId(userId);
        queryPageDto.setAccountType(accountType);
        queryPageDto.setBookTypeList(getNewDriverBookTypeList(accountType));
        queryPageDto.setPageSize(req.getPageSize());
        queryPageDto.setCurrPage(req.getPageNum());
        //变更类型为收入和支出
        queryPageDto.setChangedTypeList(Arrays.asList(NumConstants.NUM_3, NumConstants.NUM_4));
        queryPageDto.setNotMatchFareItemList(Arrays.asList(NOT_MATCH_FARE_FREEZE));
        queryPageDto.setNeedCount(Boolean.FALSE);
        Response<PageResult<PayAccountFlowsDto>> flowsRes = unifiedAccountFacade.getPayAccountFlowListPage(queryPageDto);
        if (null == flowsRes || !flowsRes.isSuccess()
                || flowsRes.getData() == null || CollectionUtils.isEmpty(flowsRes.getData().getList())) {
            log.warn("query account flow list is empty.");
            return Response.createSuccess(new PageResult<>());
        }
        List<FreezeRecordResVo> resultList = flowsRes.getData().getList().stream()
                .map(FreezeRecordResVo::buildFreezeCordeResVo).collect(Collectors.toList());
        return Response.createSuccess(
                new PageResult<>(resultList, flowsRes.getData().getTotalCount(),
                        flowsRes.getData().getPageSize(), flowsRes.getData().getCurrPage()));
    }

    /**
     * 查询提现记录
     *
     * @param req FreezeRecordReq
     * @return {@link Response<PageResult< WithdrawRecordResVo >>}
     */
    @ApiOperation(value = "查询提现记录", notes = "查询提现记录")
    @PostMapping("/queryWithdrawRecordPage")
    public Response<PageResult<WithdrawRecordResVo>> queryWithdrawRecordPage(@RequestBody FreezeRecordReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        QueryWithdrawOrderDto queryWithdrawOrderDto = new QueryWithdrawOrderDto();
        queryWithdrawOrderDto.setUserId(userId);
        queryWithdrawOrderDto.setAccountType(accountType);
        queryWithdrawOrderDto.setPageNum(req.getPageNum() == null ? NumConstants.NUM_1 : req.getPageNum());
        queryWithdrawOrderDto.setPageSize(req.getPageSize() == null ? NumConstants.NUM_10 : req.getPageSize());
        queryWithdrawOrderDto.setStartDate(DateUtils.getDateAddMonth(new Date(), -3));
        Response res = withdrawOrderService.queryOrder(
                WithdrawOrderQueryType.QUERY_PAGE.getCode(), queryWithdrawOrderDto);

        if (null == res || !res.isSuccess() || null == res.getData()) {
            return Response.createSuccess(new PageResult<>());
        }
        List<WithdrawRecordResVo> resVoList = new ArrayList<>();
        PageResult<WithdrawOrderDto> result = (PageResult<WithdrawOrderDto>) res.getData();
        for (WithdrawOrderDto withdrawOrderDto : result.getList()) {
            WithdrawRecordResVo resVo = new WithdrawRecordResVo();
            resVo.setOrderUuid(withdrawOrderDto.getOrderUuid());
            resVo.setWithdrawAmount(MoneyUtils.fenToYuan(withdrawOrderDto.getWithdrawAmount()));
            resVo.setCreateTime(withdrawOrderDto.getCreateTime());
            resVo.setWithdrawStatus(withdrawOrderDto.getWithdrawStatus());
            //设置提现失败原因描述和处理方法描述
            setWithdrawFailReason(withdrawOrderDto, resVo);
            resVoList.add(resVo);
        }
        return Response.createSuccess(
                new PageResult<>(resVoList, result.getTotalCount(), result.getPageSize(), result.getCurrPage()));
    }

    /**
     * 验证银行卡信息
     * 银行卡四要素验证接口
     *
     * @param req 要求的事情
     * @return {@link Response}
     */
    @ApiOperation(value = "银行卡四要素验证", notes = "银行卡四要素验证")
    @PostMapping("/verifyBankCardInfo")
    public Response verifyBankCardInfo(@RequestBody CardBindReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        try {
            if (StringUtils.isNotBlank(req.getAgreementCode())) {
                Response<Boolean> signRes = null;
                if (PayAccountTypeEnum.PASSENGER_TYPE.getType() == accountType) {
                    signRes = signHitchDriverAgreement(req, userId);
                } else {
                    signRes = signDriverAgreement(req, userId);
                }
                if (null == signRes || !signRes.isSuccess() || signRes.getData() == null
                        || BooleanUtils.isFalse(signRes.getData())) {
                    log.warn("签署协议失败, driverUuid=" + userId);
                }
            }
        } catch (Exception e) {
            log.error("签署协议失败, driverUuid=" + userId, e);
        }
        // 四要素验证超过错误次数限制
        if (!bindCardErrorCheckBiz.checkOutOfErrorLimit(userId)) {
            return Response.createError(switchConfig.getBindCardErrLimitMsg());
        }

        DriverResDto driver = RequestContextHelper.getLoginDriver();
        if (checkIsSelfCard(driver, req.getCardNo(), req.getMobile())) {
            bindCardErrorCheckBiz.setVerifyBankCardInfo(userId, req.getCardNo());
            return Response.createSuccess();
        }
        return Response.createError("四要素验证失败");
    }

    /**
     * 顺风车车司机签约
     *
     * @param req    CardBindReq
     * @param userId String
     * @return
     */
    private Response<Boolean> signHitchDriverAgreement(CardBindReq req, String userId) {
        AgreementSignReqDto reqDto = new AgreementSignReqDto();
        List<Integer> agreementTypes = new ArrayList<>();
        agreementTypes.add(HITCH_DRIVER_AGREEMENT);
        reqDto.setAgreementTypes(agreementTypes);
        reqDto.setAgreementObject(NumConstants.NUM_1);
        reqDto.setSignChannel(NumConstants.NUM_1);
        reqDto.setMobile(req.getMobile());
        reqDto.setSignChannel(NumConstants.NUM_0);
        reqDto.setUserUuid(userId);
        return passengerAgreementSignService.signAgreement(reqDto);
    }

    /**
     * 网约车司机签约
     *
     * @param req    CardBindReq
     * @param userId String
     * @return
     */
    private Response<Boolean> signDriverAgreement(CardBindReq req, String userId) {
        AgreementSignReqDto reqDto = new AgreementSignReqDto();
        List<String> agreementCodes = new ArrayList<>();
        agreementCodes.add(req.getAgreementCode());
        reqDto.setAgreementCodes(agreementCodes);
        reqDto.setAgreementObject(NumConstants.NUM_2);
        reqDto.setDeviceNo(req.getDeviceNo());
        reqDto.setDriverType("0");
        reqDto.setMobile(req.getMobile());
        reqDto.setSignChannel(NumConstants.NUM_0);
        reqDto.setUserUuid(userId);
        return driverAgreementService.signAgreement(reqDto);
    }

    /**
     * 绑定银行卡
     *
     * @param req 请求参数
     * @return {@link Response}
     */
    @ApiOperation(value = "绑定银行卡", notes = "绑定银行卡")
    @PostMapping("/cardBind")
    public Response cardBind(@RequestBody CardBindReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        //这里加个分布式锁，防止重复绑卡
        String lockKey = RedisKeyConstant.PAY_CENTER_DRIVER_CARD_BIND + userId;
        if (!lockUtils.lockByCoreLink(lockKey)) {
            return Response.createError("请勿重复操作");
        }
        try {
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        DriverResDto driver = RequestContextHelper.getLoginDriver();

        //查询银行卡信息
        BankCardDto bankCardDto = new BankCardDto();
        bankCardDto.setBankCard(req.getCardNo());
        Response<BankCardResponse> bankCardRes = bankCardService.bankCardInfo(bankCardDto);
        if (null == bankCardRes || !bankCardRes.isSuccess() || null == bankCardRes.getData()) {
            return Response.createError("银行卡信息查询失败");
        }

        if (!bankCardRes.getData().isBankSupportWithdraw()) {
            return Response.createError(BindCardErrMsgEnum.getMsgByCode(bankCardRes.getData().getNotSupportReason()));
        }
        // 白名单司机绑卡，不需要发送验证码
        if (StringUtils.isNotBlank(req.getMobile())) {
            if (!bindCardErrorCheckBiz.checkVerifyBankCardInfo(userId, req.getCardNo())) {
                return Response.createError("请进行四要素验证");
            }
            //校验验证码
            SmsVerifyCodeReq smsReq = new SmsVerifyCodeReq();
            smsReq.setMobile(req.getMobile());
            smsReq.setUserId(userId);
            smsReq.setTmepIdx("01");
            smsReq.setVerifyCode(req.getVerifyCode());
            Response<String> checkResponse = withdrawConfigService.checkSmsVerifyCode(smsReq);
            if (!checkResponse.isSuccess()) {
                return Response.createError(checkResponse.getMsg());
            }
        } else {
            WithdrawWhiteListDto dto = new WithdrawWhiteListDto();
            dto.setUserId(userId);
            dto.setRecordStatus(NumberConstants.NUMBER_0);
            Response<WithdrawWhiteListDto> whiteListRes = bindCardService.queryWithdrawWhiteList(dto);
            if (null == whiteListRes || !whiteListRes.isSuccess() || null == whiteListRes.getData()) {
                return Response.createError("非白名单司机，不能绑卡！");
            }
        }

        //绑卡
        BindCardDto bindCardDto = new BindCardDto();
        bindCardDto.setUserId(userId);
        bindCardDto.setAccountType(accountType);
        bindCardDto.setBankName(req.getBankName());
        bindCardDto.setCardNo(req.getCardNo());
        bindCardDto.setCardType(NumConstants.NUM_1);
        // 白名单司机
        if (StringUtils.isNotBlank(req.getName())) {
            bindCardDto.setName(req.getName().trim());
        } else {
            if (StringUtils.isNotBlank(driver.getName())) {
                bindCardDto.setName(driver.getName().trim());
            }
        }
        return bindCardService.saveBind(bindCardDto);
        } catch (Exception e){
            log.error("绑定银行卡 异常 userId={}",userId,e);
        } finally {
            lockUtils.unLock(lockKey);
        }
        return  Response.createError("绑定银行卡失败");
    }

    /**
     * 提现限制检察
     *
     * @param req 请求参数
     * @return {@link Response}
     */
    @ApiOperation(value = "提现限制检察", notes = "提现限制检察")
    @PostMapping("/drawCashLimitCheck")
    public Response drawCashLimitCheck(@RequestBody @Validated DrawCashLimitCheckReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        long drawAmount = MoneyUtils.yuanToFen(req.getWithdrawAmount());
        Response<String> res = withdrawConfigService.checkWithdrawRule(userId, accountType, new Date(), drawAmount);
        if (res.isSuccess()) {
            return Response.createSuccess(true);
        } else {
            return Response.createError(res.getMsg());
        }
    }

    /**
     * 查询绑卡信息
     *
     * @return {@link Response}
     */
    @ApiOperation(value = "查询绑卡信息", notes = "查询绑卡信息")
    @PostMapping("/queryBindInfo")
    public Response<BindCardInfoVo> queryBindInfo() {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        DriverResDto driver = RequestContextHelper.getLoginDriver();

        //查询绑卡信息
        BindCardDto bindCardDto = new BindCardDto();
        bindCardDto.setUserId(userId);
        bindCardDto.setAccountType(accountType);
        Response<BindCardDto> response = bindCardService.queryBindInfo(bindCardDto);
        if (null == response || !response.isSuccess() || null == response.getData()) {
            return Response.createSuccess();
        }
        BindCardInfoVo bindCardInfoVo = new BindCardInfoVo();
        BeanUtils.copyProperties(response.getData(), bindCardInfoVo);
        bindCardInfoVo.setName(driver.getName());

        return Response.createSuccess(bindCardInfoVo);
    }

    /**
     * 查询绑卡信息
     *
     * @return {@link Response}
     */
    @ApiOperation(value = "银行卡信息查询", notes = "银行卡信息查询")
    @PostMapping("/bankcardInfo")
    public Response<BankcardInfoResVo> bankcardInfo(@RequestBody @Validated BankcardInfoReq req) {
        BankCardDto bankCardDto = new BankCardDto();
        bankCardDto.setBankCard(req.getBankCard());
        Response<BankCardResponse> response = bankCardService.bankCardInfo(bankCardDto);

        if (null == response || !response.isSuccess() || null == response.getData()) {
            return Response.createError("银行卡信息查询失败");
        }

        BankcardInfoResVo vo = new BankcardInfoResVo();
        BeanUtils.copyProperties(response.getData(), vo);
        if (StringUtils.isNotBlank(response.getData().getNotSupportReason())) {
            vo.setMsg(BindCardErrMsgEnum.getMsgByCode(response.getData().getNotSupportReason()));
        }
        return Response.createSuccess(vo);
    }

    /**
     * 银行列表
     *
     * @return {@link Response<List<WithdrawBankDto>>}
     */
    @ApiOperation(value = "银行列表", notes = "银行列表")
    @PostMapping("/bankList")
    public Response<List<WithdrawBankDto>> bankList() {
        Response<List<WithdrawBankDto>> res = withdrawBankService.selectBankList();
        if (null == res || !res.isSuccess() || CollectionUtils.isEmpty(res.getData())) {
            return Response.createError("支持银行列表查询失败");
        }
        List<WithdrawBankDto> bankList = res.getData().stream()
                .filter(a -> !StringUtils.contains(switchConfig.getBankWithout(), a.getBankCode()))
                .collect(Collectors.toList());
        res.setData(bankList);
        return res;
    }

    /**
     * 查询司机钱包协议
     *
     * @return {@link Response<DriverAgreementResVo>}
     */
    @ApiOperation(value = "查询钱包协议", notes = "查询钱包协议")
    @PostMapping("/queryDriverWalletAgreement")
    public Response<DriverAgreementResVo> queryDriverWalletAgreement() {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        DriverAgreementResVo resVo = new DriverAgreementResVo();
        if (PayAccountTypeEnum.PASSENGER_TYPE.getType() == accountType) {
            queryHitchDriverAgreement(userId, resVo);
        } else {
            queryDriverAgreement(userId, resVo);
        }
        return Response.createSuccess(resVo);
    }

    /**
     * 查询顺风车司机协议
     *
     * @param userId String
     * @param resVo  DriverAgreementResVo
     */
    private void queryHitchDriverAgreement(String userId, DriverAgreementResVo resVo) {
        AgreementUnSignReqDto reqDto = new AgreementUnSignReqDto();
        List<Integer> agreementTypes = new ArrayList<>();
        agreementTypes.add(HITCH_DRIVER_AGREEMENT);
        reqDto.setAgreementTypes(agreementTypes);
        reqDto.setUserUuid(userId);
        reqDto.setAgreementObject(NumberConstants.NUMBER_1);
        reqDto.setSignChannel(NumberConstants.NUMBER_1);
        Response<List<AgreementUnSignResDto>> agreementsRes = passengerAgreementSignService.getUserUnSignAgreements(reqDto);
        if (agreementsRes.isSuccess() && CollectionUtil.isNotEmpty(agreementsRes.getData())) {
            resVo.setAgreementCode(agreementsRes.getData().get(0).getAgreementCode());
            resVo.setProtocolLink(agreementsRes.getData().get(0).getProtocolLink());
        }
    }

    /**
     * 查询网约车司机协议
     *
     * @param userId String
     * @param resVo  DriverAgreementResVo
     */
    private void queryDriverAgreement(String userId, DriverAgreementResVo resVo) {
        AgreementUnSignReqDto reqDto = new AgreementUnSignReqDto();
        reqDto.setAgreementType(NumberConstants.NUMBER_0);
        reqDto.setUserUuid(userId);
        reqDto.setAgreementObject(NumberConstants.NUMBER_2);
        reqDto.setDriverType("0");
        reqDto.setSignChannel(0);
        reqDto.setSecondAgreementType(NumberConstants.NUMBER_2);
        Response<List<AgreementUnSignResDto>> agreementsRes = driverAgreementService.getUserNormalUnSignAgreements(reqDto);
        if (agreementsRes.isSuccess() && CollectionUtil.isNotEmpty(agreementsRes.getData())) {
            resVo.setAgreementCode(agreementsRes.getData().get(0).getAgreementCode());
            resVo.setProtocolLink(agreementsRes.getData().get(0).getProtocolLink());
        }
    }

    /**
     * 获取账本流水
     *
     * @param req 请求参数
     * @return {@link Response<QueryWithdrawOrderResDto>}
     */
    @ApiOperation(value = "获取支出详情", notes = "获取支出详情")
    @PostMapping("/withdrawDetail")
    public Response<QueryWithdrawOrderResDto> accountBookFlows(@RequestBody @Validated WithDrawDetailReq req) {
        QueryWithdrawOrderDto queryDto = new QueryWithdrawOrderDto();
        queryDto.setOrderUuid(req.getBizNo());
        Response response = withdrawOrderService.queryOrder(NumConstants.NUM_3, queryDto);
        if (null == response || !response.isSuccess() || null == response.getData()) {
            return Response.createError("查询失败");
        }
        WithdrawOrderDto withdrawOrderDto = (WithdrawOrderDto) response.getData();
        QueryWithdrawOrderResDto resDto = new QueryWithdrawOrderResDto();
        BeanUtils.copyProperties(withdrawOrderDto, resDto);
        resDto.setWithdrawAmount(MoneyUtils.fenToYuan(withdrawOrderDto.getWithdrawAmount()));
        return Response.createSuccess(resDto);
    }

    /**
     * 解绑银行卡
     *
     * @param req 请求参数
     * @return {@link Response}
     */
    @ApiOperation(value = "解绑银行卡", notes = "解绑银行卡")
    @PostMapping("/unBindCard")
    public Response unBindCard(@RequestBody CardBindReq req) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        String mobile = RequestContextHelper.getDriverMobile();
        //校验验证码
        SmsVerifyCodeReq smsReq = new SmsVerifyCodeReq();
        smsReq.setMobile(mobile);
        smsReq.setUserId(userId);
        smsReq.setTmepIdx("02");
        smsReq.setVerifyCode(req.getVerifyCode());
        Response<String> checkResponse = withdrawConfigService.checkSmsVerifyCode(smsReq);
        if (!checkResponse.isSuccess()) {
            return Response.createError(checkResponse.getMsg());
        }
        // 解绑
        BindCardDto bindCardDto = new BindCardDto();
        bindCardDto.setUserId(userId);
        bindCardDto.setCardNo(req.getCardNo());
        bindCardDto.setAccountType(accountType);
        bindCardDto.setCardType(1);
        bindCardDto.setBankName(req.getBankName());
        return bindCardService.modifyBindInfo(NumberConstants.NUMBER_1, bindCardDto);
    }

    /**
     * 自我检查卡片
     * 检查是否本人银行卡
     *
     * @param driver   司机
     * @param bankCard 银行卡
     * @param mobile   mobile
     * @return boolean
     */
    private boolean checkIsSelfCard(DriverResDto driver, String bankCard, String mobile) {
        BankCardAuthenticationReqDto backCardAuthenticationReqDto = new BankCardAuthenticationReqDto();
        backCardAuthenticationReqDto.setRealName(driver.getName().trim());
        backCardAuthenticationReqDto.setIdCard(driver.getIdCard());
        backCardAuthenticationReqDto.setMobile(mobile);
        backCardAuthenticationReqDto.setBankCard(bankCard);
        log.info("调用开放平台校验银行卡四要素请求:{}", JSON.toJSONString(backCardAuthenticationReqDto));

        if (isTest) {
            return true;
        }
        Response<BankCardAuthenticationRespDto> res = openApiOcrRest.bankCardAuthentication(backCardAuthenticationReqDto);
        if (null != res && res.isSuccess() && null != res.getData() && SUCCESS_CODE.equals(res.getData().getResCode())) {
            return true;
        }
        bindCardErrorCheckBiz.incError(driver.getUuid());
        return false;
    }

    /**
     * 设置提现失败原因描述和处理方法描述
     *
     * @param withdrawOrderDto dto
     * @param vo               vo
     */
    public void setWithdrawFailReason(WithdrawOrderDto withdrawOrderDto, WithdrawRecordResVo vo) {
        if (!WithdrawStatus.WITHDRAW_FAIL.getCode().equals(withdrawOrderDto.getWithdrawStatus())
                && !WithdrawStatus.WITHDRAW_BACK_TICKET.getCode().equals(withdrawOrderDto.getWithdrawStatus())) {
            return;
        }
        if (StringUtils.isBlank(withdrawOrderDto.getRemark())) {
            vo.setRemark(getWithDrawFailDesc("提现失败异常描述"));
            return;
        }
        String withDrawFailDesc = this.getWithDrawFailDesc(withdrawOrderDto.getRemark());
        if (StringUtils.isBlank(withDrawFailDesc)) {
            withDrawFailDesc = getWithDrawFailDesc("提现失败异常描述");
        }
        vo.setRemark(withDrawFailDesc);
    }

    /**
     * 获取提现失败描述
     *
     * @param name
     * @return
     */
    private String getWithDrawFailDesc(String name) {
        String valueStr = cache.getIfPresent(name);
        if (StringUtils.isNotBlank(valueStr)) {
            log.info("name={},valueStr={}", name, valueStr);
            return valueStr;
        }
        DictDto dto = new DictDto();
        dto.setParentCode("WD0001");
        dto.setDictName(name);
        Response<List<DictDto>> listResponse = dictService.selectList(dto);
        if (!listResponse.isSuccess() || CollectionUtils.isEmpty(listResponse.getData())) {
            return "";
        }
        cache.put(listResponse.getData().get(0).getDictName(), listResponse.getData().get(0).getDictValue());
        return listResponse.getData().get(0).getDictValue();
    }


    /**
     * 获取司机的账本类型
     *
     * @return 非出租车司机为23，出租车司机为23,24
     */
    private List<Integer> getNewDriverBookTypeList(int accountType) {
        return Arrays.asList(BookTypeEnum.DRIVER_SALARY.getType(),
                BookTypeEnum.TAXI_DRIVER_CLEAR.getType());
    }


    /**
     * 查询司机是否在提现白名单之内
     *
     * @return true:在；false:不在
     */
    @ApiOperation(value = "查询提现白名单", notes = "查询提现白名单")
    @PostMapping("/queryWithdrawWhiteList")
    @Deprecated
    public Response<Boolean> queryWithdrawWhiteList() {
        String userId = RequestContextHelper.getDriverUuid();
        WithdrawWhiteListDto dto = new WithdrawWhiteListDto();
        dto.setUserId(userId);
        dto.setRecordStatus(NumberConstants.NUMBER_0);
        Response<WithdrawWhiteListDto> whiteListRes = bindCardService.queryWithdrawWhiteList(dto);
        if (null != whiteListRes && whiteListRes.isSuccess() && null != whiteListRes.getData()) {
            return Response.createSuccess(true);
        }
        return Response.createSuccess(false);
    }

    @ApiOperation(value = "查询约约司机提现入口", notes = "查询约约司机提现入口")
    @PostMapping("/queryOutWithdrawEntrance")
    @Deprecated
    public Response<Boolean> queryOutWithdrawEntrance() {
        String idCard = RequestContextHelper.getDriverIdCard();
        UserAccountOutDto queryParam = new UserAccountOutDto();
        queryParam.setIdCardNo(idCard);
        Response<UserAccountOutDto> userAccountRes = outWithdrawService.getUserAccount(queryParam);
        // 不存在直接返回
        if (null == userAccountRes || !userAccountRes.isSuccess() || null == userAccountRes.getData()) {
            return Response.createSuccess(false);
        }
        UserAccountOutDto userAccount = userAccountRes.getData();
        BigDecimal totalAmt = userAccount.getPlatformAmount().add(userAccount.getAwardAmount());
        // 金额大于0 返回true
        if (totalAmt.compareTo(BigDecimal.ZERO) > 0) {
            return Response.createSuccess(true);
        }
        // 180天之内是否有提现记录
        WithdrawRecordOutDto queryWithdrawRecord = new WithdrawRecordOutDto();
        queryWithdrawRecord.setAccountId(userAccount.getId());
        queryWithdrawRecord.setStartCreateTime(com.t3.ts.utils.DateUtils.addDays(new Date(), -180));
        Response<List<WithdrawRecordOutDto>> recordRes = outWithdrawService.queryWithdrawRecord(queryWithdrawRecord);
        if (null != recordRes && recordRes.isSuccess() && !CollectionUtils.isEmpty(recordRes.getData())) {
            return Response.createSuccess(true);
        }
        return Response.createSuccess(false);
    }


    /**
     * 招行开卡 返回跳转参数
     *
     * @param cmbCardApplyDto the ali user dto
     * @return the adaptive response
     */
    @PostMapping("/cmbCard/apply")
    @ApiOperation(value = "招行开卡申请", notes = "招行开卡申请")
    public Response apply(@RequestBody CmbCardApplyDto cmbCardApplyDto) {
        String driverName = RequestContextHelper.getDriverName();
        String idCard = RequestContextHelper.getDriverIdCard();
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        cmbCardApplyDto.setUserId(userId);
        cmbCardApplyDto.setAccountType(accountType);
        cmbCardApplyDto.setIdCard(idCard);
        cmbCardApplyDto.setName(driverName);

        if (StringUtils.isBlank(cmbCardApplyDto.getUserId()) || cmbCardApplyDto.getAccountType() == null
                || StringUtils.isBlank(cmbCardApplyDto.getIdCard())) {
            log.error("招行开卡申请 司机信息缺失 cmbCardApplyDto={}", JSON.toJSONString(cmbCardApplyDto));
            return Response.createError("招行开卡申请 司机信息缺失");
        }

        //加签
        JSONObject json = new JSONObject();
        json.put("reqTime", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        json.put("idcEncrypt", DigestUtils.md5DigestAsHex(cmbCardApplyDto.getIdCard().getBytes()));
        json.put("id", cmbCardApplyDto.getUserId());
        AdaptiveRequest adaptiveRequest = new AdaptiveRequest();
        adaptiveRequest.setApiMethod(PayCodeEnum.CMB_APPLY_CARD.value());
        adaptiveRequest.setData(json.toJSONString());
        AdaptiveResponse response = adaptiveCoreService.call(adaptiveRequest);
        if (null != response && response.getSuccess() && null != response.getData()) {
            json.putAll((JSONObject) response.getData());
            return Response.createSuccess("成功", json);
        }
        return Response.createError("加签失败");
    }

}
