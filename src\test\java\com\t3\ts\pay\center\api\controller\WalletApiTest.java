package com.t3.ts.pay.center.api.controller;

import com.t3.ts.account.center.dto.PersonSuperviseAccountDto;
import com.t3.ts.pay.center.api.business.EnterprisePaymentBusiness;
import com.t3.ts.pay.center.api.dto.BasePageReq;
import com.t3.ts.pay.center.api.dto.DriverPaymentReq;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WalletApiTest {

    @Mock
    EnterprisePaymentBusiness enterprisePaymentBusiness;
    @Mock
    Logger log;
    @InjectMocks
    WalletApi walletApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetAccountFlowPassenger() throws Exception {
        when(walletApi.getAccountFlowPassenger(any(), any())).thenReturn(null);

        Response result = walletApi.getAccountFlowPassenger(new BasePageReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAccountInfo() throws Exception {
        when(walletApi.accountInfo(any(), any())).thenReturn(null);

        Response result = walletApi.accountInfo(new QueryDto(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetAccountCashFlowPassenger() throws Exception {
        when(walletApi.getAccountCashFlowPassenger(any(), any())).thenReturn(null);

        Response result = walletApi.getAccountCashFlowPassenger(null, new QueryDto());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBindCardInfo() throws Exception {
        when(walletApi.bindCardInfo(any(), any())).thenReturn(null);

        Response result = walletApi.bindCardInfo(null, new QueryDto());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBindCard() throws Exception {
        when(walletApi.bindCard(any(), any())).thenReturn(null);

        Response result = walletApi.bindCard(null, new PersonSuperviseAccountDto());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCashDetail() throws Exception {
        when(walletApi.cashDetail(any(), any())).thenReturn(null);

        Response result = walletApi.cashDetail(null, new QueryDto());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testBookBalance() throws Exception {
        when(walletApi.bookBalance(any(), any())).thenReturn(null);

        Response result = walletApi.bookBalance(null, new QueryDto());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testPersonOpenAcc() throws Exception {
        when(walletApi.personOpenAcc(any(), any())).thenReturn(null);

        Response result = walletApi.personOpenAcc(null, new QueryDto());
        Assert.assertEquals(null, result);
    }
}
