package com.t3.ts.pay.center.api.rest;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.pay.center.api.rest.mall.MallOrder;
import com.t3.ts.pay.center.api.rest.mall.MallOrderData;
import com.t3.ts.result.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName MallRest.java
 * @Description TODO
 * @createTime 2021年07月14日 14:41:00
 */
@Component
public class MallRest extends BaseRest {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private SlbConfig slbConfig;

    /**
     * 商城下单-券套餐
     *
     * @param mallOrder 下单参数
     * @return 下单情况
     */
    public Response<MallOrderData> mallOrderAdd(MallOrder mallOrder) {
        String postHttp = sendPostHttp(slbConfig.getMallUrl() + "/api/mall/order/add",
                JSONObject.toJSONString(mallOrder));
        if (StrUtil.isNotBlank(postHttp)) {
            Response<MallOrderData> response = JSONObject.parseObject(postHttp,
                    new TypeReference<Response<MallOrderData>>() {
                    });
            return response;
        }
        return null;
    }

    /**
     * 充值订单校验
     *
     * @param orderCode orderCode
     * @param userUuid  userUuid
     * @return RechargeCheckResp
     */
    public Response<?> rechargeCheck(String orderCode, String userUuid, String payTypeName) {
        JSONObject request = new JSONObject();
        request.put("orderCode", orderCode);
        request.put("userUuid", userUuid);
        request.put("payTypeName", payTypeName);
        String response = sendPostHttp(slbConfig.getMember() + "/mall/payCheck",
                request.toJSONString());
        logger.info("MallRest.rechargeCheck,request:{},response:{}", request.toJSONString(), response);
        return JSONUtil.toBean(response, Response.class);
    }

    /**
     * 更新订单支付信息
     *
     * @param req          req
     * @param settlementId settlementId
     */
    public void updatePayInfo(PayOrderSureReq req, String settlementId) {
        JSONObject request = new JSONObject();
        request.put("generatePayStatus", 0);
        request.put("orderCode", req.getOrderCode());
        request.put("payNo", settlementId);
        request.put("payOrderChannel", req.getPayOrderChannel());
        request.put("payTypeName", req.getPayTypeName());
        String response = sendPostHttp(slbConfig.getMember() + "/mall/updatePayInfo",
                request.toJSONString());
        logger.info("MallRest.updatePayInfo,request:{},response:{}", request.toJSONString(), response);
    }
}
