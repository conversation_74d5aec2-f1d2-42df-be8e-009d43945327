package com.t3.ts.pay.center.api.dto.vo;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-06
 */
public class PayWayVo {
    /**
     * 支付渠道.1:支付宝,2:微信,3:一网通
     */
    private Integer payChannel;

    /**
     * 支付名称
     */
    private String payName;

    /**
     * 是否推荐标识 0:否 1:是
     */
    private int recommendFlag;

    /**
     * 优惠文案
     */
    private List<String> discountTags;

    /**
     * 渠道支付优惠文案
     */
    private String channelPreferenceMsg;

    /**
     * 是否有渠道支付优惠券
     */
    private Boolean channelPreferenceFlag = Boolean.FALSE;

    /**
     * 是否展示微信支付分免密入口
     */
    private Boolean showNoPassword = Boolean.FALSE;

    /**
     * 是否展示支付宝支付并开通免密
     */
    private Boolean showAliPayAndSign = Boolean.FALSE;

    /**
     * app免密支付新体验，默认false，true表示是新版本
     */
    private Boolean useNewWxSign = Boolean.FALSE;

    public Boolean getUseNewWxSign() {
        return useNewWxSign;
    }

    public void setUseNewWxSign(Boolean useNewWxSign) {
        this.useNewWxSign = useNewWxSign;
    }

    public Boolean getShowAliPayAndSign() {
        return showAliPayAndSign;
    }

    public void setShowAliPayAndSign(Boolean showAliPayAndSign) {
        this.showAliPayAndSign = showAliPayAndSign;
    }

    /**
     * 按钮标题
     */
    private String noPasswordTitle;

    /**
     * 按钮副标题
     */
    private String noPasswordSubTitle;

    /**
     * 按钮图标
     */
    private String noPasswordIcon;


    public Boolean getChannelPreferenceFlag() {
        return channelPreferenceFlag;
    }

    public void setChannelPreferenceFlag(Boolean channelPreferenceFlag) {
        this.channelPreferenceFlag = channelPreferenceFlag;
    }

    public String getChannelPreferenceMsg() {
        return channelPreferenceMsg;
    }

    public void setChannelPreferenceMsg(String channelPreferenceMsg) {
        this.channelPreferenceMsg = channelPreferenceMsg;
    }

    public int getRecommendFlag() {
        return recommendFlag;
    }

    public void setRecommendFlag(int recommendFlag) {
        this.recommendFlag = recommendFlag;
    }

    public List<String> getDiscountTags() {
        return discountTags;
    }

    public void setDiscountTags(List<String> discountTags) {
        this.discountTags = discountTags;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public String getPayName() {
        return payName;
    }

    public void setPayName(String payName) {
        this.payName = payName;
    }

    public Boolean getShowNoPassword() {
        return showNoPassword;
    }

    public void setShowNoPassword(Boolean showNoPassword) {
        this.showNoPassword = showNoPassword;
    }

    public String getNoPasswordTitle() {
        return noPasswordTitle;
    }

    public void setNoPasswordTitle(String noPasswordTitle) {
        this.noPasswordTitle = noPasswordTitle;
    }

    public String getNoPasswordSubTitle() {
        return noPasswordSubTitle;
    }

    public void setNoPasswordSubTitle(String noPasswordSubTitle) {
        this.noPasswordSubTitle = noPasswordSubTitle;
    }

    public String getNoPasswordIcon() {
        return noPasswordIcon;
    }

    public void setNoPasswordIcon(String noPasswordIcon) {
        this.noPasswordIcon = noPasswordIcon;
    }
}
