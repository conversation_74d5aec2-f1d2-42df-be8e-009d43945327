package com.t3.ts.pay.center.api.config;

import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.pay.center.api.config.interceptor.HttpTraceLogInterceptor;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.util.SpringUtils;
import com.t3.ts.pay.common.http.driver.dto.res.DriverResDto;
import com.t3.ts.pay.common.num.NumConstant;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.maven.artifact.versioning.DefaultArtifactVersion;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_4;


/**
 * 请求上下文帮助器
 *
 * <AUTHOR>
 * @since 2020/10/26
 */
@Component
public class RequestContextHelper {

    private static final String REQUEST_HEADER_DEVICE_FINGERPRINT_TOKEN = "riskDeviceToken";
    private static final String REMOTE_IP = "remoteip";

    /**
     * 获取App版本号
     *
     * @return {@link String}
     */
    public static String getGrayVersion() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        return request.getHeader(HttpTraceLogInterceptor.HEADER_GRAYVERSION);
    }

    /**
     * 获取设备指纹token
     *
     * @return {@link String}
     */
    public static String getDeviceFingerPrintToken() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        return request.getHeader(REQUEST_HEADER_DEVICE_FINGERPRINT_TOKEN);
    }

    /**
     * 获取乘客IP
     *
     * @return {@link String}
     */
    public static String getRemoteIp() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        return request.getHeader(REMOTE_IP);
    }

    /**
     * 获取乘客uuid
     *
     * @return {@link String}
     */
    public static String getPassengerUuid() {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attr.getRequest();
            return (String) request.getAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_UUID);
        }
        return SpringUtils.getBean(RequestContextHelperInstead.class).getPassengerUuid();
    }

    /**
     * 获取用户手机号码
     *
     * @return {@link String}
     */
    public static String getPassengerMobile() {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attr.getRequest();
            return (String) request.getAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_MOBILE);
        }
        return SpringUtils.getBean(RequestContextHelperInstead.class).getPassengerMobile();
    }

    /**
     * 司机手机号
     *
     * @return {@link String}
     */
    public static String getDriverMobile() {
        return SpringUtils.getBean(RequestContextHelperInstead.class).getDriverMobile();
    }

    /**
     * 司机uuid
     *
     * @return {@link String}
     */
    public static String getDriverUuid() {
        return SpringUtils.getBean(RequestContextHelperInstead.class).getDriverUuid();
    }

    /**
     * 司机身份证号
     *
     * @return {@link String}
     */
    public static String getDriverIdCard() {
        return SpringUtils.getBean(RequestContextHelperInstead.class).getDriverIdCard();
    }

    /**
     * 司机name
     *
     * @return {@link String}
     */
    public static String getDriverName() {
        return SpringUtils.getBean(RequestContextHelperInstead.class).getDriverName();
    }

    /**
     * 获取登录司机
     *
     * @return {@link String}
     */
    public static DriverResDto getLoginDriver() {
        return SpringUtils.getBean(RequestContextHelperInstead.class).getLoginDriver();
    }


}
