package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-13 13:48
 * @des: 行程支付入参对象
 */
@Data
@ApiModel
public class RoutePaymentReq {

    @NotBlank(message = "行程uuid不能为空")
    @ApiModelProperty(value = "行程uuid", required = true)
    private String orderUuid;

    // 使用省心打标记 1表示使用省心打
    @ApiModelProperty(value = "使用省心打标记 1表示使用省心打", required = false)
    private Integer unWarriedArrive;

    /**
     * 优惠券uuid
     */
    @ApiModelProperty(value = "优惠券uuid，使用券套餐时此处传的是券套餐中的某张优惠券id")
    private String couponUuid;

    /**
     * 券套餐商品sku
     */
    @ApiModelProperty(value = "券套餐商品sku，不为空时表示购买券套餐", required = false)
    private String skuCode;

    @ApiModelProperty(value = "新券套餐商品uuid，不为空时表示购买新券套餐", required = false)
    private String skuUuid;

    @ApiModelProperty(value = "新券套餐商品skuCode",  required = false)
    private String skuCodeNew;
    /**
     * 使用券套餐里面的优惠券 1表示使用
     */
    @ApiModelProperty(value = "1表示购买券套餐并使用券，2表示只购买券套餐", required = false)
    private Integer useCounponActivity;

    /**
     * 车费支付方式【包含余额+一种第三方支付】
     */
    @NotNull(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式", required = true)
    private List<Integer> payTypeList;


    /**
     * {@link com.t3.ts.pay.remote.constants.EnumPayOrderType}
     */
    @ApiModelProperty(value = "支付订单类型", required = true)
    private Integer payOrderType;


    /**
     * 出租车扫码支付
     */
    @ApiModelProperty(value = "出租车扫码支付")
    private Boolean taxiScanPay = false;


    /**
     * 微信小程序code-微信小程序支付
     */
    @ApiModelProperty(value = "小程序code", required = true)
    private String code;

    /**
     * 支付并签约标识
     */
    @ApiModelProperty(value = "支付并签约标识", required = false)
    private String payAndSign;

    /**
     * 微信免密签约 记录用户是否点击滑块取消 0-取消 1-选中 null-不记录
     */
    private Integer selWxSign;
    /**
     * 亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择
     */
    @ApiModelProperty(value = "亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择")
    private Integer payForOtherType;
    /**
     * 为true时 表示从收银台发起的支付为免密支付，默认false
     */
    @ApiModelProperty(value = "为true时 表示从收银台发起的支付为免密支付，默认false")
    private Boolean noSecret;

    /**
     * 权益卡uuid
     */
    @ApiModelProperty(value = "权益卡uuid")
    private String privilegeUuid;

    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;

    /**
     * 用户支付时使用的终端，类似下单打车时的终端 Srorder.extInfo.subSource
     * 微信APP 九宫格入口 userChannel= 201
     */
    @ApiModelProperty(value = "用户支付时使用的终端")
    private Integer userChannel;

    /**
     * 来源
     */
    private int source = 0;

    /**
     * 支付宝  用户付款中途退出返回商户网站的地址
     */
    private String quitUrl;


    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 新老人标识
     */
    private String sellType;

    /**
     * 来源
     * 注释同 {@link com.t3.ts.pay.center.api.dto.trade.RechargeReq.subSource}  字段注释
     */
    private String subSource;

    /**
     * 用户ip
     */
    private String userIp;

}
