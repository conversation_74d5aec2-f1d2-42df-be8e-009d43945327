package com.t3.ts.pay.center.api.service.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Description: 简单三方支付流程
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:25
 */
@Slf4j
@Component
public class ThanksPayHandlerAbstract extends AbstractSimpleThirdPayHandler implements PayHandler {

    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    /**
     * get current type
     *
     * @return {@link String}
     */
    @Override
    public String getType() {
        return "generalOnlyThird";
    }

    /**
     * 参数校验
     *
     * @param context 上下文
     */
    @Override
    public void checkParam(PayContext context) {
        PayHandler.super.checkParam(context);
        JSONObject json = JSON.parseObject(context.getMessage());
        context.setAmount(json.getBigDecimal("amount"));
        context.getExtendParam().put("input_routeId", json.getString("routeId"));
        context.getExtendParam().put("input_driverId", json.getString("driverId"));
        String remark = json.getString("remark");
        if (StringUtils.isNotEmpty(remark) && remark.trim().length() > NumConstants.NUM_140) {
            throw new IllegalArgumentException("备注字段过长");
        }
        context.getExtendParam().put("input_remark", remark);
    }


    /**
     * save settlement
     *
     * @param context 上下文
     */
    @Override
    public void checkSettlement(PayContext context) {
        SettlementGeneralDto dto = new SettlementGeneralDto();
        dto.setBizType(getBizType(context));
        dto.setOrderUuid((String) context.getExtendParam().get("input_routeId"));
        dto.setPaymentSubject(context.getUserId());
        dto.setPaymentSubjectType(1);
        dto.setIncomeSubject((String) context.getExtendParam().get("input_driverId"));
        dto.setSettleType(0);
        dto.setOrderFare(context.getAmount());
        dto.setTotalFare(context.getAmount());
        dto.setOtherFareDetail((String) context.getExtendParam().get("input_remark"));
        Response response = settlementGeneralService.addSettlement(dto);
        if (!response.isSuccess()) {
            throw new IllegalStateException("保存结算失败");
        }
        context.getExtendParam().put("inner_settleId", response.getData());
    }


    /**
     * get  pay  type
     * pay-center EnumPayOrderType
     *
     * @return {@link Integer}
     */
    @Override
    public Integer getBizType(PayContext context) {
        return BizType.THANKS.getType();
    }

}
