package com.t3.ts.pay.center.api.enums;

/**
 * Describer:
 * maoyn
 * 2021/10/27 10:10
 */
public enum InvoiceProductLine {
    CZC(1, "出租车"),
    Z<PERSON>(2, "专享"),
    K<PERSON>(4, "快享"),
    <PERSON><PERSON>(5, "顺风车"),
    <PERSON><PERSON>(6, "惠享"),
    ZDJS(7, "自动驾驶"),
    BC(10, "包车"),
    BC_ZC(11, "包车-专车"),
    BC_KC(12, "包车-快车"),
    QY_YC_KX(13, "企业用车-快享"),
    QY_YC(14, "企业用车"),
    QY_YC_BC(15, "企业用车-包车");

    private Integer productLine;
    private String productMsg;

    InvoiceProductLine(Integer productLine, String productMsg) {
        this.productLine = productLine;
        this.productMsg = productMsg;
    }

    /**
     * getByProductLine
     *
     * @param productLine 1
     * @return {@link RouteStatus}
     */
    public static InvoiceProductLine getByProductLine(Integer productLine) {
        for (InvoiceProductLine invoiceProductLine : values()) {
            if (invoiceProductLine.getProductLine().equals(productLine)) {
                return invoiceProductLine;
            }
        }
        return null;
    }

    public Integer getProductLine() {
        return this.productLine;
    }

    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    public String getProductMsg() {
        return this.productMsg;
    }

    public void setProductMsg(String productMsg) {
        this.productMsg = productMsg;
    }
}
