package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁移 by ivy .2021/09/17 14:20
 *
 * @Author: qul
 * @Date: 2021/4/25 15:12
 */
@Getter
@Setter
public class MallInvoiceResDto implements Serializable {

    private static final long serialVersionUID = -8253382988527160023L;

    /**
     * 订单ID
     */
    private String orderNo;

    /**
     * 城市CODE
     */
    private String cityCode;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户id
     */
    private String userUuid;

    /**
     * orderCreateTime
     */
    private String orderCreateTime;

    /**
     * 业务类型 0积分商城 7 商城礼品卡
     */
    private Integer bizType;
    /**
     * 可开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 开票状态 0-待开票 1-已开票
     */
    private Integer invoiceStatus;

    /**
     * 开票次数
     */
    private Integer invoiceTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品数量
     */
    private Integer skuNum;

    private Integer invoiceClass;

    private Integer invoiceSubjectCode;

    /**
     * 开票主体
     */
    private String invoiceSubjectName;

    /**
     * 开票内容
     */
    private String invoiceContent;
    /**
     * 可开票订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 可开票服务金额
     */
    private BigDecimal serviceAmount;
    /**
     * 服务费开票类别(9 供电*充电服务费)
     */
    private Integer invoiceServiceClass;
    /**
     * 服务费开票内容
     */
    private String invoiceServiceContent;
}
