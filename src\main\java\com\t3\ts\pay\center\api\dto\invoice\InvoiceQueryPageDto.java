package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: wuss
 * @Date: 2020/10/27 15:20
 */
@ApiModel
@Data
public class InvoiceQueryPageDto {

    @ApiModelProperty(value = "发票信息", required = true)
    private PassengerInvoiceDetailDto passengerInvoiceDto;

    @ApiModelProperty(value = "页数")
    private int pageNum = 0;

    @ApiModelProperty(value = "每页条数")
    private int pageSize = 1;
}
