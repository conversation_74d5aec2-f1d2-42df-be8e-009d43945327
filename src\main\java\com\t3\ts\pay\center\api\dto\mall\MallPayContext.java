package com.t3.ts.pay.center.api.dto.mall;

import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.pay.center.api.rest.mall.RechargeCheckResp;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 商城支付上下文
 *
 * <AUTHOR>
 * @date 2024-03-19 18:10
 */
@Data
@Builder
public class MallPayContext {
    private PayOrderSureReq req;
    private RechargeCheckResp mallOrder;
    private String settlementId;
    private List<Integer> payTypeList;

    public PaywayEnum[] getPayWayEnum() {
        return PayWayConvert.getPayWayEnum(getPayTypeList());
    }
}
