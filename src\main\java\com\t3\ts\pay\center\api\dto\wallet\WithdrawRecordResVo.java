package com.t3.ts.pay.center.api.dto.wallet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现记录ResVo
 *
 * <AUTHOR>
 * @date 2023/05/10
 */
@Data
public class WithdrawRecordResVo {

    /**
     * 提现申请单号
     */
    @ApiModelProperty(value = "提现申请单号")
    private String orderUuid;

    /**
     * 提现金额
     */
    @ApiModelProperty(value = "提现金额")
    private String withdrawAmount;

    /**
     * 提现状态:1-初始化,2-审核中,3-审核通过,4-审核不通过,5-付款中,6-付款成功,7-付款失败,8-提现成功(已对账),9-提现失败(已对账)'
     */
    @ApiModelProperty(value = "提现状态:1-初始化,2-审核中,3-审核通过,4-审核不通过,5-付款中,6-付款成功,7-付款失败,"
            + "8-提现成功(已对账),9-提现失败(已对账)'")
    private Integer withdrawStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty("备注")
    private String remark;

}
