package com.t3.ts.pay.center.api.dto.trade;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: hujd
 * @Date: 2022/08/05 17:08
 */
@Getter
@Setter
public class SecretFreeReq {

    /**
     * 免密类型【1:支付宝,2:微信,3:一网通,8:银联】
     * EnumPayOrderChannel.BALANCE.getCode()  具体定义在此枚举中
     */
    private String secretFreeType;

    /**
     * 获取还是关闭免密支付
     * 【0:获取免密支付串,1:关闭】
     */
    private String operateType;

    /**
     * 0-取消优先支付 1-设置优先支付
     */
    private Integer priority;

    /**
     * 支付渠道 （1：支付宝、8：银联、3：一网通、2：微信）
     */
    private Integer payChannel;

    /**
     * 是否新用户
     */
    private Boolean newUser;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 签约终端
     */
    private String signTerminal;

    /**
     * 设备指纹
     */
    private String deviceToken;

    /**
     * 用户IP
     */
    private String ip;

    private String advanceSerial;

    private String sence;

    /**
     * 签约APP
     */
    private String signApp;

    /**
     * 当用户进入信用服务开通/授权页面后，点击左上角的回退按钮，中断开通流程，跳转回商户的页面地址。
     * 支持scheme协议。不传该链接时，默认返回上一级页面，由外部app唤起支付宝的情况，会返回支付宝首页。
     */
    private String cancelBackLink;
    /**
     * 用户成功完成信用服务开通/授权流程后，跳转回商户的页面地址。支持scheme协议。
     * 不传该链接时，默认返回上一级页面，由外部app 唤起支付宝的情况，会返回支付宝首页。
     */
    private String returnBackLink;
}
