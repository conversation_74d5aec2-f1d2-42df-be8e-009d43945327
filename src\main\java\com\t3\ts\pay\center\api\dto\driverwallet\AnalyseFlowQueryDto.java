package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.finance.center.dto.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 司机流水周期汇总
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
@Data
@ApiModel("司机流水周期汇总V2")
public class AnalyseFlowQueryDto extends Page implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("用户id")
    private String userId;

    /**
     * 费用分类
     */
    @ApiModelProperty("费用分类")
    private String classCode;


    /**
     * 2022-08-01
     */
    @ApiModelProperty("2024-01-01")
    private String startDate;


    /**
     * 2022-08-01
     */
    @ApiModelProperty("2022-01-10")
    private String endDate;



}


