package com.t3.ts.pay.center.api.bo;

import com.t3.ts.settlement.centre.dto.SettlementRouteDTO;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.travel.config.operate.dto.CityDto;
import lombok.Data;

import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * @Author: hupo
 * @Date: 2019.9.26
 */
@Data
public class InvoiceDataBo {

    /**
     * 行程列表
     */
    private List<RoutePlanBo> routePlanList;
    /**
     * 行程费用列表（个人）
     */
    private List<SettlementRouteDTO> settleRouteList;
    /**
     * 行程费用列表（企业）
     */
    private List<SrOrderDto> srOrderList;
    /**
     * 城市信息列表
     */
    private List<CityDto> cityList;

    private int totalCount;
    private int currPage;
    private int pageSize;


    /**
     * 底部点击文案
     */
    private String tips;

    /**
     * 下次查询月份索引
     */
    private String nextIndex;

    /**
     * 列表是否已触底
     */
    private Boolean endFlag = false;

    private Boolean hasMore;
    /**
     * @return {@link InvoiceDataBo}
     */
    public static InvoiceDataBo newInstance() {
        return new InvoiceDataBo();
    }

}
