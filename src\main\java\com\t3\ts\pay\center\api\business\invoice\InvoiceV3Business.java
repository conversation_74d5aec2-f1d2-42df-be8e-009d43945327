package com.t3.ts.pay.center.api.business.invoice;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.invoice.center.dto.*;
import com.t3.ts.invoice.center.form.GetSimilarHeaderListForm;
import com.t3.ts.invoice.center.form.route.GetRouteInvoiceListForm;
import com.t3.ts.invoice.center.service.*;
import com.t3.ts.pay.center.api.bo.InvoiceDataBo;
import com.t3.ts.pay.center.api.bo.InvoiceFareBo;
import com.t3.ts.pay.center.api.bo.PassengerInvoiceSearchQueryBo;
import com.t3.ts.pay.center.api.bo.RoutePlanBo;
import com.t3.ts.pay.center.api.business.RouteInfoBusiness;
import com.t3.ts.pay.center.api.cache.ApiCacheKeyUtils;
import com.t3.ts.pay.center.api.cache.RedisCacheServiceV2;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.config.valueconfig.RouteConfig;
import com.t3.ts.pay.center.api.constants.*;
import com.t3.ts.pay.center.api.dto.MyPageResult;
import com.t3.ts.pay.center.api.dto.bill.InvoiceSendReq;
import com.t3.ts.pay.center.api.dto.invoice.*;
import com.t3.ts.pay.center.api.dto.vo.InvoiceRouteV3Vo;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceItemVo;
import com.t3.ts.pay.center.api.dto.vo.PassengerInvoiceDetailVo;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceResVo;
import com.t3.ts.pay.center.api.enums.InvoiceClassEnum;
import com.t3.ts.pay.center.api.enums.InvoiceProductLine;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.dto.SettlementRouteDTO;
import com.t3.ts.settlement.centre.dto.UnifiedDto;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.dto.sr.SrOrderItemDTO;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.OrderStatus;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.settlement.centre.service.SettlementRouteService;
import com.t3.ts.settlement.centre.service.SettlementUnifiedService;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_0;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_10;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_2;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_5;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_8;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.*;
import static com.t3.ts.pay.center.api.constants.NumConstants.NUM_11;
import static com.t3.ts.pay.center.api.constants.NumConstants.NUM_16;
import static com.t3.ts.pay.center.api.constants.NumConstants.NUM_7;
import static com.t3.ts.pay.center.api.constants.NumConstants.NUM_9;

/**
 * @Author: ivy
 * @Date: 2021/9/22 9:46
 * @Description:
 */

@Slf4j
@Component
public class InvoiceV3Business {

    @DubboReference
    private PassengerInvoiceService passengerInvoiceService;

    @DubboReference
    private SettlementRouteService settlementRouteService;

    @DubboReference
    private SrOrderService srOrderService;

    @DubboReference
    private RouteInvoiceService routeInvoiceService;

    @DubboReference
    private FrequentlyInvoiceHeaderService frequentlyInvoiceHeaderService;

    @DubboReference
    private SettlementUnifiedService settlementUnifiedService;

    @Autowired
    private RouteConfig routeConfig;

    @Resource
    private SwitchConfig switchConfig;

    @DubboReference
    private UnifiedService unifiedService;
    @Autowired
    private RedisCacheServiceV2 redisCacheServiceV2;
    @DubboReference
    private InvoiceConfigService invoiceConfigService;

    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    @DubboReference
    private InvoiceManagerService invoiceManagerService;

    private static final String COMPANY_REFUND = "companyRefund";

    private static final String PAY_CHANNEL = "payChannel";

    private static final String CHANNEL_DISCOUNT_AMOUNT = "channelDiscountAmount";



    /**
     * 获取发票详情 支持出行发票和商城发票
     *
     * @param invoiceReq 入参
     * @return {@link Response<PassengerInvoiceDetailVo>}
     */
    public Response<PassengerInvoiceDetailVo> getInvoiceInfoAll(InvoiceReq invoiceReq) {
        PassengerInvoiceDto passengerInvoiceDto = new PassengerInvoiceDto();
        passengerInvoiceDto.setUuid(invoiceReq.getUuid());
        // update by ivy .2021/09/18 修改为dubbo方式
        Response<PassengerInvoiceDto> passengerInvoiceDtoResponse =
                passengerInvoiceService.invoiceAllDetail(passengerInvoiceDto);
        PassengerInvoiceDto data = passengerInvoiceDtoResponse.getData();
        if (ObjectUtil.isNull(data)) {
            return Response.createSuccess();
        }
        PassengerInvoiceDetailVo vo = BeanUtils.propertiesCopy(data, PassengerInvoiceDetailVo.class);
        boolean b = (InvoiceClassEnum.MALL.getType().equals(data.getInvoiceClass())
                || InvoiceClassEnum.CHARGING_FEE.getType().equals(data.getInvoiceClass()))
                && !CollectionUtils.isEmpty(data.getMallList());
        if (b) {
            formatMallData(vo, data.getMallList());
        }
        return Response.createSuccess(vo);
    }

    /**
     * 查询行程开票详情-这里主要展示行程费用中不可开票的明细
     *
     * @param invoiceRoutDetailReq 请求参数
     * @param passengerId          乘客id
     * @return 出参
     */
    public Response<InvoiceBillingDetail> billingDetailByRoute(InvoiceRoutDetailReq invoiceRoutDetailReq,
                                                               String passengerId) {
        String routePlanUuid = invoiceRoutDetailReq.getRoutePlanUuid();
        if (StringUtils.isBlank(routePlanUuid)) {
            return Response.createError("请求参数不能为空");
        }

        InvoiceBillingDetail result = new InvoiceBillingDetail();
        List<NonInvoicedItem> nonInvoiced = new ArrayList<>();

        Response<SrOrderDto> srOrderDtoResponse = srOrderService.querySrOrderByRoutePlanId(routePlanUuid);
        if (null != srOrderDtoResponse && srOrderDtoResponse.isSuccess() && srOrderDtoResponse.getData() != null) {
//            // 1.积分支付金额、礼品卡支付金额、企业支付金额，这些金额从settlement-center中获取（t_sr_pay_detail）
//            JSONObject payDetail = getPayDetail(routePlanUuid);
            // 查询费用详情
            JSONObject fareDetail = getFareDetail(routePlanUuid, passengerId);

            BigDecimal companyPay = new BigDecimal(0);
            BigDecimal companyRefund = new BigDecimal(0);
            BigDecimal couponPay = new BigDecimal(0);
            BigDecimal giftCardPay = new BigDecimal(0);
            BigDecimal integralPay = new BigDecimal(0);
            BigDecimal giftPay = new BigDecimal(0);
            BigDecimal rechargeGiftPay = new BigDecimal(0);
            BigDecimal thirdChannelDiscount = new BigDecimal(0);

            if (null != fareDetail
                    && fareDetail.get("refundDetail") != null) {
                JSONObject refundDetailObject = fareDetail.getJSONObject("refundDetail");
                if (refundDetailObject.containsKey(COMPANY_REFUND)) {
                    companyRefund = new BigDecimal(refundDetailObject.getString(COMPANY_REFUND));
                }
            }

            BigDecimal invoiceAmount = BigDecimal.ZERO;

            // 2.可开票金额、实际支付金额从发票中心获取
            Response<RouteInvoiceDto> invoiceResp = routeInvoiceService.getRouteInvoiceInfo(routePlanUuid);
            if (null != invoiceResp && invoiceResp.isSuccess() && invoiceResp.getData() != null) {
                RouteInvoiceDto data = invoiceResp.getData();
                invoiceAmount = data.getInvoiceAmount();
                // 2.1可开票金额
                result.setInvoiceAmount(invoiceAmount + "元");
                // 2.3附加费
                if (null != data.getServiceAmount() && data.getServiceAmount().compareTo(BigDecimal.ZERO) > NUM_0) {
                    nonInvoiced.add(getNonInvoicedItem("附加费", data.getServiceAmount(), "serviceAmount"));
                }
            }

            if (null != fareDetail
                    && fareDetail.get("detail") != null) {
                JSONArray detail = fareDetail.getJSONArray("detail");
                for (int i = 0; i < detail.size(); i++) {
                    JSONObject fareDetailObject = detail.getJSONObject(i);
                    if (null != fareDetailObject) {
                        if (NumConstants.STR_9.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            companyPay = companyPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        } else if (NumConstants.STR_5.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            couponPay = couponPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        } else if (NumConstants.STR_34.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            giftCardPay = giftCardPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        } else if (NumConstants.STR_37.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            integralPay = integralPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        } else if (NumConstants.STR_22.equals(fareDetailObject.getString(PAY_CHANNEL))
                                || NumConstants.STR_4.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            giftPay = giftPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        } else if (NumConstants.STR_103.equals(fareDetailObject.getString(PAY_CHANNEL))) {
                            rechargeGiftPay =
                                    rechargeGiftPay.add(new BigDecimal(fareDetailObject.getString("actAmount")));
                        }
                        if (fareDetailObject.containsKey(CHANNEL_DISCOUNT_AMOUNT)) {
                            thirdChannelDiscount = new BigDecimal(fareDetailObject.getString(CHANNEL_DISCOUNT_AMOUNT));
                        }
                    }
                }
                // 实际支付金额 - 优惠卷金额 + 企业支付金额
                BigDecimal actFare = new BigDecimal(fareDetail.getString("actFare"));
                actFare = actFare.subtract(couponPay).add(companyPay).subtract(companyRefund);
                result.setPayAmount(actFare + "元");

                // 如果企业转个人 原行程的实付金额都会退掉，用开票金额展示
                SettlementGeneralDto dto = new SettlementGeneralDto();
                dto.setOrderUuid(routePlanUuid);
                dto.setOrderStatus(OrderStatus.SUCCESS.getStatus());
                dto.setBizType(BizType.COMPANY_TO_CUSTOMER.getType());
                Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(dto);
                if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
                    result.setPayAmount(invoiceAmount + "元");
                }
            }

            if (giftCardPay.compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("礼品卡支付", giftCardPay, "giftCardPay"));
            }
            if (integralPay.compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("T币支付", integralPay, "integralPay"));
            }
            if (giftPay.compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("打车金支付", giftPay, "giftPay"));
            }
            if (rechargeGiftPay.compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("充值赠金支付", rechargeGiftPay, "rechargeGiftPay"));
            }
            if (companyPay.subtract(companyRefund).compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("企业支付", companyPay.subtract(companyRefund), "companyPay"));
            }
            // 3.1三方渠道优惠金额
            if (thirdChannelDiscount.compareTo(BigDecimal.ZERO) > 0) {
                nonInvoiced.add(getNonInvoicedItem("渠道优惠", thirdChannelDiscount, CHANNEL_DISCOUNT_AMOUNT));
            }
            result.setNonInvoicedItems(nonInvoiced);
            return Response.createSuccess(result);
        }
        return Response.createError("该行程结算单不存在");
    }

    /**
     * 发送电子发票和行程单
     * 同一发票单日最多发送10次
     *
     * @param invoiceSendReq 发票发送请求
     * @return {@link Response}
     */
    public Response sendInvoiceAndRouteList(InvoiceSendReq invoiceSendReq) {
        String redisKey = String.format(ApiCacheKeyUtils.INVOICE_EMAIL_LIMIT, invoiceSendReq.getUuid());
        String lockKey = String.format(ApiCacheKeyUtils.INVOICE_EMAIL_LIMIT_LOCK, invoiceSendReq.getUuid());
        Boolean lockRes = false;
        Response response = null;
        try {
            //Lock用户操作场景
            lockRes = redisCacheServiceV2.getLock(lockKey);
            if (!lockRes) {
                return Response.createSuccess(ResultErrorEnum.USER_CONCURRENT_ERROR.getMsg());
            }
            // 获取同一发票当日发送次数
            Integer sendInvoiceTimes = redisCacheServiceV2.getValue(redisKey, Integer.class);
            if (Objects.nonNull(sendInvoiceTimes) && sendInvoiceTimes >= switchConfig.getSendEmailLimit()) {
                // 发票发送次数超限
                return Response.createSuccess(ResultErrorEnum.INVOICE_EMAIL_LIMIT_ERROR.getMsg());
            }
            response = doSendInvoiceAndRouteList(invoiceSendReq.getUuid(), invoiceSendReq.getEmail(),
                    invoiceSendReq.getApplySource(), invoiceSendReq.getInvoiceClass());
            int timeSec = (int) TimeUnit.MILLISECONDS.toSeconds(DateUtils.getMillsecBeforeMoment(
                    NumConstants.NUM_23_INT, NumConstants.NUM_59_INT, NumConstants.NUM_59_INT,
                    NumConstants.NUM_999_INT));
            // 发票发送进行计数，失效时间为隔天
            if (Objects.isNull(sendInvoiceTimes)) {
                redisCacheServiceV2.setValue(redisKey, 1, timeSec);
            } else {
                redisCacheServiceV2.setValueIncr(redisKey, 1L);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (lockRes) {
                redisCacheServiceV2.releaseLock(lockKey);
            }
        }
        return response;
    }

    /**
     * 发送发票和路线列表吗
     * Description: 向指定邮箱发送发票和行程<br>
     *
     * @param invoiceId    发票id<br>
     * @param email        邮箱<br>
     * @param applySource  申请来源(1:app申请 2:客服申请)<br>
     * @param invoiceClass 发票类
     * @return com.t3.ts.result.Response <br>
     * <AUTHOR> <br>
     * @taskId <br>
     */
    private Response doSendInvoiceAndRouteList(String invoiceId, String email, Integer applySource,
                                               Integer invoiceClass) {
        PassengerInvoiceDto passengerInvoiceDto = new PassengerInvoiceDto();
        passengerInvoiceDto.setUuid(invoiceId);
        passengerInvoiceDto.setEmail(email);
        boolean b = ObjectUtil.isNotNull(invoiceClass) && (InvoiceClassEnum.MALL.getType().equals(invoiceClass)
                || InvoiceClassEnum.CHARGING_FEE.getType().equals(invoiceClass));
        Integer sendType = b ? SendTypeEnum.INVOICE.getType() : SendTypeEnum.INVOICE_AND_ROUTE.getType();
        if (SendTypeEnum.INVOICE_AND_ROUTE.getType().equals(sendType)) {
            return passengerInvoiceService.sendInvoice(passengerInvoiceDto, applySource, null, null);
        } else if (SendTypeEnum.INVOICE.getType().equals(sendType)) {
            return passengerInvoiceService.sendInvoiceWithoutItineraries(passengerInvoiceDto, applySource, null, null);
        } else if (SendTypeEnum.ROUTE.getType().equals(sendType)) {
            return passengerInvoiceService.sendItineraryList(passengerInvoiceDto);
        } else {
            return Response.createError("不存在此操作类型");
        }
    }

    /**
     * 发送行程单
     *
     * @param invoiceSendReq 发票发送请求
     * @param userMobile     用户移动
     * @return {@link Response}
     */
    public Response sendRouteList(InvoiceSendReq invoiceSendReq, String userMobile) {
        invoiceSendReq.setPassengerMobile(userMobile);
        PassengerInvoiceDto passengerInvoiceDto = BeanUtils.propertiesCopy(invoiceSendReq, PassengerInvoiceDto.class);
        return passengerInvoiceService.sendItineraryList(passengerInvoiceDto);
    }

    /**
     * 查询发票历史记录列表
     *
     * @param invoiceReq    入参
     * @param passengerUuid 乘客uuid
     * @return {@link Response<PageResult<PassengerInvoiceDto>>}
     */
    public Response<PageResult<PassengerInvoiceDto>> queryInvoiceHistoryList(InvoiceHistoryReq invoiceReq,
                                                                             String passengerUuid) {
        PassengerInvoiceSearchQueryBo passengerInvoiceDto = new PassengerInvoiceSearchQueryBo();
        passengerInvoiceDto.setPassengerUuid(passengerUuid);
        List<Integer> statusList = Lists.newArrayList(NUM_1, NUM_2, NUM_5, NUM_7, NUM_9, NUM_10, NUM_11);
        passengerInvoiceDto.setStatusList(statusList);
        if (!CollectionUtils.isEmpty(invoiceReq.getInvoiceClassList())) {
            if (invoiceReq.getInvoiceClassList().contains(InvoiceClassEnum.ROUTE.getType())
                    && invoiceReq.getInvoiceClassList().contains(InvoiceClassEnum.MALL.getType())) {
                passengerInvoiceDto.setAllInclude(true);
            } else if (invoiceReq.getInvoiceClassList().contains(InvoiceClassEnum.ROUTE.getType())) {
                passengerInvoiceDto.setInvoiceClass(InvoiceClassEnum.ROUTE.getType());
            } else if (invoiceReq.getInvoiceClassList().contains(InvoiceClassEnum.MALL.getType())) {
                passengerInvoiceDto.setInvoiceClass(InvoiceClassEnum.MALL.getType());
            } else if (invoiceReq.getInvoiceClassList().contains(InvoiceClassEnum.CHARGING_FEE.getType())) {
                passengerInvoiceDto.setInvoiceClass(InvoiceClassEnum.CHARGING_FEE.getType());
            }
        }
        PassengerInvoiceSearchDto dto = BeanUtils.propertiesCopy(passengerInvoiceDto, PassengerInvoiceSearchDto.class);
        return passengerInvoiceService.listBillingHistoryByStatus(dto, invoiceReq.getCurrPage(),
                invoiceReq.getPageSize());
    }


    /**
     * 发票抬头模糊查询
     * @param form
     * @return
     */
    public Response getFuzzyHeaderList(GetSimilarHeaderListForm form) {
        try {
            if (StringUtils.isEmpty(form.getHeader())) {
                return Response.createError("抬头必填");
            }
            if (form.getHeader().length() < NumConstant.NUM_3) {
                return Response.createSuccess();
            }
            return invoiceManagerService.invoiceHeaderFuzzyQuery(form.getHeader());
        } catch (Exception e) {
            log.error("发票抬头模糊查询异常", e);
        }
        return Response.createError("发票抬头模糊查询异常");
    }


    /**
     * 发票抬头详情查询
     * @param form
     * @return
     */
    public Response getDetailHeader(GetSimilarHeaderListForm form) {
        try {
            if (StringUtils.isEmpty(form.getHeader())) {
                return Response.createError("抬头必填");
            }
            return invoiceManagerService.invoiceHeaderDetailQuery(form.getHeader());
        } catch (Exception e) {
            log.error("发票抬头详情查询异常", e);
        }
        return Response.createError("发票抬头详情查询异常");
    }


    /**
     * 发票管家授权信息查询
     * @param dto dto
     * @return Response
     */
    public Response queryInvoiceManagerSignInfo(AuthorizationSignQueryDto dto) {
        try {
            if ((CollectionUtils.isEmpty(dto.getPayChannels()) && CollectionUtils.isEmpty(dto.getBizTypes()))
                    || StringUtils.isEmpty(dto.getUserId())) {
                return Response.createError("缺少必填参数");
            }
            return invoiceManagerService.querySignInfoList(BeanUtils.propertiesCopy(dto, QueryAuthorizationSignDto.class));
        } catch (Exception e) {
            log.error("发票授权信息查询异常", e);
        }
        return Response.createError("发票授权信息查询异常");
    }


    /**
     * 发票管家授权
     * @param dto dto
     * @return Response
     */
    public Response invoiceManagerSign(InvoiceSignDto dto) {
        try {
            return invoiceManagerService.invoiceManagerSign(dto);
        } catch (Exception e) {
            log.error("发票管家授权异常", e);
        }
        return Response.createError("发票管家授权失败");
    }


    /**
     * 发票管家授权解约
     * @param dto dto
     * @return Response
     */
    public Response invoiceManagerUnSign(InvoiceSignDto dto) {
        try {
            return invoiceManagerService.invoiceManagerCancelSign(dto);
        } catch (Exception e) {
            log.error("发票管家解约异常", e);
        }
        return Response.createError("发票管家解约失败");
    }

    /**
     * 发票取消
     *
     * @param invoiceForm 发票的形式
     * @return {@link Response}
     */
    public Response invoiceCancellation(InvoiceForm invoiceForm) {

        String uuid = invoiceForm.getUuid();
        if (StringUtils.isBlank(uuid)) {
            return Response.createError("参数异常");
        }
        // 查询发票详情
        PassengerInvoiceDto req = new PassengerInvoiceDto();
        req.setUuid(uuid);
        Response<PassengerInvoiceDto> invoiceInfo = passengerInvoiceService.invoiceDetail(req);
        if (!invoiceInfo.isSuccess() || Objects.isNull(invoiceInfo.getData())
                || Integer.valueOf(1).equals(invoiceInfo.getData().getModifyStatus())) {

            return Response.createError("提交失败,当前发票无法重开");
        }

        //作废发票
        Response<String> response = passengerInvoiceService.invalidInvoice(uuid, invoiceForm.getApplySource(), null,
                null);
        if (!response.isSuccess()) {
            return Response.createError("发票作废失败,请稍后重试");
        }

        // 出行发票 才和行程相关
        if (InvoiceClassEnum.ROUTE.getType().equals(invoiceInfo.getData().getInvoiceClass())) {
            Response<String> orderUuids = getOrderUuids(uuid);
            if (!orderUuids.isSuccess()) {
                return orderUuids;
            }

            // 将行程开票状态改为未开票
            List<String> routePlanUuids = Arrays.asList(orderUuids.getData().split(","));
            Response modifyRouteStatus = modifyRouteStatus(routePlanUuids, PassengerConstants.ROUTE_UNBILLED);
            if (!modifyRouteStatus.isSuccess()) {
                return Response.createError("操作失败");
            }
        }

        return Response.createSuccess("操作成功");
    }

    /**
     * 校验开票订单是否跟分类开票订单id一致
     *
     * @param totalOrderUuid totalOrderUuid
     * @param orderToCodes   orderToCodes
     * @param totalMoney     总金额
     * @param userId         userId
     * @return Response
     */
    public Response checkOrderUuid(String totalOrderUuid, BigDecimal totalMoney, List<OrderToCode> orderToCodes,
                                   String userId) {
        if (StringUtils.isNotEmpty(totalOrderUuid) && ObjectUtil.isNotNull(totalMoney)
                && CollectionUtil.isNotEmpty(orderToCodes)) {
            String[] orderUuids = totalOrderUuid.split(",");
            BigDecimal money = orderToCodes.stream().map(OrderToCode::getMoney).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            Response result = check(totalMoney, orderToCodes, orderUuids, money);
            if (!result.isSuccess()) {
                return result;
            }
            // 查询可开票金额是合法
            //依赖key
            Response<List<RouteInvoiceDto>> routeInvoicePageListRes =
                    routeInvoiceService.getRouteInvoiceInfoList(CollectionUtil.toList(orderUuids));
            if (ObjectUtil.isNotEmpty(routeInvoicePageListRes)
                    && CollectionUtil.isNotEmpty(routeInvoicePageListRes.getData())) {
                BigDecimal reduce =
                        routeInvoicePageListRes.getData().stream()
                                .map(e -> ObjectUtil.isNull(e.getInvoiceAmount()) ? BigDecimal.ZERO
                                        : e.getInvoiceAmount())
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                /**
                 * 越权判断
                 */
                boolean allMatch = routeInvoicePageListRes.getData().stream()
                        .allMatch(e -> StringUtils.equals(userId, e.getPassengerUuid()));

                if (reduce.compareTo(money) == 0 && allMatch) {
                    return Response.createSuccess();
                } else {
                    return Response.createError("开票金额不符");
                }
            } else {
                return Response.createError("开票金额不符");
            }

        } else if (!(StringUtils.isEmpty(totalOrderUuid) && ObjectUtil.isNotNull(totalMoney)
                && CollectionUtil.isEmpty(orderToCodes))) {
            return Response.createError("开票行程不能为空");
        }
        return Response.createSuccess();
    }

    /**
     * 提交开票
     *
     * @param invoiceForm   invoiceForm
     * @param passengerUuid passengerUuid
     * @return Response Response
     */
    public Response invoiceBilling(InvoiceRelevantFormV3 invoiceForm, String passengerUuid) {
        BigDecimal money = invoiceForm.getMoney();
        String uuid = invoiceForm.getUuid();
        String orderUuid = invoiceForm.getOrderUuid();
        String header = invoiceForm.getHeader();
        InvoiceResVo invoiceResVo = new InvoiceResVo();
        if (ObjectUtil.isNotNull(invoiceForm.getT3Transport())) {
            Response response = checkMoney(invoiceForm.getT3Transport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
        }
        if (ObjectUtil.isNotNull(invoiceForm.getDfTransport())) {
            Response response = checkMoney(invoiceForm.getDfTransport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
        }
        if (ObjectUtil.isNotNull(invoiceForm.getFawTransport())) {
            Response response = checkMoney(invoiceForm.getFawTransport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
        }
        Integer billType = invoiceForm.getBillType();
        Response<String> checkResponse = checkRedoneBill(billType, header, invoiceForm, uuid, orderUuid);
        if (!checkResponse.isSuccess()) {
            return checkResponse;
        }
        orderUuid = checkResponse.getData();
        Response checkOrderUuidRes = Response.createSuccess();
        //由于需要同时开多个运力来源的发票，但支付结算只提供单一运力来源的开发票能力，所以要循环调用接口，
        // 入参多了三个运力来源内部类，出参封装一个Vo，告诉前端那些成功，哪些失败。
        //T3运力来源
        Map<String, InvoiceRelevantFormV3> param = new HashMap<>(NUM_16);
        if (ObjectUtil.isNotNull(invoiceForm.getT3Transport())) {
            T3Transport t3Transport = invoiceForm.getT3Transport();
            BigDecimal t3Money;
            String t3OrderUuid;
            //重新开票h5会在外部提供参数。所以走原来逻辑塞值
            if (PassengerConstants.BILLTYPE_REDONE.equals(billType) && StringUtils.isNotBlank(uuid)
                    && invoiceForm.getTransportType().equals(CommonNumConst.T3_TRANSPORT)) {
                t3Money = money;
                t3OrderUuid = orderUuid;

                InvoiceReq invoiceReq = new InvoiceReq();
                invoiceReq.setUuid(uuid);
                PassengerInvoiceDto req = new PassengerInvoiceDto();
                req.setUuid(uuid);
                Response<PassengerInvoiceDto> invoiceInfo = passengerInvoiceService.invoiceDetail(req);

                if (invoiceInfo.isSuccess() && ObjectUtil.isNotNull(invoiceInfo.getData())) {
                    List<OrderToCode> orderToCodes = new ArrayList<>();
                    OrderToCode orderToCode = new OrderToCode();
                    orderToCode.setOrderUuid(orderUuid);
                    orderToCode.setMoney(invoiceInfo.getData().getMoney());
                    orderToCode.setInvoiceSubjectCode(invoiceInfo.getData().getInvoiceSubjectCode());
                    orderToCodes.add(orderToCode);
                    invoiceForm.getT3Transport().setOrderToCodes(orderToCodes);
                    invoiceForm.getT3Transport().setMoney(invoiceInfo.getData().getMoney());
                }

            } else {

                if (StringUtils.isEmpty(t3Transport.getOrderUuid())) {
                    checkOrderUuidRes = Response.createError("没有选择开票行程");
                }
                t3Money = t3Transport.getMoney();
                t3OrderUuid = t3Transport.getOrderUuid();
            }
            //由于中台只提供单一运力来源，所以要重新赋值给中台的数据
            invoiceForm.setTransportType(CommonNumConst.T3_TRANSPORT);
            invoiceForm.setMoney(t3Money);
            InvoiceRelevantFormV3 t3invoiceForm = BeanUtils.propertiesCopy(invoiceForm, InvoiceRelevantFormV3.class);
            t3invoiceForm.setInvoicePassengerUuid(passengerUuid);
            t3invoiceForm.setInvoiceMoney(t3Money);
            t3invoiceForm.setInvoiceOrderUuid(t3OrderUuid);
            param.put("t3Transport", t3invoiceForm);
        }
        //东风运力
        if (ObjectUtil.isNotNull(invoiceForm.getDfTransport())) {
            DFTransport dfTransport = invoiceForm.getDfTransport();
            if (StringUtils.isEmpty(dfTransport.getOrderUuid())) {
                checkOrderUuidRes = Response.createError("没有选择开票行程");
            }
            BigDecimal dfMoney;
            String dfOrderUuid;
            dfMoney = dfTransport.getMoney();
            dfOrderUuid = dfTransport.getOrderUuid();
            invoiceForm.setTransportType(CommonNumConst.DF_TRANSPORT);
            invoiceForm.setMoney(dfMoney);
            InvoiceRelevantFormV3 dfinvoiceForm = BeanUtils.propertiesCopy(invoiceForm, InvoiceRelevantFormV3.class);
            dfinvoiceForm.setInvoicePassengerUuid(passengerUuid);
            dfinvoiceForm.setInvoiceMoney(dfMoney);
            dfinvoiceForm.setInvoiceOrderUuid(dfOrderUuid);
            param.put("dfTransport", dfinvoiceForm);
        }
        //一汽运力
        if (ObjectUtil.isNotNull(invoiceForm.getFawTransport())) {
            FAWTransport fawTransport = invoiceForm.getFawTransport();
            if (StringUtils.isEmpty(fawTransport.getOrderUuid())) {
                checkOrderUuidRes = Response.createError("没有选择开票行程");
            }
            BigDecimal fawMoney;
            String fawOrderUuid;
            fawMoney = fawTransport.getMoney();
            fawOrderUuid = fawTransport.getOrderUuid();
            invoiceForm.setTransportType(CommonNumConst.FAW_TRANSPORT);
            invoiceForm.setMoney(fawMoney);

            InvoiceRelevantFormV3 fawinvoiceForm = BeanUtils.propertiesCopy(invoiceForm, InvoiceRelevantFormV3.class);
            fawinvoiceForm.setInvoicePassengerUuid(passengerUuid);
            fawinvoiceForm.setInvoiceMoney(fawMoney);
            fawinvoiceForm.setInvoiceOrderUuid(fawOrderUuid);

            param.put("fawTransport", fawinvoiceForm);
        }
        if (!checkOrderUuidRes.isSuccess()) {
            return checkOrderUuidRes;
        }
        Response response = getResponse(param);
        log.info("开票结果：{}", JSONUtil.toJsonStr(response));
        Response result;
        if (response.isSuccess()) {
            invoiceResVo.setDfInvoiceRes(1);
            invoiceResVo.setFawInvoiceRes(1);
            invoiceResVo.setT3InvoiceRes(1);
            result = Response.createSuccess(invoiceResVo);
        } else {
            invoiceResVo.setDfInvoiceRes(0);
            invoiceResVo.setT3InvoiceRes(0);
            invoiceResVo.setFawInvoiceRes(0);
            result = Response.createError(response.getMsg(), invoiceResVo);
        }
        return result;
    }

    /**
     * @param totalMoney   totalMoney
     * @param orderToCodes orderToCodes
     * @param orderUuids   orderUuids
     * @param money        money
     * @return Response
     */
    private Response check(BigDecimal totalMoney, List<OrderToCode> orderToCodes, String[] orderUuids,
                           BigDecimal money) {
        if (orderUuids.length != orderToCodes.size()) {
            return Response.createError("开票行程数量不一致");
        } else {
            Map<String, String> orderMap = new HashMap<>(NUM_16);
            for (String orderUuid : orderUuids) {
                orderMap.put(orderUuid, "1");
            }
            for (OrderToCode orderToCode : orderToCodes) {
                orderMap.put(orderToCode.getOrderUuid(), "1");
            }
            if (orderMap.size() != orderUuids.length) {
                return Response.createError("开票行程内容不一致");
            }
        }
        if (ObjectUtil.isNotNull(totalMoney) && totalMoney.compareTo(money.setScale(NUM_2)) != 0) {
            return Response.createError("开票金额不一致");
        }
        return Response.createSuccess();
    }

    /**
     * 构建不可开票项
     *
     * @param item   不可开票内容
     * @param amount 金额
     * @param type   类型
     * @return 不可开票项
     */
    private NonInvoicedItem getNonInvoicedItem(String item, BigDecimal amount, String type) {
        NonInvoicedItem nonInvoicedItem = new NonInvoicedItem();
        nonInvoicedItem.setItem(item);
        nonInvoicedItem.setAmount(amount + "元");
        nonInvoicedItem.setType(type);
        return nonInvoicedItem;
    }


    /**
     * 得到支付细节
     *
     * @param orderUuid   订单uuid
     * @param passengerId 用户id
     * @return {@link JSONObject}
     */
    private JSONObject getFareDetail(String orderUuid, String passengerId) {
        com.t3.ts.pay.remote.dto.UnifiedDto dto = new com.t3.ts.pay.remote.dto.UnifiedDto();
        dto.setSceneType("t3pay.pay.route.fareDetail");
        JSONObject extendParam = new JSONObject();
        extendParam.put("journeyId", orderUuid);
        extendParam.put("userId", passengerId);
        dto.setExtendParam(extendParam.toJSONString());
        Response handle = unifiedService.handle(dto);
        if (null != handle && Boolean.TRUE.equals(handle.isSuccess())) {
            return JSON.parseObject((String) handle.getData());
        }
        return new JSONObject();
    }


    /**
     * 得到支付细节
     *
     * @param orderUuid 订单uuid
     * @return {@link JSONObject}
     */
    private JSONObject getPayDetail(String orderUuid) {
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.settlement.query.all.pay.detail");

        JSONObject json = new JSONObject();
        json.put("orderUuid", orderUuid);
        dto.setExtendParam(json.toJSONString());
        Response handle = settlementUnifiedService.handle(dto);
        if (null != handle && Boolean.TRUE.equals(handle.isSuccess())) {
            return JSON.parseObject((String) handle.getData());
        }
        return new JSONObject();
    }


    /**
     * 按照行程列表开票
     *
     * @param pageReq pageReq
     * @param uuid    uuid
     * @return {@link MyPageResult<InvoiceRouteV3Vo>}
     */
    public Response<?> billingByRoute(InvoiceRoutReq pageReq, String uuid) {
        if (StringUtils.isBlank(uuid)) {
            return Response.createError("乘客uuid不能为空");
        }
        if (checkParams(pageReq)) {
            return Response.createError("参数不正确", ResultErrorEnum.UNIFY_ERROR_CODE.getCode());
        }
        MyPageResult<InvoiceRouteV3Vo> pageResult = new MyPageResult<>();
        List<InvoiceRouteV3Vo> invoiceRouteVoList = null;
        InvoiceDataBo invoiceDataBo = new InvoiceDataBo();
        try {
            for (int i = 0; i < switchConfig.getBillingCount(); i++) {
                Response<InvoiceDataBo> invoiceDataBoResponse = initInvoice(pageReq, uuid);
                if (!invoiceDataBoResponse.getSuccess()) {
                    return Response.createError(invoiceDataBoResponse.getMsg());
                }
                // 非空判断
                if (Objects.nonNull(invoiceDataBoResponse.getData())) {
                    invoiceDataBo = invoiceDataBoResponse.getData();
                    if (CollectionUtil.isNotEmpty(invoiceDataBoResponse.getData().getRoutePlanList())) {
                        invoiceRouteVoList = Lists.newArrayList(invoiceRouteList(invoiceDataBo));
                        if (CollectionUtil.isNotEmpty(invoiceRouteVoList)
                                || StringUtils.isBlank(pageReq.getNextIndex())) {
                            break;
                        }
                    }
                    if (isTimeBoolean(pageReq) && invoiceDataBo.getEndFlag()) {
                        break;
                    }
                    processPage(pageReq, invoiceDataBo);
                }
            }
        } catch (Exception e) {
            log.warn("可开票列表查询异常", e);
        }
        pageResult.setList(invoiceRouteVoList);
        pageResult.setTotalCount(invoiceDataBo.getTotalCount());
        pageResult.setCurrPage(invoiceDataBo.getCurrPage());
        pageResult.setPageSize(invoiceDataBo.getPageSize());
        pageResult.setTips(invoiceDataBo.getTips());
        pageResult.setNextIndex(invoiceDataBo.getNextIndex());
        pageResult.setEndFlag(invoiceDataBo.getEndFlag());
        log.info("可开票列表查询成功,index:{}", pageResult.getNextIndex());
        return Response.createSuccess("获取成功", pageResult);
    }

    /**
     * 参数校验
     *
     * @param pageReq 分页参数
     * @return true|false
     */
    public static boolean checkParams(InvoiceRoutReq pageReq) {
        final boolean number = NumberUtil.isNumber(pageReq.getNextIndex());
        if (number) {
            final Date parseDate = DateUtils.format(pageReq.getNextIndex(), "yyyyMM");
            return ObjectUtil.isNull(parseDate);
        }
        return true;
    }

    /**
     * @param pageReq pageReq
     * @return boolean
     */
    private boolean isTimeBoolean(InvoiceRoutReq pageReq) {
        return StringUtils.isNotBlank(pageReq.getStartDay())
                || StringUtils.isNotBlank(pageReq.getEndDay())
                || StringUtils.isNotBlank(pageReq.getStartMonth())
                || StringUtils.isNotBlank(pageReq.getEndMonth());
    }

    /**
     * 索引处理
     *
     * @param pageReq       请求
     * @param invoiceDataBo 结果
     */
    private void processPage(InvoiceRoutReq pageReq, InvoiceDataBo invoiceDataBo) {
        if (!invoiceDataBo.getHasMore()) {
            //  继续去下一个nextIndex
            Date index = DateUtils.format(pageReq.getNextIndex(), "yyyyMM");
            String nextIndex =
                    DateUtils.getMonthFormat(DateUtils.getDateAddMonth(index, NUM_NEGATIVE_3));
            pageReq.setNextIndex(nextIndex);
            // currPage pageSize 重置
            pageReq.setCurrPage(NUM_1);
            pageReq.setPageSize(NUM_10);
        } else {
            // 小于阈值 && hashMore 查询下一个分页
            //  继续去下一个nextIndex
            Integer currPage = pageReq.getCurrPage();
            pageReq.setCurrPage(++currPage);
        }
    }

    /**
     * 组装行程请求数据
     *
     * @param pageReq 请求参数
     * @param uuid    乘客uuid
     * @return 组装
     */
    private GetRouteInvoiceListForm getGetRouteInvoiceListForm(InvoiceRoutReq pageReq, String uuid) {
        GetRouteInvoiceListForm getRouteInvoiceListForm = new GetRouteInvoiceListForm();
        getRouteInvoiceListForm.setPageSize(pageReq.getPageSize());
        getRouteInvoiceListForm.setCurrPage(pageReq.getCurrPage());
        getRouteInvoiceListForm.setPassengerUuid(uuid);
        getRouteInvoiceListForm.setInvoiceStatus(0);
        getRouteInvoiceListForm.setFilterMoney(1);
        List<String> routePlanUuids = pageReq.getRoutePlanUuids();
        if (CollectionUtil.isNotEmpty(routePlanUuids)) {
            getRouteInvoiceListForm.setPassengerUuid(pageReq.getVirtualPassengerUuid());
            getRouteInvoiceListForm.setRoutePlanUuids(routePlanUuids);
        } else {
            getRouteInvoiceListForm.setSources(switchConfig.getInvoiceSource());
            // 本次需求 日期筛选为精确到天，，以前精确到月
            if (StrUtil.isNotBlank(pageReq.getStartDay())) {
                DateTime startMonth = DateUtil.beginOfDay(DateUtil.parse(pageReq.getStartDay(), "yyyyMMdd"));
                getRouteInvoiceListForm.setStartTime(startMonth);
            } else if (StrUtil.isNotBlank(pageReq.getStartMonth())) {
                DateTime startMonth = DateUtil.beginOfMonth(DateUtil.parse(pageReq.getStartMonth(), "yyyyMM"));
                getRouteInvoiceListForm.setStartTime(startMonth);
            }
            if (StrUtil.isNotBlank(pageReq.getEndDay())) {
                DateTime endMonth = DateUtil.endOfDay(DateUtil.parse(pageReq.getEndDay(), "yyyyMMdd"));
                getRouteInvoiceListForm.setEndTime(endMonth);
            } else if (StrUtil.isNotBlank(pageReq.getEndMonth())) {
                DateTime endMonth = DateUtil.endOfMonth(DateUtil.parse(pageReq.getEndMonth(), "yyyyMM"));
                getRouteInvoiceListForm.setEndTime(endMonth);
            }
            if (StringUtils.isNotBlank(pageReq.getCityCode())) {
                getRouteInvoiceListForm.setCityCode(pageReq.getCityCode());
            }
        }
        return getRouteInvoiceListForm;
    }

    /**
     * 行程发票列表(包括企业个人支付发票)
     *
     * @param invoiceDataBo invoiceDataBo
     * @return InvoiceRouteV3Vo
     */
    private List<InvoiceRouteV3Vo> invoiceRouteList(InvoiceDataBo invoiceDataBo) {
        Map<String, SettlementRouteDTO> srdMap = new HashMap<>(CommonNumConst.NUM_16);
        for (SettlementRouteDTO srd : invoiceDataBo.getSettleRouteList()) {
            srdMap.put(srd.getRoutePlanUuid(), srd);
        }
        Map<String, SrOrderDto> srOrderMap = new HashMap<>(CommonNumConst.NUM_16);
        for (SrOrderDto srOrderDto : invoiceDataBo.getSrOrderList()) {
            srOrderMap.put(srOrderDto.getRoutePlanUuid(), srOrderDto);
        }
        List<InvoiceRouteV3Vo> invoiceRouteVoList = Lists.newArrayList();
        invoiceDataBo.getRoutePlanList().forEach(routePlanDto -> {
            if (Objects.isNull(routePlanDto.getInvoiceAmount())
                    || (routePlanDto.getInvoiceAmount().compareTo(BigDecimal.ZERO) <= 0)) {
                return;
            }

            InvoiceRouteV3Vo invoiceRouteVo = new InvoiceRouteV3Vo();
            invoiceRouteVo.setRouteUuid(routePlanDto.getUuid());
            invoiceRouteVo.setOrderNo(routePlanDto.getRouteNo());
            invoiceRouteVo.setOriginAddress(routePlanDto.getOriginAddress());
            invoiceRouteVo.setDestAddress(routePlanDto.getDestAddress());
            invoiceRouteVo.setDeparTime(routePlanDto.getDeparTime());
            invoiceRouteVo.setRemark(routePlanDto.getRemark());
            invoiceRouteVo.setInvoiceSource(routePlanDto.getInvoiceSource());
            invoiceRouteVo.setInvoiceSubjectCode(routePlanDto.getInvoiceSubjectCode());
            invoiceRouteVo.setInvoiceSubjectName(routePlanDto.getInvoiceSubjectName());
            invoiceRouteVo.setInvoiceContent(routePlanDto.getInvoiceContent());
            InvoiceProductLine invoiceProductLine = convertToProductLine(routePlanDto.getTypeEnt(),
                    routePlanDto.getTypeModule(),
                    routePlanDto.getTypeTrip());
            invoiceRouteVo.setBillMoney(nullToZero(routePlanDto.getInvoiceAmount()).toEngineeringString());
            if (ObjectUtil.isNotNull(invoiceProductLine)) {
                invoiceRouteVo.setProductLine(invoiceProductLine.getProductLine());
            }
            SettlementRouteDTO srd = srdMap.get(routePlanDto.getUuid());
            SrOrderDto srOrderDto = srOrderMap.get(routePlanDto.getUuid());
            if (srd != null) {
                BigDecimal additionalMoney = nullToZero(srd.getAdditionalMoney());
                BigDecimal payAmount = nullToZero(srd.getPayAmount());
                invoiceRouteVo.setPayMoney(payAmount);
                invoiceRouteVo.setAdditionalMoney(additionalMoney);
                invoiceRouteVo.setTransportType(srd.getTransportType());
            } else if (srOrderDto != null) {
                InvoiceFareBo invoiceFareBo = getInvoiceFare(srOrderDto);
                if (Objects.nonNull(invoiceFareBo)) {
                    invoiceRouteVo.setAdditionalMoney(invoiceFareBo.getAdditionalMoney());
                    invoiceRouteVo.setPayMoney(payMoney(srOrderDto, invoiceFareBo));
                } else {
                    //补充兜底数据
                    log.info("srOrderDto is not available,info: {}", srOrderDto);
                    invoiceRouteVo.setAdditionalMoney(BigDecimal.ZERO);
                    invoiceRouteVo.setPayMoney(routePlanDto.getInvoiceAmount());
                }
            } else {
                //补充兜底数据
                invoiceRouteVo.setAdditionalMoney(BigDecimal.ZERO);
                invoiceRouteVo.setPayMoney(routePlanDto.getInvoiceAmount());
            }

            invoiceRouteVoList.add(invoiceRouteVo);
        });
        return invoiceRouteVoList;
    }

    /**
     * 支付金额
     * @param srOrderDto srOrderDto
     * @param invoiceFareBo invoiceFareBo
     * @return BigDecimal
     */
    private BigDecimal payMoney(SrOrderDto srOrderDto, InvoiceFareBo invoiceFareBo) {
        BigDecimal payAmount = invoiceFareBo.getPayAmount();
        // 如果个人支付为0 可能是企业转个人订单
        if (invoiceFareBo.getPayAmount().compareTo(BigDecimal.ZERO) == NUM_0) {
            String routePlanUuid = srOrderDto.getRoutePlanUuid();
            SettlementGeneralDto dto = new SettlementGeneralDto();
            dto.setOrderUuid(routePlanUuid);
            Response<List<SettlementGeneralDto>> selectSettlement = settlementGeneralService.selectSettlement(dto);
            if (selectSettlement.isSuccess() && null != selectSettlement.getData()
                    && selectSettlement.getData().get(0).getBizType() == BizType.COMPANY_TO_CUSTOMER.getType()) {
                payAmount = selectSettlement.getData().get(0).getPayAmount();
            }
        }
        return payAmount;
    }

    /**
     * 为空转为0
     *
     * @param var 任意BigDecimal
     * @return BigDecimal
     */
    public static BigDecimal nullToZero(BigDecimal var) {
        return var == null ? BigDecimal.ZERO : var;
    }

    /**
     * @param srOrderDto 订单子项列表
     * @return InvoiceFareBo
     */
    private InvoiceFareBo getInvoiceFare(SrOrderDto srOrderDto) {
        List<SrOrderItemDTO> srOrderItemDTOList = srOrderDto.getSrOrderItemDTOList().stream()
                .filter(s -> Integer.valueOf(NUM_2).equals(s.getItemType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(srOrderItemDTOList)) {
            SrOrderItemDTO srOrderItemDTO = srOrderItemDTOList.get(0);

            BigDecimal additionalMoney = nullToZero(srOrderItemDTO.getAdditionalMoney());
            BigDecimal payAmount = nullToZero(srOrderItemDTO.getPaidFare());
            BigDecimal billMoney = payAmount.subtract(additionalMoney).setScale(NUM_2, RoundingMode.DOWN);

            InvoiceFareBo invoiceFareBo = InvoiceFareBo.newInstance();
            invoiceFareBo.setAdditionalMoney(additionalMoney);
            invoiceFareBo.setPayAmount(payAmount);
            invoiceFareBo.setBillMoney(billMoney);
            return invoiceFareBo;
        }
        return null;
    }

    /**
     * @param typeEnt    个人/企业
     * @param typeModule typeModule
     * @param typeTrip   typeTrip
     * @return cInvoiceProductLine
     * @author: qul
     * @Description: 转化成业务线
     * @CreateDate: 2021/4/25 11:50
     */
    private static InvoiceProductLine convertToProductLine(Integer typeEnt, Integer typeModule, Integer typeTrip) {
        if (ObjectUtil.isNull(typeEnt) || ObjectUtil.isNull(typeModule) || ObjectUtil.isNull(typeTrip)) {
            return null;
        }
        InvoiceProductLine invoiceProductLine = InvoiceProductLine.getByProductLine(typeModule);
        if (null == invoiceProductLine) {
            if (NUM_2 == typeEnt) {
                invoiceProductLine = InvoiceProductLine.QY_YC;
            } else if (NUM_8 == typeTrip) {
                invoiceProductLine = InvoiceProductLine.BC;
            }
        }
        return invoiceProductLine;
    }

    /**
     * @param dto 入参
     * @return 结果
     */
    public Response<?> queryPageForCallCenter(InvoiceQueryPageDto dto) {
        log.info("queryPageForCallCenter InvoiceQueryPageDto:{}", JSONObject.toJSONString(dto));
        if (dto.getPassengerInvoiceDto().getHeaderType() == null) {
            return Response.createError("headerType不能为空");
        }
        List<FrequentlyInvoiceHeaderDto> list = null;
        try {
            // 查询默认抬头
            PassengerInvoiceDetailDto pdto = dto.getPassengerInvoiceDto();
            FrequentlyInvoiceHeaderReq param = new FrequentlyInvoiceHeaderReq();
            param.setUserUuid(pdto.getPassengerUuid());
            param.setHeader(pdto.getHeader());
            param.setHeaderType(pdto.getHeaderType());
            Response<List<FrequentlyInvoiceHeaderDto>> response
                    = frequentlyInvoiceHeaderService.queryFrequentlyInvoiceHeaderList(param);
            list = response.getData();
            // 存在默认抬头不查历史
            if (CollectionUtil.isNotEmpty(response.getData())
                    && response.getData().get(0).getDefaultFlag().equals(1)) {
                return Response.createSuccess(list.get(0));
            }
            dto.getPassengerInvoiceDto().setInvoiceClass(CommonNumConst.NUM_1);
            dto.getPassengerInvoiceDto().setHeaderType(pdto.getHeaderType());
            PassengerInvoiceDto passengerInvoiceDto
                    = BeanUtils.propertiesCopy(dto.getPassengerInvoiceDto(), PassengerInvoiceDto.class);
            Response<PageResult<PassengerInvoiceDto>> pageResultResponse =
                    passengerInvoiceService.queryPageForCallCenter(
                            passengerInvoiceDto, dto.getPageNum(), dto.getPageSize());
            List<PassengerInvoiceDto> palist = pageResultResponse.getData().getList();
            if (CollectionUtil.isNotEmpty(palist)) {
                PassengerInvoiceDto pal = palist.get(0);
                FrequentlyInvoiceHeaderDto headerDto
                        = BeanUtils.propertiesCopy(pal, FrequentlyInvoiceHeaderDto.class);
                return Response.createSuccess(headerDto);
            }
            return Response.createSuccess(list);
        } catch (Exception e) {
            log.warn("调用 queryPageForCallCenter 异常:", e);
            return Response.createError(e.getMessage());
        }
    }

    /**
     * 设置购物中心数据格式
     *
     * @param vo       返回对象
     * @param mallList 列表
     */
    private void formatMallData(PassengerInvoiceDetailVo vo, List<InvoiceMallDto> mallList) {
        vo.setMallOrderNum(mallList.size());
        Date startDate = mallList.get(0).getCreateTime();
        Date endDate = mallList.get(0).getCreateTime();
        List<MallInvoiceItemVo> mallVoList = new ArrayList<>();
        for (InvoiceMallDto dto : mallList) {
            MallInvoiceItemVo mallInvoiceVo = new MallInvoiceItemVo();
            mallInvoiceVo.setOrderNo(dto.getOrderNo());
            mallInvoiceVo.setCityName(dto.getCity());
            mallInvoiceVo.setAmount(dto.getAmount());
            mallInvoiceVo.setBizType(dto.getBizType());
            mallInvoiceVo.setCreateTime(dto.getCreateTime().getTime());
            mallInvoiceVo.setName(dto.getSkuName());
            mallInvoiceVo.setQuantity(dto.getSkuNum());
            mallInvoiceVo.setInvoiceClass(vo.getInvoiceClass());
            mallInvoiceVo.setInvoiceSubjectCode(vo.getInvoiceSubjectCode());
            mallVoList.add(mallInvoiceVo);
            if (ObjectUtil.isNotNull(dto.getCreateTime()) && dto.getCreateTime().compareTo(startDate) < 0) {
                startDate = dto.getCreateTime();
            }
            if (ObjectUtil.isNotNull(dto.getCreateTime()) && dto.getCreateTime().compareTo(endDate) > 0) {
                endDate = dto.getCreateTime();
            }
        }
        vo.setMallList(mallVoList);
        vo.setMallStartDate(startDate.getTime());
        vo.setMallEndDate(endDate.getTime());
    }


    /**
     * 初始化发票列表数据
     *
     * @param pageReq pageReq
     * @param uuid    uuid
     * @return InvoiceDataBo
     */
    private Response<InvoiceDataBo> initInvoice(InvoiceRoutReq pageReq, String uuid) {
        log.info("InvoiceV3Business.initInvoice, request InvoiceRoutReq:{}", JSONUtil.toJsonStr(pageReq));
        String nowIndex = pageReq.getNextIndex();
        GetRouteInvoiceListForm getRouteInvoiceListForm = getGetRouteInvoiceListForm(pageReq, uuid);
        // index处理
        if (StringUtils.isEmpty(nowIndex)) {
            if (Objects.nonNull(getRouteInvoiceListForm.getEndTime())) {
                nowIndex = DateUtil.format(getRouteInvoiceListForm.getEndTime(), "yyyyMM");
            } else {
                nowIndex = DateUtil.format(new Date(), "yyyyMM");
            }
        }
        getRouteInvoiceListForm.setIndex(nowIndex);
        // 根据乘客Id， 行程id集合，开票状态分页查询行程(排除出租车)
        Response<PageResult<RouteInvoiceDto>> routeInvoicePageListRes = routeInvoiceService.getRouteInvoicePageList(
                getRouteInvoiceListForm);
        log.info("GetRouteInvoiceListForm: request:{}, response:{}", JSONUtil.toJsonStr(getRouteInvoiceListForm),
                JSONUtil.toJsonStr(routeInvoicePageListRes.getData().getList()));
        if (!routeInvoicePageListRes.isSuccess()) {
            return Response.createError("查询行程信息失败,请稍后重试");
        }
        InvoiceDataBo invoiceDataBo = InvoiceDataBo.newInstance();
        boolean hasMore = false;
        if (Objects.nonNull(routeInvoicePageListRes.getData())) {
            hasMore = routeInvoicePageListRes.getData().isHasMore();
            if (CollectionUtil.isNotEmpty(routeInvoicePageListRes.getData().getList())) {
                List<RouteInvoiceDto> personalRoutePlanData = routeInvoicePageListRes.getData().getList();
                List<String> routes = personalRoutePlanData.stream().map(RouteInvoiceDto::getRouteUuid)
                        .collect(Collectors.toList());
                // 获取行程费用（个人）
                Response<List<SettlementRouteDTO>> settlementList =
                        settlementRouteService.getRouteFareByRouteUuids(routes);
                List<SettlementRouteDTO> settleRouteRespData = null;
                if (!settlementList.getSuccess() || settlementList.getData() == null) {
                    settleRouteRespData = new ArrayList<>();
                } else {
                    settleRouteRespData = settlementList.getData();
                }

                // 获取行程费用（企业）
                Response<List<SrOrderDto>> srorderListResponse = srOrderService.getRouteFareByRoutePlanIds(routes);
                List<SrOrderDto> srOrderDtos = null;
                if (!srorderListResponse.getSuccess() || srorderListResponse.getData() == null) {
                    srOrderDtos = new ArrayList<>();
                } else {
                    srOrderDtos = srorderListResponse.getData();
                }
                List<RoutePlanBo> routePlanDtos = Lists.newArrayList();
                for (RouteInvoiceDto personalRoutePlanDatum : personalRoutePlanData) {
                    RoutePlanBo routePlanDto = new RoutePlanBo();
                    BeanUtils.copyProperties(personalRoutePlanDatum, routePlanDto);
                    routePlanDto.setInvoiceAmount(personalRoutePlanDatum.getInvoiceAmount());
                    routePlanDto.setUuid(personalRoutePlanDatum.getRouteUuid());
                    routePlanDtos.add(routePlanDto);
                }
                invoiceDataBo.setRoutePlanList(routePlanDtos);
                invoiceDataBo.setSettleRouteList(settleRouteRespData);
                invoiceDataBo.setSrOrderList(srOrderDtos);
                invoiceDataBo.setTotalCount(routeInvoicePageListRes.getData().getTotalCount());
                invoiceDataBo.setCurrPage(routeInvoicePageListRes.getData().getCurrPage());
                invoiceDataBo.setPageSize(routeInvoicePageListRes.getData().getPageSize());
            }
        }
        Integer queryMonthLimit = routeConfig.getQueryMonthLimit();
        String nextIndex = nowIndex;
        String tips = "轻触加载更多行程";
        boolean endFlag = RouteInfoBusiness.checkListEnd(nextIndex);
        if (Objects.nonNull(getRouteInvoiceListForm.getStartTime())) {
            // 查询是否达到查询开始时间
            Calendar queryCalendar = Calendar.getInstance();
            queryCalendar.setTime(com.t3.ts.utils.DateUtils.format(nextIndex, "yyyyMM"));

            DateTime dateTime = DateUtil.beginOfMonth(getRouteInvoiceListForm.getStartTime());
            endFlag = dateTime.getTime() >= queryCalendar.getTime().getTime();
        }
        if (!hasMore) {
            nextIndex = RouteInfoBusiness.getQueryMonthPre(nextIndex, queryMonthLimit);
            tips = endFlag ? "没有更多了" : "点击查看前三个月可开票订单";
        }

        invoiceDataBo.setNextIndex(nextIndex);
        invoiceDataBo.setTips(tips);
        invoiceDataBo.setEndFlag(endFlag);
        invoiceDataBo.setHasMore(hasMore);
        return Response.createSuccess(invoiceDataBo);
    }

    /**
     * 得到订单uuid
     *
     * @param uuid uuid
     * @return {@link Response<String>}
     */
    public Response<String> getOrderUuids(String uuid) {
        PassengerInvoiceDto passengerInvoiceDto = new PassengerInvoiceDto();
        passengerInvoiceDto.setUuid(uuid);
        Response<List<InvoiceRouteDto>> listResp = passengerInvoiceService.invoiceItineraryList(passengerInvoiceDto);
        if (!listResp.isSuccess() || CollectionUtils.isEmpty(listResp.getData())) {
            return Response.createError("查询行程列表失败");
        }

        List<InvoiceRouteDto> data = listResp.getData();
        String collect = data.stream().map(InvoiceRouteDto::getRouteUuid).collect(Collectors.joining(","));
        return Response.createSuccess("查询成功", collect);
    }

    /**
     * 修改行程开票状态
     *
     * @param routePlanUuids uuid路线计划
     * @param noteStatus     注意状态
     * @return {@link Response}
     */
    public Response modifyRouteStatus(List<String> routePlanUuids, String noteStatus) {
        return Response.createSuccess("修改开票状态成功");
    }

    /**
     * 校验开票金额是否满足开票条件
     *
     * @param orderToCodes orderToCodes
     * @return Response
     */
    private Response checkMoney(List<OrderToCode> orderToCodes) {
        if (CollectionUtil.isNotEmpty(orderToCodes)) {
            Map<Integer, String> codeMap = orderToCodes.stream().collect(
                    Collectors.toMap(OrderToCode::getInvoiceSubjectCode,
                            OrderToCode::getOrderUuid, (k1, k2) -> k2));
            Map<Integer, BigDecimal> moneyMap = orderToCodes.stream().collect(
                    Collectors.toMap(OrderToCode::getInvoiceSubjectCode,
                            OrderToCode::getMoney, (k1, k2) -> k1.add(k2).setScale(NUM_2)));
            for (Map.Entry<Integer, String> temp : codeMap.entrySet()) {
                Response<InvoiceSubjectClassDto> resp = invoiceConfigService.getSubjectClassInfo(NumConstants.NUM_1,
                        temp.getKey());
                if (null == resp || null == resp.getData()) {
                    return Response.createError("查询发票主体失败");
                }
                InvoiceSubjectClassDto data = resp.getData();
                if (ObjectUtil.isNotNull(data) && Objects.nonNull(data.getLimitAmount())
                        && ObjectUtil.isNotNull(moneyMap.get(temp.getKey()))
                        && data.getLimitAmount().compareTo(moneyMap.get(temp.getKey())) < 0) {
                    return Response.createError("单张发票,开票金额达到上限");
                }
            }
        } else {
            return Response.createError("开票主体不能为空");
        }
        return Response.createSuccess();
    }

    /**
     * 重开票检查
     *
     * @param billType    billType
     * @param header      header
     * @param invoiceForm invoiceForm
     * @param uuid        uuid
     * @param orderUuid   orderUuid
     * @return String
     */
    private Response<String> checkRedoneBill(Integer billType, String header, InvoiceRelevantFormV3 invoiceForm,
                                             String uuid, String orderUuid) {
        if (PassengerConstants.BILLTYPE_REDONE.equals(billType)) {
            Response checkResponse = Response.createSuccess();
            if (StringUtils.isBlank(uuid)) {
                checkResponse = Response.createError("发票id不能为空");
            } else if (!invoiceForm.getTransportType().equals(CommonNumConst.T3_TRANSPORT)) {
                //同时呼叫暂不支持第三方开票
                checkResponse = Response.createError("第三方发票暂不支持重新开票");
            }
            if (!checkResponse.isSuccess()) {
                return checkResponse;
            }
            //重新开票走的是先红冲再开票的逻辑，但是h5传值不会传内部类，只会在外部传运力类型，所以手动赋值T3Transport
            T3Transport t3Transport = new T3Transport();
            t3Transport.setTransportType(invoiceForm.getTransportType());
            invoiceForm.setT3Transport(t3Transport);

            // 判断发票状态
            PassengerInvoiceDto req = new PassengerInvoiceDto();
            req.setUuid(uuid);
            Response<PassengerInvoiceDto> invoiceInfo = passengerInvoiceService.invoiceDetail(req);
            // 查看发票抬头是否变更
            if (StringUtils.isBlank(orderUuid) && invoiceInfo.isSuccess()
                    && Objects.nonNull(invoiceInfo.getData()) && StringUtils.isNotBlank(header)
                    && StringUtils.equals(header, invoiceInfo.getData().getHeader())) {
                return Response.createError("提交失败，开票信息未变更");
            }

            if (invoiceInfo.isSuccess() && Objects.nonNull(invoiceInfo.getData())
                    && Integer.valueOf(NUM_5).equals(invoiceInfo.getData().getStatus())) {

                if (!invoiceInfo.isSuccess() || Objects.isNull(invoiceInfo.getData())
                        || Integer.valueOf(NUM_1).equals(invoiceInfo.getData().getModifyStatus())) {
                    return Response.createError("提交失败，当前发票无法重开");
                }

                // 重新开票,先作废原发票
                Response<String> response = passengerInvoiceService.invalidInvoice(uuid,
                        invoiceForm.getApplySource(), null, null);
                if (!response.isSuccess()) {
                    return Response.createError("重新开票失败,请稍后重试");
                }
            }

            if (StringUtils.isBlank(orderUuid)) {
                Response<String> orderUuidRes = getOrderUuids(uuid);
                if (!orderUuidRes.isSuccess()) {
                    return orderUuidRes;
                }
                orderUuid = orderUuidRes.getData();
            }
        }
        return Response.createSuccess("", orderUuid);
    }

    /**
     * 开票请求
     *
     * @param map map
     * @return Response
     */
    private Response getResponse(Map<String, InvoiceRelevantFormV3> map) {
        List<PassengerInvoiceDto> passengerInvoiceDtos = new ArrayList<>();
        InvoiceRelevantFormV3 t3Transport = map.get("t3Transport");
        Response<List<PassengerInvoiceDto>> response = null;
        if (ObjectUtil.isNotNull(t3Transport) && ObjectUtil.isNotNull(t3Transport.getT3Transport())) {
            response = assembleParam(t3Transport,
                    t3Transport.getT3Transport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
            passengerInvoiceDtos.addAll(response.getData());
        }
        InvoiceRelevantFormV3 dfTransport = map.get("dfTransport");
        if (ObjectUtil.isNotNull(dfTransport) && ObjectUtil.isNotNull(dfTransport.getDfTransport())) {
            response = assembleParam(dfTransport, dfTransport.getDfTransport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
            passengerInvoiceDtos.addAll(response.getData());
        }

        InvoiceRelevantFormV3 fawTransport = map.get("fawTransport");
        if (ObjectUtil.isNotNull(fawTransport) && ObjectUtil.isNotNull(fawTransport.getFawTransport())) {
            response = assembleParam(fawTransport, fawTransport.getFawTransport().getOrderToCodes());
            if (!response.isSuccess()) {
                return response;
            }
            passengerInvoiceDtos.addAll(response.getData());
        }

        try {
            log.info("billingByGroup请求 开票入参--{}", JSON.toJSONString(passengerInvoiceDtos));
            Response<List<PassengerInvoiceDto>> listResponse =
                    passengerInvoiceService.billingByGroup(passengerInvoiceDtos, false);
            log.info("billingByGroup接口返回 开票入参--{}, 返回 {}", JSON.toJSONString(passengerInvoiceDtos),
                    JSON.toJSONString(listResponse));
            if (null != listResponse && CollectionUtil.isNotEmpty(listResponse.getData())) {
                //修改行程为已开票
                return Response.createSuccess("开票成功");
            }
            return Response.createError("开票失败,请稍后重试");
        } catch (Exception e) {
            log.error("invoiceBilling param={} error:", JSON.toJSONString(passengerInvoiceDtos), e);
            return Response.createError("开票出错");
        }
    }

    /**
     * 开票拼装参数
     *
     * @param invoiceForm  invoiceForm
     * @param orderToCodes orderToCodes
     * @return List<PassengerInvoiceDto>
     */
    private Response<List<PassengerInvoiceDto>> assembleParam(InvoiceRelevantFormV3 invoiceForm,
                                                              List<OrderToCode> orderToCodes) {
        Response<List<RouteInvoiceDto>> response = checkInvoice(invoiceForm);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg());
        }
        if (CollectionUtil.isEmpty(orderToCodes)) {
            return Response.createError("开票主体不能为空");
        }

        Map<Integer, BigDecimal> moneyMap = orderToCodes.stream().collect(
                Collectors.toMap(OrderToCode::getInvoiceSubjectCode,
                        OrderToCode::getMoney, (k1, k2) -> k1.add(k2).setScale(NUM_2)));

        List<PassengerInvoiceDto> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(response.getData())) {
            List<RouteInvoiceDto> routeInvoiceDtos = response.getData();
            String passengerUuid = invoiceForm.getInvoicePassengerUuid();

            Map<Integer, List<String>> routeUuidMap = routeInvoiceDtos.stream().collect(Collectors.toMap(
                    RouteInvoiceDto::getInvoiceSubjectCode,
                    invoiceDto -> {
                        List<String> getNameList = new ArrayList<>();
                        getNameList.add(invoiceDto.getRouteUuid());
                        return getNameList;
                    },
                    (List<String> value1, List<String> value2) -> {
                        value1.addAll(value2);
                        return value1;
                    }
            ));

            if (CollectionUtil.isNotEmpty(routeUuidMap)) {
                for (Map.Entry<Integer, List<String>> temp : routeUuidMap.entrySet()) {
                    Integer invoiceSubjectCode = temp.getKey();
                    List<String> routeUuids = temp.getValue();
                    PassengerInvoiceDto passengerInvoiceDto = BeanUtils.propertiesCopy(invoiceForm,
                            PassengerInvoiceDto.class);
                    passengerInvoiceDto.setUuid(null);
                    passengerInvoiceDto.setPassengerUuid(passengerUuid);
                    passengerInvoiceDto.setTransportType(invoiceForm.getTransportType());
                    List<InvoiceRouteDto> itineraryList = Lists.newArrayList();

                    routeInvoiceDtos.forEach(routeInvoiceDto -> {
                        if (routeUuids.contains(routeInvoiceDto.getRouteUuid())) {
                            InvoiceRouteDto invoiceRouteDto = BeanUtils.propertiesCopy(routeInvoiceDto,
                                    InvoiceRouteDto.class);
                            invoiceRouteDto.setAmount(routeInvoiceDto.getInvoiceAmount());
                            invoiceRouteDto.setCity(routeInvoiceDto.getCityName());
                            invoiceRouteDto.setOrderNo(routeInvoiceDto.getRouteNo());
                            // 里程数
                            invoiceRouteDto.setTrip(invoiceRouteDto.getTrip());
                            invoiceRouteDto.setServiceAmount(null);
                            itineraryList.add(invoiceRouteDto);
                        }
                    });
                    passengerInvoiceDto.setItineraryList(itineraryList);
                    passengerInvoiceDto.setInvoiceSubjectCode(invoiceSubjectCode);
                    passengerInvoiceDto.setSendType(1);
                    passengerInvoiceDto.setAgentCompanyUuid(routeInvoiceDtos.get(0).getAgentUuid());
                    passengerInvoiceDto.setAgentCompanyName(routeInvoiceDtos.get(0).getAgentName());
                    passengerInvoiceDto.setMoney(moneyMap.get(invoiceSubjectCode));
                    list.add(passengerInvoiceDto);
                }
            }

        }
        return Response.createSuccess(list);
    }

    /**
     * 校验开票检查
     *
     * @param invoiceForm invoiceForm
     * @return List<RouteInvoiceDto>
     */
    private Response<List<RouteInvoiceDto>> checkInvoice(InvoiceRelevantFormV3 invoiceForm) {
        if (ObjectUtil.isNotNull(invoiceForm)) {
            String orderUuid = invoiceForm.getInvoiceOrderUuid();
            // 开发票
            List<String> routePlanUuids = Arrays.asList(orderUuid.split(","));
            Response<List<RouteInvoiceDto>> routeInvoicePageListRes =
                    routeInvoiceService.getRouteInvoiceInfoList(routePlanUuids);
            if (!routeInvoicePageListRes.isSuccess()
                    || CollectionUtils.isEmpty(routeInvoicePageListRes.getData())) {
                return Response.createError("查询行程信息失败,请稍后重试");
            }
            if (routePlanUuids.size() != routeInvoicePageListRes.getData().size()) {
                return Response.createError("开票行程数量和支付行程数量不一致");
            }

            return Response.createSuccess(routeInvoicePageListRes.getData());
        }
        return Response.createSuccess(new ArrayList<>());
    }
}

