package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.result.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Describer: 出租车司机端配置
 *
 * <AUTHOR>
 */
@Api(tags = "出租车司机端配置")
@Slf4j
@RestController
@RequestMapping("/api/taxiDriver")
public class TaxiDriverController extends BaseApi {


    @Autowired
    private SwitchConfig switchConfig;

    /**
     * 查询赚钱码配置
     *
     * @return  {maxAmount:5000}
     */
    @ApiOperation(value = "查询赚钱码配置", notes = "查询赚钱码配置")
    @PostMapping("/queryRecommendCodeConfig")
    public Response<JSONObject> queryRecommendCodeConfig(@RequestBody JSONObject json) {
        try {
            return Response.createSuccess(JSON.parseObject(switchConfig.getRecommendCodeConfig()));
        } catch (Exception e) {
            log.info("查询赚钱码配置异常 ", e);
        }
        return Response.createError();
    }
}
