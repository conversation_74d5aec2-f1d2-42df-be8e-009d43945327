package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:06
 * @description:
 */
@Data
public class BaseFareDTO implements Serializable {
    /**
     * 起步价
     **/
    private BigDecimal startFare;
    /**
     * 起步里程
     **/
    private BigDecimal startTrip;

    /**
     * 起步时长（分钟）
     **/
    private Integer startDuration;

    /**
     * 最低消费
     */
    private BigDecimal lowestFare;
    /**
     * 计算模式 1:先低消,后折扣， 2 先折扣 后低消
     */
    private Integer model;

    public enum ModelType {
        before(1, "计算折扣矩阵前生效"),
        after(2, "计算折扣矩阵后生效");

        private int type;
        private String desc;

        ModelType(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public int getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }
    }

}
