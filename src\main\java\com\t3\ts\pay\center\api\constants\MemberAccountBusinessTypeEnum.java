package com.t3.ts.pay.center.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <b>
 * MemberAccountBusinessTypeEnum
 * 2020/6/28
 * 会员中心账户业务线枚举：
 * 1:快享 2:企业用车 3:专享 4:包车 5:顺风车
 * </b>
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum MemberAccountBusinessTypeEnum {
    /**
     * 企业用车
     */
    BUSINESS_TYPE_ENTERPRISE(2, "企业用车"),
    /**
     * 包车
     */
    BUSINESS_TYPE_CHARTERED(4, "包车");

    private Integer businessType;

    private String desc;
}
