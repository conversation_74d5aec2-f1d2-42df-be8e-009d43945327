package com.t3.ts.pay.center.api.config.interceptor;

import com.t3.ts.pay.center.api.util.SpringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.t3.ts.auth.center.constants.SystemIdEnum;
import com.t3.ts.auth.center.dto.PhoneSubjectDTO;
import com.t3.ts.auth.center.service.UserSubjectService;
import com.t3.ts.constant.Constants;
import com.t3.ts.context.vo.CompanyDTO;
import com.t3.ts.org.manager.dto.res.OrgEmployeeResDto;
import com.t3.ts.org.manager.service.OrgEmployeeService;
import com.t3.ts.passenger.dto.PassengerInfoDto;
import com.t3.ts.passenger.service.PassengerService;
import com.t3.ts.pay.center.api.bo.EnterpriseInfoBo;
import com.t3.ts.pay.center.api.config.PayCenterInterceptorConfig;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.List;
import java.util.Objects;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_2;


/**
 * 乘客令牌拦截器
 *
 * <AUTHOR>
 * @since 2020/10/26
 */
@Component
public class PassengerTokenInterceptor extends HandlerInterceptorAdapter {

    private static final String IS_ENTERPRISE_USER = "isEnterpriseUser";
    public static final String REQUEST_HEADER_DEVICE_FINGERPRINT_TOKEN = "riskDeviceToken";
    private static final String ENTERPRISE_INFO = "enterpriseInfo";
    private static final String DEVICE_TOKEN_KEY = "deviceToken";
    private static final String BASIC_URL1 = "/api/passenger/v1/";
    private static final String BASIC_URL2 = "/api/passenger/v2/";
    private static final List<String> BLOCK_INTERCEPTION_LIST =
            Lists.newArrayList("route/create", "route/callAgain",
                    "route/newCallAgain",
                    "enterprise/confirmationCar",
                    "enterprise/callAgain",
                    "enterprise/newCallAgain",
                    "route/notFinishList");
    private static final List<String> TOKEN_WHITE_LIST =
            Lists.newArrayList("/api/payment/v2/pay/makeOrder", "/api/driver/");
    @DubboReference
    private PassengerService passengerService;
    @DubboReference
    private OrgEmployeeService orgEmployeeService;
    @DubboReference
    private UserSubjectService userSubjectService;

    public static final ThreadLocal<String> THREAD_LOCAL_PASSENGER_MOBILE = new ThreadLocal<>();

    /**
     * 前手柄
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理程序
     * @return boolean* @throws Exception 例外情况
     */
    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            String token = getToken(request);
            response.setCharacterEncoding("utf-8");
            //免token校验接口
            if (checkTokenWhiteList(request.getRequestURI())) {
                return true;
            }

            if (token == null) {
                outPrint(response.getWriter(), Response.createError(ResultErrorEnum.ERR_MSG_LOGIN_OTHER));
                return false;
            }

            // 获取乘客信息
            Response<PassengerInfoDto> passengerByToken = passengerService.findPassengerByToken(token);
            boolean isNullDeviceToken = StringUtils.isNotEmpty(request.getHeader(DEVICE_TOKEN_KEY));
            boolean isNullPassengerInfo = !passengerByToken.isSuccess() || passengerByToken.getData() == null;

            /*多设备登录拦截*/
            boolean flag = (isNullDeviceToken && isNullPassengerInfo)
                    || (isNullPassengerInfo || StringUtils.isBlank(passengerByToken.getData().getUuid()));
            if (flag) {
                outPrint(response.getWriter(), Response.createError(ResultErrorEnum.ERR_MSG_LOGIN_OTHER));
                return false;
            }

            // 封号拦截
            String accountStatus = passengerByToken.getData().getAccountStatus();
            if (checkBlockUrl(request.getRequestURI()) && !getAccountStatus(accountStatus)) {
                outPrint(response.getWriter(), Response.createError(ResultErrorEnum.ERR_MSG_USER_CLOSE));
                return false;
            }

            // 企业用车信息
            if (!enterpriseInfo(request.getRequestURI(), passengerByToken.getData().getMobile(), request)) {
                outPrint(response.getWriter(), Response.createError(ResultErrorEnum.ERR_CODE_DISABLE_ENTERPRISE));
                return false;
            }

            request.setAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_UUID, passengerByToken.getData().getUuid());
            request.setAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_MOBILE,
                    passengerByToken.getData().getMobile());
            request.setAttribute("nickname", passengerByToken.getData().getNickname());

            response.reset();
        }
        return true;
    }

    /**
     * 企业信息
     * 企业用车信息
     *
     * @param requestURI 请求uri
     * @param mobile     移动
     * @param request    请求
     * @return boolean
     */
    private boolean enterpriseInfo(String requestURI, String mobile, HttpServletRequest request) {
        if (null != requestURI
                && "/api/passenger/v1/user/passengerInfo".equals(requestURI)) {
            Response<OrgEmployeeResDto> orgResponse = orgEmployeeService.checkRole(mobile);
            request.setAttribute(IS_ENTERPRISE_USER, orgResponse.getSuccess());
            if (orgResponse.isSuccess() && Objects.nonNull(orgResponse.getData())) {
                request.setAttribute("employId", orgResponse.getData().getOrgEmployeeId());
            }
        }
        if (null != requestURI
                && requestURI.contains("enterprise")
                && !"/api/passenger/v1/enterprise/prepaymentsDetail".equals(requestURI)
                && !"/api/passenger/v1/enterprise/route/fareItems".equals(requestURI)
                && !"/api/passenger/v1/enterprise/register".equals(requestURI)
                && !"/api/payment/v1/enterprise/cashierDeskPro".equals(requestURI)
                && !"/api/payment/v1/enterprise/payForAllBusiness".equals(requestURI)
        ) {
            Response<OrgEmployeeResDto> orgResponse = orgEmployeeService.checkRole(mobile);
            if (!orgResponse.getSuccess()) {
                request.setAttribute(IS_ENTERPRISE_USER, false);
                return false;
            } else {
                request.setAttribute(IS_ENTERPRISE_USER, true);
                request.setAttribute("employId", orgResponse.getData().getOrgEmployeeId());
                PhoneSubjectDTO phoneSubject = new PhoneSubjectDTO();
                phoneSubject.setPhone(mobile);
                phoneSubject.setSystemId(SystemIdEnum.ORG_MANAGER.getValue());
                List<CompanyDTO> list = userSubjectService.getCompanyDTOByPhone(phoneSubject);
                if (CollectionUtils.isEmpty(list)) {
                    request.setAttribute(ENTERPRISE_INFO, EnterpriseInfoBo.instance());
                } else {
                    EnterpriseInfoBo enterpriseInfoBo = EnterpriseInfoBo.instance();
                    enterpriseInfoBo.setCompanyId(list.get(0).getId());
                    enterpriseInfoBo.setCompanyName(list.get(0).getName());
                    request.setAttribute(ENTERPRISE_INFO, enterpriseInfoBo);
                }
                if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
                    EnterpriseInfoBo enterpriseInfoBo = EnterpriseInfoBo.instance();
                    enterpriseInfoBo.setCompanyId(list.get(0).getId());
                    enterpriseInfoBo.setCompanyName(list.get(0).getName());
                    enterpriseInfoBo.setSubsidiaryId(list.get(1).getId());
                    enterpriseInfoBo.setSubsidiaryCode(list.get(1).getCode());
                    enterpriseInfoBo.setSubsidiaryName(list.get(1).getName());
                    request.setAttribute(ENTERPRISE_INFO, enterpriseInfoBo);
                }
            }
        }
        return true;
    }

    /**
     * 查看挡路URL
     *
     * @param requestURI 请求URI
     * @return boolean
     */
    private boolean checkBlockUrl(String requestURI) {

        for (String url : BLOCK_INTERCEPTION_LIST) {
            if ((BASIC_URL1 + url).equalsIgnoreCase(requestURI) || (BASIC_URL2 + url).equalsIgnoreCase(requestURI)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否可以免token调用接口
     *
     * @param requestURI 请求URI
     * @return boolean
     */
    private boolean checkTokenWhiteList(String requestURI) {
        for (String url : TOKEN_WHITE_LIST) {
            if (url.equalsIgnoreCase(requestURI) || requestURI.startsWith(url)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取令牌
     * 获取token
     *
     * @param request 请求
     * @return {@link String}
     */
    private String getToken(HttpServletRequest request) {
        if (StringUtils.isBlank(request.getHeader(Constants.TOKEN_KEY))) {
            return request.getParameter(Constants.TOKEN_KEY);
        }
        return request.getHeader(Constants.TOKEN_KEY);
    }

    /**
     * 获取帐户状态
     * 获取账号状态
     * true:正常，false:不正常
     *
     * @param accountStatus 帐户状态
     * @return boolean
     */
    private boolean getAccountStatus(String accountStatus) {
        if (StringUtils.isNotBlank(accountStatus)) {
            Integer intStatus = Integer.parseInt(accountStatus);
            return !intStatus.equals(NUM_2) && !intStatus.equals(1);
        }
        return true;
    }

    /**
     * 创建json对象
     *
     * @param msg     味精
     * @param errCode 错误码
     * @param data    数据
     * @return {@link Response}
     */
    private Response createJsonObject(String msg, int errCode, Object data) {
        return Response.createError(msg, errCode, data);
    }

    /**
     * 输出打印
     *
     * @param out      输出
     * @param response 响应
     */
    private void outPrint(PrintWriter out, Response response) {
        out.println(JSON.toJSON(response));
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) throws Exception {
        THREAD_LOCAL_PASSENGER_MOBILE.remove();
    }

}
