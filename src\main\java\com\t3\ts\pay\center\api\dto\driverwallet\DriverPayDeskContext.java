package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.pay.center.api.dto.vo.PayWayVo;
import com.t3.ts.pay.remote.dto.DriverPayDetail;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class DriverPayDeskContext implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 入参
     */
    private DriverPayDeskDto driverPayDeskDto;

    /**
     * 出参
     */
    private DriverPayDeskVo driverPayDeskVo;


    /*这些是前端用不到，但是上下文中要使用的参数 start**/

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 已经支付渠道金额明细
     */
    private List<DriverPayDetail> payDetail;

    /**
     * 账本余额支付金额,证明选了余额支付
     */
    private boolean chooseBookPay = false;

    /**
     * 账本余额支付金额
     * 余额为0 也不代表乘客没选
     */
    private BigDecimal bookPayFare = BigDecimal.ZERO;



    /**
     * 选择了哪个三方支付渠道
     * 证明选择了三方支付渠道
     */
    private PayWayVo chooseThirdPayChannel;

    /**
     * 三方待支付金额
     */
    private BigDecimal thirdPayFare = BigDecimal.ZERO;

    /**
     * 版本号
     */
    private String grayVersion;


    /*这些是前端用不到，但是上下文中要使用的参数 end **/
}
