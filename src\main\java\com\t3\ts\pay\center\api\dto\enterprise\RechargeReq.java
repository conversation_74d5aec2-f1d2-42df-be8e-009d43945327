package com.t3.ts.pay.center.api.dto.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 企业个人支付充值
 *
 * <AUTHOR>
 * @date 2019.11.4
 */
@ApiModel
@Getter
@Setter
public class RechargeReq {

    /**
     * 充值结算ID
     */
    @ApiModelProperty(value = "充值结算ID")
    private String settlementId;
    /**
     * 充值支付方式
     */
    @ApiModelProperty(value = "充值支付方式")
    private Integer rechargePayType;
    /**
     * 申请单ID
     */
    @ApiModelProperty(value = "申请单ID")
    private String requisitionUuid;

	/**
	 * 申请单ID
	 */
	@ApiModelProperty(value = "微信小程序code（微信小程序支付使用）")
	private String code;


}
