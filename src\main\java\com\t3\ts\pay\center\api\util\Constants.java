package com.t3.ts.pay.center.api.util;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL> Date:     2020/6/8, 0008 20:42
 */
public final class Constants {
    /**
     * Instantiates a new Constants.
     */
    private Constants() {
    }

    /**
     * The constant PAY_CENTER_PREFIX.
     */
    public static final String PAY_CENTER_PREFIX = "t3pay.pay";

    public static final String PAY_CENTER_TRADE_PREFIX = "t3pay.trade";
    /**
     * The constant ACCOUNT_CENTER_PREFIX.
     */
    public static final String ACCOUNT_CENTER_PREFIX = "t3pay.account";
    /**
     * The constant SETTLEMENT_CENTER_PREFIX.
     */
    public static final String SETTLEMENT_CENTER_PREFIX = "t3pay.settlement";

    /**
     * The constant NUMBER_200.
     */
    public static final Integer NUMBER_200 = 200;
    /**
     * The constant NUMBER_500.
     */
    public static final Integer NUMBER_500 = 500;
    public static final Integer NUMBER_35 = 35;
    public static final int N_2 = 2;
    public static final int N_3 = 3;
    public static final int N_100 = 100;
}
