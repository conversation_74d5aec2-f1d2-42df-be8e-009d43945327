package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.remote.constants.PaywayEnum;
import java.util.Arrays;
import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:02
 */
public class PayWayConvertTest {

    @Test
    public void testGetPayWayEnum() throws Exception {
        PaywayEnum[] result = PayWayConvert.getPayWayEnum(Arrays.<Integer>asList(Integer.valueOf(0)));
        Assert.assertArrayEquals(new PaywayEnum[] {PaywayEnum.THIRD_PAY_FLAG}, result);
    }

    @Test
    public void testGetPayWayEnum2() throws Exception {
        PaywayEnum[] result = PayWayConvert.getPayWayEnum(Integer.valueOf(0));
        Assert.assertArrayEquals(new PaywayEnum[] {PaywayEnum.THIRD_PAY_FLAG}, result);
    }

    @Test
    public void testGetThirdType() throws Exception {
        int result = PayWayConvert.getThirdType(Arrays.<Integer>asList(Integer.valueOf(0)));
        Assert.assertEquals(0, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
