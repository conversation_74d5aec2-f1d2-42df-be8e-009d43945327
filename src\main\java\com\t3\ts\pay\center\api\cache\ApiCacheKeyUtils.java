package com.t3.ts.pay.center.api.cache;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-02-19 15:27
 * @des: 缓存key工具类
 */
public class ApiCacheKeyUtils implements Serializable {

    /**
     * 图形验证码
     */
    public static final String IMAGE_CODE = "API:PASSENGER:IMAGE";

    /**
     * 滑块验证码
     */
    public static final String IMAGE_CODE_SLIDE = "API:PASSENGER:SLIDE:IMAGE";

    /**
     * 短信验证码
     */
    public static final String SMS_CODE = "API:PASSENGER:SMS:";

    /**
     * 发送验证码次数
     */
    public static final String SMS_CODE_COUNT = "API:PASSENGER:SMS:COUNT:";

    /**
     * 验证码验证错误
     */
    public static final String SMS_CODE_ERROR = "API:PASSENGER:SMS:ERROR:";

    /**
     * APP端日志缓存key
     */
    public static final String APP_LOG = "API:MONITOR:LOG:";

    /**
     * 进行中行程订单车辆vin码
     */
    public static final String ROUTE_PLAN_VIN = "API:PASSENGER:ROUTEPLAN:";

    /**
     * 行程分享活动
     */
    public static final String ROUTE_SHARE_ACTIVITY_ID = "API:PASSENGER:ROUTESHARE:ACTIVITYID:";

    /**
     * 行程分享活动
     */
    public static final String RECOMMEND_ACTIVITY_USER_FLAG = "API:PASSENGER:RECOMMEND:FLAG:";

    /**
     * 新人券领取标识
     */
    public static final String RECOMMEND_ACTIVITY_USER_DELETE_FLAG = "API:PASSENGER:RECOMMEND:DELETE:FLAG:";

    public static final int RECOMMEND_ACTIVITY_USER_EXPIRE = 7 * 24 * 60;

    public static final String CITY_CODE_REDIS_KEY = "API:PASSENGER:CITY:CODE:";
    public static final String CITY_UUID_REDIS_KEY = "API:PASSENGER:CITY:UUID:";

    private static final String BASE_CODE = "PAY:CENTER:";
    /**
     * 限制电子发票发送邮箱次数
     */
    public static final String INVOICE_EMAIL_LIMIT = BASE_CODE + "INVOICE:SEMAIL:ID:%s";
    /**
     * 限制电子发票发送邮箱次数
     */
    public static final String INVOICE_EMAIL_LIMIT_LOCK = BASE_CODE + "INVOICE:SEMAIL:LOCK:%s";

    /**
     * 绑卡校验失败次数
     */
    public static final String BIND_CARD_ERROR = BASE_CODE + "CARD_BIND_ERR:%s";

    /**
     * 四要素绑卡通过缓存
     */
    public static final String VERIFY_BANK_CARD_INFO = BASE_CODE + "VERIFY_BANK_CARD_INFO:%s";

    /**
     * 约约司机身份确认token
     */
    public static final String YUEYUE_CONFIRM_TOKEN = BASE_CODE + "CONFIRM:TOKEN:%s";
}
