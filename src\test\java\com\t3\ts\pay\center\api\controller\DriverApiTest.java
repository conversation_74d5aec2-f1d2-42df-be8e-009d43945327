package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.EnterprisePaymentBusiness;
import com.t3.ts.pay.center.api.dto.DriverPaymentReq;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DriverApiTest {

    @Mock
    EnterprisePaymentBusiness enterprisePaymentBusiness;
    @Mock
    Logger log;
    @InjectMocks
    DriverApi driverApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPay() throws Exception {
        when(driverApi.pay(any(), any())).thenReturn(null);

        DriverPaymentReq req = new DriverPaymentReq();
        req.setBizType(81);
        req.setOrderId("541443FA8A8547CD9D4C00BEF53F12AA");
        req.setPayChannel(1);

        Response result = driverApi.pay(new DriverPaymentReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQueryPayResult() throws Exception {

        Response result = driverApi.queryPayResult(new DriverPaymentReq());
        Assert.assertEquals(null, result);
    }
}
