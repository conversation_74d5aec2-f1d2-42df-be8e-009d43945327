package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.center.api.constants.NumConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;


/**
 * 常用获取客户端信息的工具
 * <AUTHOR>
 */
public final class NetworkUtil {
    /**
     * 网络跑龙套
     */
    private NetworkUtil() {
    }

    /**
     * Logger for this class
     */
    private static Logger logger = LoggerFactory.getLogger(NetworkUtil.class);
    /**
     * IP分割符
     */
    private static final String IP_SPLIT = ",";

    /**
     * 获取外网IP
     * @param request 请求
     * @return IP
     */
    public static String getOutIp(HttpServletRequest request) {
        try {
            String ip = request.getHeader("x-forwarded-for");
            logger.info("getOutIp x-forwarded-for:{}", ip);

            if (StringUtils.isEmpty(ip)) {
                return null;
            }

            String[] ips = ip.split(IP_SPLIT);
            return ips[0].trim();
        } catch (Exception e) {
            logger.error("getOutIp error,information:", e);
            return null;
        }
    }

    /**
     * 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址;
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getIpAddress(HttpServletRequest request) {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址
        try {
            String ip = request.getHeader("X-Forwarded-For");
            if (logger.isInfoEnabled()) {
                logger.info("getIpAddress(HttpServletRequest) - X-Forwarded-For - String ip={}", ip);
            }

            if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                    if (logger.isInfoEnabled()) {
                        logger.info("getIpAddress(HttpServletRequest) - Proxy-Client-IP - String ip={}", ip);
                    }
                }
                if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");
                    if (logger.isInfoEnabled()) {
                        logger.info("getIpAddress(HttpServletRequest) - WL-Proxy-Client-IP - String ip={}", ip);
                    }
                }
                if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_CLIENT_IP");
                    if (logger.isInfoEnabled()) {
                        logger.info("getIpAddress(HttpServletRequest) - HTTP_CLIENT_IP - String ip={}", ip);
                    }
                }
                if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                    ip = request.getHeader("HTTP_X_FORWARDED_FOR");
                    if (logger.isInfoEnabled()) {
                        logger.info("getIpAddress(HttpServletRequest) - HTTP_X_FORWARDED_FOR - String ip={}", ip);
                    }
                }
                if (ip == null || ip.length() == 0 || NumConstants.STRING_UNKNOWN.equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                    if (logger.isInfoEnabled()) {
                        logger.info("getIpAddress(HttpServletRequest) - getRemoteAddr - String ip={}", ip);
                    }
                }
            } else if (ip.length() > NumConstants.NUM_15) {
                String[] ips = ip.split(",");
                for (int index = 0; index < ips.length; index++) {
                    String strIp = ips[index];
                    if (!("unknown".equalsIgnoreCase(strIp))) {
                        ip = strIp;
                        break;
                    }
                }
            }
            return ip;
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }
}
