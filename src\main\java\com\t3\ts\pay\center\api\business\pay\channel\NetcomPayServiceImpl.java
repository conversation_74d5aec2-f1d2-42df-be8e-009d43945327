package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.NetComPayDto;
import com.t3.ts.pay.remote.service.NetComService;
import com.t3.ts.result.Response;
import java.util.Objects;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("netcomPayServiceImpl")
public class NetcomPayServiceImpl implements PayChannelService {

    @DubboReference
    private NetComService netComService;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.NETCOM.getCode());

        NetComPayDto dto = new NetComPayDto();
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        dto.setOrderNo(paymentResp.getMsg());
        dto.setUserId(paymentInfoBo.getPassengerUuid());
        //免密支付
        if (paymentInfoBo.isNoSecret()) {
            // 调用免密支付接口
            Response response = netComService.noPwdPay(dto);
            rechargePayVo.setSdk(response.getMsg());
            if (!response.isSuccess()) {
                rechargePayVo.setCode(NumConstants.STR_500);
                rechargePayVo.setSdk("一网通免密支付失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        } else {
            // 一般支付
            Response h5PayInfo = netComService.getH5PayInfo(dto);
            rechargePayVo.setSdk(String.valueOf(h5PayInfo.getData()));
            if (!h5PayInfo.isSuccess() || Objects.isNull(h5PayInfo.getData())) {
                rechargePayVo.setSdk("获取一网通支付串失败");
                rechargePayVo.setCode(NumConstants.STR_500);
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        }
        return Response.createSuccess(rechargePayVo);
    }
}
