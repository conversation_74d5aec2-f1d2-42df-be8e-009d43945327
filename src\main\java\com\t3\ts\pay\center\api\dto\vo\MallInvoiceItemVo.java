package com.t3.ts.pay.center.api.dto.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.math.BigDecimal;
/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * <AUTHOR>
 * @Date:  2021/4/25 18:04
 */
@Getter
@Setter
public class MallInvoiceItemVo implements Serializable {

    private static final long serialVersionUID = -5852720360681556589L;
    /**
     * 订单ID
     */
    private String orderNo;
    /**
     * 城市CODE
     */
    private String cityCode;
    /**
     * 城市名
     */
    private String cityName;
    /**
     * 业务类型 0积分商城 7 商城礼品卡
     */
    private Integer bizType;

    /**
     * 时间戳（毫秒）
     */
    private Long createTime;

    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 商品数量
     */
    private Integer quantity;
    /**
     * 商品名称
     */
    private String name;

    private Integer invoiceClass;

    private Integer invoiceSubjectCode;
}
