package com.t3.ts.pay.center.api.dto.chartered;

import com.t3.ts.pay.center.api.constants.PayCenterMethodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <b>
 * PayDeskReqDTO
 * 2020/6/4
 * 包车收银台入参dto
 * </b>
 *
 * <AUTHOR>
 */
@ApiModel("企业个人收银台")
@Getter
@Setter
public class PayDeskReqDTO implements Serializable {
    private static final long serialVersionUID = 1807357747159732227L;

    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称", required = true)
    private String method;

    /**
     * 参数形式
     */
    @ApiModelProperty("参数形式")
    private String format;

    /**
     * 编码类型
     */
    @ApiModelProperty("编码类型")
    private String charset;

    /**
     * 业务参数
     */
    @ApiModelProperty(value = "业务参数", required = true)
    private PayDeskContentDTO content;

    /**
     * @param
     * @return
     * @author: qul
     * @Description: 构造函数
     * @CreateDate:
     */
    public PayDeskReqDTO() {
        this.method = PayCenterMethodEnum.PAY_CENTER_METHOD_PAY_DESK.getMethod();
        this.format = "JSON";
        this.charset = "UTF-8";
    }

    /**
     * 动态赋值接口名称
     *
     * @param payCenterMethodEnum 支付结算pay-center-api接口枚举
     */
    public PayDeskReqDTO(PayCenterMethodEnum payCenterMethodEnum) {
        this.method = payCenterMethodEnum.getMethod();
        this.format = "JSON";
        this.charset = "UTF-8";
    }

}
