package com.t3.ts.pay.center.api.util;

import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:03
 */
public class RSAUtilsTest {

    @Test
    public void testEncryptByPublicKey() throws Exception {
        byte[] result = RSAUtils.encryptByPublicKey(new byte[] {(byte) 0}, "publicKey");
        Assert.assertArrayEquals(new byte[] {(byte) 0}, result);
    }

    @Test
    public void testDecrypt() throws Exception {
        String result = RSAUtils.decrypt(new byte[] {(byte) 0}, null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testDecrypt2() throws Exception {
        String result = RSAUtils.decrypt("data", "base64PrivateKey");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
