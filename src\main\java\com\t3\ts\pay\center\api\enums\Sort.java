package com.t3.ts.pay.center.api.enums;

import lombok.Data;

import java.io.Serializable;

/**
 * Author:   liuss
 * Date:     2020/7/16 17:47
 * Description: 排序字段
 */
@Data
public class Sort implements Serializable {
    private static final long serialVersionUID = 5985893461753525048L;

    /**
     * 排序字段
     */
    private String sortName;
    /**
     * 排序方式
     */
    private String sortRule;

    public Sort(String sortName, String sortRule) {
        this.sortName = sortName;
        this.sortRule = sortRule;
    }

}
