package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.dto.UnifiedDto;
import com.t3.ts.account.center.service.AccountUnifiedService;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.pay.center.api.dto.SettlementGeneralApiDto;
import com.t3.ts.pay.center.api.util.Constants;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/8/20/0020 11:24
 */
@RestController
@Slf4j
@RequestMapping("/pay/api/v1/account")
public class GiveController extends BaseApi {

    @DubboReference
    private AccountUnifiedService accountUnifiedService;

    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    /**
     * 失效打车金查询
     *
     * @param queryDto 查询dto
     * @param request  请求
     * @return {@link Response<JSONObject>}
     */
    @PostMapping("/expireGiveDetail")
    @ApiOperation(value = "失效打车金查询", notes = "失效打车金查询")
    public Response<JSONObject> expireGiveDetail(@RequestBody QueryDto queryDto, HttpServletRequest request) {
        Map<String, Object> param = new HashMap<>(Constants.N_3);
        param.put("currentPage", queryDto.getCurrentPage());
        param.put("pageSize", queryDto.getPageSize());
        param.put("userId", getUserUid(request));
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.account.expire.give.detail.query");
        dto.setExtendParam(JSON.toJSONString(param));
        return accountUnifiedService.handle(dto);
    }

    /**
     * 打车金明细查询
     *
     * @param queryDto 查询dto
     * @param request  请求
     * @return {@link Response<JSONObject>}
     */
    @PostMapping("/giveDetail")
    @ApiOperation(value = "打车金明细查询", notes = "打车金明细查询")
    public Response<JSONObject> giveDetail(@RequestBody QueryDto queryDto, HttpServletRequest request) {
        Map<String, Object> param = new HashMap<>(Constants.N_3);
        param.put("currentPage", queryDto.getCurrentPage());
        param.put("pageSize", queryDto.getPageSize());
        param.put("userId", getUserUid(request));
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.account.give.detail.query");
        dto.setExtendParam(JSON.toJSONString(param));
        return accountUnifiedService.handle(dto);
    }

    /**
     * 选择结算
     * 查询通用结算单-根据结算单ID订单ID
     *
     * @param dto dto
     * @return {@link Response< List < SettlementGeneralDto >>}
     */
    @PostMapping("/general/selectsettlement")
    @ApiOperation(value = "查询通用结算单-根据结算单ID订单ID", notes = "查询通用结算单-根据结算单ID订单ID")
    public Response<List<SettlementGeneralDto>> selectSettlement(@RequestBody SettlementGeneralApiDto dto,
                                                                 HttpServletRequest request) {
        log.info("SettlementGeneralController.selectSettlement.dto={}", JSON.toJSONString(dto));
        try {
            SettlementGeneralDto srOrderDto = BeanUtils.propertiesCopy(dto, SettlementGeneralDto.class);
            Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(srOrderDto);
            log.info("SettlementGeneralController.selectSettlement.dto={},response={}", JSON.toJSONString(dto),
                    JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.info("SettlementGeneralController.selectSettlement.dto={}", JSON.toJSONString(dto), e);
            return Response.createError("查询通用结算单-根据结算单ID订单ID-失败-异常");
        }
    }
}
