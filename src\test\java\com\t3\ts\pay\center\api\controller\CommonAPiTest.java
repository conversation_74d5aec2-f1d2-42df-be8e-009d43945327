package com.t3.ts.pay.center.api.controller;

import com.t3.ts.channelmgr.center.dto.PayChannelReqDto;
import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.common.CommonBusiness;
import com.t3.ts.result.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class CommonAPiTest {

    @Resource
    private CommonBusiness commonBusiness;

    @Test
    public void channelListTest(){
        Response response = commonBusiness.channelList(new PayChannelReqDto());
        System.out.println(response.getMsg());
    }
}
