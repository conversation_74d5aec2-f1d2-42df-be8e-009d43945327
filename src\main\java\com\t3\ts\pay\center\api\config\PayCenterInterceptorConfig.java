package com.t3.ts.pay.center.api.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PayCenterInterceptorConfig {

    @Value("${interceptor.enabled:false}")
    private String interceptorEnabled;

    public boolean isInterceptorEnabled() {
        return StrUtil.equals(interceptorEnabled, "true");
    }

}
