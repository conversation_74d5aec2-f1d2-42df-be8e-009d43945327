package com.t3.ts.pay.center.api.rest.route.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ligq
 *
 * 行程信息接口出参
 */
@Data
public class RouteStateDTO implements Serializable {
    private static final long serialVersionUID = -8375008505252866790L;
    /**
     * 行程状态 7：待评价 8 行程结束  9 行程取消 10 关闭
     */
    private Integer status;

    /**
     *  行程需求的创建时间
     */
    private Long requireCreateTime;

    /**
     * 司机接单时间
     */
    private Date driReceiveTime;
}
