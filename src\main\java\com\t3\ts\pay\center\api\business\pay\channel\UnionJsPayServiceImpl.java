package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import org.springframework.stereotype.Service;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Service("unionJsPayServiceImpl")
public class UnionJsPayServiceImpl implements PayChannelService {
    /**
     * 获取充值支付信息
     *
     * @param paymentInfoBo 入参
     * @return 出参
     */
    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        try {
            RechargePayBo rechargePayVo = new RechargePayBo();
            rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
            rechargePayVo.setPayType(EnumPayOrderChannel.UNION_JS_PAY.getCode());

            Response paymentResp = paymentInfoBo.getPaymentResp();
            if (paymentResp == null) {
                rechargePayVo.setCode(NumConstants.STR_500);
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
            final String payUrl = String.valueOf(paymentResp.getData());
            rechargePayVo.setSdk(URLDecoder.decode(payUrl, StandardCharsets.UTF_8.toString()));
            return Response.createSuccess(rechargePayVo);
        } catch (Exception e) {
            return Response.createError();
        }
    }
}
