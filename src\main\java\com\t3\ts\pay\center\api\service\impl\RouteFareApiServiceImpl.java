package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.vo.RouteFareDetailVo;
import com.t3.ts.pay.center.api.service.RouteFareApiService;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/7/21 21:15
 * @description:
 */
@Slf4j
@Service
public class RouteFareApiServiceImpl implements RouteFareApiService {

    @Override
    public Response<RouteFareDetailVo> getOrderFareItems(RouteFareItemReq req) {
        return Response.createError("该接口未被调用，废弃");
    }
}
