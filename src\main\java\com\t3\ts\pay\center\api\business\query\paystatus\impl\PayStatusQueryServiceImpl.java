package com.t3.ts.pay.center.api.business.query.paystatus.impl;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.business.query.paystatus.handler.PayStatusQueryHandlerFactory;
import com.t3.ts.pay.center.api.business.query.paystatus.PayStatusQueryService;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.business.query.paystatus.handler.PayStatusHandler;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 支付服务
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:04
 */
@Component
@Slf4j
public class PayStatusQueryServiceImpl implements PayStatusQueryService {

    @Autowired
    private PayStatusQueryHandlerFactory factory;

    /**
     * 查询
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    @Override
    public Response<JSONObject> doQuery(QueryStatusContext context) {
        try {
            return process(context);
        } catch (Exception e) {
            log.error("PayStatusQueryServiceImpl.doQuery error : ", e);
            return Response.createError(e.getMessage());
        }

    }

    /**
     * 处理
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    @SuppressWarnings("unchecked")
    private Response<JSONObject> process(QueryStatusContext context) {

        String payType = context.getType();

        PayStatusHandler handler = factory.getHandler(payType);

        if (null == handler) {
            return Response.createError("非法的提交类型: " + payType);
        }
        return handler.handler(context);
    }
}
