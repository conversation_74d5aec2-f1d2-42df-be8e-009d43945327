package com.t3.ts.pay.center.api.service.invoice;

import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;

/**
 * @Author: ivy
 * @Date: 2021/10/11 19:18
 * @Description: 商城开票
 */
public interface MallInvoiceService {

    /**
     * queryMallBilling
     *
     * @param req           请求类
     * @param passengerUuid 乘客id
     * @return {@link PageResult<MallInvoiceVo>}
     */
    Response<PageResult<MallInvoiceVo>> queryMallBilling(InvoiceRoutReq req, String passengerUuid);

    /**
     * submitMallBill
     *
     * @param req           请求类
     * @param passengerUuid 乘客id
     * @return {@link PageResult<MallInvoiceVo>}
     */
    Response<?> submitMallBill(InvoiceMallSubmitReq req, String passengerUuid);
}
