package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/8/24/0024 15:35
 */

@Data
public class QueryBookDetailDto implements Serializable {
    /**
     * The constant serialVersionUID.
     */
    private static final long serialVersionUID = 1220181165534058120L;
    /**
     * 当前页
     */
    private Integer currentPage;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 账户类型
     * {@link com.t3.ts.account.center.constants.BookTypeEnum}
     */
    private Integer accountType;
    /**
     * 支入支出类型  1.收入 2.支出 3.全部
     */
    private String changedType;
    /**
     * 业务类型 142：充值-本金 159：充值-赠金
     */
    private Integer flowType;

    private Integer bookType;
}
