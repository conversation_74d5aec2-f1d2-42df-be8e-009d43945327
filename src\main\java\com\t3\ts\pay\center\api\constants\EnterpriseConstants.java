package com.t3.ts.pay.center.api.constants;

/**
 * <AUTHOR>
 * @date 2019.11.4
 */
public final class EnterpriseConstants {

    private EnterpriseConstants() {

    }

    /**
     * 乘客端系统ID
     */
    public static final String SYSTEM_ID = "9";

    /**
     * 行程主状态--未接到车
     */
    public static final Integer MAIN_NOCAR_ROUTE_STATUS = 10;

    /**
     * 行程子状态--未叫到车, 可以再试一次
     */
    public static final Integer SUB_NOCAR_ROUTE_CAN_RECALL = 101;

    /**
     * 行程子状态--未叫到车, 不能再试一次
     */
    public static final Integer SUB_NOCAR_ROUTE_NOTCAN_RECALL = 102;

    /**
     * traceId名称常量
     */
    public static final String LOG_TRACE_ID = "traceId";

    /**
     * 有待支付订单
     */
    public static final Integer WAIT_FOR_PAY_ORDER = 9001;

    /**
     * @
     */
    public static final String MAIL_AT = "@";

    /**
     * ?
     */
    public static final String QUESTION_MARK = "?";

    public static final Integer MINUS_ONE = -1;
    public static final Integer MINUS_SIX = -6;
    public static final Integer INTEGER_ZERO = 0;
    /**
     * 2
     */
    public static final Integer INTEGER_TWO = 2;
    public static final Integer INTEGER_THREE = 3;
    /**
     * 4
     */
    public static final Integer INTEGER_FOUR = 4;
    /**
     * 5
     */
    public static final Integer INTEGER_FIVE = 5;
    public static final Integer INTEGER_SIX = 6;
    public static final Integer SEVEN = 7;
    public static final Integer EIGHT = 8;
    /**
     * 9
     */
    public static final Integer INTEGER_NINE = 9;
    /**
     * 10
     */
    public static final Integer INTEGER_TEN = 10;
    public static final Integer TWELVE = 12;
    public static final Integer SIXTEEN = 16;
    public static final Integer NINETEEN = 19;
    public static final int TWENTY_THREE = 23;
    /**
     * 24
     */
    public static final int INTEGER_TWENTY_FOUR = 24;

    /**
     * 30
     */
    public static final Integer INTEGER_THIRTY = 30;
    public static final Integer THIRTY_FIVE = 35;
    public static final Integer FIFTY = 50;
    public static final Integer FIFTY_NINE = 59;
    public static final int INTEGER_SIXTY = 60;
    public static final int INTEGER_SIXTY_FOUR = 64;

    public static final int NINETY_NINE = 99;
    public static final int INTEGER_ONE_HUNDRED = 100;
    /**
     * 1000
     */
    public static final int INTEGER_ONE_THOUSAND = 1000;
    public static final int TWO_THOUSAND = 2000;
    public static final int INTEGER_3600 = 3600;
    /**
     * 100000
     */
    public static final Integer INTEGER_100000 = 100000;
    /**
     * 一天的秒数
     */
    public static final int SECONDS_PER_DAY = 86400;
    /**
     * 企业用车额度不足，需要个人支付剩余费用
     */
    public static final Integer NEED_PERSONAL_PAY = 105004;
    /**
     * 企业用车额度不足，需要扣除账户余额
     */
    public static final Integer NEED_WALLET_PAY = 105007;
    public static final Integer CODE_105011 = 105011;
    /**
     * 1500秒
     */
    public static final Integer SECONDS_1500 = 1500;

    public static final Integer CODE_5009 = 5009;

    public static final Integer CODE_100004 = 100004;
    public static final Integer CODE_108001 = 108001;
    public static final Integer CODE_128 = 128;
    public static final Integer CODE_201 = 201;
    public static final Integer CODE_202 = 202;
    public static final Integer CODE_980 = 980;
    public static final Integer CODE_999 = 999;
    public static final Integer CODE_9999 = 9999;

    /**
     * 13
     */
    public static final String NUM_STR_13 = "13";

    public static final String NOT_PAY_STATE = "订单为非待支付状态";

    public static final String NON_REQUSITION_ID = "申请单ID不能为空";
}
