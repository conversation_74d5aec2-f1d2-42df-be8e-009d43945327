package com.t3.ts.pay.center.api.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.*;

/**
 * @Author: YJ
 * @Date: 2019/4/26 9:09
 * @Version: v1
 * @Description: 充值入参
 */
@Data
public class RechargeReq implements Serializable {

    /**
     * 充值支付方式
     */
    private Integer rechargePayType;

    /**
     * 充值项uuid
     */
    private String rewardUuid;

    /**
     * 充值活动uuid
     */
    private String activityUuid;

    /**
     * 充值金额
     */
    private Double rechargeAmount;

    /**
     * 行程Id
     */
    private String routePlanUuid;
    /**
     * 订单号，也是行程Id，适配小程序没有统一
     */
    private String orderUuid;

    /**
     * 预付款流水号
     */
    private String prePayNo;

    /**
     * 预付款类型 1：企业
     */
    private String enterprise;
    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;
    /**
     * 1：顺风车
     */
    private Integer bizLineFlag;
    /**
     * 支付并签约标识
     */
    @ApiModelProperty(value = "支付并签约标识", required = false)
    private String payAndSign;
    /**
     * 微信免密签约 记录用户是否点击滑块取消 0-取消 1-选中 null-不记录
     */
    private Integer selWxSign;
    /**
     * 为true时 表示从收银台发起的支付为免密支付，默认false
     */
    private Boolean noSecret = false;
    /**
     * 微信小程序code-微信小程序支付
     */
    private String code;
    /**
     * 来源
     */
    private int source = 0;
    /**
     * 支付宝 h5 回调地址
     */
    private String quit_url;

    /**
     * 用户判断用户从哪个云闪付地域进入的(目前 有 上海和天津 用于获取商户配置)
     */
    private String subSource;

    /**
     * 用户ip
     */
    private String userIp;
}
