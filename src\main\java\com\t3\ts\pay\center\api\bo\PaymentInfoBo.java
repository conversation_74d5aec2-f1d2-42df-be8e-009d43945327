package com.t3.ts.pay.center.api.bo;

import com.t3.ts.result.Response;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-13 15:25
 * @des: 支付信息业务对象
 */
@Data
@Builder
@AllArgsConstructor
public class PaymentInfoBo {

    public PaymentInfoBo() {
    }

    public PaymentInfoBo(boolean noSecret, Response paymentResp, String passengerUuid, String passengerMobile) {
        this.noSecret = noSecret;
        this.paymentResp = paymentResp;
        this.passengerUuid = passengerUuid;
        this.passengerMobile = passengerMobile;
    }

    public PaymentInfoBo(boolean noSecret, Response paymentResp, String passengerUuid, String passengerMobile, String outIp) {
        this.noSecret = noSecret;
        this.paymentResp = paymentResp;
        this.passengerUuid = passengerUuid;
        this.passengerMobile = passengerMobile;
        this.outIp = outIp;
    }

//    public PaymentInfoBo(boolean noSecret, Response paymentResp, String passengerUuid, String passengerMobile,
//                         List<Integer> aggPayTypeList) {
//        this.noSecret = noSecret;
//        this.paymentResp = paymentResp;
//        this.passengerUuid = passengerUuid;
//        this.passengerMobile = passengerMobile;
//        this.aggPayTypeList = aggPayTypeList;
//    }

    private Boolean taxiScanPay = false;

    /**
     * 是否免密支付
     */
    private boolean noSecret;

    /**
     * 统一支付返回的支付信息
     */
    private Response paymentResp;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    private String passengerMobile;
//    /**
//     * 支付类型 支持聚合支付渠道
//     */
//    private List<Integer> aggPayTypeList;
    /**
     * 支付并签约标识
     */
    private String payAndSign;

    /**
     * 渠道来源 101 -我的南京
     */
    private int source;
    /**
     * 是否纯电动车：1-是，0-否（发放蚂蚁森林能量）
     *
     */
    private Integer isElectricCar;
    /**
     * 车牌号（发放蚂蚁森林能量）
     */
    private String routePlanUuid;
    /**
     * 车牌号（发放蚂蚁森林能量）
     */
    private String plateNum;
    /**
     * 外网IP
     */
    private String outIp;
}
