package com.t3.ts.pay.center.api.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.interactive.exception.ExceptionUtils;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.result.Response;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 路线装客户端
 *
 * <AUTHOR>
 * @date 2020/10/10
 */
@Component
@Slf4j
public class PassengerFeignClient extends BaseRest {

    @Resource
    private SlbConfig slbConfig;

    /**
     * @param userId 获取用户手机号
     * @return {@link String}
     */
    public String getPassengerMobile(String userId) {
        try {
            JSONObject param = new JSONObject();
            param.put("uuid", userId);
            String url = slbConfig.getPassenger() + "/passenger/getPassengerById";
            String response = sendPostHttp(url, JSONObject.toJSONString(param));
            Response resp = JSONObject.parseObject(response, Response.class);
            if (null != response && resp.isSuccess() && null != resp.getData()) {
                JSONObject jsonObject = JSON.parseObject(resp.getData().toString());
                return jsonObject.getString("mobile");
            }
        } catch (Exception e) {
            log.error("查询乘客手机号异常:{}.", ExceptionUtils.getFullStackTrace(e));
        }
        return null;
    }

    /**
     * 保存用户扩展信息
     *
     * @param param param
     * @return boolean
     */
    public boolean saveUserExtend(JSONObject param) {
        try {
            String url = slbConfig.getCuaUserCore() + "/user/extend/add";
            String response = sendPostHttp(url, JSONObject.toJSONString(param));
            Response resp = JSONObject.parseObject(response, Response.class);
            return null != resp && resp.isSuccess();
        } catch (Exception e) {
            log.error("getUserExtend error", e);
        }
        return false;
    }

    /**
     * 获取用户扩展信息
     *
     * @param param param
     * @return boolean
     */
    public JSONObject getUserExtend(JSONObject param) {
        String url = slbConfig.getCuaUserCore() + "/user/extend/query";
        try {
            String response = sendPostHttp(url, JSONObject.toJSONString(param));
            Response resp = JSONObject.parseObject(response, Response.class);
            if (null != response && resp.isSuccess() && null != resp.getData()) {
                return JSON.parseObject(resp.getData().toString());
            }
        } catch (Exception e) {
            log.error("getUserExtend error", e);
        }
        return null;
    }
}
