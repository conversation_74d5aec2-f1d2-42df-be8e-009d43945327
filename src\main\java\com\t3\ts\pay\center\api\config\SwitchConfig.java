package com.t3.ts.pay.center.api.config;

import com.t3.ts.utils.StringUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date: 2019-10-22 09:37
 * @des: 平台开关配置信息
 */
@Component
@Data
public class SwitchConfig {
    /**
     * 是否应用开关
     */
    @Value("${platform.switch.diffUserSwitch:true}")
    private Boolean diffUserSwitch;

    /**
     * 出行、企业用车预付款流水号对应用户id与支付不一致时是否使用外部用户id替换当前用户id
     */
    @Value("${platform.switch.diffUserReplace:false}")
    private Boolean diffUserReplace;

    /**
     * 是否开启立即支付 FALSE--非立即支付（默认值） TRUE--立即支付
     */
    @Value("${platform.switch.isPayInstant:false}")
    private Boolean isPayInstant;

    /**
     * 本地模式
     */
    @Value("${platform.switch.localModel:false}")
    private Boolean localModel;

    /**
     * app是否展示校验三方支付方式必选提示-降级开关
     */
    @Value("${platform.switch.thirdPayTypeToast:true}")
    private Boolean thirdPayTypeToast;

    /**
     * app是否展示校验三方支付方式必选提示信息
     */
    @Value("${platform.switch.thirdPayTypeToastMsg:'需勾选第三方进行支付'}")
    private String thirdPayTypeToastMsg;

    /**
     * 是否支持招行聚合支付
     */
    @Value("${platform.switch.supportCmbAgg:false}")
    private Boolean supportCmbAgg;

    @Value("${platform.switch.cmbAggWhiteList:[]}")
    private List<String> cmbAggWhiteList;

    @Value("${platform.switch.cmbAggBlackList:[]}")
    private List<String> cmbAggBlackList;

    @Value("${platform.switch.supportCmbAggAli:false}")
    private Boolean supportCmbAggAli;

    @Value("${platform.switch.cmbAggAliWhiteList:[]}")
    private List<String> cmbAggAliWhiteList;

    @Value("${platform.switch.cmbAggAliBlackList:[]}")
    private List<String> cmbAggAliBlackList;

    @Value("${platform.switch.cmbAggErrorCode:'PTNJN35'}")
    private String cmbAggErrorCode;

    @Value("${platform.switch.cmbAggNewPayUrl:'pages/newPay/index?'}")
    private String cmbAggNewPayUrl;

    @Value("${platform.switch.cmbAggPercent:-1}")
    private Integer cmbAggPercent;

    @Value("${platform.switch.cmbAggAliPercent:-1}")
    private Integer cmbAggAliPercent;


    /**
     * 是否是招行聚合非生产环境
     */
    @Value("${platform.switch.cmbAggEnvUat:true}")
    private Boolean cmbAggEnvUat;

    @Value("${platform.switch.quickPassBlackList:[]}")
    private List<String> quickPassBlackList;

    @Value("${platform.switch.quickPassWhiteList:[]}")
    private List<String> quickPassWhiteList;

    /**
     * >=0 <=1024
     */
    @Value("${platform.switch.quickPassPercent:-1}")
    private Integer quickPassPercent;

    /**
     * 打车金是否启用开关
     */
    @Value("${platform.switch.giveCanUse:false}")
    private Boolean giveCanUse;

    /**
     * 发票主体查询开关
     */
    @Value("${platform.switch.invoiceHeaderFlag:true}")
    private Boolean invoiceHeaderFlag;

    /**
     * 可开票列表查询次数
     */
    @Value("${platform.switch.billingCount:4}")
    private Integer billingCount;

    /**
     * 现金账户充值上限 单位分
     */
    @Value("${platform.switch.cashRechargeLimit:800000}")
    private Integer cashRechargeLimit;

    /**
     * 感谢费替换为充值功能的开关
     */
    @Value("${platform.switch.cashSwitch:false}")
    private Boolean cashSwitch;


    /**
     * 用资产接口查询车辆信息
     */
    @Value("${platform.switch.qryCar:true}")
    private Boolean qryCar;

    /**
     * 客户端连接超时时间
     */
    @Value(value = "${platform.switch.okClient.connectTimeout:2000}")
    private Integer okClientConnectTimeout;

    /**
     * 客户端写超时时间
     */
    @Value(value = "${platform.switch.okClient.writeTimeout:500}")
    private Integer okClientWriteTimeout;


    /**
     * 客户端读超时时间
     */
    @Value(value = "${platform.switch.okClient.readTimeout:500}")
    private Integer okClientReadTimeout;

    /**
     * 账户T币不足提示
     */
    @Value(value = "${integral.msg.t1:T币(共%s个,满%s个可用)}")
    private String integralMsgT1;

    /**
     * 订单金额不足提示
     */
    @Value(value = "${integral.msg.t2:T币(共%s个,待支付行程费满%s元可用)}")
    private String integralMsgT2;

    /**
     * 可抵扣提示
     */
    @Value(value = "${integral.msg.t3:T币(共%s个,本单可用<font color='#FF8533'>%s</font>个,"
            + "抵<font color='#FF8533'>%s</font>元)}")
    private String integralMsgT3;

    @Value(value = "${integral.msg.t4:T币(共%s个,本单可用<font color='#FF8533'>%s</font>个)}")
    private String integralMsgT4;

    /**
     * 支持聚合支付的系统
     */
    @Value(value = "${platform.switch.supportCmbAggAliSys:P_i}")
    private List<String> supportCmbAggAliSys;


    @Value("${platform.switch.cmbAggT3MiniWhiteList:[]}")
    private List<String> cmbAggT3MiniWhiteList;

    @Value("${platform.switch.cmbAggT3MiniBlackList:[]}")
    private List<String> cmbAggT3MiniBlackList;

    @Value("${platform.switch.cmbAggT3MiniPercent:-1}")
    private Integer cmbAggT3MiniPercent;

    @Value("${platform.switch.cmbAggT3MiniAppId:gh_9ae6f3fe322c}")
    private String cmbAggT3MiniAppId;

    @Value("${platform.switch.driverAppId:wx5c3aab6a8c04ab62}")
    private String driverWxAppId;

    /**
     * 企业用车敏感订单标签
     * 618:待确认企业敏感订单
     * 619:待确认企业确认费用订单
     */
    @Value("${dispute.labels:618,619}")
    private String disputeLabels;

    /**
     * 企业预估敏感订单
     * 654:高速费不一致
     * 655:实际预估不一致
     * 657:异议订单(新)
     */
    @Value("${dispute.company.sensitive:654,655,657}")
    private String companySensitiveLabels;

    @Value("${dispute.openFlag:true}")
    private Boolean disputeFlag;
    /**
     * 发票发送邮箱次数限制
     */
    @Value("${bill.invoice.sendEmailLimit:10}")
    private int sendEmailLimit;

    /**
     * 芝麻分展示开关
     */
    @Value("${platform.switch.zhima.show:true}")
    private Boolean zhimaShow;

    /**
     * 芝麻分-商场 展示开关
     */
    @Value("${platform.switch.zhima.show.mall:false}")
    private Boolean zhimaMallShow;

    /**
     * T 币抵扣开关
     */
    @Value("${platform.switch.integral:false}")
    private Boolean integral;

    /**
     * 斗金广告位开关 （0：开；1：关闭）
     */
    @Value("${platform.switch.recommendPassenger:0}")
    private String recommendPassengerFlag;

    /**
     * 支持开票的来源
     */
    @Value(value = "${platform.switch.source.invoice:1,2,3,4,5,6,8,14,19,89,90,100}")
    private List<Integer> invoiceSource;

    @Value("${platform.wallet.drawCashTip:您的账户余额每天仅可发起%s次提现，每周累计提现总额不得超过2万元}")
    private String drawCashTip;

    /**
     * 单天提现次数限制
     */
    @Value("${platform.wallet.drawCashCntLimit:1}")
    private int drawCashCntLimit;

    @Value("${platform.wallet.prepareTip:包含近7天的所有收入，当天的收入7天后可以提现}")
    private String prepareTip;

    /**
     * 三方运力
     */
    @Value(value = "${platform.switch.thirdTransport:-99}")
    private List<Integer> thirdTransport;

    /**
     * APP专属券判断开关（默认开启）
     */
    @Value("${platform.switch.appExclusive:true}")
    private boolean appExclusive;

    @Value("${platform.payDesk.preMsg:提前预付车费，车主接单更积极}")
    private String preMsg;

    @Value("${platform.payDesk.paySucessMsg:已开通免密扣款}")
    private String paySucessMsg;

    /**
     * 司机端是否支持招行聚合支付
     */
    @Value("${platform.switch.driverSupportCmbAgg:false}")
    private Boolean driverSupportCmbAgg;

    @Value("${platform.switch.driverCmbAggBlackList:[]}")
    private List<String> driverCmbAggBlackList;

    @Value("${platform.switch.driverCmbAggWhiteList:[]}")
    private List<String> driverCmbAggWhiteList;

    @Value("${platform.switch.driverCmbAggPercent:-1}")
    private Integer driverCmbAggPercent;

    @Value("${platform.switch.driverSupportCmbAggAli:false}")
    private Boolean driverSupportCmbAggAli;

    @Value("${platform.switch.driverCmbAggAliBlackList:[]}")
    private List<String> driverCmbAggAliBlackList;

    @Value("${platform.switch.driverCmbAggAliWhiteList:[]}")
    private List<String> driverCmbAggAliWhiteList;

    @Value("${platform.switch.driverCmbAggAliPercent:-1}")
    private Integer driverCmbAggAliPercent;

    @Value("${driverWallet.oldIncomeClassify:10000,30020,30050,30010,30040,30060,30000,30070,}")
    private String oldIncomeClassify;

    @Value("${driverWallet.oldPayOutClassify:20000,20020,20030,}")
    private String oldPayOutClassify;

    @Value("${driverWallet.incomeDesc:收入汇总}")
    private String incomeDesc;

    @Value("${driverWallet.payOutDesc:支出汇总}")
    private String payOutDesc;

    @Value("${giftcard.channel.giftCardNotUseChannel:}")
    private List<Integer> giftCardNotUseChannel;
    /**
     * 指定业务不支持渠道
     * 阿波罗配置格式 payOrderTypeNotUseChannelMap: "{'435':'1,46'}"
     */
    @Value("#{${giftcard.channel.payOrderTypeNotUseChannelMap:{}}}")
    private Map<String, String> payOrderTypeNotUseChannelMap;

    @Value("${giftcard.channel.usealiDesc:礼品卡购买暂不支持}")
    private String giftcardChannelUsealiDesc;

    @Value("${driverWallet.conf.bindCardErrLimitMsg:今日失败校验次数过多，请改天再试}")
    private String bindCardErrLimitMsg;

    @Value("${driverWallet.conf.bindCardErrCount:5}")
    private Integer bindCardErrCount;

    @Value("${driverWallet.bank-without:ALIPAY}")
    private String bankWithout;

    @Value("${platform.switch.closeJustHasCancelFareSwitch:false}")
    private Boolean closeJustHasCancelFareSwitch;

    @Value("${platform.wallet.questionDesc1:提现时间：工作日%s-%s（重大节假日除外）;}")
    private String questionDesc1;

    @Value("${platform.wallet.questionDesc2:每天提现最大金额不得高于%s元，当周提现累计金额不得超过%s元;}")
    private String questionDesc2;

    @Value("${platform.wallet.questionDesc3:每天只可以提现%s次。}")
    private String questionDesc3;

    @Value("${taxi.wallet.recommendCodeConfig:{maxAmount:500}}")
    private String recommendCodeConfig;

    /**
     * 企业用车个人预付款开关
     */
    @Value("${platform.switch.companyPrePayFlag:true}")
    private Boolean companyPrePayFlag;
    /**
     * 开关是否打开
     */
    @Value("${netComPayPoints.enabled:true}")
    private Boolean netComEnabled;

    /**
     * 按钮标题
     */
    @Value("${netComPayPoints.btnTitle:开通免密并支付}")
    private String netComBtnTitle;

    /**
     * 按钮副标题
     */
    @Value("${netComPayPoints.btnSubTitle:''}")
    private String netComBtnSubTitle;

    /**
     * 按钮图标url
     */
    @Value("${netComPayPoints.btnIcon:''}")
    private String netComBtnIcon;

    /**
     * 钱包借钱额度开关
     */
    @Value("${platform.quota.switch:true}")
    private boolean quotaSwitch;
    /**
     * 钱包借钱额度兜底，默认20w元
     */
    @Value("${platform.quota.availableAmount:20000000}")
    private Long quotaAvailableAmount;
    /**
     * 充电卡账本描述
     */
    @Value("${driverWallet.pay.chargingElectricityCardBookDesc:充电卡(余额[%s]元)}")
    private String chargingElectricityCardBookDesc;
    /**
     * 充电卡账本，展示的图标
     */
    @Value("${driverWallet.pay.chargingElectricityCardIcon:'https://tx-t3propublic.t3go.cn/node-bff-resource/default/3758716315813211679'}")
    private String chargingElectricityCardIcon;

    /**
     * 司机薪酬账本，余额展示描述
     */
    @Value("${driverWallet.pay.accountSalaryBookDesc:余额([%s]元)}")
    private String accountSalaryBookDesc;

    /**
     * 司机薪酬账本，展示的图标
     */
    @Value("${driverWallet.pay.accountSalaryIcon:https://tx-t3propublic.t3go.cn/node-bff-resource/default/3886539793945111575}")
    private String accountSalaryIcon;
    /**
     * 勾选充电卡时提示
     * 提示1：充电桩不支持充电卡
     */
    @Value("${driverWallet.pay.chargingElectricityCardCanNotUseMsg:该场站不支持使用}")
    private String chargingElectricityCardCanNotUseMsg;
    /**
     * 勾选充电卡时提示
     * 提示2：不支持支付安心保
     */
    @Value("${driverWallet.pay.insureFareCanNotUseMsg:不支持支付安心保}")
    private String insureFareCanNotUseMsg;

    /**
     * 选了充电卡余额，也选了三方支付
     */
    @Value("${driverWallet.pay.thirdPayTypeToastMsgModel1:充电卡抵扣后还需支付%s元}")
    private String thirdPayTypeToastMsgModel1;
    /**
     * 选了充电卡余额，未择了三方支付
     * 账本充电卡不足时候
     */
    @Value("${driverWallet.pay.thirdPayTypeToastMsgModel2:[充电卡抵扣后还需支付%s元，请再选择一种支付方式]}")
    private String thirdPayTypeToastMsgModel2;

    /**
     * 选了钱包余额，未择了三方支付
     * 钱包余额足够时候
     * 钱包抵扣[27]元
     */
    @Value("${driverWallet.pay.thirdPayTypeToastMsgModel3:钱包抵扣[%s]元}")
    private String thirdPayTypeToastMsgModel3;

    /**
     * 选了钱包余额，未择了三方支付
     * 钱包余额不足时。
     */
    @Value("${driverWallet.pay.thirdPayTypeToastMsgModel4:[钱包抵扣后还需支付%s元，请再选择一种支付方式]}")
    private String thirdPayTypeToastMsgModel4;

    /**
     * 选了钱包余额，也选择了三方支付
     * 钱包抵扣[27]元 + 微信支付[10]元
     */
    @Value("${driverWallet.pay.thirdPayTypeToastMsgModel5:钱包抵扣[%s]元 + %s支付[%s]元}")
    private String thirdPayTypeToastMsgModel5;

    /**
     * 是否开启商城余额支付 20250801后开启
     */
    @Value("${driverWallet.pay.openBookPay:false}")
    private boolean openBookPay;


    /**
     * 接口公钥
     * /pay/V2
     */
    @Value("${driverWallet.pay.v2.publicKey:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvqLXQ44Nf/7cwHC/siCc8sYBawTs1KUn/IneFFLMyQN/csl2RaXl880+DsdqBVL0IQvbHS1ZuKLe9922vYiw5ZRrg6lgWbB2DIuNnNcHMXXshkAsUhs00ytEGjdokVrdsW9bNC3ihtY3bonhaNxQmLjb0rfDpLxD/DDcYmnIdtzvlz+BXwAUJAkGcP6nx7KnDQeY67k5v7K1AqCOyxAoG41GPd5y2tq3v187vZ/Z9HitIrHN+qqMxR0Ic4tHt1ljxyl8Qql8eTV5w5fT5A37yeWHpOrJ6JwMV8oZ3AWBmOjsuaETFzGtpOskVorM4GpCBMTPok3cWmW4NSGyh5HPrwIDAQAB}")
    private String payV2PublicKey;
    /**
     * 接口私钥
     * /pay/V2
     */
    @Value("${driverWallet.pay.v2.privateKey:MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+otdDjg1//tzAcL+yIJzyxgFrBOzUpSf8id4UUszJA39yyXZFpeXzzT4Ox2oFUvQhC9sdLVm4ot733ba9iLDllGuDqWBZsHYMi42c1wcxdeyGQCxSGzTTK0QaN2iRWt2xb1s0LeKG1jduieFo3FCYuNvSt8OkvEP8MNxiach23O+XP4FfABQkCQZw/qfHsqcNB5jruTm/srUCoI7LECgbjUY93nLa2re/Xzu9n9n0eK0isc36qozFHQhzi0e3WWPHKXxCqXx5NXnDl9PkDfvJ5Yek6snonAxXyhncBYGY6Oy5oRMXMa2k6yRWiszgakIExM+iTdxaZbg1IbKHkc+vAgMBAAECggEAG6ALY729FRRAtx5fmReCxueMow6Gp1X7kmwEI5vQEz0PhfnNys/8n7TZTlj1cyoPOI64ROUigFAR0YduiM67G7nDTsQHs1eP88dxUQ4T+8Xj7Zx1D7zjjcpSRx5KZ+3r/F9vrwMLNZDrXXhbks+CWm9HY6S/lCq4C16lQRmlNBwVkidHB5LbbQ42+yNQc6wzqZoYU4qUL07jnccr96WbaJL34xSUcWAQAvF0DIPv5tIUi1PgW4L8FmXFX0+CNJm6Y5iIIYnnGxAFyUHspxZdSPLdPBFrW57+noZ/9bkZUaYSvjy2xfuunNwhVjbo5ldEFbo6OzC9444iBRyjp/wBIQKBgQD6Ng2mdZ6RHpdAUrTQknsqcCbTIlAHCFy79oRIFG9FlZXlvfBZdasn14S+Rd0jgv30tvH/r6caw+lbzYTyw3ZWAnq9PLHNz/Ifh+vYwDKlb0DVxsYdKDtEBEBjm21fSmFCLHFBYaurVt7AKN2tAk2/DGJ4WmS3R5bx2hwr25OL8QKBgQDDC+/4UxP1saX1ocxYWpZBLi0/lU7J6iwxeZWA8WdcCRi1fYS27TfvWzZNU1EOMPKOcIk6iZGfF3G7qSeiqd4s0A9j7/9f0PBONAYthTGqvpd7QcgR44vFSxIRkqV53cNPG9oZZJbyvJn1U5i3Pr5lq1zeuRJ+NWQ6zGFYE0U1nwKBgQDBBvhK5cx5ig8HeYayrX7FQ5ZAJeV1N3dLLqfob5NULE1i2sBUU5nB99sZaw5p+gFvS7+wEFOrK16ZCxk4q6BcRDdqTCujanwQ5SwGoGeyBv0OrvVhwwrNszX1Yg/jhp83jM+mr/HfEfpwZlZ1xSQzcht5W8r9NgZ1+FzpXNEcEQKBgHvof4CANrPlMYtESG7wzXeHAFPeZHk9fOnJVcLemU0MslfJ75/OP7Sde3fQMDQVwGHg+Czp7ErnN+eB5J4fz/ckDkLs2o1iTOdBgZM1hIH2JoG1HobOyaKCuG3Vfa5XxZXiGcH79zEpw5zoiKIRCB/Kp2sOo8Lt5Ei+QjETvOn9AoGAMEBDfNTNmvf39OGc0IR1vAZ1Bm8+JJ6LrHSEL4fLPY1dJN9JDPq6Wh0sjfDZ0JALjeEvkOYAS0K0RtRQbUe6uhWZ4xNamBKJ96v0WCMUq7SwEZx/PRg74UVpj7g8Yo0ESEcaLWjpNn/Ep/j/cN0K6O5WIVeJpwpCQPW9PNTjBKo=}")
    private String payV2PrivateKey;

    /**
     * 签名版本（商城响应签名版本）
     */
    @Value("${driverWallet.pay.v2.version:v2}")
    private String payV2Version;


    /**
     * 签名版本（商城响应签名版本）
     */
    @Value("${driverWallet.pay.v2.isEncrypt:true}")
    private boolean isEncrypt;

    public boolean isPayOrderTypeNotUseChannel(Integer bizType, List<Integer> payChannels) {
        if (bizType == null || CollectionUtils.isEmpty(payChannels)  || this.payOrderTypeNotUseChannelMap == null) {
            return false;
        }
        String channelStr = this.payOrderTypeNotUseChannelMap.get(bizType+"");
        if (StringUtils.isBlank(channelStr)) {
            return false;
        }
        String[] channelArr = channelStr.split(",");
        List<String> channelList = Arrays.asList(channelArr);
        for (Integer payChannel : payChannels) {
            if (channelList.contains(payChannel+"")){
                return true;
            }
        }
        return false;
    }
}
