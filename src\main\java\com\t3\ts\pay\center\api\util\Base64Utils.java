package com.t3.ts.pay.center.api.util;

import cn.hutool.core.codec.Base64;


/**
 * <p>
 * BASE64编码解码工具包
 * </p>
 * <p>
 * 依赖javabase64-1.3.1.jar
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2012-5-19
 */
public class Base64Utils {

    /**
     * <p>
     * BASE64字符串解码为二进制数据
     * </p>
     *
     * @param base64 base64
     * @return byte[]
     */
    public static byte[] decode(String base64) {
        return Base64.decode(base64.getBytes());
    }
}
