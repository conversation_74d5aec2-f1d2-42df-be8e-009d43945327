package com.t3.ts.pay.center.api.business.query.paystatus;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 查询支付状态返回
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 15:27
 */
@Data
public class PayStatusDto implements Serializable {

    private static final long serialVersionUID = -1;

    /**
     * 行程id
     */
    private String routePlanUuid;
    /**
     * 结算ID
     */
    private String settleId;
    /**
     * 支付状态，1：初始化，2：待（支付）结算，3：（支付）结算中，4：（支付）结算成功
     */
    private Integer payStatus;
}
