package com.t3.ts.pay.center.api.service.pay;

import com.t3.ts.pay.center.api.constants.CommonNumConst;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 支付context
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 16:37
 */
@Data
public class PayContext {

    /**
     * 支付类型
     */
    private String type;
    /**
     * 报文信息
     */
    private String message;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 乘客手机号
     */
    private String passengerMobile;
    /**
     * 微信小程序支付时code
     */
    private String wxCode;
    /**
     * 支付宝小程序code
     */
    private String code;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 支付方式列表
     */
    private List<Integer> payChannelList;

    /**
     * 支付聚合支付的渠道
     */
    private List<Integer> aggPayChannelList;
    /**
     * 扩展参数
     */
    private Map<String, Object> extendParam = new HashMap<>(CommonNumConst.NUM_16);
    /**
     * 订单Id
     */
    private String orderId;
    /**
     * 业务类型
     */
    private Integer bizType;

    private String settleId;

    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;
}
