package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.pay.center.api.dto.sign.SignButtonVo;
import com.t3.ts.pay.common.util.JSONFilterUtils;
import com.t3.ts.utils.StringUtils;

import java.util.Objects;

/**
 * @Description:工具类
 * @Author: wuss
 * @Date: 2022/1/13 19:34
 */
public class CommonUtils {
    /**
     * 判断字段内容中是否存在该目标值
     *
     * @param value   目标值
     * @param content 内容以，分割
     * @return boolean
     */
    public static boolean isContentValue(Integer value, String content) {
        if (Objects.nonNull(value) && StringUtils.isNotEmpty(content)) {
            String[] split = content.split(",");
            for (String config : split) {
                if (config.equals(value.toString())) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 判断是否展示 渠道免密签约
     *
     * @param isSign       是否已签约
     * @param signButtonVo 用户签约点击情况
     * @return boolean
     */
    public static boolean isShowSign(boolean isSign, SignButtonVo signButtonVo) {
        // 已签约:不展示
        if (isSign) {
            return false;
        }
        // 未签约+未取消：展示
        if (null == signButtonVo || null == signButtonVo.getSelSign()) {
            return true;
        }
        // 未签约+已取消：不展示
        return NumConstant.NUM_0 != signButtonVo.getSelSign();
    }

    /**
     * 过滤关键字后的字符串
     *
     * @param content 内容
     * @return {@link String}
     */
    public static String filterString(String content) {
        Object o = JSON.parse(content);
        //过滤关键字
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("verifyCode");
        filter.getExcludes().add("cardNo");
        return JSONObject.toJSONString(o, filter);
    }

    /**
     * 获取入参filter
     *
     * @return {@link SimplePropertyPreFilter}
     */
    public static SimplePropertyPreFilter getRequestFilter() {
        return JSONFilterUtils.getFilterByExcludes("verifyCode", "cardNo");
    }
}
