package com.t3.ts.pay.center.api.service;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.dto.trade.PayDeskVoV4;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.center.api.service.pay.PayContext;
import com.t3.ts.result.Response;

/**
 * Description: 支付入口
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:12
 */
public interface PayService {

    /**
     * 进行支付
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    Response<JSONObject> doPay(PayContext context);

    /**
     * 新的收银台
     *
     * @param req 请求参数
     * @return {@link com.t3.ts.result.Response<PayDeskVoV4>}
     */
    Response<PayDeskVoV4> deskInfo(PayTradeReq req);

}
