package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import org.springframework.stereotype.Service;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Service("balanceServiceImpl")
public class BalanceServiceImpl implements PayChannelService {

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
        return Response.createSuccess(rechargePayVo);
    }
}
