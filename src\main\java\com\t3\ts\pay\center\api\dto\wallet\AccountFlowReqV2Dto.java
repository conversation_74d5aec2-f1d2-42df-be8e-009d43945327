package com.t3.ts.pay.center.api.dto.wallet;

import com.t3.ts.finance.center.dto.Page;
import com.t3.ts.finance.center.sumanalyse.dto.PayAccountAnalyseSumV2Vo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 账户流水查询ReqVo
 *
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
public class AccountFlowReqV2Dto extends Page implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 司机ID
     */
    private String userId;

    /**
     * 账户类型,3:t3司机,4:出租车司机
     */
    private Integer accountType;

    /**
     *
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 费用分类
     */
    @ApiModelProperty("费用分类")
    private String classCode;


    /**
     * 业务订单ID
     */
    private String orderUuid;


    private List<PayAccountAnalyseSumV2Vo> analyseSumV2List;


}
