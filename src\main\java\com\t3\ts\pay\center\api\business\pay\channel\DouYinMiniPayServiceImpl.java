package com.t3.ts.pay.center.api.business.pay.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import org.springframework.stereotype.Service;

/**
 * @NAME: DouYinMiniPayServiceImpl
 * @DATE: 2022/12/7
 * @author: chent
 * @desc: 抖音小程序支付
 */
@Service("douYinMiniPayServiceImpl")
public class DouYinMiniPayServiceImpl implements PayChannelService {

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        Response<String> paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        JSONObject jsonObject = JSON.parseObject(paymentResp.getData());
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setOrderNo(jsonObject.getString("order_id"));
        rechargePayVo.setSdk(jsonObject.getString("order_token"));
        rechargePayVo.setPayType(EnumPayOrderChannel.DOUYIN_MINI_PAY.getCode());
        return Response.createSuccess(rechargePayVo);
    }
}
