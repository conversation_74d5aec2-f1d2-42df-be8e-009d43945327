package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.remote.exception.PayExceptionCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 15:55
 */
public class ResponseUtil02Test {
    @Mock
    Logger log;
    @InjectMocks
    ResponseUtil responseUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWriteSuccessResponse() throws Exception {
        ResponseUtil.writeSuccessResponse(null, "result");
    }

    @Test
    public void testWriteFailResponse() throws Exception {
        ResponseUtil.writeFailResponse(null, PayExceptionCode.SYSTEM_ERROR);
    }

    @Test
    public void testWriteFailResponse2() throws Exception {
        ResponseUtil.writeFailResponse(null, Integer.valueOf(0), "errorMessage");
    }

    @Test
    public void testGetSuccessMessage() throws Exception {
        String result = ResponseUtil.getSuccessMessage("data");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testGetFailMessage() throws Exception {
        String result = ResponseUtil.getFailMessage(Integer.valueOf(0), "message");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme