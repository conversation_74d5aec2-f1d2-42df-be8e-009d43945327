package com.t3.ts.pay.center.api.exception;

import com.t3.ts.enums.BaseEnumInterface;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public enum BizExceptionEnum implements BaseEnumInterface {


    //系统异常码
    SYSTEM_ERROR(100001, "系统异常"),
    RPC_ERROR(100002, "RPC调用异常"),
    GET_LOCK_FAIL_ERROR(100003, "获取锁失败异常"),
    UNLOCK_ERROR(100004, "解锁失败"),

    //业务异常码
    PARAM_ERROR(30001, "参数错误"),
    ACCOUNT_STATUS_ERROR(30002, "账户状态异常"),
    PAY_DUPLICATED(30003, "参数错误"),
    PRE_CHANNEL_ERROR(30004, "不支持的预付渠道");
    private Integer code;
    private String desc;

    BizExceptionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return desc;
    }

}
