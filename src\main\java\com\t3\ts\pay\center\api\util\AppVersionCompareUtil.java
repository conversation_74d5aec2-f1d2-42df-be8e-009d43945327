package com.t3.ts.pay.center.api.util;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AppVersionCompareUtil {

    /**
     *  带前缀的版本号（如 "D_a_2.9.0" 或 "D_i_2.9.0" 或 "D_h_2.9.0"）
     */
    public static final String VERSION1 = "D_x_2.9.0";

    /**
     *  带前缀的版本号（如 "D_a_3.11.0" 或 "D_i_3.11.27" 或 "D_h_3.11.27"）
     */
    public static final String VERSION2 = "D_x_3.11.0";



    /**
     * 比较两个带平台前缀的版本号大小
     *
     * @param version1 安卓版本号（如 "D_a_3.11.3"）
     * @param version2 iOS 版本号（如 "D_i_3.11.27"）
     * @return 负数（安卓版本更小）、0（相等）、正数（安卓版本更大）
     */
    public static boolean compare(String version1, String version2) {
        try {

            // 提取核心版本号
            String version1Core = extractCoreVersion(version1);
            String version2Core = extractCoreVersion(version2);

            // 比较核心版本号（按语义化版本规则）
            return compareSemanticVersion(version1Core, version2Core) >= 0;
        } catch (Exception e) {
            log.error("版本号比较错误, version1={}, version2={}", version1, version2, e);
        }
        return false;
    }

    /**
     * 提取核心版本号（去除平台前缀）
     *
     * @param version 带前缀的版本号（如 "D_a_3.11.3" 或 "D_i_3.11.27"）
     * @return 核心版本号（如 "3.11.3"）
     */
    private static String extractCoreVersion(String version) {
        // 按下划线分割，取最后一段（假设前缀格式为 "D_x_"）
        String[] parts = version.split("_");
        if (parts.length < 3) {
            throw new IllegalArgumentException("无效的版本号格式: " + version);
        }
        return parts[2]; // 第三段是核心版本（如 "3.11.3"）
    }


    /**
     * 比较两个语义化版本号（如 "3.11.3" 和 "3.11.27"）
     */
    private static int compareSemanticVersion(String v1, String v2) {
        String[] parts1 = v1.split("\\.");
        String[] parts2 = v2.split("\\.");

        int maxLength = Math.max(parts1.length, parts2.length);
        for (int i = 0; i < maxLength; i++) {
            // 缺失段视为0（如 "3.11" 等价于 "3.11.0"）
            int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
            int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
            if (num1 != num2) {
                return Integer.compare(num1, num2);
            }
        }
        return 0;
    }

    // 测试用例
    public static void main(String[] args) {
        String androidVer = "D_a_3.11.3";
        String iosVer = "D_i_3.11.27";
        String hongMengVer = "D_h_3.11.27";

        boolean result = compare(androidVer, iosVer);

        System.out.println(androidVer + " > " + iosVer +"是"+result); // 输出此结果

    }

}
