package com.t3.ts.pay.center.api.dto.fare;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/7/21 20:51
 * @description:
 */
@Getter
@Setter
public class ModifyDetailBean {
    /**
     * 订单费
     */
    private String orderFare;
    /**
     * 取消费
     */
    private String cancelFare;
    /**
     * 服务费
     */
    private String serviceFare;
    /**
     * 停车费
     */
    private String parkingFare;
    /**
     * 司机减免费
     */
    private String driverReliefFare;
    /**
     * 高速费
     */
    private String highwayFare;
    /**
     * 路桥费
     */
    private String roadBridgeFare;
    /**
     * 其他费
     */
    private String otherFare;
    /**
     * 调整后订单费
     */
    private String adjustOrderFare;
    /**
     * 调整后服务费
     */
    private String adjustServiceFare;
    /**
     * 调整后停车费
     */
    private String adjustParkingFare;
    /**
     * 调整后高速费
     */
    private String adjustHighwayFare;
    /**
     * 调整后路桥费
     */
    private String adjustRoadBridgeFare;
    /**
     * 调整后其他费用
     */
    private String adjustOtherFare;
}
