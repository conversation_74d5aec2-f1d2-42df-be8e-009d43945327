package com.t3.ts.pay.center.api.util;

import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/10/9 19:37
 */
public class AESUtil {
    /**
     * 解密ecb
     *
     * @param data 密文
     * @param key  秘钥
     * @return string
     * @throws Exception 异常
     */
    public static String decryptByECB(String data, String key) throws Exception {
        try {
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(data);
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keyspec);
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString.trim();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 加密ecb
     *
     * @param data 待加密数据
     * @param key  秘钥
     * @return string 密文
     * @throws Exception 异常
     */
    public static String encryptByECB(String data, String key) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encryptedBytes = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }
}
