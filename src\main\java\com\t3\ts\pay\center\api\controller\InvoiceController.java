package com.t3.ts.pay.center.api.controller;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.invoice.center.dto.InvoiceSignDto;
import com.t3.ts.invoice.center.dto.PassengerInvoiceDto;
import com.t3.ts.invoice.center.form.GetSimilarHeaderListForm;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.MyPageResult;
import com.t3.ts.pay.center.api.dto.bill.InvoiceSendReq;
import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.AuthorizationSignQueryDto;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceBillingDetail;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceForm;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceHistoryReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceQueryPageDto;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRelevantFormV3;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutDetailReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceHeaderVO;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceResVo;
import com.t3.ts.pay.center.api.service.invoice.InvoiceHeaderService;
import com.t3.ts.pay.center.api.service.invoice.MallInvoiceService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.regex.Pattern;

/**
 * 迁移 by ivy .2021/09/17 14:04
 *
 * @Author: YJ
 * @Date: 2020/3/9 16:00
 * @Description: 同时呼叫平台开票api
 */
@Api(tags = "同时呼叫平台开票api")
@Slf4j
@RestController
@RequestMapping(value = {"/api/invoice", "/api/driver/invoice"})
public class InvoiceController extends BaseApi {

    private final InvoiceV3Business invoiceBusiness;
    private final InvoiceHeaderService invoiceHeaderService;
    private final MallInvoiceService mallInvoiceService;

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[0][0-9]{2,3}-[0-9]{5,10}$");
    private static final String EMAIL_PATTEN = "^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\"
            + ".[A-Za-z0-9]+$";
    private static final String HEADER_PATTEN = "[a-zA-Z0-9\\u4e00-\\u9fa5\\(\\)\\（\\）]*";

    /**
     * @param invoiceBusiness      构造方法
     * @param invoiceHeaderService 常用抬头
     * @param mallInvoiceService   商城开票
     */
    public InvoiceController(InvoiceV3Business invoiceBusiness,
                             InvoiceHeaderService invoiceHeaderService,
                             MallInvoiceService mallInvoiceService) {
        this.invoiceBusiness = invoiceBusiness;
        this.invoiceHeaderService = invoiceHeaderService;
        this.mallInvoiceService = mallInvoiceService;
    }

    /**
     * 查询商城可开票列表
     *
     * @param req     入参
     * @param request 入参
     * @return Response 分页对象
     */
    @PostMapping("/v1/queryMallBilling")
    @ApiOperation(value = "查询商城可开票列表", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/25 14:00")
    public Response<PageResult<MallInvoiceVo>> queryMallBilling(@RequestBody InvoiceRoutReq req,
                                                                HttpServletRequest request) {
        return mallInvoiceService.queryMallBilling(req, getUserUid(request));
    }

    /**
     * 商城发票开票请求
     *
     * @param req     入参
     * @param request 入参
     * @return Response
     */
    @PostMapping("/v1/submitMallBill")
    @ApiOperation(value = "商城发票提交接口", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/25 14:00")
    public Response submitMallBill(@RequestBody InvoiceMallSubmitReq req, HttpServletRequest request) {
        req.setPassengerMobile(getUserMobile(request));
        return mallInvoiceService.submitMallBill(req, getUserUid(request));
    }

    /**
     * 根据条件分页查询发票历史信息
     *
     * @param dto     dto
     * @param request request
     * @return {@link Response}
     */
    @PostMapping("/query/queryHeaderWithHistory")
    @ApiOperation(value = "根据条件分页查询发票历史信息", notes = "根据条件分页查询发票历史信息")
    public Response queryPageForCallCenter(@RequestBody InvoiceQueryPageDto dto, HttpServletRequest request) {
        if (StringUtils.isBlank(getUserUid(request))) {
            return Response.createError("乘客uid 不能为空");
        }
        if (ObjectUtil.isNull(dto.getPassengerInvoiceDto())) {
            return Response.createError("参数不能为空");
        }
        dto.getPassengerInvoiceDto().setPassengerUuid(getUserUid(request));
        return invoiceBusiness.queryPageForCallCenter(dto);
    }

    /**
     * 添加常用标题
     *
     * @param req     入参
     * @param request 入参
     * @return {@link Response}
     */
    @PostMapping("/v1/addFrequentlyUsedHeaders")
    @ApiOperation(value = "添加常用抬头", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/27 16:00")
    public Response<?> addFrequentlyUsedHeaders(@RequestBody AddInvoiceHeaderReq req, HttpServletRequest request) {
        if (StringUtils.isBlank(getUserUid(request))) {
            return Response.createError("无法获取乘客信息");
        }
        if (StringUtils.isNotBlank(req.getEmail()) && !req.getEmail().matches(EMAIL_PATTEN)) {
            return Response.createError("请输入正确的邮箱");
        }
        if (StringUtils.isNotBlank(req.getBankAccount()) && !NumberUtil.isNumber(req.getBankAccount())) {
            return Response.createError("开户账号只允许填写数字");
        }
        if (StringUtils.isNotBlank(req.getRegisterTel()) && !NumberUtil.isNumber(req.getRegisterTel())) {
            // 区号判断
            if (!NUMBER_PATTERN.matcher(req.getRegisterTel()).matches()) {
                return Response.createError("注册电话只允许填写数字,区号允许带-");
            }
        }
        if (StringUtils.isNotBlank(req.getHeader())
                && !Pattern.compile(HEADER_PATTEN).matcher(req.getHeader()).matches()) {
            return Response.createError("请输入正确的抬头");
        }
        req.setUserUuid(getUserUid(request));
        return invoiceHeaderService.addInvoiceHeader(req);
    }
    /**
     * 编辑常用标题
     *
     * @param req     入参
     * @param request 入参
     * @return {@link Response}
     */
    @PostMapping("/v1/updateFrequentlyUsedHeaders")
    @ApiOperation(value = "编辑常用标题", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/27 16:00")
    public Response<?> updateFrequentlyUsedHeaders(@RequestBody AddInvoiceHeaderReq req, HttpServletRequest request) {
        if (StringUtils.isNotBlank(req.getEmail()) && !req.getEmail().matches(EMAIL_PATTEN)) {
            return Response.createError("请输入正确的邮箱");
        }
        if (StringUtils.isNotBlank(req.getBankAccount()) && !NumberUtil.isNumber(req.getBankAccount())) {
            return Response.createError("开户账号只允许填写数字");
        }
        if (StringUtils.isNotBlank(req.getRegisterTel()) && !NumberUtil.isNumber(req.getRegisterTel())) {
            // 区号判断
            if (!NUMBER_PATTERN.matcher(req.getRegisterTel()).matches()) {
                return Response.createError("注册电话只允许填写数字,区号允许带-");
            }
        }
        if (StringUtils.isNotBlank(req.getHeader())
                && !Pattern.compile(HEADER_PATTEN).matcher(req.getHeader()).matches()) {
            return Response.createError("请输入正确的抬头");
        }
        return invoiceHeaderService.saveOrUpdateInvoiceHeader(req, getUserUid(request));
    }

    /**
     * 删除常用标题
     *
     * @param req     入参
     * @param request 入参
     * @return {@link Response}
     */
    @PostMapping("/v1/deleteFrequentlyUsedHeaders")
    @ApiOperation(value = "用户删除常用抬头", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/27 16:00")
    public Response deleteFrequentlyUsedHeaders(@RequestBody DelInvoiceHeaderReq req, HttpServletRequest request) {
       return invoiceHeaderService.deleteInvoiceHeader(req, getUserUid(request));
    }

    /**
     * 查询常用标题
     *
     * @param req     入参
     * @param request 入参
     * @return {@link Response}
     */
    @PostMapping("/v1/queryFrequentlyUsedHeaders")
    @ApiOperation(value = "查询用户常用抬头", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/27 16:00")
    public Response<?> queryFrequentlyUsedHeaders(@RequestBody AddInvoiceHeaderReq req, HttpServletRequest request) {
        return invoiceHeaderService.queryInvoiceHeader(req, getUserUid(request));
    }

    /**
     * 查询发票头
     *
     * @param req     绿色
     * @param request 请求
     * @return {@link Response<InvoiceHeaderVO>}
     */
    @PostMapping("/v1/queryInvoiceHeader")
    @ApiOperation(value = "查询开票抬头", notes = "接口负责人：闫军<br/>最后更新时间：2020/3/9 16:00")
    public Response<InvoiceHeaderVO> queryInvoiceHeader(@RequestBody InvoiceHeaderReq req, HttpServletRequest request) {
        return invoiceHeaderService.queryInvoiceHeader(req.getHeaderType(), getUserUid(request));
    }

    /**
     * 获取发票详情
     *
     * @param invoiceReq 请求参数
     * @return Response 返回
     */
    @PostMapping("/v3/info")
    @ApiOperation(value = "获取发票详情", notes = "接口负责人：赵明瑞<br/>最后更新时间：2021/09/17 16:00")
    public Response<?> invoiceInfo(@RequestBody @NotNull InvoiceReq invoiceReq) {
        if (StringUtils.isBlank(invoiceReq.getUuid())) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR.getMsg(),
                    ResultErrorEnum.PARAM_NULL_ERROR.getCode());
        }
        return invoiceBusiness.getInvoiceInfoAll(invoiceReq);
    }

    /**
     * 按照行程开票列表(同时呼叫平台)
     *
     * @param pageReq 分页入参
     * @param request 入参
     * @return {@link MyPageResult <>} 行程开票列表
     */
    @PostMapping("/v3/billingByRoute")
    @ApiOperation(value = "按照行程开票列表", notes = "接口负责人：赵明瑞<br/>最后更新时间：2020/10/31 09:55")
    public Response<?> billingByRoute(@RequestBody InvoiceRoutReq pageReq,
                                      HttpServletRequest request) {
        return invoiceBusiness.billingByRoute(pageReq, getUserUid(request));
    }

    /**
     * 行程开票信息详情
     *
     * @param invoiceRoutDetailReq 参数
     * @param request              入参
     * @return 返回
     */
    @PostMapping("/v3/invoice/detail")
    @ApiOperation(value = "行程开票信息详情", notes = "行程开票信息详情")
    public Response<InvoiceBillingDetail> billingDetailByRoute(@RequestBody InvoiceRoutDetailReq invoiceRoutDetailReq,
                                                               HttpServletRequest request) {
        return invoiceBusiness.billingDetailByRoute(invoiceRoutDetailReq, getUserUid(request));
    }




    /**
     * 发送发票
     *
     * @param invoiceSendReq 发票发送请求
     * @return {@link Response}
     */
    @ApiOperation(value = "发送电子发票和行程单", notes = "接口负责人：闫军<br/>最后更新时间：2020/3/25 16:00")
    @PostMapping("/v1/sendInvoice")
    public Response sendInvoice(@RequestBody InvoiceSendReq invoiceSendReq) {
        return invoiceBusiness.sendInvoiceAndRouteList(invoiceSendReq);
    }

    /**
     * 发送路由列表
     *
     * @param invoiceSendReq 发票发送请求
     * @param request        请求
     * @return {@link Response}
     */
    @ApiOperation(value = "发送行程单", notes = "接口负责人：闫军<br/>最后更新时间：2020/3/25 16:00")
    @PostMapping("/v1/sendRouteList")
    public Response sendRouteList(@RequestBody InvoiceSendReq invoiceSendReq, HttpServletRequest request) {
        return invoiceBusiness.sendRouteList(invoiceSendReq, getUserMobile(request));
    }

    /**
     * 查询开票历史
     *
     * @param req     入参
     * @param request 请求
     * @return {@link Response<PageResult<PassengerInvoiceDto>>}
     */
    @PostMapping("/v1/historyList")
    @ApiOperation(value = "查询开票历史", notes = "接口负责人：屈丽<br/>最后更新时间：2021/4/26 11:00")
    public Response<PageResult<PassengerInvoiceDto>> queryInvoiceHistoryList(@RequestBody InvoiceHistoryReq req,
                                                                             HttpServletRequest request) {
        return invoiceBusiness.queryInvoiceHistoryList(req, getUserUid(request));
    }

    /**
     * 按路线计费
     *
     * @param invoiceForm 发票格式
     * @return {@link Response}
     */
    @PostMapping("/v1/cancellation")
    @ApiOperation(value = "发票作废", notes = "接口负责人：闫军<br/>最后更新时间：2020/3/25 16:00")
    public Response billingByRoute(@RequestBody InvoiceForm invoiceForm) {
        return invoiceBusiness.invoiceCancellation(invoiceForm);
    }

    /**
     * 提交开票(同时呼叫平台)
     *
     * @param invoiceForm 提交开票入参
     * @param request     http
     * @return true or false
     */
    @PostMapping("/v1/billing")
    @ApiOperation(value = "提交开票", notes = "接口负责人：赵明瑞<br/>最后更新时间：2020/10/30 19:55")
    public Response<InvoiceResVo> invoiceBilling(@RequestBody @Valid InvoiceRelevantFormV3 invoiceForm,
                                                 HttpServletRequest request) {
        //t3运力存在开票主体,一汽,东风不存在开票主体概率,一汽东风开票主体是一个固定值
        if (getUserUid(request) == null) {
            return Response.createError("未获取到乘客id");
        }
        if (getUserMobile(request) == null) {
            return Response.createError("未获取到乘客手机号");
        }
        if (ObjectUtil.isNotNull(invoiceForm.getT3Transport())) {
            Response response = invoiceBusiness.checkOrderUuid(invoiceForm.getT3Transport().getOrderUuid(),
                    invoiceForm.getT3Transport().getMoney(), invoiceForm.getT3Transport().getOrderToCodes(),
                    getUserUid(request));
            if (!response.isSuccess()) {
                return response;
            }
        }
        if (ObjectUtil.isNotNull(invoiceForm.getDfTransport())) {
            Response response = invoiceBusiness.checkOrderUuid(invoiceForm.getDfTransport().getOrderUuid(),
                    invoiceForm.getDfTransport().getMoney(), invoiceForm.getDfTransport().getOrderToCodes(),
                    getUserUid(request));
            if (!response.isSuccess()) {
                return response;
            }
        }
        if (ObjectUtil.isNotNull(invoiceForm.getFawTransport())) {
            Response response = invoiceBusiness.checkOrderUuid(invoiceForm.getFawTransport().getOrderUuid(),
                    invoiceForm.getFawTransport().getMoney(), invoiceForm.getFawTransport().getOrderToCodes(),
                    getUserUid(request));
            if (!response.isSuccess()) {
                return response;
            }
        }
        invoiceForm.setPassengerMobile(getUserMobile(request));

        String passengerUuid = invoiceForm.getVirtualPassengerUuid();
        if (StringUtils.isEmpty(passengerUuid)) {
            passengerUuid = getUserUid(request);
        }

        return invoiceBusiness.invoiceBilling(invoiceForm, passengerUuid);
    }


    /**
     * 发票抬头模糊查询
     * @param form form
     * @return Response
     */
    @PostMapping("/header/fuzzy/query")
    @ApiOperation(value = "发票抬头模糊查询", notes = "发票抬头模糊查询")
    public Response getFuzzyHeaderList(@RequestBody GetSimilarHeaderListForm form) {
        log.info("getFuzzyHeaderList.req:{}", JSONObject.toJSONString(form));

        return invoiceBusiness.getFuzzyHeaderList(form);
    }

    /**
     * 发票抬头详情查询
     * @param form form
     * @return
     */
    @PostMapping("/header/detail/query")
    @ApiOperation(value = "发票抬头详情查询", notes = "发票抬头详情查询")
    public Response getDetailHeader(@RequestBody GetSimilarHeaderListForm form) {
        log.info("getDetailHeader.req:{}", JSONObject.toJSONString(form));
        return invoiceBusiness.getDetailHeader(form);
    }


    /**
     * 获取用户uid
     *
     * @param request 请求
     * @return {@link String}
     */
    protected String getUserUid(HttpServletRequest request) {
        String userUid = super.getUserUid(request);
        if (StringUtils.isEmpty(userUid)) {
            userUid = request.getHeader(PassengerConstants.AUTH_USERID);
        }
        return userUid;
    }


    /**
     * 发票管家授权信息查询
     * @param dto dto
     * @return Response
     */
    @PostMapping("/manager/queryInfo")
    @ApiOperation(value = "查询用户授权信息", notes = "查询用户授权信息")
    public Response queryInvoiceManagerInfo(@RequestBody AuthorizationSignQueryDto dto, HttpServletRequest request) {
        dto.setUserId(getUserUid(request));
        log.info("queryInvoiceManagerInfo.req:{}", JSONObject.toJSONString(dto));
        return invoiceBusiness.queryInvoiceManagerSignInfo(dto);
    }


    /**
     * 发票管家授权签约
     * @param dto dto
     * @return Response
     */
    @PostMapping("/manager/sign")
    @ApiOperation(value = "发票管家授权签约", notes = "发票管家授权签约")
    public Response invoiceManagerSign(@RequestBody InvoiceSignDto dto, HttpServletRequest request) {
        log.info("invoiceManagerSign.req:{}", JSONObject.toJSONString(dto));
        dto.setUserId(getUserUid(request));
        return invoiceBusiness.invoiceManagerSign(dto);
    }


    /**
     * 发票管家授权解约
     * @param dto dto
     * @return Response
     */
    @PostMapping("/manager/unSign")
    @ApiOperation(value = "发票管家授权解约", notes = "发票管家授权解约")
    public Response invoiceManagerUnSign(@RequestBody InvoiceSignDto dto, HttpServletRequest request) {
        log.info("invoiceManagerUnSign.req:{}", JSONObject.toJSONString(dto));
        dto.setUserId(getUserUid(request));
        return invoiceBusiness.invoiceManagerUnSign(dto);
    }
}
