package com.t3.ts.pay.center.api.constants;

import com.t3.ts.enums.BaseEnumInterface;

/**
 * 结果错误枚举
 *
 * <AUTHOR>
 * @since 2020/10/26
 */
public enum ResultErrorEnum implements BaseEnumInterface {


    /**
     * 缺少参数或参数为空
     */
    MISSING_PARAMETER_OR_PARAMETER_IS_EMPTY(100001, "缺少参数或参数为空"),
    /**
     * 参数非法
     */
    PARAMETER_IS_ILLEGAL(100002, "参数非法"),
    /**
     * RPC返回失败结果
     */
    RPC_RETURN_ERROR(100101, "RPC返回失败结果"),
    /**
     * RPC返回了意外的空数据
     */
    RPC_RETURN_NULL_DATA(100102, "RPC返回了意外的空数据"),
    /**
     * 上传的文件不符合格式
     */
    FILE_TYPE_ERROR(100103, "上传的文件不符合格式"),
    /**
     * 网络异常
     */
    RPC_CUSTOM_EXCEPTION(100104, "网络异常"),
    /**
     * 网络异常
     */
    GLOBLE_EXCEPTION(100105, "网络开小差了，请稍后重试", "网络异常"),
    /**
     * 请重新再试
     */
    PLANTRIP_PARAMETER_IS_ILLEGAL(100106, "费用预估异常，请重新再试"),
    /**
     * 请联系管理员
     */
    SYSTEM_ERROR(10000, "系统异常，请联系管理员"),
    /**
     * 相关参数不能为空
     */
    PARAM_NULL_ERROR(10001, "相关参数不能为空"),
    /**
     * 参数格式错误
     */
    PARAM_FORAMT_ERROR(10002, "参数格式错误"),
    /**
     * 时间设置错误
     */
    PARAM_TIME_ERROR(10003, "时间设置错误"),
    /**
     * 请重新登录
     */
    LOGIN_TIMEOUT(10004, "登录超时,请重新登录"),
    /**
     * 无访问权限
     */
    NONE_PERMISSION(10005, "无访问权限"),
    /**
     * 需修改初始密码
     */
    PASSWORD_IS_POOL(10006, "需修改初始密码"),
    /**
     * 账号异常
     */
    ACCOUNT_IS_EXCEPTION(10007, "账号异常"),
    /**
     * RPC请求异常
     */
    RPC_RESPONSE_ERROR(10008, "RPC请求异常"),
    /**
     * 请退出后重试
     */
    ERR_MSG_LOGIN_OTHER(10010, "系统繁忙，请退出后重试", "登录过期，请重新登录"),
    /**
     * 请退出后重试
     */
    ERR_MSG_USER_CLOSE(10011, "系统繁忙，请退出后重试", "登录过期，请重新登录"),
    /**
     * 您的企业已停用企业用车服务
     */
    ERR_CODE_DISABLE_ENTERPRISE(9999, "您的企业已停用企业用车服务"),
    /**
     * 统一错误码
     */
    UNIFY_ERROR_CODE(20005, "统一错误码"),
    /**
     * 请重新呼叫
     */
    PRE_PAY_NO_TIMEOUT(5007, "系统繁忙，请重新呼叫", "预付流水号已过期请重新下单"),
    /**
     * 请重新呼叫
     */
    PRE_PAY_AMOUNT_TIMEOUT(5008, "统繁忙，请重新呼叫", "预付金额已过期请重新发起"),
    /**
     * 请刷新查看
     */
    ROUTE_PAYED(5009, "已付款，请刷新查看", "行程已支付完成"),
    /**
     * 请稍后重试
     */
    ROUTE_CANCEL(6002, "系统繁忙，请稍后重试", "PAY100054", "行程已取消"),
    /**
     * 请稍后重试
     */
    UN_SUPPOURT_PAY_TYPE(6003, "系统繁忙，请稍后重试", "不支持的支付方式"),
    /**
     * 请重新呼叫
     */
    GET_RECHARGE_IFNO_ERROR(6006, "系统繁忙，请重新呼叫", "获取预付款支付信息失败"),
    /**
     * 请刷新查看
     */
    HAS_PAYED(6008, "已付款，请刷新查看", "您已支付过，请稍后刷新查看结果"),
    /**
     * 请稍后重试
     */
    PAY_ERROR(6100, "系统繁忙，请稍后重试", "支付失败"),
    /**
     * 请稍后重试
     */
    ROUTE_INFO_ERROR(6101, "系统繁忙，请稍后重试", "未获取到行程信息"),
    /**
     * 请稍后重试
     */
    PAY_DESK_INFO_ERROR(6102, "系统繁忙，请稍后重试", "获取支付收银台异常"),
    /**
     * 请稍后重试
     */
    CHANNEL_LIST_ERROR(6103, "系统繁忙，请稍后重试", "获取支付类型列表失败"),
    /**
     * 请稍后重试
     */
    WX_CAN_USE_ERROR(6104, "系统繁忙，请稍后重试", "获取微信是否可以接口失败"),
    /**
     * 请稍后重试
     */
    UNIFIED_PAY_INFO_ERROR(6105, "系统繁忙，请稍后重试", "获取统一支付信息异常"),
    /**
     * 请稍后重试
     */
    SAVE_RECHARGE_SETTLEMENT_ERROR(6107, "系统繁忙，请稍后重试", "生成充值结算单失败"),
    /**
     * 请稍后重试
     */
    WX_MINI_USER_ERROR(6108, "系统繁忙，请稍后重试", "微信小程序用户解析失败"),
    /**
     * 请稍后重试
     */
    COUPON_ACTIVITY_ERROR(6109, "系统繁忙，请稍后重试", "券套餐下单失败"),
    /**
     * 请稍后重试
     */
    ALI_MINI_PARAM_ERROR(6110, "系统繁忙，请稍后重试", "支付宝小程序渠道参数异常"),
    /**
     * 请稍后重试
     */
    WX_MINI_PARAM_ERROR(6111, "系统繁忙，请稍后重试", "微信小程序渠道参数异常"),
    /**
     * 订单已付款
     */
    PAY_ERROR_THIRD_PAY_SUCCESS(6112, "您的订单已付款，请刷新查看", "PAY100051", "三方支付成功,无需再支付,请确认订单状态"),
    /**
     * 订单已付款
     */
    PAY_ERROR_DUPLICATED_PAYMENT(6113, "您的订单已付款，请刷新查看", "PAY100013", "请勿重复支付"),
    /**
     * 请退出后重试
     */
    PAY_ERROR_INSUFFICIENT_INTEGRAL(6114, "可用积分不足，请稍后重试", "PAY100061", "积分不足"),
    /**
     * 请退出后重试
     */
    PAY_ERROR_CAN_NOT_USE_COUPON(6115, "系统繁忙，请退出后重试", "PAY100015", "支付失败"),
    /**
     * 请退出后重试
     */
    PAY_ERROR_SEPARATE_FAILED(6116, "系统异常，请稍后重试", "PAY100003", "分账失败"),
    /**
     * 请退出后重试
     */
    PAY_ERROR_INSUFFICIENT_BALANCE(6117, "余额不足，请稍后重试", "PAY100017", "余额不足"),
    /**
     * 请退出后重试
     */
    PAY_ERROR_NOT_SUPPORT(6118, "系统繁忙，请退出后重试", "PAY100048", "支付失败"),

    CHANNEL_COUPON_ERROR(6119, "当前支付方式与用券条件不匹配", "PAY100063", "当前支付方式与用券条件不匹配"),
    /**
     * 下单信息过期
     */
    WX_MINI_PARAM_TIME_OUT_ERROR(6120, "支付失败，请返回T3出行APP查看", "PAY100076", "下单信息过期"),

    DISPUTER_ORDER_PAY_BEFORE_NEED_BEFORE(6121, "INNER100077"),
    /**
     * 敏感订单老版本用户，提示升级版本
     */
    DISPUTER_ORDER_PAY_BEFORE_NEED_UPDATE(6122, "为了提升用车体验，您的用车订单仍有信息待您确认，需要升级到最新版本",
            "INNER100077", "INNER100077"),
    /**
     * 用户并发错误
     */
    USER_CONCURRENT_ERROR(100108, "操作失败，频率过高"),
    /**
     * 发票电子邮件限制错误
     */
    INVOICE_EMAIL_LIMIT_ERROR(100107, "操作频繁，请稍后再试"),

    /**
     * 正在支付中
     */
    PAY_NOT_FINISHED_NEW(50101001, "正在支付中，请稍后重试", "PAY100041", "正在支付中,请稍后再试"),

    /**
     * 微信支付分已为该行程创建订单，不可重复创建
     */
    WXPAYSCORE_SIGN_CREATED(50101003, "微信支付分订单支付中，请退出后重新支付",
            "", "微信支付分已为该行程创建订单，不可重复创建"),

    /**
     * 当前账户余额中包含礼品卡,行程中的附加费、节日服务费、跨城费需要通过第三方支付
     */
    CAN_NOT_USE_GIFT_CARD_MONEY(50101007, "请勾第三方支付方式，附加费、跨城费、节日服务费需要第三方支付",
            "PAY100058", "当前账户余额中包含礼品卡,行程中的附加费、节日服务费、跨城费需要通过第三方支付"),

    /**
     * 支付中或已完成支付
     */
    ONGOING_PAYMENT(50101008, "您的订单已付款，请刷新查看", "PAY100025", "支付中或已完成支付"),

    /**
     * 行程存在预付款，不支持线下收款
     */
    ROUTE_PRE_NOT_OFFLINE(50101009, "行程存在预付款，不支持线下收款", "PAY100066", "行程存在预付款，不支持线下收款"),

    /**
     * 行程订单已完成支付
     */
    ROUTE_ORDER_PAIED(50101010, "已付款，请刷新查看", "INNER100078", "已付款，请刷新查看"),



    /**
     * 第三方扣款失败
     */
    THIRD_ORDER_DEDUCTION_FAIL(60101001, "第三方支付失败，请稍后重试", "PAY100022", "第三方扣款失败"),

    /**
     * 结算单查询失败
     */
    INVOKE_SETTLEMENT_ERROR(60101003, "系统异常，请稍后重试", "PAY100007", "结算单查询失败"),

    /**
     * 优惠券使用异常
     */
    COUPON_USE_ERROR(60101004, "系统异常，请稍后重试", "PAY100016", "优惠券使用异常,请重试"),

    /**
     * 余额扣款失败
     */
    BALANCE_DEDUCTION_FAIL(60101005, "系统异常，请稍后重试", "PAY100020", "余额扣款失败"),

    ROUTE_NO_NEED_PAY(5009, "已付款，请刷新查看", "PAY100079", "行程已支付,并且不再进行券包购买,行程进行完单处理"),

    MALL_PACKAGE_BUY_UPPER_LIMIT(5010, "商品购买数量已经达到上限"),

    MALL_PACKAGE_BUY_UPPER_LIMIT_OUTSIDE(5011, "券套餐商品购买数量已经达到上限", "券套餐商品购买数量已经达到上限"),

    COUPON_CAN_NO_USE(5012, "优惠券不可用", "优惠券不可用"),

    COUPON_NEED_THIRD_CHANNEL(5013, "该优惠券不支持使用资产支付", "PAY100063", "PAY100063"),
    SIGN_VERSION_LOW(5014, "请升级APP至最新版本，享受更好的产品体验", "请升级APP至最新版本，享受更好的产品体验"),

    RISK_ERROR(7001, "订单检测异常，不允许支付", "PAY107001", "PAY107001"),

    PAY_ACCOUNT_RECHARGE_CASH_CANNOT_USE(7002, "充值本金暂不可用", "PAY100084", "PAY100084 充值本金不能使用"),

    PAY_ACCOUNT_RECHARGE_GIFT_CANNOT_USE(7003, "充值赠金暂不可用", "PAY100085", "PAY100085 充值赠金不能使用"),

    PAY_ACCOUNT_GIFT_CANNOT_USE(7004, "打车金暂不可用", "PAY100086", "PAY100086 打车金不能使用"),

    GIFT_CARD_CANNOT_USE(7005, "礼品卡暂不可用", "PAY100087", "PAY100087 礼品卡不能使用"),

    COMPANY_GIFT_CARD_CANNOT_USE(7006, "企业礼品卡暂不可用", "PAY100088", "PAY100088 企业礼品卡不能使用");





    private Integer code;

    private String msg;

    /**
     * 对应pay-center PayCenterExceptionCode
     */
    private String innerCode;

    private String innerMsg;


    ResultErrorEnum(Integer errorCode, String msg, String innerMsg) {
        this.code = errorCode;
        this.msg = msg;
        this.innerMsg = innerMsg;
    }

    ResultErrorEnum(Integer errorCode, String msg, String innerCode, String innerMsg) {
        this.code = errorCode;
        this.msg = msg;
        this.innerCode = innerCode;
        this.innerMsg = innerMsg;
    }

    ResultErrorEnum(Integer errorCode, String msg) {
        this.code = errorCode;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    /**
     * pay-center返回错误码映射
     *
     * @param innerMsg pay-center返回错误码
     * @return ResultErrorEnum
     */
    public static ResultErrorEnum getResultErrorEnum(String innerMsg) {
        for (ResultErrorEnum resultErrorEnum : ResultErrorEnum.values()) {
            if (innerMsg.equals(resultErrorEnum.innerMsg)) {
                return resultErrorEnum;
            }
        }
        return null;
    }
}


