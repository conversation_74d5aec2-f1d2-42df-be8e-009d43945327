package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:24
 * @des 1.0
 */

@Data
public class Balance {

    // 用户钱包余额（赠币+礼品卡）
    private BigDecimal balanceTotal;

    // 赠送币金额
    private BigDecimal giftCurrency;

    // 礼品卡余额
    private BigDecimal giftCard;
    /**
     * 充值本金
     */
    private BigDecimal rechargeCash;
    /**
     * 充值赠金
     */
    private BigDecimal rechargeGift;
    /**
     * 打车金
     */
    private BigDecimal giftCash;
    /***
     * 企业礼品卡
     */
    private BigDecimal companyGiftCard;
    /**
     * 充值余额标识 =1 标识金额》0
     */
    private Integer rechargeFlag;

}
