package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.EnterprisePaymentBusiness;
import com.t3.ts.pay.center.api.dto.chartered.PayTradeCharteredReq;
import com.t3.ts.pay.center.api.dto.enterprise.AdvanceSerialReq;
import com.t3.ts.pay.center.api.dto.enterprise.PayReq;
import com.t3.ts.pay.center.api.dto.enterprise.RechargeReq;
import com.t3.ts.pay.center.api.dto.enterprise.RequisitionUuidReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:00
 */
public class EnterpriseApiTest {
    @Mock
    EnterprisePaymentBusiness enterprisePaymentBusiness;
    @Mock
    Logger log;
    @InjectMocks
    EnterpriseApi enterpriseApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCashierDeskPro() throws Exception {
        when(enterprisePaymentBusiness.cashierDeskPro(any())).thenReturn(null);

        Response result = enterpriseApi.cashierDeskPro(new PayTradeCharteredReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testPayForPackage() throws Exception {
        when(enterprisePaymentBusiness.payForAllBusiness(any(), anyString(), anyString())).thenReturn(null);

        Response result = enterpriseApi.payForPackage(new PayReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testRecharge() throws Exception {
        when(enterprisePaymentBusiness.recharge(any(), anyString())).thenReturn(null);

        Response result = enterpriseApi.recharge(new RechargeReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testQuerySettlementIsSuccess() throws Exception {
        when(enterprisePaymentBusiness.querySettlementIsSuccess(anyString(), anyString(), anyString()))
                .thenReturn(null);

        Response result = enterpriseApi.querySettlementIsSuccess(new AdvanceSerialReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCheckIsCreateApplication() throws Exception {
        when(enterprisePaymentBusiness.checkIsCreateApplication(anyString())).thenReturn(null);

        Response result = enterpriseApi.checkIsCreateApplication(new RequisitionUuidReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCheckIsModifyRoutePlan() throws Exception {
        when(enterprisePaymentBusiness.checkIsModifyRoutePlan(anyString())).thenReturn(null);

        Response result = enterpriseApi.checkIsModifyRoutePlan(new RoutePlanUuidReq("routePlanUuid"));
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetDeviceFingerPrintToken() throws Exception {
        String result = EnterpriseApi.getDeviceFingerPrintToken(null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
