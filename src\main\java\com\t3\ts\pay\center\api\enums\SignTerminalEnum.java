package com.t3.ts.pay.center.api.enums;

import lombok.Getter;

/**
 * 签约终端枚举
 *
 * <AUTHOR>
 * @Date 2021/8/23 14:33
 */
@Getter
public enum SignTerminalEnum {

    /**
     * ANDROID
     */
    ANDROID("1", "android"),
    /**
     * IOS
     */
    IOS("2", "IOS平台"),
    /**
     * H5
     */
    H5("3", "H5"),
    /**
     * WECHAT
     */
    WECHAT("4", "微信小程序"),
    /**
     * ZFB
     */
    ZFB("5", "支付宝小程序"),

    HARMONY_OS("6", "鸿蒙OS");


    private String code;
    private String msg;

    /**
     * description
     *
     * @param code code
     * @param msg msg
     * <AUTHOR> YongJin
     */
    SignTerminalEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
