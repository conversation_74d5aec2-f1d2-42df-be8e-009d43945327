package com.t3.ts.pay.center.api.dto.enterprise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 企业用车个人支付
 *
 * <AUTHOR>
 * @date 2019.11.4
 */
@ApiModel
@Getter
@Setter
public class PayReq {

    /**
     * 行程id
     */
    @ApiModelProperty(value = "行程id")
    private String routePlanUuid;

    /**
     * 车费支付方式【包含余额+一种第三方支付】
     */
    @ApiModelProperty(value = "车费支付方式【包含余额+一种第三方支付】")
    private List<Integer> payTypeList;

    /**
     * 业务类型 2 专车  4快车 6惠享  8包车
     */
    @ApiModelProperty(value = "业务类型 2 专车  4快车 6惠享 8包车")
    private Integer businessType;
}
