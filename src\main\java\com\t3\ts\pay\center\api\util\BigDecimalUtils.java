package com.t3.ts.pay.center.api.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020.10.23
 */
public class BigDecimalUtils {

    /**
     * @param str str
     * @return BigDecimal
     */
    public static BigDecimal bigDecimalOf(String str) {

        if (StringUtils.isEmpty(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(str);
    }

    /**
     * @param bigDecimal bigDecimal
     * @return BigDecimal
     */
    public static BigDecimal bigDecimalValue(BigDecimal bigDecimal) {

        if (ObjectUtils.isEmpty(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 判断一个数是否大于0
     *
     * @param decimal decimal
     * @return boolean
     */
    public static boolean greaterThanZero(BigDecimal decimal) {
        if (ObjectUtils.isEmpty(decimal)) {
            return false;
        }
        return decimal.compareTo(BigDecimal.ZERO) > 0;
    }

}
