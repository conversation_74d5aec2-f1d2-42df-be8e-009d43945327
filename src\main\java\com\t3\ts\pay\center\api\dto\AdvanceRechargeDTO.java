package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-15 22:04
 * @des: 预付款充值传输对象
 */
@Data
public class AdvanceRechargeDTO {

    /**
     * 支付方式
     *
     * @see com.t3.ts.pay.remote.constants.EnumPayOrderChannel
     */
    private Integer payType;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 预付款流水号
     */
    private String prePayNo;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 小程序openid
     */
    private String openId;

    /**
     * 支付宝授权码
     */
    private String aliAuthCode;

    /**
     * 支付并签约标识
     */
    private String payAndSign;

    /**
     * 来源 0：企业用车个人预付款
     */
    private Integer sourceType;

    /**
     * 端上支持聚合支付的渠道列表
     */
    private List<Integer> aggPayTypeList;

    /**
     * source 来源 101 -我的南京 预付款
     */
    private int source;

    /**
     * 支付宝  用户付款中途退出返回商户网站的地址
     */
    private String quitUrl;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 行程Id
     */
    private String routePlanUuid;
    /**
     * 为true时 表示从收银台发起的支付为免密支付，默认false
     */
    private Boolean noSecret;

    /**
     * 1：顺风车
     */
    private Integer bizLineFlag;

    /**
     * 银联授权码
     */
    private String authCode;

    /**
     * 标识 这里固定
     */
    private String appUpIdentifier;

    /**
     * 来源
     * 注释同 {@link com.t3.ts.pay.center.api.dto.trade.RechargeReq.subSource}  字段注释
     */
    private String subSource;

    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;

    /**
     * 用户ip
     */
    private String userIp;
}
