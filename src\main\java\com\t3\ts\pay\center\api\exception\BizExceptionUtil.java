package com.t3.ts.pay.center.api.exception;

import com.t3.ts.enums.common.BussiEnum;
import com.t3.ts.pay.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务异常类
 *
 * <AUTHOR>
 */
@Slf4j
public class BizExceptionUtil {

    /**
     * 创建默认异常 - 编码500
     *
     * @param errorMsg 错误消息
     */
    public static void create(String errorMsg) {
        throw new BizException(errorMsg, BussiEnum.ERROR.getCode(), null);
    }

    /**
     * 创建默认异常 - 自定义 code
     *
     * @param errorMsg 错误消息
     * @param code code
     */
    public static void create(String errorMsg, Integer code) {
        throw new BizException(errorMsg, code, null);
    }

    /**
     * @param e e
     */
    public static void create(BizException e) {
        throw e;
    }


    /**
     * 创建默认异常 - 编码500
     *
     * @param errorMsg 错误消息
     * @param data     额外返回数据
     */
    public static void create(String errorMsg, Object data) {
        throw new BizException(errorMsg, BussiEnum.ERROR.getCode(), data);
    }

    /**
     * 创建
     * 创建异常
     *
     * @param exceptionEnum Exception枚举
     */
    public static void create(BizExceptionEnum exceptionEnum) {
        throw new BizException(exceptionEnum.getMsg(), exceptionEnum.getCode(), null);
    }

    /**
     * 创建
     * 创建异常
     *
     * @param exceptionEnum Exception枚举
     * @param data          data
     */
    public static void create(BizExceptionEnum exceptionEnum, Object data) {
        throw new BizException(exceptionEnum.getMsg(), exceptionEnum.getCode(), data);
    }


    /**
     * 打印异常消息
     *
     * @param errorMsg 异常消息
     */
    public static void log(String errorMsg) {
        try {
            BizExceptionUtil.create(errorMsg);
        } catch (BizException e) {
            log.error("", e);
        }
    }


}
