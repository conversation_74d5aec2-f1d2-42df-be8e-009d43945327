package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Data;

import java.io.*;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceBillingDetail.java
 * @Description 行程开票信息明细
 * @createTime 2022年01月19日 09:03:00
 */
@Data
public class InvoiceBillingDetail implements Serializable {

    private static final long serialVersionUID = 6632607325741209647L;

    private String invoiceAmount;

    private String payAmount;

    private List<NonInvoicedItem> nonInvoicedItems;
}
