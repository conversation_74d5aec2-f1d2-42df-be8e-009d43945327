package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 账户流水查询ReqVo
 *
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
public class AccountFlowReqVo {

    /**
     * 司机ID
     */
    private String userId;

    /**
     * 账户类型,3:t3司机,4:出租车司机
     */
    private Integer accountType;

    /**
     * 业务id
     */
    private Integer bizType;

    /**
     * 业务id集合
     */
    private List<Integer> bizTypeList;

    /**
     * 账本类型
     */
    private Integer bookType;

    /**
     * 账本类型集合
     */
    private List<Integer> bookTypeList;

    /**
     * 1-收入，2-支出
     */
    private Integer changedType;

    /**
     * 当前页
     */
    private Integer currPage;

    /**
     * 费用项
     */
    private Integer fareItem;

    /**
     * 费用项集合
     */
    private List<Integer> fareItemList;

    /**
     * 过滤不展示的费用项 800101, 500101, 900101, 900102
     */
    private List<Integer> notMatchFareItemList;

    private String orderNo;

    private String orderUuid;

    /**
     * 每页数量
     */
    private Integer pageSize;

    private String settleUuid;


    /**
     *
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    private Integer startId;

    private String yearMonth;

    private String yearMonthDay;

    private String weekStartDate;

    private String weekEndDate;

    private Integer dateType;

    /**
     * 1收支明细  2收入明细
     */
    private Integer visitType;
}
