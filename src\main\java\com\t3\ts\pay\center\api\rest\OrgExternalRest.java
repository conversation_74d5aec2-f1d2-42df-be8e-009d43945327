package com.t3.ts.pay.center.api.rest;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.RequisitionDto;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业用车rest
 *
 * @author: hujd
 * @date: 2022-03-03 14:44
 */
@Component
@Slf4j
public class OrgExternalRest extends BaseRest {

    @Resource
    private SlbConfig slbConfig;

    /**
     * 获取预付款信息
     *
     * @param advanceSerial 预付款流水号
     * @return {@link List <String>}
     */
    public RequisitionDto getAdvanceInfo(String advanceSerial) {
        JSONObject entity = new JSONObject();
        entity.put("advanceSerial", advanceSerial);
        String postHttp = sendPostHttp(slbConfig.getOrgExternalUrl() + "/api/external/payCenter/getAdvanceInfo",
                entity.toJSONString());
        log.info("OrgExternalRest.getAdvanceInfo,resp:{}", JSONUtil.toJsonStr(postHttp));
        if (StringUtils.isNotBlank(postHttp)) {
            Response<?> resp = JSONObject.parseObject(postHttp, Response.class);
            if (null != resp && resp.isSuccess() && null != resp.getData()) {
                return JSONUtil.toBean(JSONUtil.toJsonStr(resp.getData()), RequisitionDto.class);
            }
        }
        return null;
    }
}
