package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName InvoiceRoutDetailReq.java
 * @Description TODO
 * @createTime 2022年01月14日 17:42:00
 */
@Data
@ApiModel
public class InvoiceRoutDetailReq implements Serializable {

    private static final long serialVersionUID = 6522323205642399463L;

    @ApiModelProperty(value = "行程id")
    private String routePlanUuid;

}
