package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.result.PageResult;

import java.util.List;

/**
 * @Author: hongwei.yan
 * @Description:
 * @Date: Create in 9:35 AM 2019/3/28
 */
public final class PageUtils {

    /**
     * 页utils
     */
    private PageUtils() {
    }

    /**
     * get页result
     * 转换为分页结果
     *
     * @param list        列表
     * @param currentPage current页
     * @param pageSize    页大小
     * @return {@link PageResult}
     */
    public static PageResult getPageResult(List<?> list, Integer currentPage, Integer pageSize) {
        return getPageResult(list, currentPage, pageSize, list.size());
    }

    /**
     * get页result
     * 转换为分页结果
     *
     * @param list        列表
     * @param currentPage current页
     * @param pageSize    页大小
     * @param count       count
     * @return {@link PageResult}
     */
    public static PageResult getPageResult(List<?> list, Integer currentPage, Integer pageSize, Integer count) {
        PageInfo page = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(count);
        pageResult.setList(page.getList());
        pageResult.setCurrPage(currentPage);
        pageResult.setPageSize(pageSize);
        return pageResult;
    }

    /**
     * get页result
     * 转换分页返回结果
     * 将com.github.pagehelper.PageInfo 转换为 com.t3.ts.result.PageResult
     *
     * @param pageInfo 页info
     * @param <T>      t
     * @param clazz    clazz
     * @return {@link PageResult<T>}
     */
    public static <T> PageResult<T> getPageResult(PageInfo<T> pageInfo, Class clazz) {
        PageResult pageResult = new PageResult<>();
        pageResult.setCurrPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotalCount((int) pageInfo.getTotal());
        pageResult.setHasMore(pageInfo.isHasNextPage());
        pageResult.setList(BeanUtils.collectionCopy(pageInfo.getList(), clazz));
        return pageResult;
    }

    /**
     * 获取页面resultt
     *
     * @param pageInfo 页面信息
     * @param clazz    clazz
     * @param <T>      t
     * @return {@link PageResult<T>}
     */
    public static <T> PageResult<T> getPageResultT(PageInfo<?> pageInfo, Class clazz) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setCurrPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotalCount((int) pageInfo.getTotal());
        pageResult.setHasMore(pageInfo.isHasNextPage());
        pageResult.setList(BeanUtils.collectionCopy(pageInfo.getList(), clazz));
        return pageResult;
    }
    /**
     * 获取页面resultt
     *
     * @param pageInfo 页面信息
     * @param clazz    clazz
     * @param <T>      t
     * @return {@link PageResult<T>}
     */
    public static <T> PageResult<T> getPageResult(PageResult<?> pageInfo, Class clazz) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setCurrPage(pageInfo.getCurrPage());
        pageResult.setPageSize(pageInfo.getPageSize());
        pageResult.setTotalCount(pageInfo.getTotalCount());
        pageResult.setHasMore(pageInfo.isHasMore());
        pageResult.setList(JSON.parseArray(JSON.toJSONString(pageInfo.getList()), clazz));
        return pageResult;
    }
}
