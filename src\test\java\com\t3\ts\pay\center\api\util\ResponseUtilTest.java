package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.remote.exception.PayExceptionCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2020/5/15, 0015 9:29
 */
@RunWith(MockitoJUnitRunner.class)
public class ResponseUtilTest {
    @Mock
    Logger log;
    @InjectMocks
    ResponseUtil responseUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWriteSuccessResponse() throws Exception {
        try {
            ResponseUtil.writeSuccessResponse(null, "result");
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void testWriteFailResponse() throws Exception {
        try {
            ResponseUtil.writeFailResponse(null, PayExceptionCode.SYSTEM_ERROR);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void testWriteFailResponse2() throws Exception {
        try {
            ResponseUtil.writeFailResponse(null, Integer.valueOf(0), "errorMessage");
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme