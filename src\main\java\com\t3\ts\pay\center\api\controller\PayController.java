package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.dto.trade.PayDeskVoV4;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.center.api.service.PayService;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.result.Response;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付核心接口
 *
 * @author: hujd
 * @date: 2022-10-26 14:02
 */
@RestController
@RequestMapping("/api/payment")
public class PayController {

    private final PayService payService;

    /**
     * @param payService 支付服务
     */
    public PayController(PayService payService) {
        this.payService = payService;
    }

    /**
     * 获取付款台信息
     *
     * @param payTradeReq 支付交易请求
     * @param request     请求
     * @return {@link Response}
     */
    @PostMapping("/v4/pay/desk/info")
    public Response<PayDeskVoV4> deskInfo(@RequestBody @Validated PayTradeReq payTradeReq, HttpServletRequest request) {
        return payService.deskInfo(setBaseParam(payTradeReq, request));
    }


    /**
     * 组装基础参数
     *
     * @param payTradeReq payTradeReq
     * @param request     request
     * @return {@link com.t3.ts.pay.center.api.dto.trade.PayTradeReq}
     */
    private PayTradeReq setBaseParam(PayTradeReq payTradeReq, HttpServletRequest request) {
        payTradeReq.setGrayBuild(HttpRequestUtil.getGrayBuild(request));
        payTradeReq.setGrayVersion(HttpRequestUtil.getGrayVersion(request));
        payTradeReq.setPassengerUuid(HttpRequestUtil.getUserUid(request));
        return payTradeReq;
    }

}
