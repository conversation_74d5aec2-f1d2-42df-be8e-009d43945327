package com.t3.ts.pay.center.api.dto.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单列表(app)
 * 返回信息
 *
 * <AUTHOR>
 */
@Data
public class RouteOnGoingOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 行程计划Id
     */
    private String routePlanId = "";
    /**
     * 行程状态，0:约车中，1:预约中，2:接乘中，3:已抵达接乘地，4:载乘中，5:已抵达目的地，6:待支付，7:待评价，8:行程结束，9:行程取消
     */
    private String routeStatus = "";
    /**
     * 订单模块类型（1:出租车,2：专车，3：拼车，4：快车）
     */
    private Integer typeModule = 0;
    /**
     * 城市id
     */
    private String cityCode = "";
    /**
     * 城市名称
     */
    private String cityName = "";
    /**
     * 区域ID
     */
    private String areaCode = "";
    /**
     * 行程城市uuid (数据库主键内部使用)
     */
    private String cityUuid = "";
    /**
     * 预估时长
     */
    private Integer planDuration = 0;
    /**
     * 预估里程（公里）
     */
    private Double planTrip;
    /**
     * 预估费用
     */
    private BigDecimal planFare = new BigDecimal(0);
    /**
     * 终点经度
     */
    private Double destLng = new Double(0);
    /**
     * 终点纬度
     */
    private Double destLat = new Double(0);
    /**
     * 1实时单 2 预约单
     */
    private Integer typeTime = 0;
    /**
     * 预计出发时间
     */
    private Date departTime = new Date();
    /**
     * 订单创建时间
     */
    private Date createTime = new Date();
    /**
     * 乘客Id
     */
    private String passengerId = "";
    /**
     * 乘客手机号
     */
    private String passengerMobile = "";
    /**
     * 叫车人姓名
     */
    private String callerName = "";
    /**
     * 乘客头像
     */
    private String passengerFace = "";
    /**
     * 乘车人的手机号码
     */
    private String passengerPhoneNumber = "";
    /**
     * 乘车人的姓名
     */
    private String passengerName = "";
    /**
     * 计费规则
     */
    private String fareModelJson = "";
    /**
     * 途径点版本号
     */
    private Integer version = 0;
    /**
     * 行程类型（本人/代客）：1 本人；2 代客
     */
    private Integer typeSelf;
    /**
     * 司机uuid
     */
    private String driverUuid = "";
    /**
     * 司机手机号
     */
    private String driverMobile = "";
    /**
     * 司机姓名
     */
    private String driverName = "";
    /**
     * 车架号
     */
    private String vin = "";
    /**
     * 服务开始里程 (预约单) 去接乘客
     */
    private Double obdStartMile = 0D;
    /**
     * 车辆到达时间 (司机到达接乘地)
     */
    private Date driArritime;
    /**
     * 乘客上车时间
     */
    private Date pasArrive;
    /**
     * 乘客上车里程（单位：km)
     */
    private Double obdGetOnMile = 0D;
    /**
     * 等待费用
     */
    private BigDecimal waitFare = new BigDecimal(0);
    /**
     * 订单费用
     */
    private BigDecimal orderFare = new BigDecimal(0);
    /**
     * 车辆运营商uuid IOV
     */
    private String agentUuid = "";
    /**
     * 司机接单设备类型 IOV
     */
    private Integer receiveDevice = 0;
    /**
     * 司机上线状态 IOV
     */
    private Integer workState = 0;
    /**
     * 车机载屏号码 IOV
     */
    private String devicePhone = "";
    /**
     * 车型Id 从IOV获取
     */
    private String carTypeId = "";
    /**
     * 车型名称 从IOV获取
     */
    private String carTypeName = "";
    /**
     * 车牌号 从IOV获取
     */
    private String carNo = "";
    /**
     * 车颜色 从IOV获取
     */
    private String carColour = "";
    /**
     * 车辆业务类型(2,专车,4快车) IOV
     */
    private Integer carType = 0;
    /**
     * 用于行程状态变更传送给大数据
     */
    private String bigData = "";
    /**
     * 区别订单是不是接力单的数据 0 普通实时单类型 1 接力单类型 2 回家单类型
     */
    private Integer routeTimeSubtype = 0;
    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    private Integer typeEnt;

    /**
     * 订单行程类型（1：用车;2日租;3半日租;4接机;5送机;6接站;7送站;9:多日订单）
     */
    private Integer typeTrip;
    /**
     * 行程费用
     */
    private String costFareStr;
    /**
     * 行程费用明细
     */
    private String costDetailStr;
    /**
     * 行程编号
     */
    private String routeNo;
    /**
     * 订单来源
     */
    private Integer source;

    /************** 新添加字段 **************/

    /**
     * 订单取消类型0 乘客无责取消 1 乘客政策免费取消 2 乘客使用免费次数取消
     * 3 乘客付费取消 4超时自动取消 5客服关闭订单(冗余字段)
     */
    private Integer cancelType;
    /**
     * 车型等级
     */
    private Integer vehicleLevel;
    /**
     * 司机去接乘客时间
     */
    private Date driverPickPassengerDate;
    /**
     * 乘客确认上车(顺风车业务)
     */
    private Date confirmArrTime;

    private Integer age;
    /**
     * 起点
     */
    private String originAddress;
    /**
     * 终点
     */
    private String destAddress;
    /**
     * 产品线 4:快享 2:专享 5:顺风车 10:包车 11:包车-专车 12:包车-快车 13:企业用车-快享
     */
    private Integer productLine;
    /**
     * 自定义上车点id
     */
    private Integer customPointFlag;
    /**
     * mainStatus 主状态
     */
    private Integer mainStatus;
    /**
     * 子状态 （新添加对应的输出结果）
     */
    private Integer subStatus;
    /**
     * 包车套餐名称
     */
    private String wrapName;
    /**
     * 计价方式 1 平台定价 2 计价器定价
     */
    private Integer fareMethod;
    /**
     * 同时呼叫运力
     */
    private List<Integer> requireTransports;
    /**
     * 运力 1-T3 2-东风运力 3-一汽运力
     */
    private Integer transport;
    /**
     * 预付流水
     */

    private String advanceSerial;


    /**
     * 预付款金额
     */

    private BigDecimal advanceAmount;


    /**
     * 服务模式 1 普通服务模式 2 助老服务模式
     */

    private Integer serviceModel;


    /**
     * 支付模式 1 个人支付 2 他人代付
     */

    private Integer payModel;


    /**
     * 乘车人
     */

    private String rideUuid;


    /**
     * 变更后的目的地址信息
     */

    private String changeDestAddress;

}
