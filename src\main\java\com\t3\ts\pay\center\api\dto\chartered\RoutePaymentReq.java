package com.t3.ts.pay.center.api.dto.chartered;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-13 13:48
 * @des: 行程支付入参对象
 */
@ApiModel
@Data
public class RoutePaymentReq {

    @ApiModelProperty(value = "行程uuid")
    @NotBlank(message = "行程uuid不能为空")
    private String orderUuid;

    /**
     * 优惠券uuid
     */
    @ApiModelProperty(value = "优惠券uuid")
    private String couponUuid;

    /**
     * 车费支付方式【包含余额+一种第三方支付】
     */
    @ApiModelProperty(value = "车费支付方式【包含余额+一种第三方支付】")
    @NotNull(message = "支付方式不能为空")
    private List<Integer> payTypeList;
}
