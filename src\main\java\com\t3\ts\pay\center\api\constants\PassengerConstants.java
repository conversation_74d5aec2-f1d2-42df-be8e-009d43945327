package com.t3.ts.pay.center.api.constants;

import com.t3.ts.constant.Constants;

/**
 * 乘客常数
 *
 * <AUTHOR>
 * @since 2020/10/26
 */
public final class PassengerConstants extends Constants {

    public static final String ROUTE_PLAN_UUID_NOT_EXIST = "行程计划id不存在";

    /**
     * /**
     * 登录后，乘客uuid
     */
    public static final String REQUEST_ATTRIBUTE_PASSENGER_UUID = "uuid";
    /**
     * 登录后，乘客手机号码
     */
    public static final String REQUEST_ATTRIBUTE_PASSENGER_MOBILE = "mobile";

    public static final String SHIFU = "师傅";

    public static final String SIJI_SHIFU = "司机师傅";

    /**
     * 重新开票
     */
    public static final Integer BILLTYPE_REDONE = 2;

    /**
     * 普通billtype 开票
     */
    public static final Integer BILLTYPE_COMMON = 1;

    public static final String TAKE_CAR_MONEY = "打车金";

    /**
     * 巴伦斯礼品卡
     */
    public static final String BALENCE_GIFTCARD = "礼品卡";

    /**
     * 巴伦斯积分
     */
    public static final String BALENCE_INTEGRAL = "T币";

    /**
     * 积分抵扣：行程费用满%s元，T币满%s可用
     */
    public static final String DEDUCT_POINTS_POINTS_AND_COST_LIMIT = "行程费用满%s元，T币满%s可用";
    /**
     * 积分抵扣：可用%sT币，抵￥%s元
     */
    public static final String DEDUCT_POINTS_CAN_PAY = "可用%sT币，抵￥%s元";

    public static final String AUTH_USERID = "authuserid";

    /**
     * 未开票路线
     */
    public static final String ROUTE_UNBILLED = "0";

    public static final String WXALREADYSIGN = "wxAlreadySign";
}
