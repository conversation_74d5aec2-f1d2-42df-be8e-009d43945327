package com.t3.ts.pay.center.api.rest.risk;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 风控校验入参
 * <AUTHOR>
 */
@Data
@Builder
public class RiskCheckReqParamDto implements Serializable {

    private static final long serialVersionUID = 4281165150058478455L;

    /**
     * 支付时间 - 时间戳
     */
    private Long payTime;
    /**
     * 支付时间 - yyyy-MM-dd HH:mm:ss
     */
    private String payDate;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * ua
     */
    @JSONField(name = "user-agent")
    private String userAgent;
    /**
     * APP版本号
     */
    private String appVersion;
    /**
     * IP - 外网ip，非内网ip
     */
    private String ip;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 设备指纹token - 顶象设备指纹服务生成
     */
    private String riskDeviceToken;
    /**
     * 终端类型
     */
    private Integer terminalType;
    /**
     * 礼品卡面额 - 面额精确到分
     */
    private String giftCardDenomination;
    /**
     * 购买数量
     */
    private Integer purchaseCount;
    /**
     * 卡类型 - 1-电子卡、2-实物卡
     */
    private String giftCardType;
    /**
     * 订单编号 - 礼品卡订单编号
     */
    private String giftCardNumber;
    /**
     * 支付渠道 - 一网通、支付宝、微信、银联、等其他支付方式
     */
    private String payChannel;
    /**
     * 事件 - GIFTCARD_PAY
     */
    private String sceneType;
}
