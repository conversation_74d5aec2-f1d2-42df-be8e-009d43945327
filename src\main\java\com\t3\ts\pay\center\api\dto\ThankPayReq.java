package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/28/0028 13:55
 */
@Data
@ApiModel
public class ThankPayReq {

    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 行程ID
     */
    private String routeId;
    /**
     * 司机ID
     */
    private String driverId;
    /**
     * 感谢费备注
     */
    private String remark;
    /**
     * 支付方式列表
     */
    private List<Integer> payTypeList;
    /**
     * 微信小程序code-微信小程序支付
     */
    private String code;
}
