package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 行程预付款详情Dto
 *
 * <AUTHOR>
 * @date 2023/07/21
 */
@Data
public class RoutePrePayDetailDto {

    /**
     * 预付款流水
     */
    private String advanceSerial;

    /**
     * 行程uuid
     */
    private String requirementId;

    /**
     * 预付款金额
     */
    private BigDecimal prepayAmount;

    /**
     * 乘客Uuid
     */
    private String passengerUuid;

    /**
     * 城市编码
     */
    private String cityCode;
}
