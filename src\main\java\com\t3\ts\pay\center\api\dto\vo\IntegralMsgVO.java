package com.t3.ts.pay.center.api.dto.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <b>
 * IntegralMsgVO
 * 2020/7/1
 * 积分抵扣限制提示vo
 * </b>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class IntegralMsgVO implements Serializable {
    private static final long serialVersionUID = -6208448643286238066L;

    /**
     * 提示文案：Android通过拼凑的形式高亮显示数字
     */
    private String msg;

    /**
     * 文案中数字
     */
    private String key1;

    /**
     * 文案中数字
     */
    private String key2;

    /**
     * 转换后的文案：IOS可以直接对文案中的数字高亮显示
     */
    private String fullMsg;
}
