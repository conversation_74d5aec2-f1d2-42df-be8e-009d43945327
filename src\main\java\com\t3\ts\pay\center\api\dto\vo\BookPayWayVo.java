package com.t3.ts.pay.center.api.dto.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
public class BookPayWayVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 账本对应的支付渠道
     * com.t3.ts.pay.remote.constants.EnumPayOrderChannel
     */
    private Integer payChannel;

    /**
     * 余额
     */
    private BigDecimal availableBalance = BigDecimal.ZERO;

    /**
     * 账本类型
     */
    private Integer bookType;

    /**
     * 账本是否可用
     */
    private boolean canUse=true;

    /**
     * 勾选充电卡时提示
     * 提示1：充电桩不支持充电卡
     */
    private String canNotUseMsg;

    /**
     * 勾选充电卡时提示
     * 提示2：不支持支付安心保
     */
    private String insureFareCanNotUseMsg;

    /**
     * 账本名称描述
     */
    private String bookDesc;

    /**
     * 图标地址
     */
    private String icon;


    /**
     * 默认
     */
    private boolean choose = true;

}
