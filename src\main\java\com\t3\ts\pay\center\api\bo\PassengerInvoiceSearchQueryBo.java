package com.t3.ts.pay.center.api.bo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * @Author: qul
 * @Date: 2021/4/28 10:17
 */
@Getter
@Setter
public class PassengerInvoiceSearchQueryBo implements Serializable {

    private static final long serialVersionUID = -2698499646669072720L;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 发票类型（1按行程2按金额
     */
    private Integer type;

    /**
     * 状态列表
     */
    private List<Integer> statusList;

    /**
     * 发票类型(1.出行服务 2.商城)
     */
    private Integer invoiceClass;

    /**
     * 不填时是否查询所有，true：是，false 默认只查行程开票,不填时，默认给false
     */
    private Boolean allInclude = false;

}
