package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:12
 * @description:
 */
@Data
public class ChannelDiscountItemDto implements Serializable {
    private static final Integer ONE = 1;
    private static final Integer ZERO = 0;

    //同一个折扣可能适用多个车型
    private HashSet<Integer> carLevelSet;

    //具体折扣
    private BigDecimal dicount;

    //是否可以适用动态折扣
    private Boolean useDynamicDiscount;

    //是否使用渠道折扣
    private Boolean useChannelDiscount;

    /**
     * 渠道计价方式 1：实时计费，0：预估一口价
     */
    private Integer channelFareMethod;

    /**
     * 一口价附加费添加方式:
     * 1：系统预置，0：司机添加
     */
    private Integer otherFareMethod;

    //系统预置附加费金额
    private BigDecimal otherFare;

    public boolean matchCarLevel(Integer carLevel) {
        return carLevelSet != null && carLevel != null && carLevelSet.contains(carLevel);
    }

    public BigDecimal otherFareAddedBySystem() {
        if (ZERO.equals(channelFareMethod) && ONE.equals(otherFareMethod)) {
            return otherFare;
        }
        return null;

    }

}
