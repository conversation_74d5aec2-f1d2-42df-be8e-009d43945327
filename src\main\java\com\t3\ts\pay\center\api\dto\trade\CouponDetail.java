package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:54
 * @des 1.0
 */

@Data
@Getter
@Setter
public class CouponDetail {

    /**
     * 第三方渠道
     */
    private String thirdChannel;

    /**
     * 最优券可抵扣金额
     */
    private BigDecimal decutionAmount;

    /**
     * 可用优惠券列表
     */
    private List<String> canUseCoupon;

    /**
     * 券包明细
     */
    private CouponActivityDetail couponActivityDetail;

    /**
     * 账户券sourceType  给前端- 新人链路用
     */
    private String couponSourceType;

    /**
     * 活动券sourceType  给前端- 新人链路用
     */
    private String activitySourceType;

    private String scoreButton;

    private String scoreDesc;

    private String sourceType;

    private String sourceName;
    
    private String sourceIconUrl;

}
