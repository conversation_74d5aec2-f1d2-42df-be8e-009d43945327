package com.t3.ts.pay.center.api.controller.invoice.v3;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/17 17:22
 * @Description: 获取发票详情test类    /api/passenger/v3/invoice/info
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class InvoiceInfoTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void getInfoTest() {
        InvoiceReq invoiceReq = new InvoiceReq();
        invoiceReq.setUuid("3176f415d5f543ca8b698d787dd5add6");
        invoiceReq.setCurrPage(1);
        invoiceReq.setStatus(1);
        invoiceReq.setType(1);
        invoiceReq.setPageSize(10);
        invoiceBusiness.getInvoiceInfoAll(invoiceReq);
    }
}
