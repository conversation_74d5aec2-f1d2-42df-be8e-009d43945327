package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.finance.center.dto.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


@Data
public class AccountSumDwyQueryDto extends Page implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("1日2周3月")
    @NotNull(message="dateType must")
    private Integer dateType;
    @ApiModelProperty("日期2022-08-01 00:00:00")
    @NotNull(message="startDate must")
    private Date startDate;
    @ApiModelProperty("日期2022-08-07 23:59:59")
    @NotNull(message="endDate must")
    private Date endDate;


}
