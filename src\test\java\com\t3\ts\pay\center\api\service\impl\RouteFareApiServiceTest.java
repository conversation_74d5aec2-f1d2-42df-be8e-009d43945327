package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.pay.center.api.dto.vo.RouteFareDetailVo;
import com.t3.ts.pay.center.api.service.RouteFareApiService;
import com.t3.ts.pay.center.api.service.SignService;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 16:20
 */
public class RouteFareApiServiceTest {
    @Mock
    Logger log;
    @InjectMocks
    RouteFareApiService routeFareApiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOpenSecretFree() {
        RouteFareItemReq req = new RouteFareItemReq();
        req.setRoutePlanUuid("");
        Response<RouteFareDetailVo> response = routeFareApiService.getOrderFareItems(req);
        Assert.assertTrue(response.isSuccess());
    }
}
