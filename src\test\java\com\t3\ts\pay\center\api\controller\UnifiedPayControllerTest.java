package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.service.impl.RouteService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2020/5/15, 0015 9:30
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedPayControllerTest {
    @Mock
    RouteService routeService;
    @Mock
    Logger log;
    @InjectMocks
    UnifiedPayController unifiedPayController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUnified() throws Exception {
        try {
            unifiedPayController.unified(null, null);
        } catch (Exception e) {
        }
    }

    @Test
    public void testUnified01() throws Exception {
        try {
            unifiedPayController.unified(null, null);
        } catch (Exception e) {
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme