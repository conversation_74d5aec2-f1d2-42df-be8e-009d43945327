package com.t3.ts.pay.center.api.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <b>
 * PayCenterMethodEnum
 * 2020/6/30
 * 支付结算pay-center-api接口枚举
 * </b>
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PayCenterMethodEnum {
    /**
     * 包车收银台
     */
    PAY_CENTER_METHOD_PAY_DESK("t3pay.pay.paydesk.fast", "包车收银台"),
    /**
     * 积分现金比例
     */
    PAY_CENTER_METHOD_EXCHANGE_POINTS("t3pay.pay.integral.value", "积分现金比例");

    /**
     * 接口名称
     */
    private final String method;

    /**
     * 接口描述
     */
    private final String desc;
}
