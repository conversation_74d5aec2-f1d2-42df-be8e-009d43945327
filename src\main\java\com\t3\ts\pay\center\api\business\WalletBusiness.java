package com.t3.ts.pay.center.api.business;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.constants.PersonSuperviseStrategyEnum;
import com.t3.ts.account.center.dto.PayAccountBookDto;
import com.t3.ts.account.center.dto.PayAccountDto;
import com.t3.ts.account.center.dto.PersonSuperviseAccountDto;
import com.t3.ts.account.center.dto.WalletAccountDto;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.account.center.service.AccountSuperviseService;
import com.t3.ts.account.center.service.PersonSuperviseService;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.interactive.exception.ExceptionUtils;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.BasePageReq;
import com.t3.ts.pay.center.api.dto.CashRefundDto;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.pay.center.api.dto.vo.PayAccountBookVo;
import com.t3.ts.pay.center.api.dto.wallet.AccountFlowDto;
import com.t3.ts.pay.center.api.dto.wallet.AmountWalletVo;
import com.t3.ts.pay.center.api.dto.wallet.QuotaDto;
import com.t3.ts.pay.center.api.dto.wallet.ThirdWalletVo;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.util.AccountUtils;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.pay.center.api.util.MoneyConvert;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.AccountFlowPage;
import com.t3.ts.pay.remote.dto.CashRefundQueryDto;
import com.t3.ts.pay.remote.dto.QryConsumeRecordDto;
import com.t3.ts.pay.remote.dto.report.ConsumeRecordDto;
import com.t3.ts.pay.remote.service.RefundQueryService;
import com.t3.ts.pay.remote.service.report.PayBusiReportService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.PreRechargeRefundDto;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.pay.common.util.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WalletBusiness.java
 * @Description TODO
 * @createTime 2021年08月02日 18:32:00
 */
@Slf4j
@Component
public class WalletBusiness {

    @DubboReference
    private PayBusiReportService payBusiReportService;
    @DubboReference
    private AccountService accountService;
    @Autowired
    private MarketingRest marketingRest;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private PersonSuperviseService personSuperviseService;
    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;
    @DubboReference
    private AccountSuperviseService accountSuperviseService;
    @DubboReference
    private RefundQueryService refundQueryService;
    @DubboReference
    private SettlementRechargeService settlementRechargeService;
    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    /**
     * 用户未绑卡code
     */
    private static final String UNBIND_CARD = "202";
    private static final String ACTIVE = "ACTIVE";

    /**
     * 乘客账户流水
     *
     * @param basePageReq   基本请求信息
     * @param passengerUuid 乘客id
     * @return {@link Response}
     */
    public Response getAccountFlowPassenger(BasePageReq basePageReq, String passengerUuid) {

        AccountFlowPage accountFlowPage = new AccountFlowPage();
        accountFlowPage.setUserId(passengerUuid);
        accountFlowPage.setCurrPage(basePageReq.getCurPage());
        accountFlowPage.setPageSize(basePageReq.getPageSize());
        if (null != basePageReq.getAccountType()) {
            accountFlowPage.setPayAccountTypeEnum(PayAccountTypeEnum.getEnumByType(basePageReq.getAccountType()));
        }
        if (null != basePageReq.getBookType()) {
            accountFlowPage.setBookTypeEnum(BookTypeEnum.parse(basePageReq.getBookType()));
        }
        Response<PageResult<ConsumeRecordDto>> response = payBusiReportService
                .getConsumeRecord(accountFlowPage);
        if (!response.isSuccess() || response.getData() == null) {
            return Response.createError("查询余额明细流水失败：" + response.getMsg());
        }

        List<ConsumeRecordDto> pageResult = response.getData().getList();
        if (CollectionUtils.isEmpty(pageResult)) {
            PageResult<List> page = new PageResult<>();
            page.setTotalCount(0);
            page.setList(new ArrayList<>());
            page.setCurrPage(basePageReq.getCurPage());
            page.setPageSize(basePageReq.getPageSize());
            return Response.createSuccess("查询余额明细成功", page);
        }

        List<AccountFlowDto> accountFlowDtos = new ArrayList<>();
        pageResult.forEach(consumeRecordDto -> {
            AccountFlowDto accountFlowDto = new AccountFlowDto();
            accountFlowDto.setChangedBalance(AccountUtils.intToBigDecimal(consumeRecordDto.getChangedBalance()));
            Integer bookType = consumeRecordDto.getBookType();
            if (null != bookType) {
                if (BookTypeEnum.CASH_BOOK.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("现金");
                } else if (BookTypeEnum.SCORE_BOOK.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("积分");
                } else if (BookTypeEnum.GIFT_BOOK.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("打车金");
                } else if (BookTypeEnum.PREPAYMENT_BOOK.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("预付款");
                } else if (BookTypeEnum.GIFT_CARD.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("礼品卡");
                } else if (BookTypeEnum.CASH_GIVE_BOOK.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("赠金");
                } else if (BookTypeEnum.COMPANY_GIFT_CARD.getType() == bookType) {
                    accountFlowDto.setChangedTypeName("企业礼品卡");
                }
            }
            accountFlowDto.setFlowType(consumeRecordDto.getFlowType());
            accountFlowDto.setChangedType(consumeRecordDto.getChangedType());
            if (null != consumeRecordDto.getCreateTime()) {
                accountFlowDto.setCreateTime(DateUtils.getDateTimeFormat(consumeRecordDto.getCreateTime()));
            }
            accountFlowDto.setPayChannel(consumeRecordDto.getPayChannel());
            accountFlowDtos.add(accountFlowDto);
        });

        PageResult<AccountFlowDto> page = new PageResult<>();
        page.setTotalCount(response.getData().getTotalCount());
        page.setHasMore(response.getData().isHasMore());
        page.setList(accountFlowDtos);
        page.setCurrPage(basePageReq.getCurPage());
        page.setPageSize(basePageReq.getPageSize());
        return Response.createSuccess("查询余额明细成功", page);
    }

    /**
     * 账户信息
     *
     * @param dto 參數
     * @return {@link Response<AmountWalletVo>}
     */
    public Response<AmountWalletVo> accountInfo(QueryDto dto) {
        AmountWalletVo walletVo = new AmountWalletVo();
        Response<WalletAccountDto> accountResponse = accountService.getAccountWithExpireGive(dto.getUserId());
        if (Boolean.TRUE.equals(accountResponse.isSuccess()) && Objects.nonNull(accountResponse.getData())) {
            WalletAccountDto accountDto = accountResponse.getData();
            // 现金
            walletVo.setCash(String.valueOf(accountDto.getAccountCash()));
            //预付款
            BigDecimal prePayCash = MoneyConvert.intOrNullConvertBig(accountDto.getPrePayCash());
            walletVo.setPrePayCash(prePayCash.toPlainString());
            //充值现金
            BigDecimal charCash = MoneyConvert.intOrNullConvertBig(accountDto.getRechargeCash());
            walletVo.setRechargeCash(charCash.toPlainString());
            // 赠送币
            BigDecimal accountGiftMoney = MoneyConvert.intOrNullConvertBig(accountDto.getAccountGiftMoney());
            // 礼品卡
            BigDecimal accountGiftCardMoney = MoneyConvert.intOrNullConvertBig(accountDto.getAccountGiftCardMoney());
            // 企业礼品卡
            BigDecimal accountCompanyGiftCardMoney =
                    MoneyConvert.intOrNullConvertBig(accountDto.getAccountCompanyGiftCardMoney());

            // 赠送币
            walletVo.setGive(accountGiftMoney.toPlainString());
            // 礼品卡
            walletVo.setGift(accountGiftCardMoney.add(accountCompanyGiftCardMoney).toPlainString());
            // 即将失效打车金
            walletVo.setGiveToExpire(MoneyConvert.intOrNullConvertBig(accountDto.getGiveToExpire()).toPlainString());
            // 赠金
            BigDecimal casGive = MoneyConvert.intOrNullConvertBig(accountDto.getRechargeGift());
            walletVo.setRechargeCashGive(casGive.toString());
            // 现金 + 赠金
            walletVo.setTotalCashAndGive(charCash.add(casGive).toString());
            // 余额
            walletVo.setBalance(accountGiftMoney.add(accountGiftCardMoney)
                    .add(accountCompanyGiftCardMoney).add(charCash).add(casGive)
                    .add(prePayCash).toPlainString());

        }
        JSONObject memberInfo = marketingRest.getMemberInfo(dto.getUserId());
        if (!memberInfo.isEmpty()
                && memberInfo.get("totalAccount") != null) {
            walletVo.setIntegral(memberInfo.getBigDecimal("totalAccount").stripTrailingZeros().toPlainString());
            walletVo.setTipContent(memberInfo.getString("tipContent"));
            walletVo.setSidebarSwitch(memberInfo.getBoolean("sidebarSwitch"));
            walletVo.setSaveMoneySwitch(memberInfo.getBoolean("saveMoneySwitch"));
        }
        // 权益卡数量
        if (!memberInfo.isEmpty()
                && memberInfo.get("privilegeCount") != null) {
            walletVo.setRightsCount(memberInfo.getBigDecimal("privilegeCount")
                    .stripTrailingZeros().toPlainString());
        }
        //顺风车车主收益
        walletVo.setPickrideProfit("0.00");
        //打车金是否开启标识
        walletVo.setGiveFareOpen(switchConfig.getGiveCanUse());

        // 充值开关 余额大于0 默认开
        if (NumberUtil.toBigDecimal(walletVo.getTotalCashAndGive()).compareTo(BigDecimal.ZERO) > 0) {
            walletVo.setRechargeSwitch(Boolean.TRUE);
        } else {
            // 查询营销  判断是否开启充值开关
            walletVo.setRechargeSwitch(marketingRest.getRechargeSwitch(dto.getAdCode(), dto.getUserId()));
        }
        // 乘推乘
        walletVo.setRecommendPassenger(marketingRest.getRecommendPassenger(dto));
        return Response.createSuccess(walletVo);
    }


    /**
     * 三方账户信息
     *
     * @param dto 參數
     * @return {@link Response<ThirdWalletVo>}
     */
    public Response<ThirdWalletVo> thirdInfo(QueryDto dto) {
        ThirdWalletVo walletVo = new ThirdWalletVo();
        QuotaDto quotaDto = marketingRest.quotaStatusQuery(dto.getUserId());
        if (null != quotaDto) {
            // 产品定义，"status" 不等于 "ACTIVE"的时候，availableAmount兜底给个20w
            if (!ACTIVE.equalsIgnoreCase(quotaDto.getStatus())) {
                quotaDto.setAvailableAmount(switchConfig.getQuotaAvailableAmount());
            }
            walletVo.setQuota(quotaDto);
        }
        return Response.createSuccess(walletVo);
    }

    /**
     * 获取乘客现金账户流水
     *
     * @param queryDto queryDto
     * @return Response
     */
    public Response getAccountCashFlowPassenger(QueryDto queryDto) {
        String userId = queryDto.getUserId();
        Integer accountType = queryDto.getAccountType();
        if (StringUtils.isEmpty(userId) || null == accountType || null == queryDto.getPageSize()
                || null == queryDto.getCurrentPage()) {
            return Response.createError("必传参数为空");
        }
        QryConsumeRecordDto qryConsumeRecordDto = new QryConsumeRecordDto();
        qryConsumeRecordDto.setUserId(userId);
        qryConsumeRecordDto.setCurrPage(queryDto.getCurrentPage());
        qryConsumeRecordDto.setPageSize(queryDto.getPageSize());
        qryConsumeRecordDto.setChangedType(queryDto.getChangedType());
        if (queryDto.getFlowTypes() != null) {
            qryConsumeRecordDto.setFlowTypes(Arrays.asList(queryDto.getFlowTypes()));
        }
        qryConsumeRecordDto.setBookTypes(Arrays.asList(queryDto.getBookTypes()));
        Response<PageResult<ConsumeRecordDto>> response = payBusiReportService.getConsumeRecord(qryConsumeRecordDto);
        if (!response.isSuccess() || response.getData() == null) {
            return Response.createError("查询余额明细流水失败：" + response.getMsg());
        }

        List<ConsumeRecordDto> pageResult = response.getData().getList();
        if (CollectionUtils.isEmpty(pageResult)) {
            PageResult<List> page = new PageResult<>();
            page.setTotalCount(0);
            page.setList(new ArrayList<>());
            page.setCurrPage(queryDto.getCurrentPage());
            page.setPageSize(queryDto.getPageSize());
            return Response.createSuccess("查询余额明细成功", page);
        }
        List<AccountFlowDto> accountFlowDtos = new ArrayList<>();
        pageResult.forEach(consumeRecordDto -> {
            AccountFlowDto accountFlowDto = new AccountFlowDto();
            accountFlowDto.setChangedBalance(AccountUtils.intToBigDecimal(consumeRecordDto.getChangedBalance()));
            final Integer bookType = consumeRecordDto.getBookType();
            if (bookType != null) {
                if (bookType.equals(BookTypeEnum.CASH_BOOK.getType())) { // 现金账本
                    // 支入支出类型  1.收入 2.支出
                    if (consumeRecordDto.getChangedType() == 1) { // 收入只有本金充值
                        if (consumeRecordDto.getFlowType().equals(EnumPayOrderType.CASH_RECHARGE.getCode())) {
                            accountFlowDto.setChangedTypeName("充值本金");
                        } else {
                            accountFlowDto.setChangedTypeName("行程退款-本金");
                        }
                    } else {
                        // 支出有 本金退款和消费
                        if (consumeRecordDto.getFlowType().equals(EnumPayOrderType.CASH_RECHARGE_REFUND.getCode())) {
                            accountFlowDto.setChangedTypeName("充值退款");
                        } else {
                            accountFlowDto.setChangedTypeName("消费本金-打车");
                        }
                    }
                }
                if (bookType.equals(BookTypeEnum.CASH_GIVE_BOOK.getType())) { // 赠金账本
                    // 支入支出类型  1.收入 2.支出
                    if (consumeRecordDto.getChangedType() == 1) {
                        if (consumeRecordDto.getFlowType().equals(EnumPayOrderType.CASH_RECHARGE_GIVE.getCode())) {
                            accountFlowDto.setChangedTypeName("充值赠金");
                        } else {
                            accountFlowDto.setChangedTypeName("行程退款-赠金");
                        }
                    } else {
                        if (consumeRecordDto.getFlowType().equals(EnumPayOrderType.CASH_RECHARGE_REFUND.getCode())) {
                            accountFlowDto.setChangedTypeName("赠金收回");
                        } else if (consumeRecordDto.getFlowType().equals(NumConstant.NUM_99999)) {
                            accountFlowDto.setChangedTypeName("赠金过期");
                        } else {
                            accountFlowDto.setChangedTypeName("消费赠金-打车");
                        }
                    }
                }
            }
            accountFlowDto.setChangedType(consumeRecordDto.getChangedType());
            if (null != consumeRecordDto.getCreateTime()) {
                accountFlowDto.setCreateTime(DateUtils.getDateTimeFormat(consumeRecordDto.getCreateTime()));
            }
            accountFlowDto.setPayChannel(consumeRecordDto.getPayChannel());
            accountFlowDtos.add(accountFlowDto);
        });

        PageResult<AccountFlowDto> page = new PageResult<>(accountFlowDtos, response.getData().getTotalCount(),
                queryDto.getPageSize(), queryDto.getCurrentPage());
        page.setHasMore(response.getData().isHasMore());
        return Response.createSuccess("查询余额明细成功", page);
    }

    /**
     * 获取乘客账户绑卡信息
     *
     * @param queryDto queryDto
     * @return Response
     */
    public Response bindCardInfo(QueryDto queryDto) {
        String userId = queryDto.getUserId();
        Integer accountType = queryDto.getAccountType();
        if (StringUtils.isEmpty(userId) || null == accountType) {
            return Response.createError("必传参数为空");
        }
        PersonSuperviseAccountDto dto = new PersonSuperviseAccountDto();
        dto.setUserId(userId);
        dto.setAccountType(accountType);
        Response response = accountSuperviseService.queryBindCardInfo(dto);
        if (response.getSuccess() && null == response.getData()) {
            Response resp = Response.createSuccess("用户未绑卡");
            resp.setBizCode(UNBIND_CARD);
            return resp;
        }
        return response;
    }

    /**
     * 绑定用户银行卡
     *
     * @param bindCardInfo 绑卡信息
     * @return Response
     */
    public Response bindCard(PersonSuperviseAccountDto bindCardInfo) {
        if (StringUtils.isEmpty(bindCardInfo.getUserId()) || null == bindCardInfo.getAccountType()
                || StringUtils.isEmpty(bindCardInfo.getBankNum()) || StringUtils.isEmpty(bindCardInfo.getBankName())) {
            return Response.createError("必传参数为空");
        }
        bindCardInfo.setBizType(PersonSuperviseStrategyEnum.PERSON_SUPERVISE_BIND_CARD.getBizType());
        //绑卡
        Response resp = personSuperviseService.process(bindCardInfo);
        return resp;
    }


    /**
     * 查询余额详情 同时检查开户状态
     *
     * @param queryDto queryDto
     * @return Response
     */
    public Response<AmountWalletVo> cashDetail(QueryDto queryDto) {
        AmountWalletVo amountWalletVo = new AmountWalletVo();
        String userId = queryDto.getUserId();
        if (StringUtils.isEmpty(userId) || queryDto.getAccountType() == null) {
            return Response.createError("必传参数为空");
        }
        //查询余额
        Response<PayAccountDto> balanceResp = unifiedAccountFacade.getAccountBalance(userId,
                PayAccountTypeEnum.PASSENGER_TYPE);
        if (!balanceResp.getSuccess() || null == balanceResp.getData()) {
            return Response.createError("查询现金账户失败");
        }
        PayAccountDto payAccountDto = balanceResp.getData();
        final List<PayAccountBookDto> books = payAccountDto.getBooks();
        final Long cAmount = books.stream().filter(book -> book.getBookType().equals(BookTypeEnum.CASH_BOOK.getType()))
                .map(PayAccountBookDto::getAvailableBalance).reduce(Long.valueOf(NumConstants.STR_0), Long::sum);
        final Long give = books.stream().filter(book -> book.getBookType()
                        .equals(BookTypeEnum.CASH_GIVE_BOOK.getType())).map(PayAccountBookDto::getAvailableBalance)
                .reduce(Long.valueOf(NumConstants.STR_0), Long::sum);
        // 现金余额
        amountWalletVo.setRechargeCash(MoneyConvert.intOrNullConvertBig(cAmount.intValue()).toString());
        // 赠金余额
        amountWalletVo.setRechargeCashGive(MoneyConvert.intOrNullConvertBig(give.intValue()).toString());
        return Response.createSuccess(amountWalletVo);
    }


    /**
     * 查询用户指定账户类型及账本类型的余额
     *
     * @param queryDto 查询条件
     * @return Response
     */
    public Response<PayAccountBookVo> bookBalance(QueryDto queryDto) {
        String userId = queryDto.getUserId();
        if (null == queryDto.getBookType() || StringUtils.isEmpty(userId) || null == queryDto.getAccountType()) {
            return Response.createError("必传参数为空");
        }
        AmountWalletVo amountWalletVo = new AmountWalletVo();
        //查询余额
        Response<PayAccountDto> balanceResp = unifiedAccountFacade.getAccountBalance(userId,
                PayAccountTypeEnum.getEnumByType(queryDto.getAccountType()),
                BookTypeEnum.parse(queryDto.getBookType())
        );
        if (!balanceResp.getSuccess() || null == balanceResp.getData()) {
            return Response.createError("查询现户失败");
        }
        PayAccountDto payAccountDto = balanceResp.getData();
        List<PayAccountBookDto> books = payAccountDto.getBooks();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(books)) {
            return Response.createError("查询账户为空");
        }
        return Response.createSuccess(new PayAccountBookVo(books.get(0)));
    }

    /**
     * @param queryDto queryDto
     * @return Response
     */
    public Response<?> openAcc(QueryDto queryDto) {
        PersonSuperviseAccountDto dto = new PersonSuperviseAccountDto();
        dto.setUserId(queryDto.getUserId());
        dto.setAccountType(queryDto.getAccountType());
        dto.setBizType(PersonSuperviseStrategyEnum.PERSON_SUPERVISE_APPLY_ON.getBizType());
        //查询用户开户信息
        return personSuperviseService.process(dto);
    }

    /**
     * 新版本查询用户预付款进度接口
     *
     * @param userId 用户id
     * @return Response
     */
    public Response<?> prePayTimeLine(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return Response.createError("参数错误");
        }
        return settlementRechargeService.getRechargeTimeLine(userId);
    }

    /**
     * 用户手动退款
     *
     * @param userId 用户id
     * @param mobile mobile
     * @return Response
     */
    public Response<?> prePayRefund(String userId, String mobile) {
        boolean refundFlag = false;
        try {
            // 发起退款
            PreRechargeRefundDto dto = new PreRechargeRefundDto();
            dto.setUserId(userId);
            dto.setType("3");
            Response<Boolean> booleanResp = settlementGeneralService.preRechargeRefund(dto);
            if (booleanResp != null && booleanResp.getCode() != null
                    && NumConstant.NUM_99999 == booleanResp.getCode()) {
                return booleanResp;
            } else if (booleanResp != null && Boolean.TRUE.equals(booleanResp.getData())) {
                refundFlag = true;
            }
            log.info("用户手动退款.userId:{}.mobile:{}.", userId, mobile);
        } catch (Exception e) {
            log.error("用户手动退款异常.cause:{}", ExceptionUtils.getFullStackTrace(e));
        }
        if (!refundFlag) {
            return Response.createError("失败");
        }
        return Response.createSuccess("退款成功");
    }

    /**
     * 校验账本可用余额
     *
     * @param userId userId
     * @return boolean
     */
    private boolean checkPrePayBookRefundBalance(String userId) {
        Response<PayAccountDto> balanceResp = unifiedAccountFacade.getAccountBalance(userId,
                PayAccountTypeEnum.PASSENGER_TYPE, BookTypeEnum.PREPAYMENT_BOOK);
        if (balanceResp == null || !balanceResp.getSuccess() || null == balanceResp.getData()
                || CollectionUtils.isEmpty(balanceResp.getData().getBooks())) {
            log.error("checkPrePayBookRefundBalance 查无账本数据.userId:{} ", userId);
            return false;
        }
        List<PayAccountBookDto> books = balanceResp.getData().getBooks();
        PayAccountBookDto rechargeBook = books.get(0);
        if (rechargeBook.getAvailableBalance() <= 0) {
            log.error("checkPrePayBookRefundBalance 可退款金额不足."
                    + "rechargeBook:{}.userId:{} ", JSON.toJSONString(rechargeBook), userId);
            return false;
        }
        return true;
    }

    /**
     * 充值退款
     *
     * @param cashRefundDto cashRefundDto
     * @return Response
     */
    public Response cashRefund(CashRefundDto cashRefundDto) {
        log.info("WalletBusiness.cashRefund.dto={}", JSON.toJSONString(cashRefundDto));
        try {
            SettlementGeneralDto srOrderDto = BeanUtils.propertiesCopy(cashRefundDto, SettlementGeneralDto.class);
            Response<String> response = settlementGeneralService.addSettlement(srOrderDto);
            if (!response.isSuccess()) {
                return Response.createError(response.getMsg(), ResultErrorEnum.UNIFY_ERROR_CODE.getCode());
            }
            log.info("WalletBusiness.cashRefund.dto={},response={}", JSON.toJSONString(cashRefundDto),
                    JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.info("WalletBusiness.cashRefund.dto={}", JSON.toJSONString(cashRefundDto), e);
            return Response.createError("新增通用结算单-失败-异常", ResultErrorEnum.UNIFY_ERROR_CODE.getCode());
        }
    }

    /**
     * 充值退款查询
     *
     * @param dto cashRefundDto
     * @return Response
     */
    public Response<PageResult<CashRefundQueryDto>> cashRefundQuery(CashRefundQueryDto dto) {
        return refundQueryService.queryCashRefund(dto);
    }
}
