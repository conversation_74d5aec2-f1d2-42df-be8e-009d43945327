package com.t3.ts.pay.center.api.config.filter;

import org.apache.dubbo.rpc.Result;
import org.apache.dubbo.rpc.RpcException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/28 17:18
 */
public class ConsumerLogFilterTest {
    @Mock
    Logger log;
    @InjectMocks
    ConsumerLogFilter consumerLogFilter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testInvoke() throws Exception {
        try {
            Result result = consumerLogFilter.invoke(null, null);
        } catch (RpcException e) {
            e.printStackTrace();
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
