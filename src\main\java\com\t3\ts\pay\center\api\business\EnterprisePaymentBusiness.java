package com.t3.ts.pay.center.api.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.member.account.remote.dto.AccountInfoDto;
import com.t3.ts.member.account.remote.service.AccountInfoService;
import com.t3.ts.org.manager.applyorder.dto.ApplyOrderDetailsDto;
import com.t3.ts.org.manager.applyorder.service.ApplyOrderService;
import com.t3.ts.pay.center.api.bo.*;
import com.t3.ts.pay.center.api.business.common.CommonPaymentBusiness;
import com.t3.ts.pay.center.api.cache.RoutePlanKey;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.constants.*;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.chartered.*;
import com.t3.ts.pay.center.api.dto.enterprise.PayReq;
import com.t3.ts.pay.center.api.dto.enterprise.RechargeReq;
import com.t3.ts.pay.center.api.dto.enterprise.RequisitionInfoVo;
import com.t3.ts.pay.center.api.dto.trade.PayDeskReq;
import com.t3.ts.pay.center.api.dto.trade.RechargePayVoV2;
import com.t3.ts.pay.center.api.dto.vo.IntegralMsgVO;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.util.BigDecimalUtils;
import com.t3.ts.pay.center.api.util.ExchangePointsUtils;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.dto.UnifiedDto;
import com.t3.ts.pay.remote.dto.marketing.IntegralDeductDto;
import com.t3.ts.pay.remote.dto.marketing.LadderDiscountDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业用车相关的乘客支付充值逻辑
 *
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: EnterprisePaymentBusiness
 * @Package com.t3.ts.pay.center.api.business
 * @date 2021/9/15 10:58
 */
@Slf4j
@Component
public class EnterprisePaymentBusiness {

    @DubboReference
    private UnifiedService unifiedService;
    @Autowired
    private CommonPaymentBusiness commonPaymentBusiness;
    @DubboReference
    private AccountInfoService accountInfoService;
    @Resource
    private T3CacheFactory cacheFactory;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;
    @Resource
    private PayChannelFactory payChannelFactory;
    @DubboReference
    private AccountService accountService;
    @DubboReference
    private ApplyOrderService applyOrderService;
    @DubboReference
    private SrOrderService srOrderService;

    /**
     * 收银台pro
     *
     * @param req req
     * @return Response
     */
    public Response cashierDeskPro(PayTradeCharteredReq req) {
        CashierDeskProVo cashierDeskProVo = new CashierDeskProVo();
        cashierDeskProVo.setRoutePlanUuid(req.getRoutePlanUuid());

        StopWatch stopWatch = new StopWatch();
        // 司机信息
        DriverInfoBo driverInfoBo = commonPaymentBusiness.getDriverInfo(req.getRoutePlanUuid(), stopWatch);
        final Integer code = 5009;
        if (!RouteStatus.ROUTE_6.getStatus().equals(driverInfoBo.getRouteStatus())) {
            return Response.createError("订单为非待支付状态", code);
        }
        cashierDeskProVo.setDriverName(driverInfoBo.getDriverName());
        cashierDeskProVo.setPlateNum(driverInfoBo.getPlateNum());

        // 获取收银台基础费用
        Response<CashierDeskBaseFareBo> response = getBaseFare(req);
        if (!response.isSuccess()) {
            return response;
        }
        CashierDeskBaseFareBo baseFareBo = response.getData();

        cashierDeskProVo.setPayTypeList(req.getPayTypeList());

        cashierDeskProVo.setAdditionalFee(baseFareBo.getAdditionalFee());
        cashierDeskProVo.setBalanceTotal(String.valueOf(baseFareBo.getBalanceTotal()));
        cashierDeskProVo.setPayDeskType(commonPaymentBusiness.getPayDeskType(baseFareBo.getPrPayed()));

        // 获取积分抵扣
        PayDeskInfoVo cashierDeskVo = getDeductPoints(
                baseFareBo.getIntegralPayed(), baseFareBo.getAdditionalFee(), baseFareBo.getRemainPay());
        cashierDeskProVo.setIntegralDeductFlag(cashierDeskVo.getIntegralDeductFlag());
        cashierDeskProVo.setIntegralMsg(cashierDeskVo.getIntegralMsg());
        cashierDeskProVo.setAvailableIntegral(new BigDecimal(cashierDeskVo.getAvailableIntegral()));

        // 积分可抵扣费用
        BigDecimal deductCost = BigDecimalUtils.bigDecimalOf(cashierDeskVo.getDeductCost());
        // 余额可支付金额
        BigDecimal balancePayable = baseFareBo.getBalancePayable();
        // 余额信息
        CashierDeskProVo balanceInfo = commonPaymentBusiness.getBalanceInfo(
                baseFareBo, balancePayable, req.getPayTypeList(), deductCost);
        cashierDeskProVo.setBalanceMsg(balanceInfo.getBalanceMsg());
        cashierDeskProVo.setBalanceFlag(balanceInfo.getBalanceFlag());
        cashierDeskProVo.setAvailableBalance(balanceInfo.getAvailableBalance());
        cashierDeskProVo.setPayItemList(getPayItemList(baseFareBo));
        cashierDeskProVo.setActualFare(baseFareBo.getRemainPay());
        cashierDeskProVo.setFinalFare(commonPaymentBusiness.getFinalFare(
                baseFareBo, req.getPayTypeList(), balancePayable, deductCost));

        return Response.createSuccess(cashierDeskProVo);
    }

    /**
     * 个人支付新接口 都支持现在的用车类型 pay方法先冗余着
     *
     * @param payReq        payReq
     * @param passengerUuid passengerUuid
     * @param type          type
     * @return Response
     */
    public Response payForAllBusiness(PayReq payReq, String passengerUuid, String type) {
        // 获取行程信息
        Response routeDetailResponse = getRouteDetailResponse(payReq.getRoutePlanUuid());
        if (Objects.nonNull(routeDetailResponse)) {
            return routeDetailResponse;
        }

        PaymentDto paymentDto = new PaymentDto();
        Integer bizType = getPayOrderType(type);
        paymentDto.setBizType(bizType);
        paymentDto.setUserId(passengerUuid);
        paymentDto.setBizId(payReq.getRoutePlanUuid());
        paymentDto.setPaywayEnums(PayWayConvert.getPayWayEnum(payReq.getPayTypeList()));
        // 积分抵扣：通过缓存获取可抵扣积分
        boolean hasIntegralType = payReq.getPayTypeList().parallelStream()
                .anyMatch(payType -> payType.equals(EnumPayOrderChannel.INTEGRAL.getCode()));
        if (hasIntegralType) {
            Response<Map<String, Object>> deductRes = deductPoints4Pay(passengerUuid);
            if (Boolean.FALSE.equals(deductRes.isSuccess())) {
                return Response.createError(deductRes.getMsg());
            }
            paymentDto.setExtendParams(deductRes.getData());
        }
        Response<String> response = unifiedPaymentFacade.pay(paymentDto);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg());
        }
        // 余额支付成功
        if (response.getData() == null) {
            RechargePayVoV2 payVo = new RechargePayVoV2();
            payVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            payVo.setIsNoSecretPayment(NumConstants.STR_0);
            return Response.createSuccess("获取支付信息成功", payVo);
        }
        int payType = getPayType(payReq.getPayTypeList());
        //是否免密支付
        boolean isNoSecret = false;
        if (PayConstants.NO_SEC_PAY.equals(String.valueOf(response.getCode()))) {
            isNoSecret = true;
        }
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret)
                .paymentResp(response)
                .passengerUuid(passengerUuid)
                .passengerMobile(RequestContextHelper.getPassengerMobile())
//                .aggPayTypeList(new ArrayList<>())
                .build();
        return returnPayInfo(payType, paymentInfoBo);
    }

    /**
     * 企业个人支付充值
     *
     * @param req           req
     * @param passengerUuid passengerUuid
     * @return Response
     */
    public Response recharge(RechargeReq req, String passengerUuid) {
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(EnumPayOrderType.RECHARGE.getCode());
        paymentDto.setUserId(passengerUuid);
        paymentDto.setSettlementId(req.getSettlementId());

        List<Integer> payTypeList = Lists.newArrayList(req.getRechargePayType());
        paymentDto.setPaywayEnums(PayWayConvert.getPayWayEnum(payTypeList));

        Response<String> response = unifiedPaymentFacade.pay(paymentDto);
        if (!response.isSuccess()) {
            return Response.createError(response.getMsg());
        }

        int payType = getPayType(payTypeList);
        //是否免密支付
        boolean isNoSecret = false;
        if (PayConstants.NO_SEC_PAY.equals(String.valueOf(response.getCode()))) {
            isNoSecret = true;
        }
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret)
                .paymentResp(response)
                .passengerUuid(passengerUuid)
                .passengerMobile(RequestContextHelper.getPassengerMobile())
//                .aggPayTypeList(new ArrayList<>())
                .build();
        Response<RechargePayBo> payResponse = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg());
        }

        RechargePayBo payInfo = payResponse.getData();
        if (NumConstants.STR_500.equals(payInfo.getCode())) {
            return Response.createError(payInfo.getSdk(), payInfo);
        }
        return Response.createSuccess(payInfo);
    }

    /**
     * 查询是否支付成功
     *
     * @param passengerUuid passengerUuid
     * @param advanceSerial advanceSerial
     * @param settlementId  settlementId
     * @return Response
     */
    public Response querySettlementIsSuccess(String passengerUuid, String advanceSerial, String settlementId) {
        Response<Boolean> response = accountService
                .queryPreRechargeExist(passengerUuid, advanceSerial, settlementId);
        if (!response.getSuccess()) {
            return Response.createError("查询结算信息失败" + response.getMsg());
        }
        return Response.createSuccess(Boolean.TRUE.equals(response.getData()));
    }

    /**
     * 验证申请单是否创建成功
     *
     * @param requisitionUuid requisitionUuid
     * @return Response
     */
    public Response checkIsCreateApplication(String requisitionUuid) {

        Response<ApplyOrderDetailsDto> response = applyOrderService.getApplyOrderDetails(requisitionUuid, null);
        if (!response.getSuccess()) {
            return Response.createError("获取申请单信息失败" + response.getMsg());
        }
        if (response.getData() == null) {
            return Response.createError("未获取到申请单信息");
        }
        ApplyOrderDetailsDto dto = response.getData();
        if (CollectionUtils.isEmpty(dto.getApplyAssociatedDtos())) {
            return Response.createError("未获取到行程信息");
        }

        RequisitionInfoVo vo = new RequisitionInfoVo();
        vo.setRequisitionUuid(requisitionUuid);
        vo.setRoutePlanUuid(dto.getApplyAssociatedDtos().get(0).getRoutePlanUuid());
        vo.setStatus(dto.getStatus());
        return Response.createSuccess(vo);
    }

    /**
     * 验证行程是否更改成功
     *
     * @param routePlanUuid v
     * @return Response
     */
    public Response checkIsModifyRoutePlan(String routePlanUuid) {

        Response response = applyOrderService.selectChangeTrip(routePlanUuid);
        if (response.getSuccess()) {
            return Response.createSuccess("行程更改成功");
        } else {
            return Response.createError("行程更改失败");
        }
    }

    /**
     * 基础费用
     *
     * @param req req
     * @return Response<CashierDeskBaseFareBo>
     */
    private Response<CashierDeskBaseFareBo> getBaseFare(PayTradeCharteredReq req) {
        PayDeskReq deskProDto = new PayDeskReq();
        deskProDto.setOrderId(req.getRoutePlanUuid());
        deskProDto.setType("1");
        deskProDto.setUserId(RequestContextHelper.getPassengerUuid());
        deskProDto.setPayWay(commonPaymentBusiness.getPayWay(req.getPayTypeList()));
        deskProDto.setPayChannel(commonPaymentBusiness.getPayChannel(req.getPayTypeList()));
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.pay.paydesk.deskPro");
        dto.setExtendParam(JSON.toJSONString(deskProDto));
        Response response = unifiedService.handle(dto);

        log.info("traceId {} com.t3.ts.pay.remote.service.UnifiedService.handle,Request={} , Resposne={}",
                MDC.get(CharteredAppConstants.LOG_TRACE_ID), JSON.toJSONString(deskProDto),
                JSON.toJSONString(response));
        if (!response.isSuccess() || ObjectUtils.isEmpty(response.getData())) {
            return Response.createError("查询收银台信息失败");
        }
        CashierDeskDto cashierDestDto = JSON.parseObject((String) response.getData(), CashierDeskDto.class);
        FareDetailDto fareDetailDto = cashierDestDto.getFareDetail();
        BalanceDto balanceDto = cashierDestDto.getBalance();
        PayedDto payed = cashierDestDto.getPayed();
        BalanceCanPayDetailDto balanceCanPayDetailDto = cashierDestDto.getBalanceCanPayDetail();

        // 剩余应付金额
        BigDecimal remainPay = BigDecimalUtils.bigDecimalOf(fareDetailDto.getRemainPay());
        // 当前支付总费用
        BigDecimal amountPayed = BigDecimalUtils.bigDecimalOf(payed.getAmountPayed());
        // 总费用
        BigDecimal totalFare = remainPay.add(amountPayed);
        // 余额已支付
        BigDecimal balancePay = BigDecimalUtils.bigDecimalOf(payed.getBalancePay()).add(
                BigDecimalUtils.bigDecimalOf(payed.getGiftCardPayed())
        );
        // 企业支付金额
        BigDecimal enterprisePay = BigDecimalUtils.bigDecimalOf(payed.getCompanyPayedAmount())
                .subtract(BigDecimalUtils.bigDecimalOf(payed.getCompanyCouponAmount()));

        CashierDeskBaseFareBo cashierDeskBaseFareBo = new CashierDeskBaseFareBo();
        cashierDeskBaseFareBo.setBalanceTotal(BigDecimalUtils.bigDecimalOf(balanceDto.getBalanceTotal()));
        cashierDeskBaseFareBo.setAdditionalFee(BigDecimalUtils.bigDecimalOf(fareDetailDto.getServiceFare()));
        cashierDeskBaseFareBo.setRemainPay(remainPay);
        cashierDeskBaseFareBo.setAmountPayed(BigDecimalUtils.bigDecimalOf(payed.getAmountPayed()));
        cashierDeskBaseFareBo.setTotalFare(totalFare);
        cashierDeskBaseFareBo.setPrPayed(BigDecimalUtils.bigDecimalOf(payed.getPrPayed()));
        cashierDeskBaseFareBo.setBalancePay(balancePay);
        cashierDeskBaseFareBo.setIntegralPayed(BigDecimalUtils.bigDecimalOf(payed.getIntegralPayed()));
        cashierDeskBaseFareBo.setGiftCardCanPayService(balanceCanPayDetailDto.getGiftCardCanPayService());
        cashierDeskBaseFareBo.setEnterprisePay(enterprisePay);
        cashierDeskBaseFareBo.setCompanyCouponAmount(BigDecimalUtils.bigDecimalOf(payed.getCompanyCouponAmount()));
        // 余额可支付金额
        BigDecimal balanceMoney = new BigDecimal(balanceCanPayDetailDto.getGiftCardCanPay())
                .add(new BigDecimal(balanceCanPayDetailDto.getGiftMoneyCanPay()));
        cashierDeskBaseFareBo.setBalancePayable(balanceMoney);
        log.info("获取收银台基础费用，结果是 {}", JSON.toJSONString(cashierDeskBaseFareBo));
        return Response.createSuccess(cashierDeskBaseFareBo);
    }

    /**
     * 获取积分抵扣
     *
     * @param integralPayed 积分已抵扣
     * @param additionalFee 附加费
     * @param remainPay     剩余应付金额
     * @return PayDeskInfoVo
     */
    private PayDeskInfoVo getDeductPoints(BigDecimal integralPayed, BigDecimal additionalFee, BigDecimal remainPay) {

        PayDeskInfoVo payDeskInfoVo = new PayDeskInfoVo();
        if (integralPayed.compareTo(BigDecimal.ZERO) > 0) {
            payDeskInfoVo.setIntegralDeductFlag(false);
            payDeskInfoVo.setAvailableIntegral(NumConstants.STR_0);
        } else {
            // 订单费用
            BigDecimal orderCost = remainPay.subtract(additionalFee);
            deductPoints(orderCost, payDeskInfoVo);
        }
        log.info("获取积分抵扣结果是 {}", JSON.toJSONString(payDeskInfoVo));
        return payDeskInfoVo;
    }

    /**
     * 计算积分抵扣
     *
     * @param orderCost     本次待支付订单费用
     * @param cashierDeskVo 收银台vo
     * @return 积分抵扣情况
     */
    private Response<PayDeskInfoVo> deductPoints(BigDecimal orderCost, PayDeskInfoVo cashierDeskVo) {
        String passengerUuid = RequestContextHelper.getPassengerUuid();
        // 可用积分
        BigDecimal availableIntegral = BigDecimal.ZERO;
        // 抵扣费用
        BigDecimal deductCost = BigDecimal.ZERO;
        cashierDeskVo.setAvailableIntegral(availableIntegral + "");
        cashierDeskVo.setDeductCost(deductCost + "");

        Response<IntegralDeductDto> deductPointsRes = queryBasicsConfigList(cashierDeskVo);
        if (!deductPointsRes.isSuccess()) {
            return getUndeductResponse(cashierDeskVo);
        }
        IntegralDeductDto integralDeductDto = deductPointsRes.getData();

        List<LadderDiscountDto> ladderDiscountDtoList = integralDeductDto.getLadderDiscountDtos();
        if (CollectionUtils.isEmpty(ladderDiscountDtoList)) {
            log.error("无法获取积分抵扣梯度规则");
            return getUndeductResponse(cashierDeskVo);
        }
        // 计算可抵扣费用
        Response<DeductPointsBo> deductCostRes = getDeductCost(integralDeductDto, ladderDiscountDtoList, orderCost);
        if (!deductCostRes.isSuccess()) {
            return getUndeductResponse(cashierDeskVo);
        }
        DeductPointsBo deductPointsBo = deductCostRes.getData();
        deductCost = deductPointsBo.getDeductCost();

        Response getExchangePointsRes = getExchangePoints(cashierDeskVo, deductPointsBo, ladderDiscountDtoList);
        if (EnterpriseConstants.CODE_999.equals(getExchangePointsRes.getCode())) {
            JSONObject exchangePointsJsonObject =
                    JSONObject.parseObject(JSON.toJSONString(getExchangePointsRes.getData()));
            String exchangePoints = exchangePointsJsonObject.getString("exchangePoints");
            BigDecimal personalPoints = new BigDecimal(exchangePointsJsonObject.getString("personalPoints"));
            // 换算可抵扣积分
            BigDecimal deductIntegral =
                    ExchangePointsUtils.changeMoney2Points(deductCost, exchangePoints, RoundingMode.DOWN);
            // 判断可用积分是否足够抵扣，否则部分抵扣
            availableIntegral = deductIntegral.compareTo(personalPoints) > 0 ? personalPoints : deductIntegral;
            // 通过费用兑换积分有精度问题，通过积分重新计算费用
            deductCost = ExchangePointsUtils.changePoints2Money(availableIntegral, exchangePoints);
            cashierDeskVo.setAvailableIntegral(availableIntegral.intValue() + "");
            cashierDeskVo.setDeductCost(deductCost + "");
            // 缓存可抵扣积分和费用，支付时使用
            try {
                cacheFactory.ExpireHash().setExpireHash(
                        RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid, cashierDeskVo, EnterpriseConstants.INTEGER_3600);
            } catch (Exception e) {
                log.info("ExpireHashService.setExpireHash key: {}, field: {}, Error: {}",
                        RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid, e);
            }
            IntegralMsgVO integralMsgVO = commonPaymentBusiness.initIntegralMsg(
                    PassengerConstants.DEDUCT_POINTS_CAN_PAY, availableIntegral.intValue() + "", deductCost + "");
            cashierDeskVo.setIntegralMsg(integralMsgVO);
            return Response.createSuccess(cashierDeskVo);
        } else {
            return getExchangePointsRes;
        }
    }

    /**
     * 封装不展示T币支付响应
     *
     * @param cashierDeskVo 收银台vo
     * @return 不展示T币支付响应
     */
    private Response<PayDeskInfoVo> getUndeductResponse(PayDeskInfoVo cashierDeskVo) {
        cashierDeskVo.setIntegralDeductFlag(false);
        return Response.createSuccess("获取收银台数据成功", cashierDeskVo);
    }

    /**
     * @param cashierDeskVo cashierDeskVo
     * @return Response<List < IntegralDeductDTO>>
     */
    private Response<IntegralDeductDto> queryBasicsConfigList(PayDeskInfoVo cashierDeskVo) {
        // 积分抵扣不允许重复使用
        if (Objects.nonNull(cashierDeskVo.getIntegralPayed())
                && cashierDeskVo.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            log.error("已使用积分抵扣金额: {}，不允许重复抵扣", cashierDeskVo.getIntegralPayed());
            return Response.createError();
        }

        // 获取积分抵扣规则
        JSONObject baseRuleCfgDto = new JSONObject();
        baseRuleCfgDto.put("businessType", MemberAccountBusinessTypeEnum.BUSINESS_TYPE_ENTERPRISE.getBusinessType());
        baseRuleCfgDto.put("recordStatus", 1);

        //产品线、业务线改造，入参变动 原businessType废弃，新增 产品线bizType 业务线bizLine
        Response<SrOrderDto> orderDtoResponse =
                srOrderService.querySrOrderByRoutePlanId(cashierDeskVo.getOrderUuid());
        Response deductPointsRes = null;
        if (null != orderDtoResponse && orderDtoResponse.isSuccess() && null != orderDtoResponse.getData()) {
            Integer bizType = orderDtoResponse.getData().getTypeModule();
            Integer bizLine = orderDtoResponse.getData().getExpandBizLine();
            deductPointsRes = commonPaymentBusiness.getIntegralDeductInfo(bizType, bizLine);
        }
        if (null == deductPointsRes || Boolean.FALSE.equals(deductPointsRes.isSuccess())
                || null == deductPointsRes.getData()) {
            log.info("积分抵扣规则获取失败: {}", JSON.toJSONString(deductPointsRes));
            return Response.createError();
        }
        return Response.createSuccess(deductPointsRes.getData());
    }

    /**
     * @param integralDeductDTO     integralDeductDTO
     * @param ladderDiscountDtoList ladderDiscountDtoList
     * @param orderCost             orderCost
     * @return 抵扣费用
     */
    private Response<DeductPointsBo> getDeductCost(IntegralDeductDto integralDeductDTO,
                                                   List<LadderDiscountDto> ladderDiscountDtoList,
                                                   BigDecimal orderCost) {
        // 抵扣费用
        BigDecimal deductCost = BigDecimal.ZERO;
        // 计算可抵扣费用
        // 每单最小抵扣金额
        BigDecimal minDeductMoney = integralDeductDTO.getMinDeductMoney();
        // 获取可用梯度规则
        List<LadderDiscountDto> ladderDiscountDtos = ladderDiscountDtoList.parallelStream()
                .filter(ladderDiscount -> orderCost.compareTo(ladderDiscount.getConsumeMoney()) >= 0)
                .sorted(Comparator.comparing(LadderDiscountDto::getConsumeMoney))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ladderDiscountDtos)) {
            LadderDiscountDto ladderDiscountDto = ladderDiscountDtos.get(ladderDiscountDtos.size() - 1);
            // 1. 计算订单费用所在梯度能抵扣的费用
            deductCost = orderCost.multiply(ladderDiscountDto.getDeductCoefficient())
                    .divide(new BigDecimal(EnterpriseConstants.INTEGER_ONE_HUNDRED))
                    .setScale(EnterpriseConstants.INTEGER_TWO, RoundingMode.DOWN);
            // 2. 比较所在梯度的最大抵扣费用
            if (Objects.nonNull(ladderDiscountDto.getMaxDeductMoney())) {
                deductCost = deductCost.compareTo(ladderDiscountDto.getMaxDeductMoney()) > 0
                        ? ladderDiscountDto.getMaxDeductMoney().setScale(
                        EnterpriseConstants.INTEGER_TWO, RoundingMode.DOWN) : deductCost;
            }
            log.info("订单费用: {}， 可抵扣订单费用: {}", orderCost, deductCost);
            // 3. 判断抵扣金额是否符合最小抵扣限制
            if (Objects.nonNull(minDeductMoney) && minDeductMoney.compareTo(deductCost) > 0) {
                log.error("积分可抵扣金额: {} 小于最小抵扣限制: {}，无法进行抵扣", deductCost, minDeductMoney);
                return Response.createError();
            }
        }

        DeductPointsBo deductPointsBo = new DeductPointsBo();
        deductPointsBo.setDeductCost(deductCost);
        deductPointsBo.setMinDeductMoney(minDeductMoney);
        deductPointsBo.setLadderDiscountDtos(ladderDiscountDtos);

        return Response.createSuccess(deductPointsBo);
    }

    /**
     * @param cashierDeskVo         cashierDeskVo
     * @param deductPointsBo        deductPointsBo
     * @param ladderDiscountDtoList ladderDiscountDtoList
     * @return Response
     */
    private Response getExchangePoints(PayDeskInfoVo cashierDeskVo, DeductPointsBo deductPointsBo,
                                       List<LadderDiscountDto> ladderDiscountDtoList) {
        // 获取积分现金兑换比例
        UnifiedDto unifiedDto = new UnifiedDto();
        unifiedDto.setSceneType(PayCenterMethodEnum.PAY_CENTER_METHOD_EXCHANGE_POINTS.getMethod());
        unifiedDto.setChannel(HttpRequestUtil.parseUa());
        Response exchangeRes = unifiedService.handle(unifiedDto);
        log.info("traceId={} >>> 积分现金比例响应结果: {}", MDC.get(CharteredAppConstants.LOG_TRACE_ID),
                JSON.toJSONString(exchangeRes));
        if (Boolean.FALSE.equals(exchangeRes.isSuccess())) {
            log.error("积分现金比例获取失败：" + exchangeRes.getMsg());
            return getUndeductResponse(cashierDeskVo);
        }
        if (null == exchangeRes.getData()) {
            log.error("未获取到积分现金比例");
            return getUndeductResponse(cashierDeskVo);
        }

        JSONObject exchangePointsRes = JSON.parseObject(JSON.toJSONString(exchangeRes.getData()));
        //积分兑换比例
        String exchangePoints = exchangePointsRes.getString("value");
        // 获取个人积分
        Response<AccountInfoDto> accountRes =
                accountInfoService.queryAccountByUserId(null, RequestContextHelper.getPassengerUuid());
        if (Boolean.FALSE.equals(accountRes.isSuccess()) || Objects.isNull(accountRes.getData())) {
            log.error("获取个人积分失败: " + accountRes.getMsg());
            return getUndeductResponse(cashierDeskVo);
        }
        BigDecimal personalPoints = accountRes.getData().getTotalAccount();

        // 积分抵扣限制校验
        cashierDeskVo.setIntegralDeductFlag(true);
        // 最小抵扣积分
        BigDecimal minPoints = ExchangePointsUtils.changeMoney2Points(
                deductPointsBo.getMinDeductMoney(), exchangePoints, RoundingMode.UP);
        // 最小订单费用
        BigDecimal minCost = ladderDiscountDtoList.stream().map(LadderDiscountDto::getConsumeMoney)
                .min(BigDecimal::compareTo).get().setScale(EnterpriseConstants.INTEGER_TWO, RoundingMode.UP);

        log.info("个人积分: {}，每单最小抵扣金额: {}，最小抵扣积分: {}",
                personalPoints, deductPointsBo.getMinDeductMoney(), minPoints);
        // 既有积分限制，也有订单费用限制
        if (personalPoints.compareTo(minPoints) < 0
                || CollectionUtils.isEmpty(deductPointsBo.getLadderDiscountDtos())) {
            BigDecimal additionalFare = cashierDeskVo.getAdditionalFee();
            if (Objects.nonNull(additionalFare) && additionalFare.compareTo(BigDecimal.ZERO) > 0) {
                return getUndeductResponse(cashierDeskVo);
            }
            IntegralMsgVO integralMsgVO = commonPaymentBusiness.initIntegralMsg(
                    PassengerConstants.DEDUCT_POINTS_POINTS_AND_COST_LIMIT, minCost + "", minPoints.intValue() + "");
            cashierDeskVo.setIntegralMsg(integralMsgVO);
            return Response.createSuccess(cashierDeskVo);
        }
        DeductPointsBo bo = new DeductPointsBo();
        bo.setPersonalPoints(personalPoints);
        bo.setExchangePoints(exchangePoints);
        return Response.createSuccess("成功", EnterpriseConstants.CODE_999, bo);
    }

    /**
     * 支付项
     *
     * @param baseFareBo 基础费用
     * @return List<PayItemVo>
     */
    private List<PayItem> getPayItemList(CashierDeskBaseFareBo baseFareBo) {
        List<PayItem> payItemList = Lists.newArrayList();
        // 【支付项明细列表】0:合计费用
        if (baseFareBo.getTotalFare().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("合计费用", String.valueOf(baseFareBo.getTotalFare()), Boolean.FALSE, 0, null));
        }
        // 【支付项明细列表】1:企业支付
        if (baseFareBo.getEnterprisePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "企业支付", String.valueOf(baseFareBo.getEnterprisePay()), Boolean.FALSE, 1, null));
        }
        // 【支付项明细列表】3:预付款已支付
        if (baseFareBo.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            final int three = 3;
            payItemList.add(new PayItem("已预付", "-" + baseFareBo.getPrPayed(), Boolean.FALSE, three, null));
        }
        // 【支付项明细列表】4:优惠券已抵扣
        if (baseFareBo.getCompanyCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
            final int fore = 4;
            payItemList.add(new PayItem(
                    "优惠券已抵扣", "-" + baseFareBo.getCompanyCouponAmount(), Boolean.FALSE, fore, null));
        }
        // 【支付项明细列表】5:余额已支付
        if (baseFareBo.getBalancePay().compareTo(BigDecimal.ZERO) > 0) {
            final int five = 5;
            payItemList.add(new PayItem("余额已支付", "-" + baseFareBo.getBalancePay(), Boolean.FALSE, five, null));
        }
        // 【支付项明细列表】6:T币已抵扣
        if (baseFareBo.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            final int six = 6;
            payItemList.add(new PayItem("T币已抵扣", "-" + baseFareBo.getIntegralPayed(), Boolean.FALSE, six, null));
        }
        // 【支付项明细列表】7:还需支付
        if (baseFareBo.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            final int seven = 7;
            payItemList.add(new PayItem("还需支付", baseFareBo.getRemainPay().toString(), Boolean.FALSE, seven, null));
        }
        return payItemList;
    }

    /**
     * 获取路线详细信息响应
     *
     * @param routePlanUuid 路线图uuid
     * @return {@link Response}
     */
    private Response getRouteDetailResponse(String routePlanUuid) {
        //814转储
        StopWatch stopWatch = new StopWatch();
        log.info("enterprisePay queryRouteINfo,routePlanUuid: {}", routePlanUuid);
        stopWatch.start("routeReadFeignClient.getRouteDetail");
        RouteInfoDto routeInfo = routeDetailClient.getRouteInfoWeak(routePlanUuid);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        if (routeInfo == null) {
            log.error("getRouteDetail error");
            return Response.createError("查询行程信息失败");
        }
        log.info("====routeReadFeignClient.getRouteDetail-->req:{},response:{}",
                routePlanUuid, JSONObject.toJSONString(routeInfo));
        String status = routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString();
        if (!RouteStatus.ROUTE_6.getStatus().equals(status)) {
            return Response.createError(ResultErrorEnum.ROUTE_PAYED);
        }
        return null;
    }

    /**
     * 根据用车类型 转换下上送支付那边的bizType
     *
     * @param type type
     * @return java.lang.Integer
     */
    private Integer getPayOrderType(String type) {
        int code = EnumPayOrderType.COMPANY_PERSONAL_PAY.getCode();
        if (StringUtils.isNotBlank(type) && (CharteredAppConstants.TYPE_TRIP.toString()).equals(type)) {
            code = EnumPayOrderType.CHARTERED_COMPANY_PERSONAL_PAY.getCode();
        }
        return code;
    }

    /**
     * 获取支付时可抵扣积分，组装支付扩展参数
     *
     * @param passengerUuid 乘客uuid
     * @return 支付扩展参数
     */
    private Response<Map<String, Object>> deductPoints4Pay(String passengerUuid) {
        List<PayDeskInfoVo> cashierDeskVos = null;
        try {
            cashierDeskVos = cacheFactory.ExpireHash().getHash(RoutePlanKey.DEDUCT_POINTS_KEY,
                    PayDeskInfoVo.class, passengerUuid);
        } catch (Exception e) {
            log.error("ExpireHashService.getHash key: {}, field: {}，Error: {}",
                    RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid, e);
        }
        if (CollectionUtils.isEmpty(cashierDeskVos)) {
            return Response.createError("获取抵扣积分失败");
        }
        try {
            cacheFactory.ExpireHash().deleteHash(RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid);
        } catch (Exception e) {
            log.error("ExpireHashService.deleteHash key: {}, field: {}，Error: {}", RoutePlanKey.DEDUCT_POINTS_KEY,
                    passengerUuid, e);
        }
        PayDeskInfoVo cashierDeskVo = cashierDeskVos.get(0);
        Map<String, Object> extendParams = new HashMap<>(CommonNumConst.NUM_16);
        extendParams.put("integralNum", Integer.parseInt(cashierDeskVo.getAvailableIntegral()));
        return Response.createSuccess(extendParams);
    }

    /**
     * 获取支付类型
     *
     * @param payTypeList payTypeList
     * @return int
     */
    private int getPayType(List<Integer> payTypeList) {
        // 去除余额和积分支付方式
        return payTypeList.parallelStream().filter(payType -> EnumPayOrderChannel.INTEGRAL.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.BALANCE.getCode() != payType)
                .findFirst().get();
    }

    /**
     * @param payType   支付类型
     * @param payInfoBo payInfoBo
     * @return Response
     */
    private Response returnPayInfo(int payType, PaymentInfoBo payInfoBo) {
        Response<RechargePayBo> payResponse = payChannelFactory.paymentInfo(payType, payInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg());
        }
        RechargePayBo payInfo = payResponse.getData();
        if (NumConstants.STR_500.equals(payInfo.getCode())) {
            return Response.createError(payInfo.getSdk(), payInfo);
        }
        if (payInfo.getPayType() == EnumPayOrderChannel.BALANCE.getCode()) {
            return Response.createSuccess("余额支付成功", payInfo);
        }
        return Response.createSuccess("获取支付信息成功", payInfo);
    }
}
