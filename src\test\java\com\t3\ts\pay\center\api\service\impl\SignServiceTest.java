package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.pay.center.api.service.SignService;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 16:20
 */
public class SignServiceTest {
    @Mock
    Logger log;
    @InjectMocks
    SignService signService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOpenSecretFree() {
        SecretFreeReq req = new SecretFreeReq();
        req.setUserId("284498cf422740d7a0e70b7d5978e2f9");
        Response<?> response = signService.openSecretFree(req);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testCloseSecretFree() {
        SecretFreeReq req = new SecretFreeReq();
        req.setUserId("284498cf422740d7a0e70b7d5978e2f9");
        Response<?> response = signService.closeSecretFree(req);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testQuerySignStatus() {
        Response<?> response = signService.querySignStatus("284498cf422740d7a0e70b7d5978e2f9");
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testPriority() {
        SecretFreeReq req = new SecretFreeReq();
        req.setUserId("284498cf422740d7a0e70b7d5978e2f9");
        Response<?> response = signService.priority(req);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testQuerySignStatusByChannel() {
        PayChannelReq req = new PayChannelReq();
        req.setUserId("284498cf422740d7a0e70b7d5978e2f9");
        req.setPayOrderChannel(NumConstants.NUM_2);
        Response<?> response = signService.querySignStatusByChannel(req);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testQuerySignList() {
        Response<?> response = signService.querySignList("284498cf422740d7a0e70b7d5978e2f9");
        Assert.assertTrue(response.isSuccess());
    }
}
