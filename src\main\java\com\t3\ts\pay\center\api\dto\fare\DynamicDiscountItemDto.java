package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date: 2018-12-04 18:48
 * @des: 动态折扣明细对象
 */
@Data
public class DynamicDiscountItemDto implements Serializable {

    private String dynamicDiscountUuid;

    private String dynamicDiscountName;
    private String dynamicDiscountDesc;

    /**
     * 1:基础折扣；2、浮动折扣；3：六边形
     */
    private Integer dynamicDiscountType;

    /**
     * 折扣率
     */
    private BigDecimal rate;

    /**
     * 折扣上限
     */
    private BigDecimal upperLimitFare = new BigDecimal("9999");

    private Integer sort;

}
