package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 司机费用大类VO
 *
 * <AUTHOR>
 * @date 2023/03/09
 */
@Data
public class DriverFareClassifyVo {

    @ApiModelProperty(value = "大类code")
    private String classifyCode;

    @ApiModelProperty(value = "大类描述")
    private String classifyDesc;

    @ApiModelProperty(value = "大类包含的费用项列表")
    private List<Integer> fareItemList;

    @ApiModelProperty(value = "收支分类，1收入，2支出")
    private Integer changedType;
}
