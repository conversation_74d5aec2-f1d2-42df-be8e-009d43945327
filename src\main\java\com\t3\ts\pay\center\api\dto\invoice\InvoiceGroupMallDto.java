package com.t3.ts.pay.center.api.dto.invoice;

import com.t3.ts.pay.center.api.bo.PassengerInvoiceBo;
import lombok.Data;

import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 14:20
 *
 * @Author: wuss
 * @Date: 2020/10/27 15:33
 * @Description: 分组开票参数
 */
@Data
public class InvoiceGroupMallDto {
    /**
     * 分组开票参数
     */
    private List<PassengerInvoiceBo> passengerInvoiceDtos;
    /**
     * 是否回调开放平台（true 回调，false 不回调）
     */
    private boolean callBackOpenApi = false;
}
