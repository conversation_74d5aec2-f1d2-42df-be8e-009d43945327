package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.dto.AccountCouponDto;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.channelmgr.center.dto.PayChannelReqDto;
import com.t3.ts.channelmgr.center.dto.PayChannelResDto;
import com.t3.ts.channelmgr.center.service.PayChannelTakeOverService;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.interactive.exception.ExceptionUtils;
import com.t3.ts.pay.center.api.business.PaymentBusiness;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.FareParam;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.common.CanUseCoupon;
import com.t3.ts.pay.center.api.dto.trade.*;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.pay.center.api.exception.BusinessException;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.rest.OperationRest;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.service.PayService;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.center.api.util.*;
import com.t3.ts.pay.common.http.driver.DriverInfoService;
import com.t3.ts.pay.common.http.driver.dto.req.DriverReqDto;
import com.t3.ts.pay.common.http.driver.dto.res.DriverResDto;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumTypeModule;
import com.t3.ts.pay.remote.constants.ExpandBizLineType;
import com.t3.ts.pay.remote.constants.PayForOtherTypeEnum;
import com.t3.ts.pay.remote.dto.*;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.travel.manager.resource.dto.res.CarDto;
import com.t3.ts.travel.manager.resource.service.CarService;
import com.t3.ts.travel.manager.resource.service.DriverService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.*;
import static com.t3.ts.pay.center.api.constants.NumConstants.COUPON_SHOW;
import static com.t3.ts.pay.center.api.constants.NumConstants.PRIVILEGE_SHOW;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Description: 支付服务
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:04
 */
@Service
@Slf4j
public class PayServiceImpl implements PayService {

    @Autowired
    private PayHandlerFactory factory;
    @Autowired
    private CommonPayHandler commonPayHandler;
    @Autowired
    private MarketingRest marketingRest;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private RouteBusiness routeBusiness;
    @Autowired
    private PaymentBusiness paymentBusiness;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @Autowired
    private OperationRest operationRest;
    @DubboReference
    private AccountSignService accountSignService;
    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQuery;
    @DubboReference
    private DriverService driverService;
    @DubboReference
    private PayChannelTakeOverService payChannelTakeOverService;
    @DubboReference
    private UnifiedService unifiedService;
    @DubboReference
    private CarService carService;
    @DubboReference(timeout = 500)
    private SrOrderService srOrderService;
    @DubboReference(timeout = 500)
    private AccountService accountService;
    @Autowired
    private ErrorMsgAdapterUtil errorMsgAdapterUtil;
    @Autowired
    private DriverInfoService driverInfoService;
    /**
     * 预估使用券套餐标识
     */
    private static final String ESTIMATE_CHECK_LABEL = "812";


    /**
     * 进行支付
     *
     * @param context 上下文
     * @return {@link Response<JSONObject>}
     */
    @Override
    @SuppressWarnings("unchecked")
    public Response<JSONObject> doPay(PayContext context) {
        try {
            return process(context);
        } catch (Exception e) {
            log.error("PayServiceImpl.doPay error : ", e);
            return Response.createError(e.getMessage());
        }

    }

    @Override
    public Response deskInfo(PayTradeReq req) {
        log.info("PayService.deskInfo.req: {}", JSON.toJSONString(req));
        PayDeskVoV4 resp = new PayDeskVoV4();
        try {
            // 校验行程状态
            RouteInfoDto routeInfo = new RouteInfoDto();
            // 判断是否赚钱码
            if (!PayWayConvert.isTaxiScanPay(req.getPayOrderType())) {
                // 不是赚钱码，查出行接口，获取出行行程信息
                routeInfo = routeDetailClient.getRouteInfoWithFareWeak(req.getOrderUuid());
                // 判断行程信息状态是否已完单 是否为： 7-待评价， 8- ，9- ，
                if (!checkRouteStatus(routeInfo)) {
                    // 行程信息状态是已完单 - 返回已支付提示
                    return Response.createError(ResultErrorEnum.ROUTE_PAYED);
                }
            }

            PreDeskRes preInfo = getPreDeskInfo(req.getPassengerUuid(), req.getOrderUuid());

            int balaceStrategy = getBalanceStrategy(req.getPayTypeList());

            // 用户选择的支付渠道列表（余额、积分、三方渠道）
            List<Integer> deskPayTypes = initPayTypeList(req.getPayTypeList());
            // 第一次处理收银台支付方式
            List<Integer> payTypes = processPayTypeList(req, deskPayTypes, preInfo);

            resp.setPayTypeList(payTypes);
            // 三方支付方式
            Integer thirdType = PayWayConvert.getThirdType(payTypes);

            // 转换支付方式
            List<String> payWay = PayWayConvert.getPayWay(payTypes);
            // 组装收银台请求信息
            PayDeskReq payDeskReq = buildPayDeskRequest(req, routeInfo, thirdType, payWay);

            Response paydeskResp = paydesk(payDeskReq);

            if (null == paydeskResp || !paydeskResp.isSuccess() || paydeskResp.getData() == null) {
                return PayUtils.convertErrorResponse(paydeskResp);
            }
            // 权益卡和优惠券的金额转化为元，并返回收银台对象
            PayDeskRes payDeskRes = deductionAmountToDoubleString(paydeskResp);
            // 城市code
            String cityCode = PayUtils.getCityCode(routeInfo, req);
            // 【支付项列表】优惠券抵扣信息
            CouponVo couponVo = processCoupon(payDeskRes);

            resp.setPassengerId(req.getPassengerUuid());
            resp.setOrderUuid(req.getOrderUuid());
            resp.setIsPayInstant(switchConfig.getIsPayInstant() && parseIsFree(req, payDeskRes));
            resp.setUseCounponActivity(req.getUseCounponActivity());
            resp.setCouponVo(couponVo);
            // 查询当前行程支付状态
            resp.setPayStatus(payStatusQuery(preInfo));
            // 处理购买券包的金额
            BigDecimal activityAmount = processCouponActivity(req, payDeskRes.getCouponDetail(), resp,
                    routeInfo);
            // 设置亲友代付标识
            activityAmount = processPayForOtherType(resp, payDeskRes, activityAmount, req);
            // 订单人是否为当前登录人 为了兼容小程序跨账号
            Boolean isCurrentPassenger = checkIsCurrentPassenger(req.getPassengerUuid(), routeInfo, payDeskRes.getRouteInfo());
            // 费用信息参数
            FareParam fareParam = buildDeskFareInfo(req, payDeskRes, resp, activityAmount);
            // 【支付项明细列表】
            buildPayItemList(resp, payDeskRes, req, fareParam, isCurrentPassenger, cityCode);
            // 费用信息
            buildFareParam(resp, fareParam);

            // 最终余额可用
            BigDecimal balanceFinal = getBalanceFinal(req, balaceStrategy, fareParam);
            // 【支付项目】钱包余额展示文案
            dealBalance(resp, fareParam, preInfo);
            // 校验充值金与优惠券互斥关系
            checkRechargeAndCoupon(fareParam, resp);
            // 检查余额提醒通知消息
            checkBalanceNoticeMessage(fareParam, resp, routeInfo);
            // 判断是否仅仅只有取消费
            resp.setJustHasCancelFareFlag(checkJustHasCancelFareFlag(fareParam));
            // 判断是否有附加费
            Boolean existAdditionalFee = hasExistAdditionalFee(fareParam);
            deskPayTypes = getDeskDisplayPayTypeList(req, resp, PayWayConvert.isTaxiScanPay(req.getPayOrderType()),
                    deskPayTypes, fareParam, existAdditionalFee);
            currentPassengerFare(resp, deskPayTypes, isCurrentPassenger, fareParam, balanceFinal);
            // 组装拼车、车参数
            buildPayDeskCarInfo(resp, routeInfo);
            // 查询折扣明细
            PassengerRouteFareItemsVo fareItems = paymentBusiness.getRouteFareItems(resp.getOrderUuid(), payDeskRes.getRouteInfo());
            buildDiscountFare(resp, fareItems, fareParam);

            //APP专属优惠券判断
            appExclusiveCountCalculate(req, payDeskRes, fareParam, resp);

            // 设置免密支付提醒
            resp.setAutoPayTip(paymentBusiness.getAutoPayTip(req.getOrderUuid(), req.getPassengerUuid()));
            return Response.createSuccess(resp);
        } catch (BusinessException e) {
            log.info("PayService.deskInfo.error:{}", JSON.toJSONString(req), e);
            errorMsgAdapterUtil.monitorPayDeskBusiness(req);
            return Response.createError(e.getMsg(), e.getCode());
        } catch (Exception e) {
            errorMsgAdapterUtil.monitorPayDeskBusiness(req);
            log.info("PayService.deskInfo.error:{}", JSON.toJSONString(req), e);
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
    }

    private List<Integer> initPayTypeList(List<Integer> payTypeList) {
        // 用户选择的支付渠道列表（余额、积分、三方渠道）
        return CollectionUtils.isNotEmpty(payTypeList) ? new ArrayList<>(payTypeList) : new ArrayList<>();
    }

    private int getBalanceStrategy(List<Integer> payTypeList) {
        int balanceStrategy = 0;
        if (CollectionUtils.isNotEmpty(payTypeList)) {
            if (payTypeList.size() == 1 && payTypeList.contains(NUM_NEGATIVE_1)) {
                balanceStrategy = 1;
            } else {
                balanceStrategy = payTypeList.contains(EnumPayOrderChannel.BALANCE.getCode()) ? NUM_2 : 0;
            }
        }
        return balanceStrategy;
    }

    /**
     * 解析是否可以免密支付
     *
     * @param req        请求对象，包含乘客的相关信息
     * @param payDeskRes 支付台响应对象，包含支付相关的信息
     * @return 如果可以免密支付返回 true，否则返回 false
     */
    private Boolean parseIsFree(PayTradeReq req, PayDeskRes payDeskRes) {
        Boolean isFree = null;
        if (Objects.nonNull(payDeskRes.getIsAnyFree())) {
            //由支付返回是否开通了任一渠道免密，减少服务调用
            isFree = payDeskRes.getIsAnyFree();
        } else {
            // 查询乘客免密情况
            isFree = paymentBusiness.getSecretFreeStatus(req.getPassengerUuid());
        }

        return isFree && !(payDeskRes.getPayed() != null && PayUtils.getBigDecimal(payDeskRes.getPayed().getPrPayed()).compareTo(BigDecimal.ZERO) > NUM_0);
    }

    private PreDeskRes getPreDeskInfo(String userId, String routePlanUuid) {
        PreDeskReq req = new PreDeskReq();
        req.setUserId(userId);
        req.setRoutePlanUuid(routePlanUuid);
        PayChannelReqDto payChannelReqDto = new PayChannelReqDto();
        payChannelReqDto.setQuickPass(Boolean.TRUE);
        req.setPayChannelReqDto(payChannelReqDto);
        Response<PreDeskRes> response = unifiedPaymentQuery.getPreDeskInfo(req);
        return response.isSuccess() ? response.getData() : new PreDeskRes();
    }

    public Integer payStatusQuery(PreDeskRes preInfo) {
        PayStatusDto statusDto = preInfo.getPayStatusDto();
        return statusDto != null ? statusDto.getPayStatus() : null;
    }

    private Boolean checkJustHasCancelFareFlag(FareParam fareParam) {
        if (switchConfig.getCloseJustHasCancelFareSwitch()) {
            return false;
        }
        //只有取消费判断 方法： 取消费大于0 并且  取消费等于总费用
        boolean p1 = fareParam.getCancelFare().compareTo(BigDecimal.ZERO) > 0;
        boolean p2 = fareParam.getTotalFare().compareTo(fareParam.getCancelFare()) == 0;
        return p1 && p2 && !Objects.equals(ExpandBizLineType.HITCH_CAR.getTypeMode(), fareParam.getExpandBizLine());
    }

    /**
     * 判断收银台是否默认勾选券套餐
     *
     * @param req                 req
     * @param resp                resp
     * @param routeInfo routeInfo
     */
    private void processCouponPackageCheck(PayTradeReq req, PayDeskVoV4 resp,
                                           RouteInfoDto routeInfo) {
        // 非第一次拉起收银台  返回端上传的
        if (req.getPassengerChooseFlag()) {
            return;
        }
        // 第一次拉起收银台
        if (routeInfo != null && CollectionUtils.isNotEmpty(routeInfo.getLabels())) {
            for (String label : routeInfo.getLabels()) {
                if (!ESTIMATE_CHECK_LABEL.equals(label)) {
                    continue;
                }
                // 预估勾选
                CouponActivityDetail couponActivityDetail = resp.getCouponActivityDetail();
                if (null != couponActivityDetail && null != couponActivityDetail.getSellStatus()
                        && Boolean.TRUE.equals(couponActivityDetail.getSellStatus())) {
                    // 本单可用
                    req.setUseCounponActivity(NUM_1);
                    resp.setUseCounponActivity(NUM_1);
                } else if (null != couponActivityDetail && null != couponActivityDetail.getSellStatus()
                        && Boolean.FALSE.equals(couponActivityDetail.getSellStatus())) {
                    // 本单不可用
                    req.setUseCounponActivity(NUM_2);
                    resp.setUseCounponActivity(NUM_2);
                }
                return;
            }
        }
    }

    /**
     * APP专属券计算
     *
     * @param payTradeReq 收银台入参
     * @param payDeskVoV4 收银台返回结果
     * @param payDeskRes  支付中心收银台响应参数
     */
    private void appExclusiveCountCalculate(PayTradeReq payTradeReq, PayDeskRes payDeskRes,
                                            FareParam fareParam, PayDeskVoV4 payDeskVoV4) {
        try {
            //APP专属券条件判断
            boolean bool = appExclusiveConditionCheck(payTradeReq, payDeskRes, fareParam);
            if (!bool) {
                return;
            }

            //查询最优券
            AccountCouponDto accountCouponDto =
                    operationRest.getBeastDiscountCoupon(buildAccountCoupon(payDeskRes, payTradeReq));
            if (ObjectUtil.isEmpty(accountCouponDto)) {
                return;
            }

            //比较并设置APP专属券响应参数
            compareAndSetAppExclusiveCoupon(payDeskVoV4, accountCouponDto);

            //去除优惠券套餐信息
            remoteCouponActivity(payTradeReq.getUseNewCouponPackage(), payDeskVoV4);

        } catch (Exception e) {
            //报错拦截，不对核心链路产生影响
            log.warn("appExclusiveCountCalculate error,information:", e);
        }
    }

    /**
     * 去除优惠券套餐信息（如果APP专属券抵扣信息存在）
     *
     * @param useNewCouponPackage
     * @param payDeskVoV4
     */
    private void remoteCouponActivity(Boolean useNewCouponPackage, PayDeskVoV4 payDeskVoV4) {
        BigDecimal appExtraPreferential = payDeskVoV4.getAppExtraPreferential();
        // 如果是最新券包信息，不管是否存在APP专属券都需要返回
        if (ObjectUtil.isEmpty(appExtraPreferential) || appExtraPreferential.compareTo(BigDecimal.ZERO) <= 0
                || useNewCouponPackage) {
            return;
        }
        log.info("appExtraPreferential remoteCouponActivity");
        //设置优惠券套餐信息为空
        payDeskVoV4.setCouponActivityDetail(null);
    }

    /**
     * 比较并设置APP专属优惠券
     * 1.计算APP专属券额外优惠金额
     * 2.计算APP专属付款金额
     *
     * @param payDeskVoV4 收银台响应参数
     */
    private void compareAndSetAppExclusiveCoupon(PayDeskVoV4 payDeskVoV4, AccountCouponDto accountCouponDto) {
        /** 第一步：计算APP专属券额外优惠金额 **/
        //APP专属券额外优惠金额
        BigDecimal appExtraPreferential = null;

        //先获取APP专属优惠券金额
        BigDecimal appCouponAmount = new BigDecimal(accountCouponDto.getDecutionAmount());
        //优惠券金额小于等于0，则无需设置APP专属券金额
        if (appCouponAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("beast app coupon amount beyond zero, appCouponAmount:{}", appCouponAmount);
            return;
        }

        //再获取当前选中优惠券金额
        BigDecimal currentCouponAmount = null;
        CouponVo couponVo = payDeskVoV4.getCouponVo();
        //当前选中了优惠券 && 优惠券可以使用（没有选择优惠券互斥的支付方式）
        if (!ObjectUtil.isEmpty(couponVo) && !couponVo.getCannotUseCouponFlag()) {
            currentCouponAmount = couponVo.getDecutionAmount();
        }
        //优惠券金额为null，默认设置为0
        if (ObjectUtil.isEmpty(currentCouponAmount)) {
            currentCouponAmount = BigDecimal.ZERO;
        }
        //计算APP专属券额外优惠金额 = APP专属优惠券金额 - 当前选中优惠券金额
        appExtraPreferential = appCouponAmount.subtract(currentCouponAmount);
        //如果小于等于0，则返回
        if (appExtraPreferential.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("appExtraPreferential beyond zero, appCouponAmount:{}, currentCouponAmount:{}",
                    appCouponAmount, currentCouponAmount);
            return;
        }
        //设置APP专属券额外优惠金额
        payDeskVoV4.setAppExtraPreferential(appExtraPreferential);


        /** 第二步：计算APP专属付款金额 **/
        //APP专属付款金额
        BigDecimal appExclusiveAmount = null;
        //先待支付费用
        BigDecimal actualFare = payDeskVoV4.getActualFare();
        //再计算APP专属付款金额 = 待支付费用-APP专属券额外优惠金额
        appExclusiveAmount = actualFare.subtract(appExtraPreferential);

        //待支付金额小于0，则设置为0
        if (appExclusiveAmount.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("appExclusiveAmount amount beyond zero, appExclusiveAmount:{}", appExclusiveAmount);
            appExclusiveAmount = BigDecimal.ZERO;
        }
        //设置计算APP专属付款金额
        payDeskVoV4.setAppExclusiveAmount(appExclusiveAmount);
    }

    /**
     * APP专属券条件校验
     *
     * @param payTradeReq 请求
     * @param payDeskRes  响应
     * @return
     */
    private boolean appExclusiveConditionCheck(PayTradeReq payTradeReq, PayDeskRes payDeskRes, FareParam fareParam) {
        String terminal = PayUtils.getTerminal(payTradeReq.getGrayVersion());
        //非微信小程序
        if (!NumConstants.STR_8.equals(terminal)) {
            log.info("appExclusiveConditionCheck not wechat applet,ignore");
            return false;
        }

        //APP专属券开关关闭
        if (!switchConfig.isAppExclusive()) {
            log.info("appExclusiveConditionCheck appExclusive is off,ignore");
            return false;
        }

        //优惠券已部分支付（企业优惠券已支付 或 个人优惠券已支付）
        Payed payed = payDeskRes.getPayed();
        BigDecimal companyCouponAmount = payed.getCompanyCouponAmount();
        BigDecimal couponPay = payed.getCouponPay();
        if (ObjectUtils.isNotNull(companyCouponAmount) && companyCouponAmount.compareTo(BigDecimal.ZERO) > 0) {
            log.info("appExclusiveConditionCheck companyCouponAmount is payed,ignore");
            return false;
        }
        if (ObjectUtils.isNotNull(couponPay) && couponPay.compareTo(BigDecimal.ZERO) > 0) {
            log.info("appExclusiveConditionCheck couponPay is payed,ignore");
            return false;
        }

        //企业礼品卡已支付
        BigDecimal companyGiftCardPayed = payed.getCompanyGiftCardPayed();
        if (ObjectUtil.isNotEmpty(companyGiftCardPayed) && companyGiftCardPayed.compareTo(BigDecimal.ZERO) > 0) {
            log.info("appExclusiveConditionCheck companyGiftCardPayed is payed,ignore");
            return false;
        }
        return true;
    }


    /**
     * 构建账户优惠请求参数
     *
     * @return
     */
    private AccountCouponDto buildAccountCoupon(PayDeskRes payDeskRes, PayTradeReq payTradeReq) {
        Response<SrOrderDto> response = srOrderService.getRouteByRoutePlanUuid(payTradeReq.getOrderUuid());
        if (org.springframework.util.ObjectUtils.isEmpty(response) || !response.isSuccess()) {
            return null;
        }
        SrOrderDto srOrderDto = response.getData();
        if (ObjectUtil.isEmpty(srOrderDto)) {
            return null;
        }

        AccountCouponDto accountCouponDto = new AccountCouponDto();
        //APP端
        accountCouponDto.setTerminal("1");
        accountCouponDto.setStatus(1);
        accountCouponDto.setUserId(payTradeReq.getPassengerUuid());
        Integer useType = srOrderDto.getTypeModule();
        accountCouponDto.setUseType(useType == null ? null : useType.toString());
        Integer typeTime = srOrderDto.getTypeTime();
        accountCouponDto.setUseLimit(typeTime == null ? null : typeTime.toString());
        Integer typeTrip = srOrderDto.getTypeTrip();
        accountCouponDto.setUseTrip(typeTrip == null ? null : typeTrip.toString());
        Integer typeVehicle = srOrderDto.getTypeVehicle();
        accountCouponDto.setUseVehicle(typeVehicle == null ? null : typeVehicle.toString());
        accountCouponDto.setCityCode(srOrderDto.getCityCode());
        accountCouponDto.setEstimateAmount(payDeskRes.getFareDetail().getOrderFare().toPlainString());
        accountCouponDto.setSettleDate(srOrderDto.getCreateTime());
        accountCouponDto.setUseSource(CouponUtils.switchCouponUseSource(accountCouponDto,
                srOrderDto.getSource()));
        accountCouponDto.setExpandBizLine(srOrderDto.getExpandBizLine());
        String startRailIds = srOrderDto.getStartRailIds();
        accountCouponDto.setStartRailIds(StringUtil.isEmpty(startRailIds) ? null : startRailIds);
        String endRailIds = srOrderDto.getEndRailIds();
        accountCouponDto.setEndRailIds(StringUtil.isEmpty(endRailIds) ? null : endRailIds);
        return accountCouponDto;
    }


    /**
     * 获取最终余额可用
     *
     * @param req            req
     * @param balaceStrategy balaceStrategy
     * @param fareParam      fareParam
     * @return BigDecimal
     */
    private BigDecimal getBalanceFinal(PayTradeReq req, int balaceStrategy, FareParam fareParam) {
        //附加费 只能三方支付
        BigDecimal balanceCanPay = fareParam.getActualFare().subtract(fareParam.getAdditionalFee());
        //本金和赠金支持跨城费和节日费
        BigDecimal rechargeInfoCanUseMoney = fareParam.getRechargeCanPay().add(fareParam.getRechargeGiftCanPay());
        //企业礼品卡支持跨城费和节日费
        //跨城费、远程调度费和节日费由充值本金和充值赠金 企业礼品卡扣除后 剩余金额
        BigDecimal festAndCrossFare = fareParam.getFestivalFee().add(fareParam.getCrossCityFee())
                .subtract(rechargeInfoCanUseMoney).subtract(fareParam.getCompanyGiftCardCanPay());
        if (festAndCrossFare.compareTo(BigDecimal.ZERO) > 0) {
            //说明充值本金和充值赠金 企业礼品卡扣除后 还有剩余金额 只能三方付
            //当期剩余金额-三方支付的金额  剩下就是余额能够支付的金额
            balanceCanPay = balanceCanPay.subtract(festAndCrossFare);
        }
        // 余额最终可扣减费用
        BigDecimal balanceFinal = BigDecimal.ZERO;
        //附加费不计算，只要余额大于0，就不能显示
        BigDecimal availableBalanceMin = fareParam.getAvailableBalance().compareTo(balanceCanPay) > 0
                ? balanceCanPay : fareParam.getAvailableBalance();
        if (checkPassengerChoose(req, balaceStrategy, balanceCanPay)) {
            balanceFinal = availableBalanceMin;
        }
        return balanceFinal;
    }

    /**
     * 折扣费用
     *
     * @param resp      resp
     * @param fareItems fareItems
     * @param fareParam fareParam
     */
    private void buildDiscountFare(PayDeskVoV4 resp, PassengerRouteFareItemsVo fareItems, FareParam fareParam) {
        // 获取客服折让费
        BigDecimal callDis = getCallDis(resp);
        BigDecimal totalDecAmt = resp.getTotalDecAmt();
        List<PayItem> payItemList = resp.getPayItemList();
        if (ObjectUtil.isNull(fareItems)) {
            return;
        }
        BigDecimal festivalFee = PayUtils.getBigDecimal(fareParam.getFestivalFareTotal());
        BigDecimal crossCityFee = PayUtils.getBigDecimal(fareParam.getCrossCityFareTotal());
        BigDecimal lostReturnFee = PayUtils.getBigDecimal(fareParam.getLostReturnFareTotal());
        BigDecimal compensationFare = PayUtils.getBigDecimal(fareParam.getCompensationFareTotal());
        BigDecimal passengerChoiceFare = PayUtils.getBigDecimal(fareParam.getPassengerChoiceFareTotal());

        // 订单总额 = + 服务费 + 跨成费 + 节日服务费
        BigDecimal totalFare = fareItems.getTotalFare();
        // 凭平台改价
        final BigDecimal advanceDutyAdjustFare = PayUtils.getBigDecimal(fareItems.getAdvanceDutyAdjustFare());
        BigDecimal onePriceCarFare = fareItems.getOnePriceCarFare();
        // 一口价有值取一口价
        if (ObjectUtil.isNotNull(onePriceCarFare) && BigDecimalUtils.greaterThanZero(onePriceCarFare)) {
            totalFare = onePriceCarFare.add(fareParam.getDispatchFareTotal()).add(fareItems.getWaitFare());
        }
        BigDecimal serviceFare = fareParam.getServiceFareTotal();
        totalFare = totalFare.add(serviceFare).add(festivalFee).add(crossCityFee)
                .add(lostReturnFee).add(compensationFare).add(advanceDutyAdjustFare).add(passengerChoiceFare);

        PayItem totalPayItem = new PayItem("订单总额", totalFare.toString(),
                Boolean.FALSE, NUM_0, 0, null);
        if (serviceFare.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + serviceFare.toPlainString() + "元附加费");
        } else if (festivalFee.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + festivalFee.toPlainString() + "元节日服务费");
        } else if (lostReturnFee.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + lostReturnFee.toPlainString() + "元物品遗失返还费");
        } else if (compensationFare.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + compensationFare.toPlainString() + "元取消费");
        }
        payItemList.add(totalPayItem);

        // 会员折扣
        BigDecimal firmDiscountFare = PayUtils.getBigDecimal(fareItems.getFirmDiscountFare());
        if (PayUtils.isGreaterThanZero(firmDiscountFare)) {
            totalDecAmt = totalDecAmt.add(firmDiscountFare);
            payItemList.add(new PayItem("企业折扣",
                    "-" + firmDiscountFare, Boolean.TRUE, NUM_8, NUM_8, null));
        }
        BigDecimal firmMinusFare = PayUtils.getBigDecimal(fareItems.getFirmMinusFare());
        if (PayUtils.isGreaterThanZero(firmMinusFare)) {
            totalDecAmt = totalDecAmt.add(firmMinusFare);
            payItemList.add(new PayItem("企业直减",
                    "-" + firmMinusFare, Boolean.TRUE, NUM_9, NUM_9, null));
        }
        if (callDis.compareTo(BigDecimal.ZERO) != 0) {
            totalDecAmt = totalDecAmt.subtract(callDis);
            payItemList.add(new PayItem("客服折让费", callDis.toPlainString(), Boolean.TRUE, NUM_10, NUM_10, null));
        }
        BigDecimal driverReliefFare = PayUtils.getBigDecimal(fareItems.getDriverReliefFare());
        if (PayUtils.isGreaterThanZero(driverReliefFare)) {
            totalDecAmt = totalDecAmt.add(driverReliefFare);
            payItemList.add(new PayItem("司机减免",
                    "-" + driverReliefFare, Boolean.TRUE, NUM_11, NUM_11, null));
        }
        BigDecimal memberDiscountFare = PayUtils.getBigDecimal(fareItems.getMemberDiscountFare());
        if (PayUtils.isGreaterThanZero(memberDiscountFare)) {
            totalDecAmt = totalDecAmt.add(memberDiscountFare);
            payItemList.add(new PayItem("会员折扣",
                    "-" + memberDiscountFare, Boolean.TRUE, NUM_12, NUM_12, null));
        }
        BigDecimal employeeDiscountFare = PayUtils.getBigDecimal(fareItems.getEmployeeDiscountFare());
        if (PayUtils.isGreaterThanZero(employeeDiscountFare)) {
            totalDecAmt = totalDecAmt.add(employeeDiscountFare);
            payItemList.add(new PayItem("员工专享",
                    "-" + employeeDiscountFare, Boolean.TRUE, NUM_13, NUM_13, null));
        }
        resp.setTotalDecAmt(totalDecAmt);
        // 排序
        Collections.sort(payItemList);
        resp.setPayItemList(payItemList);
    }

    /**
     * 获取客服折让费
     *
     * @param resp resp
     * @return BigDecimal
     */
    private BigDecimal getCallDis(PayDeskVoV4 resp) {
        BigDecimal callDis = BigDecimal.ZERO;
        JSONObject fareDetail = getFareDetail(resp.getOrderUuid(), resp.getPassengerId());
        if (ObjectUtil.isNotNull(fareDetail)) {
            JSONObject modifyDetail = fareDetail.getJSONObject("modifyDetail");
            if (ObjectUtil.isNotNull(modifyDetail)) {
                BigDecimal adjustOrderFare = modifyDetail.getBigDecimal("adjustOrderFare");
                BigDecimal orderFare = modifyDetail.getBigDecimal("orderFare");
                callDis = adjustOrderFare.subtract(orderFare);
                // 附加费调价
                BigDecimal adjustServiceFare = modifyDetail.getBigDecimal("adjustServiceFare");
                BigDecimal serviceFare = modifyDetail.getBigDecimal("serviceFare");
                callDis = callDis.add(adjustServiceFare.subtract(serviceFare));
                // 取消费
                BigDecimal adjustCancelFare = modifyDetail.getBigDecimal("adjustCancelFare");
                BigDecimal cancelFare = modifyDetail.getBigDecimal("cancelFare");
                callDis = callDis.add(adjustCancelFare.subtract(cancelFare));

                // 物品返回单
                BigDecimal adjustLostReturnFare = modifyDetail.getBigDecimal("adjustLostReturnFare");
                BigDecimal lostReturnFare = modifyDetail.getBigDecimal("lostReturnFare");
                callDis = callDis.add(adjustLostReturnFare.subtract(lostReturnFare));

                // 节日服务费
                BigDecimal adjustFestivalFare = modifyDetail.getBigDecimal("adjustFestivalFare");
                BigDecimal festivalFare = modifyDetail.getBigDecimal("festivalFare");
                callDis = callDis.add(adjustFestivalFare.subtract(festivalFare));

                // 跨城费
                BigDecimal adjustCrossCityFare = modifyDetail.getBigDecimal("adjustCrossCityFare");
                BigDecimal crossCityFare = modifyDetail.getBigDecimal("crossCityFare");
                callDis = callDis.add(adjustCrossCityFare.subtract(crossCityFare));

                // 远程调度费
                BigDecimal adjustDispatchFare = modifyDetail.getBigDecimal("adjustDispatchFare");
                BigDecimal dispatchFare = modifyDetail.getBigDecimal("dispatchFare");
                callDis = callDis.add(adjustDispatchFare.subtract(dispatchFare));

                // 赔偿金
                BigDecimal adjustCompensationFare = modifyDetail.getBigDecimal("adjustCompensationFare");
                BigDecimal compensationFare = modifyDetail.getBigDecimal("compensationFare");
                callDis = callDis.add(adjustCompensationFare.subtract(compensationFare));

                // 乘客自选费
                BigDecimal adjustPassengerChoiceFare = modifyDetail.getBigDecimal("adjustPassengerChoiceFare");
                BigDecimal passengerChoiceFare = modifyDetail.getBigDecimal("passengerChoiceFare");
                callDis = callDis.add(adjustPassengerChoiceFare.subtract(passengerChoiceFare));

                // 超时等待费
                BigDecimal adjustSplitWaitFare = modifyDetail.getBigDecimal("adjustSplitWaitFare");
                BigDecimal splitWaitFare = modifyDetail.getBigDecimal("splitWaitFare");
                callDis = callDis.add(adjustSplitWaitFare.subtract(splitWaitFare));
            }
        }
        return callDis;
    }

    /**
     * 获取费用明细
     *
     * @param journeyId   订单uuid
     * @param passengerId 用户id
     * @return {@link JSONObject}
     */
    private JSONObject getFareDetail(String journeyId, String passengerId) {
        com.t3.ts.pay.remote.dto.UnifiedDto dto = new com.t3.ts.pay.remote.dto.UnifiedDto();
        dto.setSceneType("t3pay.pay.route.fareDetail");
        JSONObject extendParam = new JSONObject();
        extendParam.put("journeyId", journeyId);
        extendParam.put("userId", passengerId);
        dto.setExtendParam(extendParam.toJSONString());
        Response handle = unifiedService.handle(dto);
        if (null != handle && Boolean.TRUE.equals(handle.isSuccess())) {
            return JSON.parseObject((String) handle.getData());
        }
        return new JSONObject();
    }

    /**
     * 校验行程状态
     *
     * @param routeInfo 行程详情信息
     * @return boolean
     */
    private boolean checkRouteStatus(RouteInfoDto routeInfo) {
        if (routeInfo == null) {
            return true;
        }
        String status = routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString();
        if (RouteStatus.ROUTE_7.getStatus().equals(status)
                || RouteStatus.ROUTE_8.getStatus().equals(status)
                || RouteStatus.ROUTE_9.getStatus().equals(status)) {
            return false;
        }
        return true;
    }


    /**
     * currentPassengerFare
     *
     * @param resp                  resp
     * @param deskDisplayPayTypes   deskDisplayPayTypes
     * @param isCurrentPassenger    isCurrentPassenger
     * @param fareParam             fareParam
     * @param availableBalanceFinal availableBalanceFinal
     */
    private void currentPassengerFare(PayDeskVoV4 resp, List<Integer> deskDisplayPayTypes,
                                      Boolean isCurrentPassenger, FareParam fareParam,
                                      BigDecimal availableBalanceFinal) {
        if (isCurrentPassenger) {
            // 加上购买券包的钱  减去T币抵扣的钱
            fareParam.setActualFare(fareParam.getActualFare().add(fareParam.getCouponActivityFee()));
            // 【支付项目】最终支付费用
            resp.setActualFare(fareParam.getActualFare());
            resp.setFinalFare(fareParam.getActualFare().subtract(availableBalanceFinal));
            resp.setFinalFareV2(resp.getFinalFare().subtract(fareParam.getCouponActivityFee()));
            //  处理三方支付校验
            setThirdPayTypeToast(resp, fareParam, deskDisplayPayTypes);
            resp.setAvailableBalanceFinal(availableBalanceFinal);
        } else {
            resp.setFinalFare(fareParam.getActualFare());
        }
        log.info("getPayDeskInfoV3 response:{}", resp);
    }

    /**
     * 处理三方支付校验
     *
     * @param resp      resp
     * @param fareParam 费用参数
     * @param payList   支付方式
     */
    private void setThirdPayTypeToast(PayDeskVoV4 resp, FareParam fareParam, List<Integer> payList) {
        if (CollectionUtils.isEmpty(payList)) {
            return;
        }
        List<Integer> thirdPayType = payList.stream()
                .filter(e -> e != EnumPayOrderChannel.INTEGRAL.getCode()
                        && e != EnumPayOrderChannel.UNWARRIED_ARRIVE.getCode()
                        && e != EnumPayOrderChannel.BALANCE.getCode()
                        && e != EnumPayOrderChannel.COUPON.getCode()).collect(Collectors.toList());
        boolean flag = resp.getFinalFare().compareTo(BigDecimal.ZERO) > 0
                && CollectionUtils.isEmpty(thirdPayType)
                && switchConfig.getThirdPayTypeToast()
                && (Objects.isNull(resp.getPayForOtherType())
                || resp.getPayForOtherType()
                != PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType());
        if (flag) {
            resp.setThirdPayTypeToast(Boolean.TRUE);
            //无三方支付 && 只有订单费待支付 && 0 < 余额可支付 < 订单费  给余额提示
            if (payList.contains(EnumPayOrderChannel.BALANCE.getCode())
                    && paymentBusiness.onlyOrderFareToPay(fareParam)
                    && paymentBusiness.balanceCanPayMoreThanZeroLessThanOrderFare(fareParam)) {
                resp.setThirdPayTypeToastMsg("可用余额不足以支付车费，您还需要选择第三方支付");
            } else {
                resp.setThirdPayTypeToastMsg(switchConfig.getThirdPayTypeToastMsg());
            }
        }
    }

    /**
     * getDeskDisplayPayTypeList
     *
     * @param payTradeReq         payTradeReq
     * @param payDeskVoV4         payDeskVoV4
     * @param taxiScanPay         taxiScanPay
     * @param deskDisplayPayTypes deskDisplayPayTypes
     * @param fareParam           fareParam
     * @param existAdditionalFee  existAdditionalFee
     * @return List<Integer>
     */
    private List<Integer> getDeskDisplayPayTypeList(PayTradeReq payTradeReq, PayDeskVoV4 payDeskVoV4,
                                                    Boolean taxiScanPay, List<Integer> deskDisplayPayTypes,
                                                    FareParam fareParam, Boolean existAdditionalFee) {
        //2、账户中是否余额
        Boolean hasBalance = fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0 ? Boolean.TRUE : Boolean.FALSE;
        //3、账户余额是否满足待支付金额
        Boolean balanceEnough = fareParam.getBalanceTotal().compareTo(fareParam.getActualFare()) >= 0
                ? Boolean.TRUE : Boolean.FALSE;
        log.info("deskDisplaypayTypeList={} existAdditionalFee={} hasBalance={} balanceEnough={} "
                        + "getPassengerChooseFlag={}", deskDisplayPayTypes, existAdditionalFee, hasBalance,
                balanceEnough, payTradeReq.getPassengerChooseFlag());
        // 当有选择券包时，当做有附加费处理，券包必须有三方支付
        if (payDeskVoV4.getUseCounponActivity() == NUM_1) {
            existAdditionalFee = Boolean.TRUE;
        }
        if (taxiScanPay) {
            // 出租车扫码支付不展示余额支付
            hasBalance = Boolean.FALSE;
        }
        // 待展示的支付方式处理
        deskDisplayPayTypes = paymentBusiness.payTypeListProc(deskDisplayPayTypes, existAdditionalFee, hasBalance, balanceEnough,
                payTradeReq.getPassengerChooseFlag(), payTradeReq.getApplicationType());
        //如果 可支付金额 有优惠券没有打车金 和礼品卡，支付方式就不能有余额支付
        deskDisplayPayTypes = getBalancePayTypeList(payDeskVoV4, deskDisplayPayTypes, fareParam);
        log.info("deskDisplaypayTypeList={}", JSONObject.toJSONString(deskDisplayPayTypes));
        // 挪个位置，解决出租车扫码付有余额不能支付问题
        if (taxiScanPay) {
            // 出租车扫码支付不展示余额支付
            payDeskVoV4.setBalanceFlag(Boolean.FALSE);
            payDeskVoV4.setBalanceMsg("出租车扫码支付不支持余额支付");
        }
        deskDisplayPayTypes = getDeskDisplayTypeList(payDeskVoV4, deskDisplayPayTypes, fareParam);
        payDeskVoV4.setPayTypeList(deskDisplayPayTypes);
        // 【支付项目】收银台类型
        if (fareParam.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV4.setPayDeskType(1);
        }
        return deskDisplayPayTypes;
    }

    /**
     * getDeskDisplayTypeList
     *
     * @param payDeskVoV4            payDeskVoV4
     * @param deskDisplaypayTypeList deskDisplaypayTypeList
     * @param fareParam              fareParam
     * @return List<Integer>
     */
    private List<Integer> getDeskDisplayTypeList(PayDeskVoV4 payDeskVoV4, List<Integer> deskDisplaypayTypeList,
                                                 FareParam fareParam) {
        //积分使用过时 过滤掉
        if (Objects.nonNull(fareParam.getIntegralPayed())
                && fareParam.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            deskDisplaypayTypeList = deskDisplaypayTypeList.stream()
                    .filter(e -> e != EnumPayOrderChannel.INTEGRAL.getCode()).collect(Collectors.toList());
        }
        //如果返回是选择亲友付，则需要抛出三方支付方式
        if (Objects.nonNull(payDeskVoV4.getPayForOtherType()) && payDeskVoV4.getPayForOtherType()
                == PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType()) {
            deskDisplaypayTypeList = paymentBusiness.filterThirdPaymentWay(deskDisplaypayTypeList);
        }
        return deskDisplaypayTypeList;
    }

    /**
     * 对资产方式显示的处理
     *
     * @param payDeskVoV4            payDeskVoV4
     * @param deskDisplaypayTypeList 桌子displaypay类型列表
     * @param fareParam              费用参数
     * @return {@link List<Integer>}
     */
    private List<Integer> getBalancePayTypeList(PayDeskVoV4 payDeskVoV4, List<Integer> deskDisplaypayTypeList,
                                                FareParam fareParam) {
        //如果余额总金额大于0，且余额或优惠券都未支付   永远展示，
        boolean flag = fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0
                && fareParam.getCouponPay().compareTo(BigDecimal.ZERO) == 0
                && fareParam.getAssetPay().compareTo(BigDecimal.ZERO) == 0
                && fareParam.getUnWarriedArrivePay().compareTo(BigDecimal.ZERO) == 0;
        if (flag) {
            payDeskVoV4.setBalanceFlag(true);
        }
        flag = (fareParam.getRechargeGift().compareTo(BigDecimal.ZERO) > 0
                || fareParam.getRechargeCash().compareTo(BigDecimal.ZERO) > 0) && fareParam.getProductLine() == 1;
        if (flag) {
            payDeskVoV4.setBalanceMsg("出租车产品不支持使用现金余额");
        }
        flag = fareParam.getIsSupportPersonRecharge() && (fareParam.getRechargeGift().compareTo(BigDecimal.ZERO) > 0
                || fareParam.getRechargeCash().compareTo(BigDecimal.ZERO) > 0);
        if (flag) {
            payDeskVoV4.setBalanceMsg("资产扣款顺序：现金余额>打车金>礼品卡，现金余额用尽后再使用打车金，以此类推。");
        }
        payDeskVoV4.setRechargeFlag(NumConstants.NUM_0);
        payDeskVoV4.setCompanyGiftCardFlag(buildCompanyGiftCardFlag(fareParam));
        return deskDisplaypayTypeList;
    }

    /**
     * 构建企业礼品卡余额标识   =1 说明企业礼品卡余额》0
     *
     * @param fareParam 费用参数
     * @return {@link Integer}
     */
    private Integer buildCompanyGiftCardFlag(FareParam fareParam) {
        if (fareParam.getCompanyGiftCard().compareTo(BigDecimal.ZERO) > 0) {
            return NUM_1;
        }
        return 0;
    }

    /**
     * 判断是否有附加费
     *
     * @param fareParam fareParam
     * @return Boolean
     */
    private Boolean hasExistAdditionalFee(FareParam fareParam) {
        //1、是否包含附加费
        Boolean existAdditionalFee = fareParam.getAdditionalFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE;
        //节日费 同 附加费 都需要三方支付
        existAdditionalFee = existAdditionalFee || (fareParam.getFestivalFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getCrossCityFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getLostReturnFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getCompensationFare().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        return existAdditionalFee;
    }

    /**
     * 检查余额提醒通知消息
     *
     * @param param        参数
     * @param resp         收银台返回参数
     * @param routeInfo dto路线计划
     */
    private void checkBalanceNoticeMessage(FareParam param, PayDeskVoV4 resp, RouteInfoDto routeInfo) {
        //余额可支付金额
        BigDecimal availableBalance = param.getAvailableBalance();
        BigDecimal orderFareToPay = param.getOrderFareToPay().add(param.getDispatchFee());
        BigDecimal balanceTotal = param.getBalanceTotal();
        //待支付订单费是否大于0
        boolean orderMoreZero = param.getOrderFareToPay().compareTo(BigDecimal.ZERO) > 0;
        //总余额是否大于0
        boolean balanceTotalMoreZero = balanceTotal.compareTo(BigDecimal.ZERO) > 0;
        //礼品卡余额大于0
        boolean giftCardMoreZero = param.getGiftCard().compareTo(BigDecimal.ZERO) > 0;
        //可用余额大于0
        boolean availableBalanceMoreZero = param.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0;
        //可用打车金>0
        boolean giftMoneyCanPayMoreZero = param.getGiftMoneyCanPay().compareTo(BigDecimal.ZERO) > 0;
        log.info("checkBalanceNoticeMessage:{}", JSONUtil.toJsonStr(param));
        //优惠券可以支付金额
        BigDecimal couponCanPay = BigDecimal.ZERO
                .add(resp.getCouponChecked() ? param.getCouponCanPay() : BigDecimal.ZERO);
        //账户内可用余额＜需支付金额 & 可用余额＜总余额 & 总余额＞0  可用打车金金额>0
        //优惠券、省心打(用户选择)、或两者一起能全额抵扣时，不展示这段提示
        if (availableBalance.compareTo(orderFareToPay) < 0 && availableBalance.compareTo(balanceTotal) < 0
                && balanceTotalMoreZero && orderMoreZero && couponCanPay.compareTo(orderFareToPay) < 0
                && giftMoneyCanPayMoreZero) {
            resp.setBalanceMsg("打车金单日限额50元，仅可支付行程费。");
        }
        //待支付订单费大于0 && 总余额大于零 && 余额可支付等于0  ##并且选择了余额  余额按钮置灰
        if (orderMoreZero && balanceTotalMoreZero && availableBalance.compareTo(BigDecimal.ZERO) < 1
                && resp.getPayTypeList().contains(EnumPayOrderChannel.BALANCE.getCode())) {
            resp.setBalanceFlag(Boolean.FALSE);
        }
        if (null != routeInfo) {
            boolean isTaxi = routeInfo.getTypeModule() != null && routeInfo.getTypeModule() == NUM_1;
            boolean isOnePrice = routeInfo.getFareMethod() != null && routeInfo.getFareMethod() == NUM_3;
            //出租车非一口价订单不能使用礼品卡
            if (isTaxi && !isOnePrice && giftCardMoreZero && orderMoreZero) {
                if (availableBalanceMoreZero) {
                    //如果可用打车金大于0，则给出提示
                    resp.setBalanceMsg(String.format("出租车打表计价订单不支持使用礼品卡，本单可用%s元打车金",
                            param.getAvailableBalance().toPlainString()));
                } else {
                    resp.setBalanceMsg("出租车打表计价订单不支持使用礼品卡");
                }
            }
            final Integer expandBizLine = routeInfo.getExpandBizLine();
            // 不支持的提醒
            if (switchConfig.getThirdTransport().contains(expandBizLine == null ? 0 : expandBizLine)) {
                resp.setBalanceMsg("第三方运力不支持使用余额");
            }
            if (null != routeInfo.getTypeModule() && ExpandBizLineType.HITCH_CAR.getTypeMode().equals(routeInfo.getExpandBizLine())) {
                resp.setBalanceMsg("顺风车不支持使用余额");
            }
        }
    }

    /**
     * 校验充值金与优惠券互斥关系
     * <p>
     * 充值金(含充值赠金)与优惠券互斥
     * 如果券已使用且可用余额中包含充值金(含充值赠金)则余额不可用
     * 企业礼品卡与优惠券互斥
     *
     * @param fareParam 费用详情
     * @param resp      返回端上信息
     */
    private void checkRechargeAndCoupon(FareParam fareParam, PayDeskVoV4 resp) {
        //如果 优惠券或者 企业礼品卡已使用 就展示余额不可勾选
        if ((PayUtils.getBigDecimal(fareParam.getCouponPay()).compareTo(BigDecimal.ZERO) > 0
                || (PayUtils.getBigDecimal(fareParam.getPrivilegePay()).compareTo(BigDecimal.ZERO) > 0))
                && PayUtils.getBigDecimal(fareParam.getCompanyGiftCard()).compareTo(BigDecimal.ZERO) > 0) {
            resp.setBalanceFlag(false);
        }
    }


    /**
     * 处理余额
     *
     * @param payDeskVoV4 收银台
     * @param fareParam   费用
     */
    private void dealBalance(PayDeskVoV4 payDeskVoV4, FareParam fareParam, PreDeskRes preInfo) {

        if (Objects.nonNull(preInfo.getPayConfigDto())
                && Objects.nonNull(fareParam.getProductLine()) && Objects.nonNull(fareParam.getExpandBizLine())) {
            PayConfigDto payConfigDto = preInfo.getPayConfigDto();
            //根据标识判断是否支持个人充值业务
            boolean flag = payConfigDto.getPersonRechargeFlag()
                    && CommonUtils.isContentValue(fareParam.getExpandBizLine(), payConfigDto.getExpandBizLines())
                    && CommonUtils.isContentValue(fareParam.getProductLine(), payConfigDto.getProductLines());
            if (flag) {
                fareParam.setIsSupportPersonRecharge(Boolean.TRUE);
            }
            //说明支持充值业务 不支持叠加 只有充值本金和充值赠金 任一个有钱才显示此文案
            flag = fareParam.getIsSupportPersonRecharge()
                    && (fareParam.getRechargeGiftCanPay().compareTo(BigDecimal.ZERO) > 0
                    || fareParam.getRechargeCanPay().compareTo(BigDecimal.ZERO) > 0);
            if (flag) {
                bulidBanalanceMsg(payDeskVoV4, fareParam,
                        "资产扣款顺序：现金余额>打车金>礼品卡，现金余额用尽后再使用打车金，以此类推。");
                return;
            }
        }
        if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV4.setBalanceFlag(Boolean.TRUE);
        } else if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) <= 0) {
            payDeskVoV4.setBalanceMsg("余额不足");
        }
    }

    /**
     * 建造资产标签描述
     *
     * @param payDeskVoV4 payDeskVoV4
     * @param fareParam   费用参数
     * @param balanceMsg  平衡味精
     */
    private void bulidBanalanceMsg(PayDeskVoV4 payDeskVoV4, FareParam fareParam, String balanceMsg) {
        if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0
                && fareParam.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV4.setBalanceFlag(Boolean.TRUE);
            payDeskVoV4.setBalanceMsg(balanceMsg);
        } else {
            payDeskVoV4.setBalanceMsg("余额不足");
        }
    }


    /**
     * checkPassengerChoose
     *
     * @param payTradeReq    payTradeReq
     * @param balaceStrategy balaceStrategy
     * @param balanceCanPay  balanceCanPay
     * @return boolean
     */
    private boolean checkPassengerChoose(PayTradeReq payTradeReq, int balaceStrategy, BigDecimal balanceCanPay) {
        //bug新增 首次拉起收银台时没带余额支付方式过来,且余额充足时  availableBalanceFinal赋值逻辑走不到
        boolean flag = (Boolean.FALSE.equals(payTradeReq.getPassengerChooseFlag()) && balaceStrategy == 0
                && balanceCanPay.compareTo(BigDecimal.ZERO) > 0)
                || (balaceStrategy != 0 && balanceCanPay.compareTo(BigDecimal.ZERO) > 0);
        return flag;
    }


    /**
     * checkIntegralPayType
     *
     * @param payTradeReq    payTradeReq
     * @param payDeskVoV4    payDeskVoV4
     * @param tMoneyStrategy tMoneyStrategy
     * @return boolean
     */
    private boolean checkIntegralPayType(PayTradeReq payTradeReq, PayDeskVoV4 payDeskVoV4, int tMoneyStrategy) {
        boolean flag = PayUtils.getBigDecimal(payDeskVoV4.getAvailableIntegral()).compareTo(BigDecimal.ZERO) > 0
                && Objects.nonNull(payDeskVoV4.getIntegralDeductFlag()) && payDeskVoV4.getIntegralDeductFlag()
                && ((tMoneyStrategy != 0 && Boolean.TRUE.equals(payTradeReq.getPassengerChooseFlag()))
                || (Boolean.FALSE.equals(payTradeReq.getPassengerChooseFlag()) && payDeskVoV4.getIntegralChecked()));
        return flag;
    }

    /**
     * 处理费用信息
     *
     * @param payDeskVoV4 payDeskVoV4
     * @param fareParam   fareParam
     */
    private void buildFareParam(PayDeskVoV4 payDeskVoV4, FareParam fareParam) {
        // 【支付项目】待支付金额
        payDeskVoV4.setActualFare(fareParam.getActualFare());
        // 【支付项目】附加费
        payDeskVoV4.setAdditionalFee(fareParam.getAdditionalFee());
        payDeskVoV4.setFestivalFee(fareParam.getFestivalFee());
        payDeskVoV4.setCrossCityFee(fareParam.getCrossCityFee());
        payDeskVoV4.setDispatchFee(fareParam.getDispatchFee());
        payDeskVoV4.setLostReturnFee(fareParam.getLostReturnFee());
        payDeskVoV4.setCompensationFare(fareParam.getCompensationFare());
        // 【支付项目】钱包余额
        payDeskVoV4.setBalance(fareParam.getBalanceTotal());
        // 【支付项目】积分（T币）抵扣  若已使用积分抵扣费用，则APP端不展示积分抵扣选项
        payDeskVoV4.setIntegralDeductFlag(Boolean.FALSE);
    }

    /**
     * 获取支付明细列表
     *
     * @param payDeskVoV4        收银台信息
     * @param payDeskRes         pay-center返回收银台信息
     * @param payTradeReq        请求参数
     * @param fareParam          支付费用明细
     * @param isCurrentPassenger 是否是本人
     * @param orgCode            orgCode
     * @return 支付明细项
     */
    private List<PayItem> buildPayItemList(PayDeskVoV4 payDeskVoV4, PayDeskRes payDeskRes, PayTradeReq payTradeReq,
                                           FareParam fareParam, Boolean isCurrentPassenger, String orgCode) {
        // 已支付费用项
        List<PayItem> payItemList = new ArrayList<>();
        // 可选择支付渠道
        List<PayItem> paySelectItemList = new ArrayList<>();

        // 【支付项明细列表】1:企业支付
        if (fareParam.getEnterprisePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("企业支付", "-" + fareParam.getEnterprisePay(), Boolean.FALSE, 1, null));
        }

        Payed payed = payDeskRes.getPayed();

        // 【支付项明细列表】3:优惠券已抵扣
        if (isCurrentPassenger) {
            if (fareParam.getCouponPay().compareTo(BigDecimal.ZERO) > 0
                    || fareParam.getCompanyCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal couponPayed;
                if (fareParam.getCouponPay().compareTo(BigDecimal.ZERO) > 0) {
                    couponPayed = fareParam.getCouponPay();
                } else {
                    couponPayed = fareParam.getCompanyCouponAmount();
                }
                payDeskVoV4.setCouponDeductFlag(Boolean.TRUE);
                payItemList.add(new PayItem("优惠券已抵扣", "-" + couponPayed,
                        Boolean.FALSE, NUM_1, NUM_2, null));
                payDeskVoV4.setTotalDecAmt(payDeskVoV4.getTotalDecAmt().add(couponPayed));
            } else {
                log.info("PaymentBusiness.unWarriedCoupon:{}", JSONUtil.toJsonStr(payDeskVoV4));
                String useCouponType = payTradeReq.getUseCouponType();
                // 【支付项明细列表】3:优惠券
                CouponVo couponVo = payDeskVoV4.getCouponVo();
                couponVo.setCannotUseCouponFlag(fareParam.getCannotUseCouponFlag());
                // 有券包，并且有企业礼品卡，只能购买不能使用里面的券
                Boolean noUseCouponActivity = payTradeReq.getUseNewCouponPackage()
                        && null != payTradeReq.getUseCounponActivity()
                        && NUM_1 == payTradeReq.getUseCounponActivity()
                        && fareParam.getCannotUseCouponFlag()
                        && null != payDeskVoV4.getCouponActivityDetail();
                if (noUseCouponActivity) {
                    payDeskVoV4.setUseCounponActivity(NUM_2);
                }
                // 1:购买券套餐并且使用 2:只购买
                Integer useCounponActivity = payDeskVoV4.getUseCounponActivity();
                CouponActivityDetail couponActivityDetail = payDeskVoV4.getCouponActivityDetail();

                buildCannotUseCouponMsg(fareParam, couponVo);
                if (StringUtils.isNotBlank(useCouponType) && !NumConstants.STR_0.equals(useCouponType)) {
                    payDeskVoV4.setCouponChecked(Boolean.TRUE);
                }
                // 专享判断
                if (StringUtils.isNotBlank(couponVo.getItemSubType())
                        && couponVo.getItemSubType().equals(NumConstants.STR_1)) {
                    JSONObject resp = marketingRest.showAppGuide(payDeskVoV4.getPassengerId(), orgCode);
                    if (resp != null && StringUtils.isNotBlank(resp.getString(PRIVILEGE_SHOW))
                            && resp.getBoolean(COUPON_SHOW)) {
                        couponVo.setItemSubTitle("APP专享");
                    }
                }
                // 券套餐
                if (null != couponActivityDetail && NUM_1 == useCounponActivity) {
                    payDeskVoV4.setCouponChecked(Boolean.TRUE);
                    couponVo.setCouponId(couponActivityDetail.getCouponNewUuid());
                    couponVo.setDecutionAmount(couponActivityDetail.getDecutionAmount());
                }
                paySelectItemList.add(new PayItem("优惠券", null, Boolean.FALSE, NUM_1, NUM_2, couponVo));
                if (couponVo.getDecutionAmount() != null && !"0".equals(payTradeReq.getUseCouponType())) {
                    payDeskVoV4.setTotalDecAmt(payDeskVoV4.getTotalDecAmt().add(couponVo.getDecutionAmount()));
                }
            }
        }

        // 【支付项明细列表】3:权益卡已支付
        if (isCurrentPassenger) {
            String usePrivilegeType = payTradeReq.getUsePrivilegeType();
            PrivilegeDetail privilegeDetail = payDeskRes.getPrivilegeDetail();
            String bestPrivilege = privilegeDetail.getCanUsePrivilege();
            // 权益卡已抵扣
            if (fareParam.getPrivilegePay().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal privilegePayed = fareParam.getPrivilegePay();
                payDeskVoV4.setPrivilegeDeductFlag(Boolean.TRUE);
                payDeskVoV4.setTotalDecAmt(payDeskVoV4.getTotalDecAmt().add(privilegePayed));
                payItemList.add(new PayItem("权益卡已抵扣", "-" + privilegePayed,
                        Boolean.FALSE, NUM_3, NUM_3, null));
            } else if (StringUtils.isNotBlank(usePrivilegeType)
                    && !"0".equals(usePrivilegeType)) {
                // 选择使用权益卡
                displayPrivilege(payDeskVoV4, orgCode, paySelectItemList, privilegeDetail, bestPrivilege, fareParam);
            } else if (StringUtils.isNotBlank(usePrivilegeType)
                    && "0".equals(usePrivilegeType) && fareParam.getCannotUsePrivilegeFlag()) {
                CouponVo couponVo = new CouponVo();
                couponVo.setCannotUseCouponFlag(true);
                couponVo.setCannotUseCouponMsg("企业礼品卡不支持叠加权益卡");
                paySelectItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
            } else if (PayUtils.compareGrayBuild(payTradeReq.getGrayBuild(), NumConstants.STR_470)
                    && StringUtils.isNotEmpty(bestPrivilege)) {
                //兼容老版本，老版本不返回权益卡费用项
                // 不使用权益卡但是有可用卡的情况，不使用也要返回支付列表
                payDeskVoV4.setPrivilegeChecked(Boolean.FALSE);
                CouponVo couponVo = new CouponVo();
                paySelectItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
            }
        }

        // 【支付项明细列表】4:预付款已支付
        if (fareParam.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("已预付", "-" + fareParam.getPrPayed(),
                    Boolean.FALSE, NUM_4, NUM_4, null));
        }
        // 【支付项明细列表】5:余额已支付
        if (fareParam.getBalancePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("余额已支付", "-" + fareParam.getBalancePay(),
                    Boolean.FALSE, NUM_5, NUM_5, null));
        }
        // 【支付项明细列表】6:T币已抵扣
        if (fareParam.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("T币已抵扣", "-" + fareParam.getIntegralPayed(),
                    Boolean.FALSE, NUM_6, NUM_6, null));
        }
        payDeskVoV4.setPayItemList(payItemList);
        payDeskVoV4.setPaySelectItemList(paySelectItemList);
        return payItemList;
    }

    /**
     * 显示权益卡信息
     *
     * @param payDeskVoV4       支付桌子vo v4
     * @param orgCode           组织代码
     * @param paySelectItemList 支付选择项目列表
     * @param privilegeDetail   特权的细节
     * @param bestPrivilege     最好的特权
     * @param fareParam         费用参数
     */
    private void displayPrivilege(PayDeskVoV4 payDeskVoV4, String orgCode, List<PayItem> paySelectItemList,
                                  PrivilegeDetail privilegeDetail, String bestPrivilege, FareParam fareParam) {
        BigDecimal privilegeDecutionAmount = PayUtils.getBigDecimal(privilegeDetail.getDecutionAmount());
        // 有可用权益卡才显示
        if (privilegeDecutionAmount.compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV4.setPrivilegeChecked(Boolean.TRUE);
            CouponVo couponVo = new CouponVo();
            if (StringUtils.isNotEmpty(bestPrivilege)) {
                AccountPrivilegeDto privilege = JSON.parseObject(bestPrivilege, AccountPrivilegeDto.class);
                couponVo.setDecutionAmount(privilegeDecutionAmount);
                couponVo.setCouponId(privilege.getCardUuid());
                couponVo.setCouponCount(privilege.getAvailableCount());
                Integer privilegeSubType = privilege.getPrivilegeSubType();
                if (privilegeSubType != null && privilege.getUseChannel() != null
                        && privilege.getPrivilegeSubType().equals(NUM_1)
                        && privilege.getUseChannel().equals(NUM_1)) {
                    // 判断是否需要展示
                    JSONObject resp = marketingRest.showAppGuide(payDeskVoV4.getPassengerId(), orgCode);
                    if (resp != null && StringUtils.isNotBlank(resp.getString(PRIVILEGE_SHOW))
                            && resp.getBoolean(PRIVILEGE_SHOW)) {
                        couponVo.setItemSubTitle(FREE_CANCEL_TITLE + privilege.getPrivilegeName());
                    }
                }
            }
            paySelectItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
            payDeskVoV4.setTotalDecAmt(payDeskVoV4.getTotalDecAmt().add(privilegeDecutionAmount));
        } else if (fareParam.getCannotUsePrivilegeFlag()) {
            CouponVo couponVo = new CouponVo();
            couponVo.setCannotUseCouponFlag(true);
            couponVo.setCannotUseCouponMsg("企业礼品卡不支持叠加权益卡");
            paySelectItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
        }
    }

    /**
     * 获取优惠券不支持的文案
     *
     * @param fareParam 费用参数
     * @param couponVo  优惠券签证官
     */
    private void buildCannotUseCouponMsg(FareParam fareParam, CouponVo couponVo) {
        if (!fareParam.getCannotUseCouponFlag()) {
            return;
        }
        switch (fareParam.getCouponNotUserType()) {
            case NUM_0:
                couponVo.setCannotUseCouponMsg("现金余额不支持叠加优惠券");
                break;
            case NUM_1:
                couponVo.setCannotUseCouponMsg("企业礼品卡不支持叠加优惠券");
                break;
            case NUM_2:
                couponVo.setCannotUseCouponMsg("现金余额和企业礼品卡不支持叠加优惠券");
                break;
            default:
                break;
        }
    }

    /**
     * 已支付费用明细
     *
     * @param req            req
     * @param payDeskRes     payDeskRes
     * @param resp           resp
     * @param activityAmount activityAmount
     * @return {@link FareParam}
     */
    private FareParam buildDeskFareInfo(PayTradeReq req, PayDeskRes payDeskRes,
                                        PayDeskVoV4 resp, BigDecimal activityAmount) {
        // 【收银台数据对象】
        FareDetail deskFareDetail = payDeskRes.getFareDetail();
        Payed payed = payDeskRes.getPayed();
        BalanceCanPayDetail balanceCanPayDetail = payDeskRes.getBalanceCanPayDetail();
        CouponVo couponVo = resp.getCouponVo();
        CouponActivityDetail couponActivityDetail = resp.getCouponActivityDetail();
        Integer useCounponActivity = resp.getUseCounponActivity();
        // 不返回券套餐时，券套餐设置为不使用
        if (couponActivityDetail == null) {
            resp.setUseCounponActivity(NUM_0);
        }
        // 待支付的钱
        BigDecimal remainPay = PayUtils.getBigDecimal(deskFareDetail.getRemainPay());
        // 如果乘客选择使用券包的券 还减掉券包中优惠券可抵扣的金额
        if (null != useCounponActivity && NUM_1 == useCounponActivity && couponActivityDetail != null) {
            remainPay = remainPay.subtract(PayUtils.getBigDecimal(couponActivityDetail.getDecutionAmount()));
        }

        FareParam fareParam = new FareParam();
        BigDecimal decutionAmount = couponVo == null ? BigDecimal.ZERO : PayUtils.getBigDecimal(couponVo.getDecutionAmount());

        //设置优惠券可抵扣金额（用户选择优惠券、或者系统评估到可用优惠券时可能大于0，不代表实际就会使用）
        fareParam.setCouponCanPay(decutionAmount);

        // 乘客没开通省心打
        if (couponVo != null) {
            //选择优惠券
            if (!NumConstants.STR_0.equals(req.getUseCouponType())
                    && decutionAmount.compareTo(BigDecimal.ZERO) > 0
                    && null != useCounponActivity && NUM_1 != useCounponActivity) {
                fareParam.setActualFare(PayUtils.getBigDecimal(remainPay).subtract(decutionAmount));
            } else {
                // 没选择优惠券
                fareParam.setActualFare(PayUtils.getBigDecimal(remainPay));
            }
        }
        // 权益卡抵扣需要在优惠券之后
        privilegeFareParam(req.getUsePrivilegeType(), payDeskRes.getPrivilegeDetail(), remainPay, fareParam);
        //待支付附加费
        fareParam.setAdditionalFee(PayUtils.getBigDecimal(deskFareDetail.getServiceFare()));
        fareParam.setFestivalFee(PayUtils.getBigDecimal(deskFareDetail.getFestivalFare()));
        fareParam.setCrossCityFee(PayUtils.getBigDecimal(deskFareDetail.getCrossCityFare()));
        fareParam.setDispatchFee(PayUtils.getBigDecimal(deskFareDetail.getDispatchFare()));
        fareParam.setLostReturnFee(PayUtils.getBigDecimal(deskFareDetail.getLostReturnFare()));
        fareParam.setCompensationFare(PayUtils.getBigDecimal(deskFareDetail.getCompensationFare()));

        fareParam.setServiceFareTotal(PayUtils.getBigDecimal(deskFareDetail.getServiceFareTotal()));
        fareParam.setFestivalFareTotal(PayUtils.getBigDecimal(deskFareDetail.getFestivalFareTotal()));
        fareParam.setCrossCityFareTotal(PayUtils.getBigDecimal(deskFareDetail.getCrossCityFareTotal()));
        fareParam.setDispatchFareTotal(PayUtils.getBigDecimal(deskFareDetail.getDispatchFareTotal()));
        fareParam.setLostReturnFareTotal(PayUtils.getBigDecimal(deskFareDetail.getLostReturnFareTotal()));
        fareParam.setCompensationFareTotal(PayUtils.getBigDecimal(deskFareDetail.getCompensationFareTotal()));
        fareParam.setPassengerChoiceFareTotal(PayUtils.getBigDecimal(deskFareDetail.getPassengerChoiceFareTotal()));
        //待支付订单费
        fareParam.setOrderFareToPay(PayUtils.getBigDecimal(deskFareDetail.getOrderFare()));
        //待支付总费用
        fareParam.setTotalFare(PayUtils.getBigDecimal(deskFareDetail.getRemainPay()));
        if (Objects.nonNull(payed)) {
            fareParam.setTotalFare(fareParam.getTotalFare().add(PayUtils.getBigDecimal(payed.getAmountPayed())));
        }
        if (StringUtils.isNotBlank(deskFareDetail.getCancelFare())) {
            fareParam.setCancelFare(BigDecimal.valueOf(Double.valueOf(deskFareDetail.getCancelFare())));
        }
        if (Objects.nonNull(payDeskRes.getBalance())) {
            //钱包打车金总余额
            balanceFareParam(payDeskRes.getBalance(), fareParam);
        }
        if (Objects.nonNull(balanceCanPayDetail)) {
            buildBalanceFare(balanceCanPayDetail, fareParam);
        }
        if (Objects.nonNull(payed)) {
            buildPayed(payed, fareParam);
        }
        if (Objects.nonNull(payDeskRes.getRouteInfo())) {
            fareParam.setExpandBizLine(payDeskRes.getRouteInfo().getExpandBizLine());
            fareParam.setProductLine(payDeskRes.getRouteInfo().getProductLine());
        }
        fareParam.setCouponActivityFee(activityAmount);
        return fareParam;
    }

    /**
     * 权益卡费用详情
     *
     * @param usePrivilegeType usePrivilegeType
     * @param privilegeDetail  privilegeDetail
     * @param remainPay        remainPay
     * @param fareParam        fareParam
     */
    private void privilegeFareParam(String usePrivilegeType, PrivilegeDetail privilegeDetail,
                                    BigDecimal remainPay, FareParam fareParam) {
        if (null != privilegeDetail && StringUtils.isNotBlank(usePrivilegeType)
                && !"0".equals(usePrivilegeType)) {
            BigDecimal privilegeDecutionAmount = PayUtils.getBigDecimal(privilegeDetail.getDecutionAmount());
            fareParam.setPrivilegeCanPay(privilegeDecutionAmount);
            BigDecimal subtract;
            if (null == fareParam.getActualFare()) {
                subtract = PayUtils.getBigDecimal(remainPay).subtract(privilegeDecutionAmount);
            } else {
                subtract = fareParam.getActualFare().subtract(privilegeDecutionAmount);
            }
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                subtract = BigDecimal.ZERO;
            }
            fareParam.setActualFare(subtract);
        }
    }

    /**
     * 余额费用详情
     *
     * @param balance   balance
     * @param fareParam fareParam
     */
    private void balanceFareParam(Balance balance, FareParam fareParam) {
        fareParam.setGiftCurrency(PayUtils.getBigDecimal(balance.getGiftCurrency()));
        fareParam.setBalanceTotal(PayUtils.getBigDecimal(balance.getBalanceTotal()));
        //钱包礼品卡总余额
        fareParam.setGiftCard(PayUtils.getBigDecimal(balance.getGiftCard()));
        fareParam.setRechargeGift(PayUtils.getBigDecimal(balance.getRechargeGift()));
        fareParam.setRechargeCash(PayUtils.getBigDecimal(balance.getRechargeCash()));
        fareParam.setGiftCash(PayUtils.getBigDecimal(balance.getGiftCash()));
        fareParam.setCompanyGiftCard(PayUtils.getBigDecimal(balance.getCompanyGiftCard()));
        fareParam.setRechargeFlag(balance.getRechargeFlag());
    }

    /**
     * 组装已支付
     *
     * @param payed     payed
     * @param fareParam fareParam
     */
    private void buildPayed(Payed payed, FareParam fareParam) {
        fareParam.setUnWarriedArrivePay(PayUtils.getBigDecimal(payed.getUnWarriedArrivePay()));
        fareParam.setIntegralPayed(PayUtils.getBigDecimal(payed.getIntegralPayed()));
        fareParam.setPrPayed(PayUtils.getBigDecimal(payed.getPrPayed()));
        fareParam.setCouponPay(PayUtils.getBigDecimal(payed.getCouponPay()));
        fareParam.setPrivilegePay(PayUtils.getBigDecimal(payed.getPrivilegePay()));
        fareParam.setBalancePay(PayUtils.getBigDecimal(payed.getBalancePay())
                .add(PayUtils.getBigDecimal(payed.getGiftCardPayed()))
                .add(PayUtils.getBigDecimal(payed.getCompanyGiftCardPayed())));
        fareParam.setAssetPay(PayUtils.getBigDecimal(payed.getRechargeCashPay())
                .add(PayUtils.getBigDecimal(payed.getRechargeGiftPay()))
                .add(PayUtils.getBigDecimal(payed.getGiftCashPay()))
                .add(PayUtils.getBigDecimal(payed.getGiftCardPayed()))
                .add(PayUtils.getBigDecimal(payed.getCompanyGiftCardPayed())));
        fareParam.setRechargeCashPay(PayUtils.getBigDecimal(payed.getRechargeCashPay()));
        fareParam.setRechargeGiftPay(PayUtils.getBigDecimal(payed.getRechargeGiftPay()));
        fareParam.setGiftCashPay(PayUtils.getBigDecimal(payed.getGiftCashPay()));
        fareParam.setCompanyGiftCardPayed(PayUtils.getBigDecimal(payed.getCompanyGiftCardPayed()));
        // 企业支付金额
        BigDecimal companyPayedAmount = PayUtils.getBigDecimal(payed.getCompanyPayedAmount());
        BigDecimal companyCouponAmount = PayUtils.getBigDecimal(payed.getCompanyCouponAmount());
        BigDecimal enterprisePay = companyPayedAmount.subtract(companyCouponAmount);
        fareParam.setEnterprisePay(enterprisePay);
        fareParam.setCompanyCouponAmount(companyCouponAmount);
    }

    /**
     * 组装余额可支付
     *
     * @param balanceCanPayDetail balanceCanPayDetail
     * @param fareParam           fareParam
     */
    private void buildBalanceFare(BalanceCanPayDetail balanceCanPayDetail, FareParam fareParam) {
        fareParam.setAvailableBalance(PayUtils.getBigDecimal(balanceCanPayDetail.getGiftCardCanPay())
                .add(PayUtils.getBigDecimal(balanceCanPayDetail.getGiftMoneyCanPay()))
                .add(PayUtils.getBigDecimal(balanceCanPayDetail.getRechargeMoneyCanPay()))
                .add(PayUtils.getBigDecimal(balanceCanPayDetail.getRechargeGiftMoneyCanPay()))
                .add(PayUtils.getBigDecimal(balanceCanPayDetail.getCompanyGiftCardCanPay())));
        fareParam.setGiftCardCanPayService(balanceCanPayDetail.getGiftCardCanPayService());
        fareParam.setGiftCardCanPayFestival(balanceCanPayDetail.getGiftCardCanPayFestival());
        fareParam.setGiftCardCanPayCrossCity(balanceCanPayDetail.getGiftCardCanPayCrossCity());
        fareParam.setGiftCardCanPayDispatch(balanceCanPayDetail.getGiftCardCanPayDispatch());
        fareParam.setRechargeCanPay(PayUtils.getBigDecimal(balanceCanPayDetail.getRechargeMoneyCanPay()));
        fareParam.setRechargeGiftCanPay(PayUtils.getBigDecimal(balanceCanPayDetail.getRechargeGiftMoneyCanPay()));
        fareParam.setGiftMoneyCanPay(PayUtils.getBigDecimal(balanceCanPayDetail.getGiftMoneyCanPay()));
        //礼品卡可支付金额
        fareParam.setGiftCardCanPay(PayUtils.getBigDecimal(balanceCanPayDetail.getGiftCardCanPay()));
        //企业礼品卡可支付金额
        fareParam.setCompanyGiftCardCanPay(PayUtils.getBigDecimal(balanceCanPayDetail.getCompanyGiftCardCanPay()));
        boolean isRechargeCanPay = fareParam.getRechargeCanPay().compareTo(BigDecimal.ZERO) > 0
                || fareParam.getRechargeGiftCanPay().compareTo(BigDecimal.ZERO) > 0;
        Integer couponNotUseType = null;
        if (fareParam.getCompanyGiftCardCanPay().compareTo(BigDecimal.ZERO) > 0) {
            fareParam.setCannotUseCouponFlag(Boolean.TRUE);
            couponNotUseType = Objects.isNull(couponNotUseType) ? NumConstants.NUM_1 : NumConstants.NUM_2;
            fareParam.setCannotUsePrivilegeFlag(Boolean.TRUE);
        }
        fareParam.setCouponNotUserType(couponNotUseType);
    }

    /**
     * 判断是否当前登录账号
     *
     * @param passengerId  passengerId
     * @param routeInfo routePlanDto
     * @return Boolean
     */
    private Boolean checkIsCurrentPassenger(String passengerId, RouteInfoDto routeInfoDto, RouteInfo routeInfo) {
        Boolean isCurrentPassenger = Boolean.TRUE;
        if (routeInfoDto == null) {
            return isCurrentPassenger;
        }
        // 顺风车取消费 司机支付
        if (null != routeInfo && null != routeInfo.getOrderReceiveTarget() && routeInfo.getOrderReceiveTarget() == NUM_2) {
            isCurrentPassenger = passengerId.equals(routeInfoDto.getDriverUuid());
            return isCurrentPassenger;
        }
        if (null != routeInfoDto) {
            isCurrentPassenger = passengerId.equals(routeInfoDto.getPassengerUuid());
        }
        return isCurrentPassenger;
    }

    /**
     * 设置亲友代付标识
     *
     * @param resp           resp
     * @param payDeskRes     payDeskRes
     * @param activityAmount activityAmount
     * @param req            req
     * @return activityAmount
     */
    private BigDecimal processPayForOtherType(PayDeskVoV4 resp, PayDeskRes payDeskRes,
                                              BigDecimal activityAmount, PayTradeReq req) {
        PayForOtherDetail payForOtherDetail = payDeskRes.getPayForOtherDetail();
        if (!Objects.nonNull(payForOtherDetail)) {
            return activityAmount;
        }
        resp.setPayForOtherType(payForOtherDetail.getPayForOtherType());
        resp.setDisableForOtherDesc(payForOtherDetail.getDisableForOtherDesc());

        // 判断是不是老年用车 老年用车无亲友关系，则券套餐不展示
        if (Objects.nonNull(payForOtherDetail.getPayForOtherType())
                && PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType() != payForOtherDetail.getPayForOtherType()) {
            activityAmount = BigDecimal.ZERO;
            // 券套餐为空或者不是最优的情况下设置为0 不购买不展示
            resp.setCouponActivityDetail(null);
            resp.setUseCounponActivity(NUM_0);
        }
        // 兼容老版本
        if (!req.getUseNewCouponPackage() && checkCouponActivity(req)) {
            resp.setUseCounponActivity(NUM_0);
        }
        return activityAmount;
    }

    /**
     * checkCouponActivity
     *
     * @param payTradeReq payTradeReq
     * @return boolean
     */
    private boolean checkCouponActivity(PayTradeReq payTradeReq) {
        return (null != payTradeReq.getUnWarriedArrive() && NUM_1 == payTradeReq.getUnWarriedArrive())
                || !(null != payTradeReq.getUseCouponType() && "2".equals(payTradeReq.getUseCouponType())
                && null != payTradeReq.getUseCounponActivity()
                && NUM_1 == payTradeReq.getUseCounponActivity());
    }

    /**
     * 设置券套餐相关的参数
     *
     * @param req          收银台返回的数据
     * @param couponDetail 优惠券信息
     * @param resp         resp
     * @return 券套餐金额
     */
    private BigDecimal processCouponActivity(PayTradeReq req, CouponDetail couponDetail, PayDeskVoV4 resp,
                                             RouteInfoDto routeInfo) {
        BigDecimal activityAmount = BigDecimal.ZERO;
        if (null != couponDetail) {
            CouponActivityDetail activityDetail = couponDetail.getCouponActivityDetail();
            if ((null != activityDetail && null != activityDetail.getBest() && activityDetail.getBest())
                    || (null != activityDetail && req.getUseNewCouponPackage())) {
                resp.setCouponActivityDetail(activityDetail);
                // 判断券包是否勾选
                processCouponPackageCheck(req, resp, routeInfo);
                // 购买券包的金额
                boolean flag = null != req.getUseCounponActivity()
                        && (NUM_1 == req.getUseCounponActivity() || NUM_2 == req.getUseCounponActivity());
                if (flag) {
                    activityAmount = PayUtils.getBigDecimal(activityDetail.getActivityDecutionAmount());
                }
            } else {
                // 券套餐为空或者不是最优的情况下设置为0 不购买不展示
                resp.setCouponActivityDetail(null);
                resp.setUseCounponActivity(NUM_0);
            }
            resp.setCouponSourceType(couponDetail.getCouponSourceType());
            resp.setActivitySourceType(couponDetail.getActivitySourceType());
            // 设置sourceInfo 信息
            resp.setScoreButton(couponDetail.getScoreButton());
            resp.setScoreDesc(couponDetail.getScoreDesc());
            resp.setSourceType(couponDetail.getSourceType());
            resp.setSourceName(couponDetail.getSourceName());
            resp.setSourceIconUrl(couponDetail.getSourceIconUrl());

        }
        return activityAmount;
    }

    /**
     * 重写获取优惠券数量的方法，信息由支付返回
     *
     * @param couponNum couponNum
     * @return Integer
     */
    private Integer getAvailableCouponCnt(String couponNum) {
        log.info("PayService.deskInfo.getAvailableCouponCnt: {}", couponNum);
        if (StringUtils.isBlank(couponNum)) {
            return NUM_0;
        }
        ChannelCouponNumVo channelCouponNumVo = JSON.parseObject(couponNum, ChannelCouponNumVo.class);
        if (Objects.nonNull(channelCouponNumVo)) {
            return channelCouponNumVo.getCanUseNum();
        }
        return NUM_0;
    }


    /**
     * 获取【支付项列表】优惠券抵扣信息
     *
     * @param payDeskRes 支付收银台返回信息
     * @return 优惠券信息
     */
    private CouponVo processCoupon(PayDeskRes payDeskRes) {
        CouponVo couponVo = new CouponVo();
        List<CanUseCoupon> canUseCouponList = Lists.newArrayList();
        CouponDetail couponDetail = payDeskRes.getCouponDetail();
        if (Objects.nonNull(payDeskRes.getCouponDetail())) {
            List<String> canUseCouponStrList = payDeskRes.getCouponDetail().getCanUseCoupon();
            if (CollectionUtils.isNotEmpty(canUseCouponStrList)) {
                for (String str : canUseCouponStrList) {
                    CanUseCoupon canUseCoupon = JSON.parseObject(str, CanUseCoupon.class);
                    canUseCouponList.add(canUseCoupon);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(canUseCouponList)) {
            if (Objects.nonNull(couponDetail)) {
                BigDecimal decutionAmount = PayUtils.getBigDecimal(couponDetail.getDecutionAmount());
                if (decutionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    couponVo.setCouponCount(canUseCouponList.size());
                    couponVo.setCouponId(canUseCouponList.get(0).getCouponId());
                    couponVo.setDecutionAmount(couponDetail.getDecutionAmount());
                    String thirdChannel = couponDetail.getThirdChannel();
                    couponVo.setThirdChannel(thirdChannel);
                    if (StringUtils.isNotEmpty(thirdChannel)) {
                        couponVo.setThirdChannelList(Lists.newArrayList(thirdChannel.split(",")));
                    }
                    couponVo.setItemSubType(canUseCouponList.get(0).getTerminal());
                    JSONObject extendValue = JSON.parseObject(canUseCouponList.get(0).getExtendValue());
                    if (extendValue != null) {
                        couponVo.setExpandedInfo(extendValue.getString("expandedInfo"));
                    }
                }
            }
        }
        // 获取可用渠道券数量
        couponVo.setCouponCount(getAvailableCouponCnt(payDeskRes.getCouponNum()));
        return couponVo;
    }

    /**
     * 权益卡和优惠券的金额转化为元，并返回收银台对象
     *
     * @param payDeskResp payDeskResp
     * @return {@link PayDeskRes}
     */
    private PayDeskRes deductionAmountToDoubleString(Response payDeskResp) {
        JSONObject payDeskRespData = JSON.parseObject((String) payDeskResp.getData());
        JSONObject couponDetailJson = payDeskRespData.getJSONObject("couponDetail");
        if (null != couponDetailJson
                && couponDetailJson.containsKey("decutionAmount")) {
            Object amount = couponDetailJson.get("decutionAmount");
            if (amount instanceof Integer) {
                couponDetailJson.put("decutionAmount", AccountUtils.toDoubleString((Integer) amount));
            }
        }

        JSONObject privilegeDetailJson = payDeskRespData.getJSONObject("privilegeDetail");
        if (null != privilegeDetailJson
                && privilegeDetailJson.containsKey("decutionAmount")) {
            Object amount = privilegeDetailJson.get("decutionAmount");
            if (amount instanceof Integer) {
                privilegeDetailJson.put("decutionAmount", AccountUtils.toDoubleString((Integer) amount));
            }
        }

        return JSON.parseObject(payDeskRespData.toString(), PayDeskRes.class);
    }

    /**
     * 查询支付收银台
     *
     * @param payDeskReq 收银台请求
     * @return 查询支付收银台
     */
    private Response paydesk(PayDeskReq payDeskReq) {
        try {
            UnifiedDto dto = new UnifiedDto();
            dto.setSceneType("t3pay.pay.paydesk.deskPro");
            dto.setExtendParam(JSON.toJSONString(payDeskReq));
            Response payDeskResp = unifiedService.handle(dto);
            log.info("deskInfo.pay-center.request:{},resp:{}", JSONUtil.toJsonStr(payDeskReq), JSONUtil.toJsonStr(payDeskResp));
            return payDeskResp;
        } catch (Exception e) {
            log.error("getPayDeskResponse.error", e);
        }
        return Response.createError();
    }

    /**
     * 组装请求参数
     *
     * @param req       req
     * @param routeInfo  routeDto
     * @param thirdType payTypes
     * @param payWay    payWay
     * @return {@link  PayDeskReq}
     */
    private PayDeskReq buildPayDeskRequest(PayTradeReq req, RouteInfoDto routeInfo,
                                           Integer thirdType, List<String> payWay) {

        PayDeskReq payDeskReq = new PayDeskReq();
        payDeskReq.setCouponId(req.getCouponUuid());
        payDeskReq.setOrderId(req.getOrderUuid());
        // 收银台类型默认值 0
        dealPayDeskType(routeInfo, payDeskReq);

        // 设置收银台查询的用户为，该行程的乘客，注意不是当前登录的用户，因为存在跨账号支付问题
        if (null != routeInfo) {
            payDeskReq.setUserId(routeInfo.getPassengerUuid());
        }
        payDeskReq.setCityCode(PayUtils.getCityCode(routeInfo, req));
        // 支付方式
        payDeskReq.setPayWay(payWay);
        // 支付渠道
        payDeskReq.setPayChannel(thirdType);
        // 是否使用优惠券
        payDeskReq.setUseCouponType(req.getUseCouponType());
        // 设置权益卡参数
        payDeskReq.setPrivilegeUuid(req.getPrivilegeUuid());
        payDeskReq.setUsePrivilegeType(req.getUsePrivilegeType());
        if (null == req.getUseCounponActivity()) {
            req.setUseCounponActivity(NumConstants.NUM_0);
        }
        //如果不是老年用车就默认 为null
        if (null != routeInfo) {
            if (Objects.isNull(routeInfo.getServiceModel()) || routeInfo.getServiceModel() != NumConstants.NUM_2) {
                req.setPayForOtherType(PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType());
            } else if (Objects.isNull(req.getPayForOtherType())) {
                req.setPayForOtherType(PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType());
            }
        }
        payDeskReq.setPayForOtherType(req.getPayForOtherType());
        payDeskReq.setCouponActivityFlag(req.getUseCounponActivity());
        // 渠道来源
        // 不完全是终端的意思，但是在这个就是用终端代替
        payDeskReq.setUserChannel(Integer.valueOf(PayUtils.getTerminal(req.getGrayVersion())));

        payDeskReq.setUserId(req.getPassengerUuid());
        if (PayWayConvert.isTaxiScanPay(req.getPayOrderType())) {
            payDeskReq.setType(NumConstants.STR_2);
            payDeskReq.setTaxiScanPay(true);
        }
        payDeskReq.setGrayVersion(req.getGrayVersion());
        payDeskReq.setGrayBuild(req.getGrayBuild());
        payDeskReq.setUseNewCouponPackage(req.getUseNewCouponPackage());
        payDeskReq.setNeedCouponNum(Boolean.TRUE);
        return payDeskReq;
    }

    /**
     * 处理收银台支付类型
     *
     * @param routeInfo 行程信息
     * @param payDeskReq   支付收银台请求信息
     */
    private void dealPayDeskType(RouteInfoDto routeInfo, PayDeskReq payDeskReq) {
        if (null == routeInfo) {
            return;
        }
        Integer typeModule = routeInfo.getTypeModule();
        // 行程类型（个人/企业）：1 个人;2 企业
        Integer typeEnt = routeInfo.getTypeEnt();
        if (null != typeEnt && NUM_2 == typeEnt) {
            //企业行程类型
            payDeskReq.setType("1");
        } else {
            boolean flag = typeModule != null && (typeModule == NUM_2 || typeModule == NUM_4
                    || typeModule == NUM_6 || typeModule == NUM_7 || typeModule == NUM_8);
            if (typeModule != null && typeModule == NUM_1) {
                //出租车
                payDeskReq.setType("2");
            } else if (flag) {
                //快享、专享、惠享
                payDeskReq.setType(NumConstants.STR_0);
            }
        }
    }

    /**
     * 根据终端类型推荐展示支付渠道
     *
     * @param req                 req
     * @param deskDisplayPayTypes deskDisplayPayTypes
     * @return payTypes
     */
    private List<Integer> processPayTypeList(PayTradeReq req, List<Integer> deskDisplayPayTypes, PreDeskRes preInfo) {
        List<Integer> payTypes = req.getPayTypeList();

        if (CollectionUtils.isEmpty(payTypes)) {
            payTypes = Lists.newArrayList();
        } else if (payTypes.size() == 1 && payTypes.contains(NUM_NEGATIVE_1)) {
            Integer applicationType = req.getApplicationType();
            //如果是微信小程序的收银台，推荐的支付方式只能是微信小程序支付
            if (Objects.nonNull(applicationType) && applicationType.equals(NUM_1)) {
                payTypes.add(EnumPayOrderChannel.ALIPAY_MINI.getCode());
                deskDisplayPayTypes.add(EnumPayOrderChannel.ALIPAY_MINI.getCode());
            } else if (Objects.nonNull(applicationType) && applicationType.equals(NUM_2)) {
                payTypes.add(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());
                deskDisplayPayTypes.add(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());
            } else {
                //推荐支付方式
                getRecommendPaymentWay(payTypes, deskDisplayPayTypes, preInfo.getPayChannelResDtoList());
            }
        }

        // 支付方式去重
        //默认初始收银台且非扫码支付 勾选余额，其他场景不勾选收银台  最终决议不自动勾选余额
        payTypes = payTypes.stream().filter(e -> !Integer.valueOf(NUM_NEGATIVE_1).equals(e)).collect(Collectors.toList());

        // 验证微信是否可用，不可用就过滤掉  --当前生产写死返回true，无序校验
//        processWxPayType(preInfo, payTypes, deskDisplayPayTypes);

        payTypes = payTypes.stream().collect(collectingAndThen(toCollection(()
                -> new TreeSet<>(Comparator.comparingInt(e -> e))), ArrayList::new));

        // 查询用户余额信息 如果用户存在企业礼品卡 则不能默认余额支付
        Integer companyGiftCardBalance = getCompanyGiftCardBalance(req);

        // 首次进入收银台默认余额支付方式
        if (!req.getPassengerChooseFlag() && companyGiftCardBalance <= 0) {
            payTypes.add(EnumPayOrderChannel.BALANCE.getCode());
            deskDisplayPayTypes.add(EnumPayOrderChannel.BALANCE.getCode());
        }

        //确定传入的支付方式
        return payTypes;
    }

    /**
     * 查询用户企业礼品卡信息
     *
     * @param req req
     * @return Integer
     */
    private Integer getCompanyGiftCardBalance(PayTradeReq req) {
        try {
            final Response<AccountDto> account = accountService.getAccountNew(req.getPassengerUuid());
            if (account.getSuccess() && Objects.nonNull(account.getData())) {
                return account.getData().getAccountCompanyGiftCardMoney();
            }
        } catch (Exception e) {
            log.warn("getCompanyGiftCardBalance failed!", e);
        }
        return 0;
    }

    /**
     * 获得推荐的付款方式
     *
     * @param payTypeList         付款类型列表
     * @param deskDisplayPayTypes 桌面显示付款类型列表
     */
    private void getRecommendPaymentWay(List<Integer> payTypeList, List<Integer> deskDisplayPayTypes,
                                        List<PayChannelResDto> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            return;
        }
        PayChannelResDto payChannelResDto = channelList.get(0);
        payTypeList.add(payChannelResDto.getPayChannel());
        //将推荐的第三方支付保存
        deskDisplayPayTypes.add(payChannelResDto.getPayChannel());
    }

    /**
     * 设置拼车信息
     *
     * @param resp         resp
     * @param routeInfoDto routeDetailDto
     */
    private void buildPayDeskCarInfo(PayDeskVoV4 resp, RouteInfoDto routeInfoDto) {
        if (null == routeInfoDto) {
            return;
        }

        if (StringUtils.isNotBlank(routeInfoDto.getDriverName())) {
            resp.setDriverName(routeInfoDto.getDriverName().charAt(0) + PassengerConstants.SHIFU);
        } else {
            resp.setDriverName(getDriverName(routeInfoDto.getDriverUuid(), routeInfoDto.getTypeModule()));
        }
        // 车牌
        if (StringUtils.isNotBlank(routeInfoDto.getPlateNum())) {
            resp.setPlateNum(routeInfoDto.getPlateNum());
        } else {
            resp.setPlateNum(getPlatName(routeInfoDto));
        }

        if (null == routeInfoDto.getExpandBizLine()
                || !ExpandBizLineType.CARPOOL.getTypeMode().equals(routeInfoDto.getExpandBizLine())) {
            return;
        }
        String carPoolNotice = PayUtils.parseCarPoolNotice(routeInfoDto);
        resp.setAdditionalHint(carPoolNotice);
    }

    /**
     * 查车牌
     *
     * @param routeInfoDto routePlanDto
     * @return String
     */
    private String getPlatName(RouteInfoDto routeInfoDto) {
        if (switchConfig.getQryCar()) {
            JSONObject jsonObject = operationRest.qryCarInfoByVin(routeInfoDto.getVin());
            if (null != jsonObject) {
                return jsonObject.getString("plateNum");
            }
        } else {
            try {
                Response<CarDto> carDtoResponse = carService.findByUuid(routeInfoDto.getCarUuid());
                if (carDtoResponse.isSuccess() && Objects.nonNull(carDtoResponse.getData())) {
                    return carDtoResponse.getData().getPlateNum();
                }
            } catch (Exception e) {
                log.error("查询车牌信息异常.{}", ExceptionUtils.getFullStackTrace(e));
            }
        }
        return null;
    }

    /**
     * 获取驱动程序名称
     * 获取司机显示的姓名
     *
     * @param actualDriverUuid 司机id
     * @param typeModule       typeModule
     * @return 司机显示姓名
     */
    private String getDriverName(String actualDriverUuid, Integer typeModule) {
        // 获取司机信息
        try {
            DriverReqDto driverReqDto = new DriverReqDto();
            driverReqDto.setDriverId(actualDriverUuid);
            // 端内顺风车司机
            if (EnumTypeModule.HITCH_CAR.getTypeMode().equals(typeModule)) {
                driverReqDto.setDriverSource(NumberConstants.NUMBER_2);
            } else {
                driverReqDto.setDriverSource(NumberConstants.NUMBER_0);
            }
            Response<DriverResDto> response = driverInfoService.queryCacheDriverInfo(driverReqDto);
            if (null != response && null != response.getData()
                    && StringUtils.isNotEmpty(response.getData().getName())) {
                return response.getData().getName().charAt(0) + PassengerConstants.SHIFU;
            } else {
                return PassengerConstants.SIJI_SHIFU;
            }
        } catch (Exception e) {
            log.error("查询司机信息失败{}", ExceptionUtils.getFullStackTrace(e));
        }
        return PassengerConstants.SIJI_SHIFU;
    }

    /**
     * 处理
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    @SuppressWarnings("unchecked")
    private Response<JSONObject> process(PayContext context) {
        if (null != context.getBizType()) {
            if (context.getBizType() == BizType.DRIVER_PRE_PAY.getType()
                    || context.getBizType() == BizType.PASSENGER_PRE_CHARGE.getType()) {
                context.setType("rechargeElectricPrePay");
            } else {
                log.info("使用的策略类:commonPayHandler");
                return commonPayHandler.process(context);
            }
        }
        String type = context.getType();
        PayHandler handler = factory.getHandler(type);

        if (null == handler) {
            return Response.createError("非法的提交类型:type:" + type);
        }
        log.info("使用的策略类:{}", handler.getClass().getSimpleName());
        return handler.process(context);
    }

}
