package com.t3.ts.pay.center.api.controller.invoice.v1;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitItem;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceMallSubmitReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.MallInvoiceVo;
import com.t3.ts.pay.center.api.service.invoice.MallInvoiceService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.pay.common.util.BeanUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: ivy
 * @Date: 2021/9/18 16:41
 * @Description: 查询商城可开票列表test类    /api/passenger/v1/invoice/queryMallBillinglistResponse = {Response@15223}
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class QueryMallBillTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Autowired
    private MallInvoiceService mallInvoiceService;

    @Test
    public void queryMallBillTest() {
        InvoiceRoutReq req = new InvoiceRoutReq();
        // 查询可开票列表
        Response<PageResult<MallInvoiceVo>> listResponse =
                mallInvoiceService.queryMallBilling(req, "967f0d3ae5cf4760923bef29d1255bea");
        Assert.assertTrue(listResponse.isSuccess());
        List<MallInvoiceVo> list = listResponse.getData().getList();
        // 申请开票
        List<InvoiceMallSubmitItem> itemList = new ArrayList<>();
        list.forEach(mallInvoiceVo -> {
            InvoiceMallSubmitItem invoiceMallSubmitItem =
                    BeanUtils.propertiesCopy(mallInvoiceVo, InvoiceMallSubmitItem.class);
            itemList.add(invoiceMallSubmitItem);
        });
        InvoiceMallSubmitReq sreq = new InvoiceMallSubmitReq();
        sreq.setMallList(itemList);

        sreq.setBillType(1);
        sreq.setHeader("胡建德测试");
        sreq.setMoney(new BigDecimal(10));
        sreq.setPassengerMobile("***********");
        sreq.setType(2);
        sreq.setPayType(0);
        sreq.setHeaderType(1);
        sreq.setContent("测试");
        sreq.setRecipient("胡建德");
        sreq.setMobile("***********");
        sreq.setArea("南京");
        sreq.setDetailAddress("南京");
        sreq.setRemark("测试");
        sreq.setOrderUuid("********");
        sreq.setInvoiceType(1);
        sreq.setEmail("<EMAIL>");
        sreq.setRegisterAddress("南京江宁");
        sreq.setRegisterTel("***********");
        sreq.setOpeningAccount("*********");
        sreq.setOpeningAccount("招商");
        sreq.setTaxNum("***************");
        mallInvoiceService.submitMallBill(sreq, "967f0d3ae5cf4760923bef29d1255bea");

    }

    @Test
    public void submitMallBill() {
        InvoiceRoutReq req = new InvoiceRoutReq();
        req.setNextIndex("202110");
        Response<PageResult<MallInvoiceVo>> list =
                mallInvoiceService.queryMallBilling(req, "fe21e27ab00c482d9a2d74b6c975d66b");
        MallInvoiceVo mallInvoiceVo = list.getData().getList().get(0);

        InvoiceMallSubmitReq invoiceForm = new InvoiceMallSubmitReq();
        invoiceForm.setUuid("");
        invoiceForm.setBillType(1);
        invoiceForm.setHeader("全记行");
        invoiceForm.setMoney(new BigDecimal(10));
        invoiceForm.setPassengerMobile("***********");
        invoiceForm.setType(2);
        invoiceForm.setPayType(0);
        invoiceForm.setHeaderType(1);
        invoiceForm.setContent("技术服务费");
        invoiceForm.setRecipient("胡建德");
        invoiceForm.setMobile("***********");
        invoiceForm.setArea("南京");
        invoiceForm.setDetailAddress("南京");
        invoiceForm.setRemark("测试");
        invoiceForm.setOrderUuid("1440947174241357920");
        invoiceForm.setInvoiceType(1);
        invoiceForm.setEmail("<EMAIL>");
        invoiceForm.setRegisterAddress("南京九龙湖");
        invoiceForm.setRegisterTel("***********");
        invoiceForm.setOpeningAccount("****************");
        invoiceForm.setOpeningAccount("招商银行");
        invoiceForm.setTaxNum("91320100MA1Y9HPJXB");
        // 商城发票列表
        List<InvoiceMallSubmitItem> itemList = new ArrayList<>();
        InvoiceMallSubmitItem invoiceMallSubmitItem = new InvoiceMallSubmitItem();
        invoiceMallSubmitItem.setOrderNo("1440947174241357920");
        invoiceMallSubmitItem.setCityName("南京");
        invoiceMallSubmitItem.setInvoiceAmount(new BigDecimal(100));
        invoiceMallSubmitItem.setSkuName("修改商品名称");
        invoiceMallSubmitItem.setSkuNum(1);
        invoiceMallSubmitItem.setBizType(1);
        invoiceMallSubmitItem.setCreateTime(new Date().getTime());
        invoiceMallSubmitItem.setInvoiceClass(2);
        invoiceMallSubmitItem.setInvoiceSubjectCode(1);
        invoiceMallSubmitItem.setCompleteTime(Long.valueOf(mallInvoiceVo.getOrderCreateTime()));
        invoiceForm.setMallList(itemList);
        Response response = mallInvoiceService.submitMallBill(invoiceForm, "2d53a198cef9455a891c82956ee8bf46");
        Assert.assertTrue(response.isSuccess());
    }
}
