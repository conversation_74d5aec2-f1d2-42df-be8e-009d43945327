package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 提现请求参数
 *
 * <AUTHOR>
 * @date 2021/07/12
 */
@Getter
@Setter
@ApiModel("提现请求参数")
public class DrawCashReq implements Serializable {
    private static final long serialVersionUID = -1176200937338579727L;
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行卡号")
    private String cardNo;

    @ApiModelProperty(value = "提款金额")
    @NotNull(message = "提现金额不能为空")
    private String withdrawAmount;

    @ApiModelProperty(value = "短信验证码")
    private String verifyCode;
}
