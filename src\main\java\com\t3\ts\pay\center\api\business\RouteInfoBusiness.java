package com.t3.ts.pay.center.api.business;

import org.apache.dubbo.config.annotation.DubboReference;
import com.t3.ts.passenger.center.service.OrderCacheService;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Objects;


/**
 * 迁移 by ivy .2021/09/17 16:00
 *
 * @Author: hupo
 * @Date: 2019-04-30
 * @Description: 行程信息相关
 */

@Slf4j
@Component
public class RouteInfoBusiness {

    @DubboReference
    private OrderCacheService orderCacheService;

    /**
     * 往前计算跨度月份
     *
     * @param index          指数
     * @param queryMonthOver 查询一个月了
     * @return {@link String}
     */
    public static String getQueryMonthPre(String index, Integer queryMonthOver) {
        if (Objects.nonNull(queryMonthOver) && queryMonthOver > 0) {
            try {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(com.t3.ts.utils.DateUtils.format(index, "yyyyMM"));
                calendar.add(Calendar.MONTH, -queryMonthOver);
                return DateUtils.getMonthFormat(calendar.getTime());
            } catch (Exception e) {
                log.error("getQueryMonthPre error: index{}", index, e);
                return index;
            }
        }
        return index;
    }

    /**
     * 校验列表查询是否已触底（不再继续查询，当前时间往前推三年）
     *
     * @param index 指数
     * @return boolean
     */
    public static boolean checkListEnd(String index) {
        try {
            //继续查询月份的时间
            Calendar queryCalendar = Calendar.getInstance();
            queryCalendar.setTime(com.t3.ts.utils.DateUtils.format(index, "yyyyMM"));
            //三年前时间
            Calendar leastCalender = Calendar.getInstance();
            leastCalender.setTime(com.t3.ts.utils.DateUtils.format(DateUtils.getMonthFormat(), "yyyyMM"));
            leastCalender.add(Calendar.YEAR, CommonNumConst.NUM_NEGATIVE_3);
            if (queryCalendar.getTimeInMillis() <= leastCalender.getTimeInMillis()) {
                return true;
            }
        } catch (Exception e) {
            log.error("checkListEnd error: index{}", index, e);
            return false;
        }
        return false;
    }
}
