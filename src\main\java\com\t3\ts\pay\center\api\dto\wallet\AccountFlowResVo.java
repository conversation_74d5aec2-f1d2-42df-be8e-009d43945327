package com.t3.ts.pay.center.api.dto.wallet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 账户流水ResVo
 *
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
public class AccountFlowResVo {

    @ApiModelProperty(value = "用户ID")
    private String userId;
    @ApiModelProperty(value = "账户类型")
    private Integer accountType;
    @ApiModelProperty(value = "流水号")
    private String orderNo;
    @ApiModelProperty(value = "结算uuid")
    private String settleUuid;
    @ApiModelProperty(value = "流水uuid")
    private String orderUuid;
    @ApiModelProperty(value = "业务类型")
    private Integer bizType;
    @ApiModelProperty(value = "账本类型")
    private Integer bookType;
    @ApiModelProperty(value = "收支类型")
    private Integer changedType;
    @ApiModelProperty(value = "变更前金额")
    private String beforeBalance;
    @ApiModelProperty(value = "变更金额")
    private String changedBalance;
    @ApiModelProperty(value = "变更后金额")
    private String afterBalance;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "变更后待入账余额")
    private String afterPrepareBalance;
    @ApiModelProperty(value = "变更后可用余额")
    private String afterAvailableBalance;
    @ApiModelProperty(value = "变更后冻结余额")
    private String afterFreezeBalance;
    @ApiModelProperty(value = "费用项")
    private Integer fareItem;
    @ApiModelProperty(value = "入账状态 (结算状态 1：入账中)")
    private Integer settleStatus;
    @ApiModelProperty(value = "是否安心跑 ( 1：安心跑)")
    private Integer anXinPao;
    @ApiModelProperty(value = "是否拼车 ( 1：拼车)")
    private Integer pinChe;
    @ApiModelProperty(value = "业务发生时间")
    private Date bizTime;
    @ApiModelProperty(value = "归属日期 yyyy-MM-dd")
    private String DateStr;


}
