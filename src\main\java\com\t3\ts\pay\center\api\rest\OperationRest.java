package com.t3.ts.pay.center.api.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.t3.ts.account.center.dto.AccountCouponDto;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.common.PassengerPortrait;
import com.t3.ts.pay.center.api.service.ResponseConverter;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/4/9 14:38
 * @description:
 */
@Component
@Slf4j
public class OperationRest extends BaseRest implements InitializingBean, ResponseConverter {

    @Resource
    private SlbConfig slbConfig;

    @Autowired
    private SwitchConfig switchConfig;

    private OkHttpClient okHttpClientTimeOut = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        okHttpClientTimeOut = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(switchConfig.getOkClientConnectTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(switchConfig.getOkClientWriteTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(switchConfig.getOkClientReadTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }


    /**
     * 查询 车辆信息
     *
     * @param vin vin
     * @return 查询车辆信息
     */
    public JSONObject qryCarInfoByVin(String vin) {
        try {
            Map map = new HashMap(NumConstants.NUM_16);
            map.put("vin", vin);
            String url = slbConfig.getAssetGateway() + "/api/assets/v1/vehicle/simple/queryByVin";
            String postHttp = sendPostHttpClient(okHttpClientTimeOut, url, JSONObject.toJSONString(map));
            if (StringUtils.isNotBlank(postHttp)) {
                JSONObject object = JSON.parseObject(postHttp);
                return object.getJSONObject("data");
            }
        } catch (Exception e) {
            log.error("qryCarInfoByVin error", e);
        }
        return null;
    }


    /**
     * 乘客画像
     *
     * @param phone 乘客uuid
     * @return {@link PassengerPortrait}
     */
    public Response<PassengerPortrait> passengerPortrait(String phone) {
        try {
            Map map = new HashMap(NumConstants.NUM_16);
            map.put("phone", phone);
            String url = slbConfig.getMarketing() + "/userGroupQuery/queryUserRegisterDetailNew";
            String postHttp = sendPostHttpClient(okHttpClientTimeOut, url, JSONObject.toJSONString(map));
            if (StringUtils.isNotBlank(postHttp)) {
                Response response = JSONObject.parseObject(postHttp, Response.class);
                PassengerPortrait passengerPortrait = new PassengerPortrait();
                passengerPortrait.setBeFincntTotal(NumConstants.STR_0);
                Response<FirstOrder> firstOrderResponse = convertData(response, FirstOrder.class);
                if (firstOrderResponse != null && firstOrderResponse.isSuccess()
                        && Objects.nonNull(firstOrderResponse.getData())
                        && Objects.nonNull(firstOrderResponse.getData().getOrderNum())) {
                    passengerPortrait.setBeFincntTotal(String.valueOf(firstOrderResponse.getData().getOrderNum()));
                }
                return Response.createSuccess("查询成功", passengerPortrait);
            }
        } catch (Exception e) {
            log.error("passengerPortrait error", e);
        }
        return Response.createError();
    }


    /**
     * 查询最优券
     *
     * @param accountCouponDto 参数
     * @return 最优券
     */
    public AccountCouponDto getBeastDiscountCoupon(AccountCouponDto accountCouponDto) {
        try {
            //入参为空则返回
            if(ObjectUtils.isEmpty(accountCouponDto)) {
                log.error("getBeastDiscountCoupon req param is empty");
                return null;
            }

            String url = slbConfig.getMarketing() + "/account/center/getBeastDiscountCoupon";
            log.info("AccountCouponFeignClient.getBeastDiscountCoupon");
            JSONObject message = JSON.parseObject(JSON.toJSONString(accountCouponDto));
            //业务线
            message.put("bizLineType", message.get("expandBizLine"));
            String postHttp = sendPostHttpClient(okHttpClientTimeOut, url, message.toJSONString());

            if(StringUtils.isEmpty(postHttp)) {
                return null;
            }

            Response<AccountCouponDto> response = JSONObject.parseObject(postHttp,
                    new TypeReference<Response<AccountCouponDto>>() {
                    });
            if (null != response && response.isSuccess()) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("调用获取优惠券异常，降级处理", e);
        }
        return null;
    }


}
