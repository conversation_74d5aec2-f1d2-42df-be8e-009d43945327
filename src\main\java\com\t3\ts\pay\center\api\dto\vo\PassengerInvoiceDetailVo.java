package com.t3.ts.pay.center.api.dto.vo;

import com.t3.ts.invoice.center.dto.InvoiceRouteDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * @Author: ivy
 * @Date: 2021/4/25 18:17
 */
@Getter
@Setter
public class PassengerInvoiceDetailVo implements Serializable {

    private static final long serialVersionUID = -9025622383684744313L;
    /**
     * uuid
     **/
    @ApiModelProperty(value = "uuid")
    private String uuid;

    /**
     * 乘客UUID
     **/
    @ApiModelProperty(value = "乘客UUID")
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    @ApiModelProperty(value = "乘客手机号")
    private String passengerMobile;

    /**
     * 发票类型（1按行程2按金额）
     **/
    @ApiModelProperty(value = "发票类型（1按行程2按金额）")
    private Integer type;

    /**
     * 支付方式(0.无（电子发票）,1.到付,2.寄付)
     **/
    @ApiModelProperty(value = "支付方式(0.无（电子发票）,1.到付,2.寄付)")
    private Integer payType;

    /**
     * 抬头类型（1：企业单位2：个人/非企业单位）
     */
    @ApiModelProperty(value = "抬头类型（1：企业单位2：个人/非企业单位）")
    private Integer headerType;

    /**
     * 发票抬头
     **/
    @ApiModelProperty(value = "发票抬头")
    private String header;

    /**
     * 金额
     **/
    @ApiModelProperty(value = "金额")
    private BigDecimal money;

    /**
     * 发票内容
     **/
    @ApiModelProperty(value = "发票内容")
    private String content;

    /**
     * 收件人
     **/
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 联系电话
     **/
    @ApiModelProperty(value = "联系电话")
    private String mobile;

    /**
     * 所在地区
     **/
    @ApiModelProperty(value = "所在地区")
    private String area;

    /**
     * 详细地址
     **/
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    /**
     * 备注
     **/
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 物流订单号
     **/
    @ApiModelProperty(value = "物流订单号")
    private String logisticsOrderNo;

    /**
     * 发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）、6作废中、7作废成功、8作废失败)、
     * 9 作废发票中、10 开票中、11 开票失败
     **/
    @ApiModelProperty(value = "发票状态(0待开票、1待寄出、2已寄出、3已取消、4已作废、5已开票（电子发票）、6作废中、"
            + "7作废成功、8作废失败)、9 作废发票中、10 开票中、11 开票失败")
    private Integer status;

    /**
     * 创建时间
     **/
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     **/
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 修改者
     **/
    @ApiModelProperty(value = "修改者")
    private String updater;

    /**
     * 订单的Id,行程开票未order表,充值开票为actual_flow表
     */
    @ApiModelProperty(value = "订单的Id,行程开票未order表,充值开票为actual_flow表")
    private String orderUuid;

    /**
     * 物流公司
     */
    @ApiModelProperty(value = "物流公司")
    private String logisticsCompany;

    /**
     * 发票号
     */
    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    /**
     * 开票类型(1.电子发票,2.纸质发票)
     */
    @ApiModelProperty(value = "开票类型(1.电子发票,2.纸质发票)")
    private Integer invoiceType;

    /**
     * 接收发票的邮箱
     */
    @ApiModelProperty(value = "接收发票的邮箱")
    private String email;

    /**
     * 电子发票JPG路径
     */
    @ApiModelProperty(value = "电子发票JPG路径")
    private String invoiceUrl;

    /**
     * 电子发票PDF路径
     */
    @ApiModelProperty(value = "电子发票PDF路径")
    private String pdfUrl;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String registerAddress;

    /**
     * 注册电话
     */
    @ApiModelProperty(value = "注册电话")
    private String registerTel;

    /**
     * 开户账号
     */
    @ApiModelProperty(value = "开户账号")
    private String openingAccount;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String openingBank;

    /**
     * 税号
     */
    @ApiModelProperty(value = "税号")
    private String taxNum;

    /**
     * 行程开始时间
     */
    @ApiModelProperty(value = "行程开始时间")
    private Date startDate;

    /**
     * 行程结束时间
     */
    @ApiModelProperty(value = "行程结束时间")
    private Date endDate;
    /**
     * 行程数量
     */
    @ApiModelProperty(value = "行程数量")
    private Integer itineraryNum;

    /**
     * 行程列表
     */
    @ApiModelProperty(value = "行程列表")
    private List<InvoiceRouteDto> itineraryList;

    /**
     * 寄送发票日期
     */
    @ApiModelProperty(value = "寄送发票日期")
    private Date expressDate;

    /**
     * 申请来源(1:app申请 2:客服申请)
     */
    @ApiModelProperty(value = "申请来源(1:app申请 2:客服申请)")
    private Integer applySource;
    /**
     * 业务类型(1:开具发票 2:发票作废)
     */
    @ApiModelProperty(value = "业务类型(1:开具发票 2:发票作废)")
    private Integer bizType;
    /**
     * 发票作废时间
     */
    @ApiModelProperty(value = "发票作废时间")
    private Date invalidDate;
    /**
     * 废票路径
     */
    @ApiModelProperty(value = "废票路径")
    private String invalidInvoiceUrl;
    /**
     * 发票代码
     */
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;
    /**
     * 申请人工号  -- 客服操作必填
     */
    @ApiModelProperty(value = "申请人工号  -- 客服操作必填")
    private String agent;

    /**
     * 申请人姓名   -- 客服操作必填
     */
    @ApiModelProperty(value = "申请人姓名   -- 客服操作必填")
    private String agentName;

    /**
     * 行程来源（1 T3，7 高德）
     */
    @ApiModelProperty(value = "行程来源（1 T3，7 高德）")
    private Integer sourceCode;

    /**
     * 发票类别(1 出行服务,2 商城)
     */
    @ApiModelProperty(value = "发票类别(1 出行服务,2 商城)")
    private Integer invoiceClass;


    /**
     * 是否可变更发票状态（0 可修改，1 不可修改）
     */
    @ApiModelProperty(value = "是否可变更发票状态（0 可修改，1 不可修改）")
    private Integer modifyStatus;
    /**
     * 不可修改原因
     */
    @ApiModelProperty(value = "不可修改原因")
    private String immutableReason;

    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    @ApiModelProperty(value = "运力类型(1 T3运力,2 东风运力,3 一汽运力)", required = true)
    private Integer transportType;

    /**
     * 运营商uuid
     */
    @ApiModelProperty(value = "运营商uuid")
    private String agentCompanyUuid;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String agentCompanyName;
    /**
     * 发票主体id
     */
    @ApiModelProperty(value = "发票主体id")
    private Integer invoiceSubjectCode;

    /**
     * 商城订单列表
     */
    @ApiModelProperty(value = "商城订单列表")
    private List<MallInvoiceItemVo> mallList;

    /**
     * 商城订单数量
     */
    @ApiModelProperty(value = "商城订单数量")
    private Integer mallOrderNum;


    @ApiModelProperty(value = "商城订单开始时间 毫秒时间戳")
    private Long mallStartDate;

    @ApiModelProperty(value = "商城订单结束时间 毫秒时间戳")
    private Long mallEndDate;

    /**
     * 电子发票发送类型 1：发送发票和行程单 2：仅发送发票 3：仅发送行程单 4：不发送邮件
     */
    @ApiModelProperty(value = "电子发票发送类型 1：发送发票和行程单 2：仅发送发票 3：仅发送行程单 4：不发送邮件")
    private Integer sendType;

    /**
     * 运营商uuidList
     */
    @ApiModelProperty(value = "运营商uuidList")
    private List<String> agentCompanyUuidList;

    /**
     * 分组id
     */
    @ApiModelProperty(value = "分组id")
    private String groupId;
    /**
     * 开票失败原因
     */
    @ApiModelProperty(value = "开票失败原因")
    private String failReason;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal rate;

}
