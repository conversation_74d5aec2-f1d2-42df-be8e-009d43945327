package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2020/3/11 10:27
 * @des 1.0
 */

@Data
@ApiModel
public class InvoiceHeaderReq {

    @NotBlank(message = "发票抬头类型不能为空")
    @ApiModelProperty(value = "发票抬头类型（1：企业单位2：个人/非企业单位）", required = true)
    private Integer headerType;

}
