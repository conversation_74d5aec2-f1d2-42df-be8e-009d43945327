package com.t3.ts.pay.center.api.dto.route;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Author:   liuss
 * Date:     2020/8/11 13:51
 * Description: 订单id入参对象
 */
@Data
public class RoutePlanUuidReq implements Serializable {

    private static final long serialVersionUID = -6354081474382299647L;

    /**
     * 订单id
     */
    private String routePlanUuid;

    /**
     * 预付款类型
     */
    private String enterprise;

    /**
     * 型号
     */
    @ApiModelProperty(value = "型号 区分手机系统")
    private String grayVersion;

    /**
     * @param routePlanUuid routePlanUuid
     */
    public RoutePlanUuidReq(String routePlanUuid) {
        this.routePlanUuid = routePlanUuid;
    }

}
