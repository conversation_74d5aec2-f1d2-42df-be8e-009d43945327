package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSON;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.pay.center.api.business.MallPaymentBusiness;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.pay.center.api.dto.mall.MallPayResp;
import com.t3.ts.pay.center.api.exception.BizExceptionUtil;
import com.t3.ts.pay.center.api.util.AESUtil;
import com.t3.ts.pay.center.api.util.NetworkUtil;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.RSAUtils;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 商城收银台
 *
 * <AUTHOR>
 * @date 2022/02/22 10:46
 */
@RestController
@Slf4j
@RequestMapping("/api/mall/order")
@Api("Mall-Api")
public class MallApi extends BaseApi {

    @Autowired
    private MallPaymentBusiness mallPaymentBusiness;
    @DubboReference
    private AccountService accountService;
    /**
     * rsa 私钥（商城响应签名私钥）
     */
    @Value("${mall.respSign.key:null}")
    private String respSignPubKey;
    /**
     * 签名版本（商城响应签名版本）
     */
    @Value("${mall.respSign.version:V1}")
    private String respSignVersion;

    /**
     * 签名版本（商城响应签名版本）
     */
    @Value("${mall.respSign.isSign:true}")
    private boolean isSign;
    /**
     * 是否设置外网IP
     */
    @Value("${mall.fraud.useOutIp:true}")
    private boolean useOutIp;



    /**
     * 确认支付订单
     * 迁移到  商城支付V2  /api/mall/order/v2/pay
     * @param payOrderSureReq payOrderSureReq
     * @param request         request
     * @return : com.t3.ts.result.Response
     */
    @Deprecated
    @PostMapping("/pay")
    @ApiOperation(value = "确认支付", notes = "确认支付")
    public Response queryPayOrder(@RequestBody PayOrderSureReq payOrderSureReq, HttpServletRequest request) {
        payOrderSureReq.setUserId(getUserUid(request));
        payOrderSureReq.setGrayBuild(getGrayBuild(request));
        payOrderSureReq.setGrayVersion(getGrayVersion(request));
        payOrderSureReq.setTerminal(PayUtils.getTerminal(payOrderSureReq.getGrayVersion(),null));
        payOrderSureReq.setOutIp(NetworkUtil.getOutIp(request));
        return mallPaymentBusiness.mallPay(payOrderSureReq, request);
    }

    /**
     * 商城支付V2版本
     *
     * @param payOrderSureReq payOrderSureReq
     * @param request         request
     * @return : com.t3.ts.result.Response
     */
    @PostMapping("/v2/pay")
    @ApiOperation(value = "商城支付V2", notes = "商城支付V2")
    public Response<?> mallPayV2(@RequestBody PayOrderSureReq payOrderSureReq, HttpServletRequest request) {
        try {
            Response<?> response = mallPayV1(payOrderSureReq, request);
            //检查结果
            if (!response.isSuccess()) {
                return response;
            }
            if (ObjectUtils.isEmpty(response.getData())) {
                return response;
            }

            if (isSign) {
                //对结果进行加密
                log.info("mallPay sign is open");
                response = encryptResp(response);
            }

            return response;
        }  catch (BizException e) {
            log.error("mallPay bizException,information:", e);
            return e.getRes();
        } catch (Exception e) {
            log.error("mallPay exception,information:", e);
            return Response.createError("服务异常", ResultErrorEnum.PAY_ERROR.getCode());
        }
    }

    public Response<?> mallPayV1(PayOrderSureReq payOrderSureReq, HttpServletRequest request) {
        //校验
        validateAndFixParam(payOrderSureReq, request);

        Response<?> response = mallPaymentBusiness.cashPay(payOrderSureReq, request);
        return response;
    }

    /**
     * 对结果进行加密
     *
     * @param response 当前响应体
     * @return 加密后响应体
     */
    private Response<MallPayResp> encryptResp(Response response) throws Exception {
        //构建AES密钥并使用RSA进行加密生成sign
        String aesKey = StringUtils.buildUUID();
        String respSign = RSAUtils.encrypt(aesKey, respSignPubKey);

        //使用AES密钥对结果进行加密生成content
        String jsonResp = JSON.toJSONString(response.getData());
        String content = AESUtil.encryptByECB(jsonResp, aesKey);

        //构建响应结果
        MallPayResp mallPayResp = MallPayResp.builder()
                .respSign(respSign)
                .respSignVer(respSignVersion)
                .content(content)
                .build();

        return Response.createSuccess(mallPayResp);
    }

    /**
     * 校验并完善入残
     *
     * @param payOrderSureReq 入参
     */
    private void validateAndFixParam(PayOrderSureReq payOrderSureReq, HttpServletRequest request) {
        String userId = getUserUid(request);
        if (Strings.isNullOrEmpty(userId)) {
            BizExceptionUtil.create("用户uuid不能为空", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        if (Strings.isNullOrEmpty(payOrderSureReq.getOrderCode())) {
            BizExceptionUtil.create("订单编码不能为空", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        if (null == payOrderSureReq.getPayOrderChannel() && CollectionUtils.isEmpty(payOrderSureReq.getPayChannels())) {
            BizExceptionUtil.create("支付渠道不能为空", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        //账户校验
        Response<AccountDto> accountResponse = accountService.getAccount(userId);
        if (null == accountResponse || null == accountResponse.getData() || !accountResponse.isSuccess()) {
            BizExceptionUtil.create("未查到账户账本", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        AccountDto accountDto = accountResponse.getData();
        //参数完善
        payOrderSureReq.setAccountType(accountDto.getAccountType());
        payOrderSureReq.setUserId(userId);
        payOrderSureReq.setGrayBuild(getGrayBuild(request));
        payOrderSureReq.setGrayVersion(getGrayVersion(request));
        payOrderSureReq.setTerminal(PayUtils.getTerminal(payOrderSureReq.getGrayVersion(),null));
        if (useOutIp) {
            payOrderSureReq.setOutIp(NetworkUtil.getOutIp(request));
        }
    }
}
