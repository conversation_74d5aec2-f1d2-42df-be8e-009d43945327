package com.t3.ts.pay.center.api.rest.route.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * ligq
 *
 * 行程信息接口出参
 */
@Data
public class BusinessDTO implements Serializable {
    private static final long serialVersionUID = -8375008505252866790L;
    /**
     * 产品线
     */
    private Integer bizType;

    /**
     * 行程来源
     */
    private Integer source;

    /**
     * 业务线
     */
    private Integer expandBizLine;

    /**
     * 行程类型（本人/代客）：1 本人；2 代客
     */
    private Integer typeSelf;

    /**
     * 用车类型 标识用户是个人用户还是企业用户
     */
    private Integer typeEnt;

    /**
     * 服务模式  表示服务模式类型，如单程、往返等。
     */
    private Integer serviceModel;

    /**
     * 行程的具体类型，如用车、日租等。影响行程安排和费用计算。
     */
    private Integer typeTrip;
}
