package com.t3.ts.pay.center.api.dto.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: ccq
 * @Date: 2020/6/16 15:49
 * @Description: 开票相关入参
 */
@Data
@ApiModel(value = "开票相关入参")
public class InvoiceRelevantFormV3 extends InvoiceRelevantForm implements Serializable {

    /**
     * （电子发票）发送类型，1：发送发票和行程单（默认） 2：仅发送发票 3：仅发送行程单 4：不发送邮件
     */
    @ApiModelProperty(value = "（电子发票）发送类型，1：发送发票和行程单（默认） 2：仅发送发票 3：仅发送行程单 4：不发送邮件")
    private Integer sendType = 1;

    /**
     * 乘客uuid(用于调用支付接口传递参数)
     */
    private String invoicePassengerUuid;
    /**
     * 金额(用于调用支付接口传递参数)
     */
    private BigDecimal invoiceMoney;
    /**
     * 行程uuid(用于调用支付接口传递参数)
     */
    private String invoiceOrderUuid;

    @ApiModelProperty(value = "被代交车人虚拟乘客id")
    private String virtualPassengerUuid;
}
