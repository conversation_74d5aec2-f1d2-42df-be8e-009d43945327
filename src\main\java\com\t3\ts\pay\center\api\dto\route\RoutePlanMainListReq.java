package com.t3.ts.pay.center.api.dto.route;

import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.enums.Sort;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/3 13:56
 * @description:
 */
@Data
@ApiModel(value = "主流程的订单列表查询入参")
public class RoutePlanMainListReq implements Serializable {
    private static final long serialVersionUID = 1160892320217347193L;
    /**
     * 当前页数
     */
    protected Integer currPage = 1;
    /**
     * 每页显示大小
     */
    protected Integer pageSize = NumConstants.NUM_10;
    /**
     * 行程uuid
     */
    private String routePlanUuid;
    /**
     * 行程编号
     */
    private String routeNo;
    /**
     * 行程来源：1 APP移动端；2 微信公众号；3 电话叫车；4 pc网站；5 后台下单 6.扬招 7高德
     */
    private Integer source;
    /**
     * 行程时效类型（1：实时，2：预约）
     */
    private Integer typeTime;
    /**
     * 用车类型：(1:出租车,2：专车，3：拼车，4：快车,5：顺风车）
     */
    private Integer typeModule;
    /**
     * 行程类型（个人/企业）：1 个人;2 企业
     */
    private Integer typeEnt;
    /**
     * 车型等级：（1经济型   2舒适型  3行政型  4商务型  5尊贵型 8智能型）
     */
    private Integer vehicleLevel;
    /**
     * 行程用途：1：用车;2日租;3半日租;4接机;5送机;6接站;7送站;8:包车
     */
    private Integer typeTrip;
    /**
     * 车辆vin
     */
    private String vin;
    /**
     * 司机id(实际接单司机UUID)
     */
    private String driverUuid;
    /**
     * 下单人ID
     */
    private String passengerUuid;
    /**
     * 公司ID
     */
    private String companyUuid;
    /**
     * 行程状态 0:约车中，1:预约中，2:接乘中，3:已抵达接乘地，4:载乘中，5:已抵达目的地，6:待支付，7:待评价，8:行程结束，9:行程取消
     */
    private String routeStatus;
    /**
     * (下单时间)查询开始时间
     */
    private Long createTimeStart;
    /**
     * (下单时间)查询结束
     */
    private Long createTimeEnd;
    /**
     * 出发(用车)时间 查询开始时间
     */
    private Long deparTimeStart;
    /**
     * 出发(用车)时间 查询结束时间
     */
    private Long deparTimeEnd;
    /**
     * 计费方式 1 平台计价  2 计价器计价  3 一口价计价
     */
    private Integer fareMethod;
    /**
     * 产品线  typeModule+typeEnt+typeTrip+source
     */
    private String productLine;
    /**
     * 行程uuid
     */
    private List<String> routePlanUuidList;
    /**
     * 行程编号list
     */
    private List<String> routeNoList;
    /**
     * 行程来源list
     */
    private List<Integer> sourceList;
    /**
     * 用车类型 list
     */
    private List<Integer> typeModuleList;

    /**
     * 车型等级 list
     */
    private List<Integer> vehicleLevelList;

    /**
     * 行程用途 list
     */
    private List<Integer> typeTripList;

    /**
     * 行程状态 list
     */
    private List<String> routeStatusList;
    /**
     * 产品线
     */
    private List<String> productLineList;
    /**
     * 排序字段
     */
    private List<Sort> sortList;

    /**
     * 需要过滤的行程ids
     */
    private List<String> uuidNotInList;

    /**
     * 下单人ID List
     */
    private List<String> passengerUuidList;

    /**
     * 司机id(实际接单司机UUID)
     */
    private List<String> driverUuidList;

    /**
     * 开票状态
     */
    private String noteStatus;

    /**
     * 1:es 2:包含hBase数据
     */
    private Integer dataType = 1;
    /**
     * 流水号
     */
    private String advanceSerial;
    /**
     * 20210111 服务模式
     */
    private Integer serviceModel;
}
