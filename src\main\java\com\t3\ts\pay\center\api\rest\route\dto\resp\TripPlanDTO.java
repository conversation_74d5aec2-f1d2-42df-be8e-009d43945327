package com.t3.ts.pay.center.api.rest.route.dto.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * ligq
 *
 * 行程信息接口出参
 */
@Data
public class TripPlanDTO implements Serializable {
    private static final long serialVersionUID = -8375008505252866790L;
    /**
     * 预估里程
     */
    private Double planTrip;
    /**
     * 送乘里程
     */
    private Double tripDistance;

    /**
     * 起点位置
     */
    private OriginAddress originAddress;


    /**
     * 终点位置
     */
    private OriginAddress destAddress;


    /**
     * 起点位置
     */
    @Data
    public static class OriginAddress {
        /**
         * 城市编码
         */
        private String cityCode;

        /**
         * 地址
         */
        private String addressDetail;

        /**
         * 区域编码
         */
        private String areaCode;
    }
}
