package com.t3.ts.pay.center.api.business.pay.channel;


import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.util.PayAbTestUtil;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.WxPayDto;
import com.t3.ts.pay.remote.service.WxPayService;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmbAggregatePayServiceImpl.java
 * @Description TODO
 * @createTime 2021年07月06日 14:12:00
 */
@Service("cmbAggregatePayServiceImpl")
@Slf4j
public class CmbAggregatePayServiceImpl implements PayChannelService {

    @Autowired
    private SwitchConfig switchConfig;

    @DubboReference
    private WxPayService wxPayService;

    @Autowired
    private PayAbTestUtil payAbTestUtil;


    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.CMB_AGGREGATE_WX_MINI_PAY.getCode());

        WxPayDto dto = new WxPayDto();
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        dto.setOrderNo(paymentResp.getMsg());
        boolean payAndSignAbTest = payAbTestUtil.wxPayAndSignAbTest(paymentInfoBo.getPassengerUuid());
        log.info("payAndSignAbTest3:{}", payAndSignAbTest);
        if (payAndSignAbTest) {
            dto.setPayAndSign(paymentInfoBo.getPayAndSign());
        }
        dto.setUserId(paymentInfoBo.getPassengerUuid());
        // 微信一般支付
        Response weiXinPayInfo = wxPayService.pay(dto);
        if (!weiXinPayInfo.isSuccess() || Objects.isNull(weiXinPayInfo.getData())) {
            String cmbAggErrorCode = switchConfig.getCmbAggErrorCode();
            String bizCode = weiXinPayInfo.getBizCode();
            if (StringUtils.isNotBlank(cmbAggErrorCode) && StringUtils.isNotBlank(bizCode)
                    && cmbAggErrorCode.contains(bizCode)) {
                weiXinPayInfo.setMsg(ResultErrorEnum.HAS_PAYED.getMsg());
            }
            rechargePayVo.setSdk("获取微信支付串失败");
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
        }

        StringBuilder path = new StringBuilder();
        if (StringUtils.isNotBlank(switchConfig.getCmbAggNewPayUrl())) {
            // 招行聚合回调使用了新的地址，从Apollo获取
            path.append(switchConfig.getCmbAggNewPayUrl());
        } else {
            path.append("pages/pay/index?");
        }
        // 非生产环境d
        if (switchConfig.getCmbAggEnvUat()) {
            path.append("myEnv=uat&");
        }
        String data = String.valueOf(weiXinPayInfo.getData());
        JSONObject jsonObject = JSONObject.parseObject(data);
        path.append("cmbOrderId=" + jsonObject.getString("cmbOrderId"));
        path.append("&orderId=" + jsonObject.getString("orderId"));
        path.append("&encryptedTradeInfo=" + jsonObject.getString("encryptedTradeInfo"));
        path.append("&merId=" + jsonObject.getString("merId"));
        path.append("&encryptedCmbOrderId=" + jsonObject.getString("encryptedCmbOrderId"));
        rechargePayVo.setSdk(path.toString());
        rechargePayVo.setCmbMiniAppId(jsonObject.getString("cmbMiniAppId"));
        return Response.createSuccess(rechargePayVo);
    }


}
