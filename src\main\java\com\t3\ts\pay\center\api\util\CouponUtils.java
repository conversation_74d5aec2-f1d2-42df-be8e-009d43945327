package com.t3.ts.pay.center.api.util;

import com.t3.ts.account.center.constants.CouponUseSourceEnum;
import com.t3.ts.account.center.dto.AccountCouponDto;
import com.t3.ts.pay.remote.constants.EnumTypeModule;
import com.t3.ts.settlement.centre.enums.Source;

/**
 * 优惠券跑龙套
 *
 * <AUTHOR>
 * @date 2019/2/26.
 */
public class CouponUtils {
    /**
     * 结算行程来源 转换为 优惠卷可支持订单来源
     *
     * @param accountCoupon 优惠卷参数
     * @param routeSource   行程来源
     * @return 优惠卷-订单来源
     */
    public static String switchCouponUseSource(AccountCouponDto accountCoupon, Integer routeSource) {
        String useSource = "";
        //通用
        if (EnumTypeModule.TAXI.getTypeMode().toString().equals(accountCoupon.getUseType())) {
            useSource = CouponUseSourceEnum.USE_SOURCE_ONLINE.getUseSource();
            //出租车 目前分 网约(非扬招) 扬招
            if (Source.YANGZHAO.getData() == routeSource) {
                useSource = CouponUseSourceEnum.USE_SOURCE_YANGZHAO.getUseSource();
            }
        }
        return useSource;
    }
}
