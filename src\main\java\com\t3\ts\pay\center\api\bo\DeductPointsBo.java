package com.t3.ts.pay.center.api.bo;

import com.t3.ts.pay.remote.dto.marketing.LadderDiscountDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 扣分bo
 *
 * <AUTHOR>
 * @date 2020/10/27
 */
@Getter
@Setter
public class DeductPointsBo {
    /**
     * 抵扣费用
     */
    private BigDecimal deductCost;
    /**
     * 计算可抵扣费用
     */
    private BigDecimal minDeductMoney;
    /**
     * 获取可用梯度规则
     */
    private List<LadderDiscountDto> ladderDiscountDtos;
    /**
     * 积分兑换比例
     */
    private String exchangePoints;
    /**
     *
     */
    private BigDecimal personalPoints;
}
