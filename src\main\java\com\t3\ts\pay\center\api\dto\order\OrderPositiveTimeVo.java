package com.t3.ts.pay.center.api.dto.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.*;
import java.util.Date;
import java.util.List;

/**
 * 订单倒计时缓存对象
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class OrderPositiveTimeVo implements Serializable {

    /**
     * 订单uuid
     */
    private String orderUuid;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 是否为会员优先派单 0-否 1-是
     */
    private Integer priorityOrderFlag;
    /**
     * 订单时效类型（1：实时，2：预约）
     */
    private Integer typeTime;
    /**
     * 展示正计时时间（单位：秒）
     */
    private Integer showTime;
    /**
     * 呼叫结束时间
     */
    private Date callEndTime;
    /**
     * 起点经度
     */
    private Double originLng;
    /**
     * 起点纬度
     */
    private Double originLat;
    /**
     * 起点区划编码
     */
    private String originAreaCode;
    /**
     * 乘客uuid
     */
    private String passengerUuid;
    /**
     * 最大等待时长（单位：秒）
     */
    private Integer maxWaitTime;
    /**
     * 实际正计时时长（单位：秒）
     */
    private Integer positiveTime;
    /**
     * 不同时间节点的提示文案信息
     */
    private List<PromptInfo> promptInfoList;
    /**
     * 同时呼叫追加呼叫最大等待时间(单位:秒)
     */
    private Integer thirdRingMaxWaitTime;

    public OrderPositiveTimeVo() {
    }

    public OrderPositiveTimeVo(String orderUuid, Date createTime, Integer typeTime) {
        this.orderUuid = orderUuid;
        this.createTime = createTime;
        this.typeTime = typeTime;
    }
}
