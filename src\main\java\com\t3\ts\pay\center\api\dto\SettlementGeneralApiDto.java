package com.t3.ts.pay.center.api.dto;

import com.t3.ts.settlement.centre.dto.SettlementGeneralDetailDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description: 统一结算模型类
 *
 * <AUTHOR> <EMAIL>
 * Date:     2020/5/10, 0010 17:36
 */
@ApiModel
@Data
public class SettlementGeneralApiDto {
    /**
     * 结算单ID
     */
    @ApiModelProperty(value = "结算单ID")
    private String settleUuid;
    /**
     * 业务类型(0 积分商城 1 司机充值 2 充电桩充电 3 司机保证金 4维保支付 5聚合支付）
     */
    @ApiModelProperty(value = "业务类型(0 积分商城 1 司机充值 2 充电桩充电 3 司机保证金 4维保支付 5聚合支付）")
    private Integer bizType;
    /**
     * 结算类型(0 支付 1退款 2过期 3扣款)
     */
    @ApiModelProperty(value = "结算类型(0 支付 1退款 2过期 3扣款)")
    private Integer settleType;
    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private String orderUuid;
    /**
     * 收入主体UUID
     */
    @ApiModelProperty(value = "收入主体UUID")
    private String incomeSubject;
    /**
     * 账户类型，1乘客，2顺风车司机，3T3司机，9运营账户
     */
    @ApiModelProperty(value = "账户类型，1乘客，2顺风车司机，3T3司机，9运营账户")
    private Integer incomeSubjectType;
    /**
     * 支出主体UUID
     */
    @ApiModelProperty(value = "支出主体UUID")
    private String paymentSubject;
    /**
     * 账户类型，1乘客，2顺风车司机，3T3司机，9运营账户
     */
    @ApiModelProperty(value = "账户类型，1乘客，2顺风车司机，3T3司机，9运营账户")
    private Integer paymentSubjectType;
    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFare;
    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    private BigDecimal otherFare;
    /**
     * 其他金额详情
     */
    @ApiModelProperty(value = "其他金额详情")
    private String otherFareDetail;
    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额")
    private BigDecimal totalFare;
    /**
     * 实际支付金额
     */
    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal payAmount;
    /**
     * 支付渠道(1支付宝；2微信；3一网通；4余额；5优惠券；6线下支付；7营销或者客服操作；8银联)
     */
    @ApiModelProperty(value = "支付渠道(1支付宝；2微信；3一网通；4余额；5优惠券；6线下支付；7营销或者客服操作；8银联) ")
    private String payChannel;
    /**
     * 支付明细
     */
    @ApiModelProperty(value = "支付明细")
    private String payDetail;
    /**
     * 结算单状态，1初始化，2待结算，3结算中，4结算成功，5结算失败
     */
    @ApiModelProperty(value = "结算单状态，1初始化，2待结算，3结算中，4结算成功，5结算失败")
    private Integer orderStatus;
    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 明细项
     */
    @ApiModelProperty(value = "明细项")
    private List<SettlementGeneralDetailDto> detailDtoList;
    @ApiModelProperty(value = "bizTypes")
    private List<Integer> bizTypes;
    @ApiModelProperty(value = "paymentSubjects")
    private List<String> paymentSubjects;
    @ApiModelProperty(value = "startDate")
    private Date startDate;
    @ApiModelProperty(value = "endDate")
    private Date endDate;
    @ApiModelProperty(value = "pageNum")
    private Integer pageNum;
    @ApiModelProperty(value = "pageSize")
    private Integer pageSize;
    /**
     * 额外参数
     */
    @ApiModelProperty(value = "额外参数")
    private String extendParam;
    @ApiModelProperty(value = "startPayTime")
    private Date startPayTime;
    @ApiModelProperty(value = "endPayTime")
    private Date endPayTime;

    @ApiModelProperty(value = "operator")
    private String operator;

}
