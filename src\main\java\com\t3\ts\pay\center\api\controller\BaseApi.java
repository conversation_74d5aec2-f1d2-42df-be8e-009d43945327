package com.t3.ts.pay.center.api.controller;

import com.t3.ts.context.ContextUtil;
import com.t3.ts.pay.center.api.config.PayCenterInterceptorConfig;
import com.t3.ts.pay.center.api.config.RequestContextHelperInstead;
import com.t3.ts.pay.center.api.config.interceptor.PassengerTokenInterceptor;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * 基本api
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Slf4j
public class BaseApi {

    /**
     * 获取ua
     * @param request 请求
     * @return ua
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("user-agent");
    }

    /**
     * 获取设备指纹
     * @param request 请求
     * @return 设备指纹
     */
    public static String getRiskDeviceToken(HttpServletRequest request) {
        return request.getHeader("riskdevicetoken");
    }

    /**
     * 获取用户uid
     *
     * @param request 请求
     * @return {@link String}
     */
    protected String getUserUid(HttpServletRequest request) {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            Object uuid = request.getAttribute("uuid");
            return uuid != null ? uuid.toString() : null;
        }
        return ContextUtil.getUserId();
    }

    /**
     * 让用户移动
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getUserMobile(HttpServletRequest request) {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            Object moblie = request.getAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_MOBILE);
            return null != moblie ? moblie.toString() : null;
        }
        return SpringUtils.getBean(RequestContextHelperInstead.class).getPassengerMobile();
    }

    /**
     * 获取grayBuild版本号
     *
     * @param request http请求
     * @return String
     */
    protected static String getGrayBuild(HttpServletRequest request) {
        return request.getHeader("grayBuild");
    }

    /**
     * 获取 grayVersion 版本号
     *
     * @param request http请求
     * @return String
     */
    public static String getGrayVersion(HttpServletRequest request) {
        return request.getHeader("grayVersion");
    }

    /**
     * 得到指纹设备令牌
     * 获取设备指纹token
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getDeviceFingerPrintToken(HttpServletRequest request) {

        return request.getHeader(PassengerTokenInterceptor.REQUEST_HEADER_DEVICE_FINGERPRINT_TOKEN);
    }
}
