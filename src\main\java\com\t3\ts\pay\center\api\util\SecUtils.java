package com.t3.ts.pay.center.api.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 加密工具類
 *
 * <AUTHOR>
 * @date 2022-07-05 14:00:00
 */
@Component
public class SecUtils {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${mynanj.publicKey:1}")
    private String publicKey;

    /**
     * 获取参数
     *
     * @param wxSdk         wxSdk
     * @param rechargePayBo rechargePayBo
     * @param payAmount     payAmount
     * @return String
     * @throws UnsupportedEncodingException UnsupportedEncodingException
     */
    public String getEncryptParams(JSONObject wxSdk, RechargePayBo rechargePayBo, Integer payAmount)
            throws UnsupportedEncodingException {

        cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
        params.put("appid", wxSdk.getStr("appid"));
        params.put("mch_id", wxSdk.getStr("mch_id"));
        params.put("nonce_str", wxSdk.getStr("nonce_str"));
        params.put("body", "出行费");
        params.put("out_trade_no", rechargePayBo.getOrderNo());
        params.put("total_fee", payAmount);
        try {
            params.put("spbill_create_ip", InetAddress.getLocalHost().getHostAddress());
        } catch (Exception e) {
            params.put("spbill_create_ip", "127.0.0.1");
        }
        params.put("notify_url", "https://pay.t3go.cn/pay/wx/callback");
        params.put("trade_type", wxSdk.getStr("trade_type"));
        params.put("partnerid", wxSdk.getStr("mch_id"));
        params.put("prepayid", wxSdk.getStr("prepay_id"));
        params.put("package", wxSdk.getStr("package"));
        params.put("noncestr", wxSdk.getStr("nonce_str"));
        params.put("timestamp", wxSdk.getStr("timestamp"));
        params.put("sign", wxSdk.getStr("sign"));

        return URLEncoder.encode(this.encryptRsa(params.toString()), StandardCharsets.UTF_8.toString());
    }

    /**
     * @param content content
     * @return String
     */
    public String encryptRsa(String content) {
        return Base64.encode(Objects.requireNonNull(encrypt(content)));
    }

    /**
     * @param content content
     * @return byte
     */
    public byte[] encrypt(String content) {
        try {
            return RSAUtils.encryptByPublicKey(content.getBytes(), publicKey);
        } catch (Exception e) {
            logger.error("加密异常", e);
        }
        return null;
    }
}
