package com.t3.ts.pay.center.api.config.valueconfig;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * slb配置
 *
 * <AUTHOR>
 * @date 2020/10/27
 */
@Component
@Data
public class SlbConfig {


    /**
     * 资产
     */
    @Value("${slb.assetGateway:null}")
    private String assetGateway;

    @Value("${slb.route:null}")
    private String route;

    @Value("${slb.mall:null}")
    private String mallUrl;

    @Value("${slb.travelconfig}")
    private String operationHost;

    /**
     * 营销slb地址
     */
    @Value("${slb.marketing}")
    private String marketing;

    /**
     * 营销slb地址
     */
    @Value("${slb.member}")
    private String member;

    @Value("${slb.pay}")
    private String pay;

    /**
     * 企业用车
     */
    @Value("${slb.org-external-api}")
    private String orgExternalUrl;

    /**
     * 用户
     */
    @Value("${slb.passenger}")
    private String passenger;


    @Value("${slb.cua-user-core:}")
    private String cuaUserCore;

    @Value("${slb.solutionGateway:http://gateway.t3go.com.cn/solution-passenger-api}")
    private String solutionGateway;

    @Value("${slb.open-portal:null}")
    private String openPortal;

    @Value("${slb.riskGateway:http://gateway.t3go.com.cn/risk-account-api}")
    private String riskGateway;

    @Value("${slb.marketingAssistGateway:http://gateway.t3go.com.cn/marketing-assist-gateway}")
    private String marketingAssistGateway;

    /**
     * The Route gateway api uri.
     */
    @Value("${slb.trade-route-url}")
    private String tradeRouteUri;

    /**
     * 连接超时
     */
    @Value(value = "${slb.config.httpConnectTimeout:2000}")
    private Integer httpConnectTimeout;
    /**
     * 写超时时间
     */
    @Value(value = "${slb.config.httpWriteTimeout:2000}")
    private Integer httpWriteTimeout;
    /**
     * 读超时时间
     */
    @Value(value = "${slb.config.httpReadTimeout:2000}")
    private Integer httpReadTimeout;


    /**
     * 协议数据
     */
    @Value("${slb.kzDataCenterGateway:http://gateway.t3go.com.cn/kz-data-center-fp}")
    private String kzDataCenterGateway;

}
