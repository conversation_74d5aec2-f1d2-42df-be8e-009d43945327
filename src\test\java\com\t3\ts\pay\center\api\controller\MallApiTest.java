package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.MallPaymentBusiness;
import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:00
 */
public class MallApiTest {
    @Mock
    MallPaymentBusiness mallPaymentBusiness;
    @Mock
    Logger log;
    @InjectMocks
    MallApi mallApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryPayOrder() throws Exception {
        Response result = mallApi.queryPayOrder(new PayOrderSureReq(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetDeviceFingerPrintToken() throws Exception {
        String result = MallApi.getDeviceFingerPrintToken(null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
