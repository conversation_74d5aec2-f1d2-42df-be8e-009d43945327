package com.t3.ts.pay.center.api.controller.invoice.v1;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceQueryPageDto;
import com.t3.ts.pay.center.api.dto.invoice.PassengerInvoiceDetailDto;
import com.t3.ts.pay.center.api.service.invoice.InvoiceHeaderService;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/18 16:41
 * @Description: 查询发票抬头test类    /api/passenger/v1/invoice/queryFrequentlyUsedHeaders
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class InvoiceHeadersTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Autowired
    private InvoiceHeaderService invoiceHeaderService;


    /**
     * 获取抬头
     */
    @Test
    public void queryHeaderTest() {
        InvoiceQueryPageDto dto = new InvoiceQueryPageDto();
        PassengerInvoiceDetailDto detailDto = new PassengerInvoiceDetailDto();
        detailDto.setPassengerUuid("c97d423518f542d193064f27d610e127");
        dto.setPassengerInvoiceDto(detailDto);
        dto.setPageNum(1);
        dto.setPageSize(0);
        Response<?> listResponse =
                invoiceBusiness.queryPageForCallCenter(dto);
        Assert.assertTrue(listResponse.isSuccess());
    }

    /**
     * 发票抬头新增
     */
    @Test
    public void invoiceAddHeader() {
        AddInvoiceHeaderReq req = new AddInvoiceHeaderReq();
        req.setHeader("胡建德单元测试抬头企业公司");
        req.setHeaderType(1);
        req.setUuid(StringUtils.buildUUID().replaceAll("-", ""));
        req.setTaxNum("9132010083498726X2");
        req.setRegisterAddress("南京市江宁南路148号");
        req.setRegisterTel("***********");
        req.setOpeningBank("招商银行江苏省分行");
        req.setBankAccount("32001881436052504287");
        req.setEmail("<EMAIL>");
        Response<?> response = invoiceHeaderService.addInvoiceHeader(req);
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * 删除
     */
    @Test
    public void delHeaderTest() {
        DelInvoiceHeaderReq addInvoiceHeaderReq = new DelInvoiceHeaderReq();
        addInvoiceHeaderReq.setUserUuid("2d53a198cef9455a891c82956ee8bf46");
        Response<?> response =
                invoiceHeaderService.deleteInvoiceHeader(addInvoiceHeaderReq, "2d53a198cef9455a891c82956ee8bf47");
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * 编辑
     */
    @Test
    public void invoiceUpdateHeader() {
        AddInvoiceHeaderReq req = new AddInvoiceHeaderReq();
        req.setHeader("我是勒布朗0114");
        req.setHeaderType(1);
        req.setUuid("64881da95ebd4ac6815fcdc35c76cad9");
        req.setDefaultFlag(1);
        Response<?> response = invoiceHeaderService.saveOrUpdateInvoiceHeader(req, "fe21e27ab00c482d9a2d74b6c975d66b");
        Assert.assertTrue(response.isSuccess());
    }
}
