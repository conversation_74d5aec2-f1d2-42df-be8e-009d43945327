package com.t3.ts.pay.center.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.wallet.AccountFlowResVo;
import com.t3.ts.pay.center.api.service.DriverWalletDwySumService;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.data.dto.DriverRouteFareQueryDto;
import com.t3.ts.pay.data.dto.SettlementCommonDto;
import com.t3.ts.pay.data.service.DriverFareService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.settlement.SCExtInfoDto;
import com.t3.ts.settlement.centre.enums.DriverBillEnum;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机钱包 日周月汇总 服务类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DriverWalletDwySumServiceImpl implements DriverWalletDwySumService {

    @DubboReference
    private DriverFareService driverFareService;


    /**
     * 查询ES补全返回给前端的数据
     * 如：
     * 是否入账中
     * 是否安薪跑
     * 是否拼车判断
     * @param listVo
     */
    @Override
    public void completionDriverSettlement(List<AccountFlowResVo> listVo) {
        List<String> orderUuids = listVo.stream().map(AccountFlowResVo::getOrderUuid)
                .filter(StringUtils::isNotBlank).distinct().limit(NumConstant.NUM_100)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderUuids)) {
            log.info("queryUpDriverFlowsV2 orderUuids is empty");
            return;
        }
        String driverUuid = listVo.get(0).getUserId();
        DriverRouteFareQueryDto queryDto = new DriverRouteFareQueryDto();
        queryDto.setFareType(NumConstant.NUM_3);
        queryDto.setRouteList(orderUuids);
        queryDto.setDriverUuid(driverUuid);
        Response<List<SettlementCommonDto>> response1 = driverFareService.getSettlementCommons(queryDto);
        log.warn("completionDriverSettlement response1={}", JSON.toJSONString(response1));
        //非空判断
        if (response1 == null || !response1.isSuccess()
                || CollectionUtils.isEmpty(response1.getData())) {
            log.warn("queryUpDriverFlowsV2 fail or is empty, driverUuid ={} orderUuids={}", driverUuid, orderUuids);
            return;
        }
        List<SettlementCommonDto> details = response1.getData();
        Map<String, SettlementCommonDto> scmap  = details.stream().collect(Collectors.toMap(SettlementCommonDto::getUuid,
                    Function.identity(), (v1, v2) -> v1));

        List<Integer> taxiRouteFares = Arrays.asList(DriverBillEnum.ROUTE_INCOME_PARENT.getType(),
                DriverBillEnum.RISK_ROUTE_FARE.getType(), DriverBillEnum.RISK_CONFIRM_ROUTE.getType(),
                DriverBillEnum.RISK_CUSTOMER_ROUTE.getType());

        listVo.stream()
                // SettlementCommonDto中的uuid就是flows里面的settleUuid
                .filter(flowsDto -> StringUtils.isNotBlank(flowsDto.getSettleUuid()))
                .forEach(flowsDto -> {
                    SettlementCommonDto commonEsDto = scmap.get(flowsDto.getSettleUuid());
                    if (null == commonEsDto) {
                        return;
                    }
                    /* 判断是否入账中状态-开始 */
                    Date readyTime = DateUtils.getReleaseDate(commonEsDto.getSettleReadyTime());
                    if (null != readyTime) {
                        //入账中状态
                        flowsDto.setSettleStatus(readyTime.after(new Date()) ? NumConstants.NUM_1 : NumConstants.NUM_0);
                    }
                    /* 判断是否入账中状态-结束 */

                    /* 判断是否安薪跑-开始 */
                    if (StringUtils.isBlank(commonEsDto.getExtInfo())) {
                        return;
                    }
                    SCExtInfoDto scExtInfoDto = JSON.parseObject(commonEsDto.getExtInfo(), SCExtInfoDto.class);
                    if (StringUtils.isNotBlank(scExtInfoDto.getLabels())
                            && scExtInfoDto.getLabels().contains("811")) {
                        flowsDto.setAnXinPao(NumConstants.NUM_1);
                    }
                    /* 判断是否安薪跑-结束 */
                    /* 拼车判断 */
                    if (null != scExtInfoDto.getExpandBizLine() && scExtInfoDto.getExpandBizLine() == NumConstants.NUM_13) {
                        flowsDto.setPinChe(NumConstants.NUM_1);
                    }
                    /* 拼车判断-结束 */

                    /* 判断出租车 是接了出租车订单还是接了网约车订单 -开始 */

                    if (flowsDto.getAccountType() == PayAccountTypeEnum.TAXI_TYPE.getType()
                            && taxiRouteFares.contains(flowsDto.getFareItem())
                            && null != scExtInfoDto.getTypeModule()) {
                        flowsDto.setRemark(flowsDto.getRemark() + "("
                                + (1 == scExtInfoDto.getTypeModule() ? "出租车" : "网约车") + ")");
                    }
                    /* 判断出租车 是接了出租车订单还是接了网约车订单 -结束 */
                });

    }

}
