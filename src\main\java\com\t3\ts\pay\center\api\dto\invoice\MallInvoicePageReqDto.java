package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Getter;
import lombok.Setter;

import java.io.*;
import java.util.Date;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * @Author: qul
 * @Date: 2021/4/25 14:31
 */
@Getter
@Setter
public class MallInvoicePageReqDto implements Serializable {

    private static final long serialVersionUID = 3638353354942090093L;

    /*
     * validTime	否	string	32	是否校验时间，默认true
     * invoiceStatusList	否	list	32	开票状态列表 开票状态 0-待开票 1-已开票
     * startTime	是	Date		查询开始时间
     * endTime	是	Date		查询结束时间
     * userUuid	是	string	32	用户ID
     * bizType	否	Integer		业务类型 0积分商城 7 商城礼品卡
     * currPage	是	integer	32	当前页	默认1
     * pageSize	是	integer	32	页大小	默认20 最大100
     */

    /**
     * 开票状态列表 开票状态 0-待开票 1-已开票
     */
    private List<Integer> invoiceStatusList;

    /**
     * 查询开始时间
     */
    private Date startTime;

    /**
     * 查询结束时间
     */
    private Date endTime;

    /**
     * 用户ID
     */
    private String userUuid;

    /**
     * 业务类型 0积分商城 7 商城礼品卡
     */
    private Integer bizType;

    /**
     * 当前页
     */
    private Integer currPage;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * com.t3.ts.app.api.enums.InvoiceClassEnum
     */
    private Integer invoiceClass;

    /**
     * 发票主体代码
     */
    private Integer invoiceSubjectCode;

    /**
     * 用户类型，默认1
     */
    private Integer accountType;

    /**
     * 订单id
     */
    private String orderNo;
}
