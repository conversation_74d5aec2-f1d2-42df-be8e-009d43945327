package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.center.api.bo.RechargePayBo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:07
 */
public class SecUtilsTest {
    @Mock
    Logger logger;
    @InjectMocks
    SecUtils secUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetEncryptParams() throws Exception {
        String result = secUtils.getEncryptParams(null, new RechargePayBo(), Integer.valueOf(0));
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testEncryptRsa() throws Exception {
        String result = secUtils.encryptRsa("content");
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testEncrypt() throws Exception {
        byte[] result = secUtils.encrypt("content");
        Assert.assertArrayEquals(new byte[] {(byte) 0}, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
