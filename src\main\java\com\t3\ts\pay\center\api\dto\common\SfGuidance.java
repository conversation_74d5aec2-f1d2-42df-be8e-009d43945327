package com.t3.ts.pay.center.api.dto.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SfGuidance implements Serializable {

    // 业务线 4:快享 2:专享 13:企业用车 10:包车
    private String productLine;

    // 城市code码
    private String cityCode;

    private String adCode;

    // 触发节点
    private String triggerNode;

    // 用户id
    private String passengerId;

    // 用户群id
    private List<String> groupIds;

    // 手机号
    private String phone;

    private Boolean prePay;
}
