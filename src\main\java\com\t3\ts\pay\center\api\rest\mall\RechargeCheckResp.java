package com.t3.ts.pay.center.api.rest.mall;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商城订单信息
 *
 * <AUTHOR>
 * @date 2022年03月22日 15:05:00
 */
@Data
public class RechargeCheckResp implements Serializable {

    /**
     * 商品类型
     */
    private Integer spuType;

    /**
     * 金额
     */
    private BigDecimal payCash;

    /**
     * 是否纯积分支付
     */
    private Boolean integralOnly = Boolean.FALSE;

    /**
     * 是否立即扣款
     */
    private Boolean payNow;
}
