package com.t3.ts.pay.center.api.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2019-11-15 22:03
 * @des: 充值支付返回对象
 */
@Data
public class RechargePayBo {

    /**
     * 第三方支付sdk字符串
     */
    private String sdk;

    /**
     * 是否免密支付
     */
    private boolean noSecret = false;

    /**
     * 支付方式
     *
     * @see com.t3.ts.pay.remote.constants.EnumPayOrderChannel
     */
    private Integer payType;

    /**
     * 结算Id
     */
    private String settlementId;


    /**
     * 招行聚合支付的时候，需要用到的小程序id
     */
    private String cmbMiniAppId;

    /**
     * t3小程序id
     */
    private String t3MiniAppId;

    /**
     * 支付信息
     */
    private String payInfo;


    /**
     * 返回码
     */
    private String code = "200";

    /**
     * 订单号
     */
    private String orderNo;
}
