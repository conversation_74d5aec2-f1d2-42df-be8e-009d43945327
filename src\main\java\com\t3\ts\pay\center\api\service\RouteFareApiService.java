package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.vo.RouteFareDetailVo;
import com.t3.ts.result.Response;

/**
 * <AUTHOR>
 * @date 2020/7/21 15:17
 * @description:
 */
public interface RouteFareApiService  {
    /**
     * 乘客获取费用详情
     *
     * @param req 参数
     * @return 出参
     */
    Response<RouteFareDetailVo> getOrderFareItems(RouteFareItemReq req);
}
