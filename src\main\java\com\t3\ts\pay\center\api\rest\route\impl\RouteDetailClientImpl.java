package com.t3.ts.pay.center.api.rest.route.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.rest.BaseRest;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.rest.route.dto.resp.RouteInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RouteDetailClientImpl extends BaseRest implements RouteDetailClient {
    /**
     * 出行交易网关地址
     */
    @Autowired
    private SlbConfig slbConfig;

    /**
     * 查询费用
     *
     * @param routePlanId 行程id
     * @param queryFare   是否查询费用（默认不查）
     * @return 查询结果
     */
    private RouteInfoDto queryDetail(String routePlanId, boolean queryFare) {
        String urlSuffix = "/trip/facade/trade/route/getRouteInfo";
        String url = slbConfig.getTradeRouteUri() + urlSuffix;

        JSONObject request = new JSONObject();
        request.put("routeId", routePlanId);
        request.put("routeFare", queryFare);
//        request.put("requirementVehicle", requirementVehicle);

        String postHttp = sendPost(url, request.toJSONString());
        if (StringUtils.isBlank(postHttp)) {
            return null;
//            throw new BusinessException(ResultErrorEnum.ROUTE_INFO_ERROR);
        }
        JSONObject respData = JSONObject.parseObject(postHttp);
        if (!respData.getBoolean("success")) {
            //失败，则返回空
            return null;
//            throw new BusinessException(ResultErrorEnum.ROUTE_INFO_ERROR);
        }
        RouteInfoResp routeInfoResp = JSON.parseObject(respData.getString("data"), RouteInfoResp.class);
        if (ObjectUtil.isEmpty(routeInfoResp)) {
            //失败，则返回空
            return null;
//            throw new BusinessExceptionsinessException(ResultErrorEnum.ROUTE_INFO_ERROR);
        }
        RouteInfoDto routeInfoDto = RouteInfoDto.builder()
                .routeId(routePlanId)
                .serviceModel(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getServiceModel())
                .driverName(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getDriverName())
                .plateNum(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getPlateNum())
                .isElectricCar(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getIsElectricCar())
                .status(routeInfoResp.getRouteStateDTO() == null ? null : routeInfoResp.getRouteStateDTO().getStatus())
                .oriAddress(routeInfoResp.getTripPlanDTO() == null ||
                        routeInfoResp.getTripPlanDTO().getOriginAddress() == null ? null :
                        routeInfoResp.getTripPlanDTO().getOriginAddress().getAddressDetail())
                .destAddress(routeInfoResp.getTripPlanDTO() == null ||
                        routeInfoResp.getTripPlanDTO().getDestAddress() == null ? null :
                        routeInfoResp.getTripPlanDTO().getDestAddress().getAddressDetail())
                .driverUuid(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getDriverUuid())
                .vin(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getVin())
                .carUuid(routeInfoResp.getTransportDTO() == null ? null : routeInfoResp.getTransportDTO().getCarUuid())
                .expandBizLine(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getExpandBizLine())
                .fareMethod(routeInfoResp.getPassengerFareDTO() == null ? null : routeInfoResp.getPassengerFareDTO().getFareMethod())
                .carPoolSuccess(routeInfoResp.getExtendInfoDTO() == null ? null : routeInfoResp.getExtendInfoDTO().getCarPoolSuccess())
                .seatNum(routeInfoResp.getPassengerDTO() == null ? null : routeInfoResp.getPassengerDTO().getSeatNum())
                .labels(routeInfoResp.getLabels())
                .typeModule(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getBizType())
                .areaCode(routeInfoResp.getTripPlanDTO() == null ||
                        routeInfoResp.getTripPlanDTO().getOriginAddress() == null ? null :
                        routeInfoResp.getTripPlanDTO().getOriginAddress().getAreaCode())
                .typeEnt(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getTypeEnt())
                .passengerUuid(routeInfoResp.getPassengerDTO() == null ? null : routeInfoResp.getPassengerDTO().getPassengerUuid())
                .typeTrip(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getTypeTrip())
                .cityCode(routeInfoResp.getTripPlanDTO() == null ||
                        routeInfoResp.getTripPlanDTO().getOriginAddress() == null ? null :
                        routeInfoResp.getTripPlanDTO().getOriginAddress().getCityCode())
                .source(routeInfoResp.getBusinessDTO() == null ? null : routeInfoResp.getBusinessDTO().getSource())
                .build();

        log.info("getRouteInfo req:{}, resp:{}", request.toJSONString(), JSON.toJSONString(routeInfoDto));
        return routeInfoDto;
    }

    @Override
    public RouteInfoDto getRouteInfo(String routePlanId) {
        return queryDetail(routePlanId, false);
    }

    @Override
    public RouteInfoDto getRouteInfoWeak(String routePlanId) {
        try {
            return queryDetail(routePlanId, false);
        } catch (Exception e) {
            log.error("查询行程信息失败：", e);
            return null;
        }
    }


    @Override
    public RouteInfoDto getRouteInfoWithFareWeak(String routePlanId) {
        try {
            return queryDetail(routePlanId, true);
        } catch (Exception e) {
            log.error("查询行程信息失败：", e);
            return null;
        }
    }
}
