package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.bo.PayDeskInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.dto.ChartedAdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.combina.PayDeskInfoService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.travel.config.operate.service.CityService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 13:59
 */
public class CharterRoutePayServiceImplTest {
    @Mock
    SettlementRechargeService settlementRechargeService;
    @Mock
    UnifiedPaymentFacade unifiedPaymentFacade;
    @Mock
    PayDeskInfoService payDeskInfoService;
    @Mock
    PayChannelFactory payChannelFactory;
    @Mock
    RouteBusiness routeBusiness;
    @Mock
    CityService cityService;
    @Mock
    Logger log;
    @InjectMocks
    CharterRoutePayServiceImpl charterRoutePayServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAdvanceRecharge() throws Exception {
        when(payChannelFactory.paymentInfo(anyInt(), any())).thenReturn(null);

        Response<RechargePayBo> result = charterRoutePayServiceImpl.advanceRecharge(new ChartedAdvanceRechargeDTO());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetPayDeskInfo() throws Exception {
        Response<PayDeskInfoBo> result = charterRoutePayServiceImpl.getPayDeskInfo("routePlanUuid", "passengerUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testIsWMPPay() throws Exception {
        boolean result = charterRoutePayServiceImpl.isWMPPay(new PaywayEnum[] {PaywayEnum.THIRD_PAY_FLAG});
        Assert.assertEquals(true, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
