package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.business.DriverWalletBusiness;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverEarlyReleaseReq;
import com.t3.ts.pay.center.api.dto.wallet.AccountBalanceResVo;
import com.t3.ts.pay.center.api.dto.wallet.EarlyReleaseAmountVo;
import com.t3.ts.pay.center.api.dto.wallet.EarlyReleasePreviewVo;
import com.t3.ts.pay.center.api.util.AccountNewUtils;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.order.GeneralItemDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.GeneralItemService;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 *
 *
 * @ClassName: DriverEarlyReleaseController
 * @Package com.t3.ts.pay.center.api.controller
 * @version v1.0.0
 * <AUTHOR>
 * @date 2025/6/11 14:45
 */
@Api(tags = "司机加速提现卡API")
@Slf4j
@RestController
@RequestMapping("/api/driver/early/release")
public class DriverEarlyReleaseController {
    @DubboReference
    private GeneralItemService generalItemService;
    @Autowired
    private DriverWalletBusiness driverWalletBusiness;
    @Autowired
    private AccountNewUtils accountNewUtils;

    /**
     * 加速卡使用预览
     * @return Response
     */
    @ApiOperation(value = "加速卡使用预览", notes = "加速卡使用预览")
    @PostMapping("/preview")
    public Response preview(@RequestBody DriverEarlyReleaseReq req){
        if (null == req || StringUtils.isEmpty(req.getCardNo())) {
            return Response.createError("卡号不能为空");
        }
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        AccountBalanceResVo accountBalanceResVo = driverWalletBusiness.queryAccountBalance(userId, accountType);
        GeneralItemDto dto = new GeneralItemDto();
        dto.setBizType(BizType.DRIVER_EARLY_RELEASE_CARD.getType());
        dto.setOrderUuid(req.getCardNo());
        dto.setUserId(userId);
        Response preview = generalItemService.preview(dto);
        if (!preview.isSuccess()) {
            return preview;
        }
        String withdrawStr = StringUtils.isEmpty(accountBalanceResVo.getWithdrawalBalance()) ?
                "0.00" : accountBalanceResVo.getWithdrawalBalance();
        BigDecimal withdrawAmount = new BigDecimal(withdrawStr);
        EarlyReleasePreviewVo earlyReleasePreviewVo = new EarlyReleasePreviewVo();
        earlyReleasePreviewVo.setCurWithdrawAmount(withdrawAmount);
        earlyReleasePreviewVo.setTotalWithdrawAmount(withdrawAmount);
        EarlyReleaseAmountVo earlyReleaseAmountVo = JSONObject.parseObject(JSONObject.toJSONString(preview.getData()),
                EarlyReleaseAmountVo.class);
        earlyReleasePreviewVo.setRelease(earlyReleaseAmountVo);
        if (earlyReleaseAmountVo != null && earlyReleaseAmountVo.getWithdrawAmount() != null) {
            earlyReleasePreviewVo.setTotalWithdrawAmount(earlyReleaseAmountVo.getWithdrawAmount().add(withdrawAmount));
        }
        return Response.createSuccess(earlyReleasePreviewVo);
    }

    /**
     * 加速卡使用
     * @return Response
     */
    @ApiOperation(value = "加速卡使用", notes = "加速卡使用")
    @PostMapping("/use")
    public Response use(@RequestBody DriverEarlyReleaseReq req){
        if (null == req || StringUtils.isEmpty(req.getCardNo())) {
            return Response.createError("卡号不能为空");
        }
        String userId = RequestContextHelper.getDriverUuid();
        GeneralItemDto dto = new GeneralItemDto();
        dto.setBizType(BizType.DRIVER_EARLY_RELEASE_CARD.getType());
        dto.setOrderUuid(req.getCardNo());
        dto.setUserId(userId);
        return generalItemService.process(dto);
    }


    /**
     * 加速卡使用状态查询
     * @return Response
     */
    @ApiOperation(value = "加速卡使用状态查询", notes = "加速卡使用状态查询")
    @PostMapping("/status")
    public Response status(@RequestBody DriverEarlyReleaseReq req){
        if (null == req || StringUtils.isEmpty(req.getCardNo())) {
            return Response.createError("卡号不能为空");
        }
        String userId = RequestContextHelper.getDriverUuid();
        GeneralItemDto dto = new GeneralItemDto();
        dto.setBizType(BizType.DRIVER_EARLY_RELEASE_CARD.getType());
        dto.setOrderUuid(req.getCardNo());
        dto.setUserId(userId);
        return generalItemService.checkResult(dto);
    }
}
