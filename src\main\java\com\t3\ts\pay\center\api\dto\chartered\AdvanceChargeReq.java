package com.t3.ts.pay.center.api.dto.chartered;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date: 2019-11-13 19:05
 * @des: 预付款请求对象
 */
@ApiModel
@Data
public class AdvanceChargeReq {

    /**
     * 订单uuid
     */
    @ApiModelProperty(value = "订单uuid")
    @NotBlank(message = "订单uuid不能为空")
    private String orderUuid;

    /**
     * 支付方式{@link com.t3.ts.pay.center.api.enums.PayChannelEnum}
     */
    @ApiModelProperty(value = "支付方式{@link com.t3.ts.api.pay.enums.PayChannelEnum}")
    @NotNull(message = "支付方式不能为空")
    private Integer payType;
}
