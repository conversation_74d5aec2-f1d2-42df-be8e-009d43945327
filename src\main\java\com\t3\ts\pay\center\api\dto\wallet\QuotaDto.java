package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;

import java.io.Serializable;

/**
 *
 *
 * @ClassName: QuotaDto
 * @Package com.t3.ts.pay.center.api.dto.wallet
 * @version v1.0.0
 * <AUTHOR>
 * @date 2025/2/7 10:51
 */
@Data
public class QuotaDto  implements Serializable {
    private Long availableAmount;
    private Long totalAmount;
    private Long usedAmount;
    private String irrRate;
    private String nextStep;
    private String status;
    private Boolean showIncreaseQuota;
    private Boolean show4passenger;
    private String url4Passenger;
    private static final long serialVersionUID = 1L;
}
