package com.t3.ts.pay.center.api.dto.chartered;

import com.t3.ts.pay.center.api.dto.vo.IntegralMsgVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2019-11-13 11:06
 * @des: 收银台数据展示对象
 */
@ApiModel
@Data
public class PayDeskInfoVo {

    /**
     * 行程id
     */
    @ApiModelProperty(value = "行程id")
    private String orderUuid;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 车牌
     */
    @ApiModelProperty(value = "车牌")
    private String plateNum;

    /**
     * 钱包余额
     */
    @ApiModelProperty(value = "钱包余额")
    private BigDecimal balance;

    /**
     * 总费用
     */
    @ApiModelProperty(value = "总费用")
    private BigDecimal totalFare;

    /**
     * 折扣价格
     */
    @ApiModelProperty(value = "折扣价格")
    private BigDecimal discountFare;

    /**
     * 待支付费用
     */
    @ApiModelProperty(value = "待支付费用")
    private BigDecimal actualFare;

    /**
     * 余额已支付费用
     */
    @ApiModelProperty(value = "余额已支付费用")
    private BigDecimal amountPayed;

    /**
     * 优惠已抵扣
     */
    @ApiModelProperty(value = "优惠已抵扣")
    private BigDecimal couponPayed;

    /**
     * 优惠券id
     */
    @ApiModelProperty(value = "优惠券id")
    private String couponId;

    /**
     * 优惠价
     */
    @ApiModelProperty(value = "优惠价")
    private BigDecimal couponMoney;

    /**
     * 优惠券数量
     */
    @ApiModelProperty(value = "优惠券数量")
    private Integer couponCount;

    /**
     * 是否预付款
     */
    @ApiModelProperty(value = "是否预付款")
    private Boolean prePay;

    /**
     * 预付款金额
     */
    @ApiModelProperty(value = "预付款金额")
    private BigDecimal prePayFare;

    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费")
    private BigDecimal additionalFee;

    /**
     * 赠送币
     */
    @ApiModelProperty(value = "赠送币")
    private BigDecimal giftCurrency;

    /**
     * 取消费
     */
    @ApiModelProperty(value = "取消费")
    private BigDecimal cancelFee;

    /**
     * 超时取消费
     */
    @ApiModelProperty(value = "超时取消费")
    private BigDecimal timeOutFee;

    /**
     * 可用余额
     */
    @ApiModelProperty("可用余额")
    private BigDecimal availableBalance;

    /**
     * 礼品卡是否支持附加费用支付：true\false
     */
    @ApiModelProperty("礼品卡是否支持附加费用支付")
    private boolean giftCardCanPayService;

    /**
     * 是否积分抵扣：true|false
     */
    @ApiModelProperty("是否积分抵扣")
    private Boolean integralDeductFlag;

    /**
     * 可用积分
     */
    @ApiModelProperty("可用积分")
    private String availableIntegral;

    /**
     * 可抵扣费用
     */
    @ApiModelProperty("可抵扣费用")
    private String deductCost;

    /**
     * 积分抵扣限制提示
     */
    @ApiModelProperty("积分抵扣限制提示")
    private IntegralMsgVO integralMsg;

    /**
     * 积分已抵扣金额
     */
    @ApiModelProperty("积分已抵扣金额")
    private BigDecimal integralPayed;

}
