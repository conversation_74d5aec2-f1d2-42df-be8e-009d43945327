package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 * @date 2023/10/31
 */
@Getter
@Setter
@ApiModel("银行卡信息VO")
public class BankcardInfoResVo {
    @ApiModelProperty(value = "卡号")
    private String bankCard;

    private String bankCardNum;
    @ApiModelProperty(value = "卡类型：借记卡、信用卡")
    private String bankCardType;

    @ApiModelProperty(value = "银行名称：工商银行")
    private String bankName;

    @ApiModelProperty(value = "是否支持此银行卡")
    private Boolean bankSupportWithdraw;

    @ApiModelProperty(value = "错误信息描述")
    private String msg;
}
