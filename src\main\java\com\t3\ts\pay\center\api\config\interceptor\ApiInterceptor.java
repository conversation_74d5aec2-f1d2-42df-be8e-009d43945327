package com.t3.ts.pay.center.api.config.interceptor;

import com.alibaba.fastjson.JSON;
import com.t3.ts.constant.Constants;
import com.t3.ts.pay.center.api.config.PayCenterInterceptorConfig;
import com.t3.ts.pay.center.api.util.SpringUtils;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Map;

/**
 * API拦截器
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Slf4j
@Component
public class ApiInterceptor extends HandlerInterceptorAdapter {
    private static final String PREFIX_URL = "/api/driver/";

    /**
     * 前手柄
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理程序
     * @return boolean* @throws Exception 例外情况
     */
    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        boolean flag = true;
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            handleBreak:
            {
                response.setCharacterEncoding("utf-8");
                // 请求是JSON格式数据
                if (request.getContentType() != null
                        && request.getContentType().contains("json")) {
                    if (!request.getRequestURI().startsWith(PREFIX_URL)
                            && StringUtils.isBlank(request.getHeader(Constants.SIGN_KEY))) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION,
                                        Constants.ERR_CODE_NO_SIGN, null));
                        flag = false;
                        break handleBreak;
                    }
                    if (StringUtils.isBlank(request.getHeader(Constants.APPID_KEY))) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION,
                                        Constants.ERR_CODE_NO_APPID, null));
                        flag = false;
                        break handleBreak;
                    }
                    if (StringUtils.isBlank(request.getHeader(Constants.NONCESTR_KEY))) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION,
                                        Constants.ERR_CODE_NO_NONCESTR, null));
                        flag = false;
                        break handleBreak;
                    }
                } else {
                    Map<String, String[]> paramMap = request.getParameterMap();
                    if (!paramMap.containsKey(Constants.SIGN_KEY)) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION,
                                        Constants.ERR_CODE_NO_SIGN, null));
                        flag = false;
                        break handleBreak;
                    } else if (!paramMap.containsKey(Constants.APPID_KEY)) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION,
                                        Constants.ERR_CODE_NO_APPID, null));
                        flag = false;
                        break handleBreak;
                    } else if (!paramMap.containsKey(Constants.NONCESTR_KEY)) {
                        outPrint(response.getWriter(),
                                createJsonObject(Constants.ERR_MSG_ACCESS_VIOLATION, Constants.ERR_CODE_NO_NONCESTR,
                                        null));
                        flag = false;
                        break handleBreak;
                    }
                }
                response.reset();
                response.setHeader("sysTime", String.valueOf(System.currentTimeMillis()));
            }
        }
        return flag;
    }

    /**
     * 输出打印
     *
     * @param out      输出
     * @param response 响应
     */
    private void outPrint(PrintWriter out, Response response) {
        out.println(JSON.toJSON(response));
        out.flush();
        out.close();
    }

    /**
     * 创建json对象
     *
     * @param msg     味精
     * @param errCode 错误码
     * @param data    数据
     * @return {@link Response}
     */
    private Response createJsonObject(String msg, int errCode, Object data) {
        return Response.createError(msg, errCode, data);
    }
}
