package com.t3.ts.pay.center.api.dto.trade;

import java.math.BigDecimal;

/**
 * 权益卡Vo
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: PrivilegeVo
 * @Package com.t3.ts.pay.center.api.dto.trade
 * @date 2022/3/29 20:34
 */
public class PrivilegeVo {
    /**
     * 可用权益卡数量
     */
    private Integer availableCount;
    /**
     * 权益卡id
     */
    private String privilegeUuid;
    /**
     * 权益卡可抵扣金额
     */
    private BigDecimal decutionAmount;
}
