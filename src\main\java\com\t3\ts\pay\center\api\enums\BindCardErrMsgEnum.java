package com.t3.ts.pay.center.api.enums;

import java.util.Objects;

/**
 * 绑卡错误信息枚举
 */
public enum BindCardErrMsgEnum {

    BANK_CARD_NOT_SUPPORT("BANK_001", "该银行卡不支持绑定，请查看可支持的银行卡列表"),
    CARD_TYPE_NOT_SUPPORT("BANK_002", "只支持借记卡");

    private String code;
    private String msg;

    BindCardErrMsgEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取枚举根据代码
     *
     * @param code 代码
     * @return {@link String}
     */
    public static String getMsgByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BindCardErrMsgEnum bindCardErrMsgEnum : BindCardErrMsgEnum.values()) {
            if (bindCardErrMsgEnum.code.equals(code)) {
                return bindCardErrMsgEnum.getMsg();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
