package com.t3.ts.pay.center.api.dto.trade;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: hujd
 * @Date: 2022-08-05 9:26
 */
@Getter
@Setter
public class SecretFreeVo {

    /**
     * 免密类型【1:支付宝,2:微信,3:一网通,8:银联】
     * EnumPayOrderChannel.BALANCE.getCode()  具体定义在此枚举中
     */
    private String secretFreeType;

    /**
     * 开启还是关闭免密支付
     * 【0:获取免密支付串,1:关闭免密支付】
     */
    private Object operateType;

    /**
     * 支付宝开通免密串
     */
    private Object aliPaySdk;

    /**
     * 芝麻串
     */
    private Object zhimaSdk;

    /**
     * 微信开通免密串
     */
    private Object wxPaySdk;

    /**
     * 一网通开通免密串
     */
    private Object netcomPaySdk;

    /**
     * 银联开通免密串
     */
    private String unionPaySdk;
}
