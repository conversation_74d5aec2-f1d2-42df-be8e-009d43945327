
package com.t3.ts.pay.center.api.dto;

import javax.validation.constraints.Min;
import java.io.Serializable;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_10;

/**
 * <AUTHOR>
 * @date: 2019-02-18 12:59
 * @des:
 */
public class BasePageReq implements Serializable {

    private static final long serialVersionUID = 6160073910156256177L;
    private String userUuid;
    private Integer accountType;
    private Integer bookType;

    @Min(value = 1)
    private Integer curPage = 1;

    private Integer pageSize = NUM_10;

    public String getUserUuid() {
        return userUuid;
    }

    public void setUserUuid(String userUuid) {
        this.userUuid = userUuid;
    }

    public Integer getCurPage() {
        return curPage;
    }

    public void setCurPage(Integer curPage) {
        this.curPage = curPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getBookType() {
        return bookType;
    }

    public void setBookType(Integer bookType) {
        this.bookType = bookType;
    }
}
