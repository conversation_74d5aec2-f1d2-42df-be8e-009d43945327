package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-11-16 09:22
 * @des: 行程支付传输对象
 */
@Data
public class RoutePayDTO {

    @NotBlank(message = "行程uuid不能为空")
    private String orderUuid;


    /**
     * 支付业务类型
     *
     * @see com.t3.ts.pay.remote.constants.EnumPayOrderType
     */
    private Integer bizType;

    /**
     * 优惠券uuid
     */
    private String couponUuid;

    /**
     * 是否使用省心打
     */
    private Integer unWarriedArrive;

    /**
     * 权益卡uuid
     */
    private String privilegeUuid;

    /**
     * 券套餐商品sku
     */
    private String skuCode;

    /**
     * 券套餐商品uuid
     */
    private String skuUuid;

    /**
     * 使用券套餐里面的优惠券 1表示使用
     */
//    @ApiModelProperty(value = "1表示购买券套餐并使用券，2表示只购买券套餐", required = false)
    private Integer useCounponActivity;

    /**
     * 车费支付方式【包含余额+一种第三方支付】
     */
    @NotBlank(message = "支付方式不能为空")
    private List<Integer> payTypeList;

    /**
     * 出租车扫码支付
     */
    @ApiModelProperty(value = "出租车扫码支付")
    private Boolean taxiScanPay = false;

    /**
     * 乘客uuid
     */
    private String passengerUuid;

    /**
     * 乘客手机号
     */
    private String passengerMobile;

    /**
     * 小程序openid
     */
    private String openId;
    /**
     * 可用积分
     */
    private String availableIntegral;

    /**
     * 支付宝授权码
     */
    private String aliAuthCode;
    /**
     * 支付类型 支持聚合支付渠道
     */
    private List<Integer> aggPayTypeList;
    /**
     * 亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择
     */
    private Integer payForOtherType;

    /**
     * 支付并签约标识
     */
    private String payAndSign;
    /**
     * 为true时 表示从收银台发起的支付为免密支付，默认false
     */
    private Boolean noSecret;

    /**
     * 渠道来源
     */
    private int source;

    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;

    /**
     * 支付宝  用户付款中途退出返回商户网站的地址
     */
    private String quitUrl;

    /**
     * 是否纯电动车：1-是，0-否（发放蚂蚁森林能量）
     *
     */
    private Integer isElectricCar;
    /**
     * 车牌号（发放蚂蚁森林能量）
     */
    private String plateNum;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 新人 老客标识
     */
    private String sellType;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 来源
     * 注释同 {@link com.t3.ts.pay.center.api.dto.trade.RechargeReq.subSource}  字段注释
     */
    private String subSource;

    /**
     * 用户ip
     */
    private String userIp;

}
