/**
 * <AUTHOR>
 * @date ：Created in 2021/4/8 16:12
 * @description：微信支付分配置
 */

package com.t3.ts.pay.center.api.config.valueconfig;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/8 16:12
 */
@Getter
@Setter
@Component
public class AliPayPointsConfig {
    /**
     * 开关是否打开
     */
    @Value("${AliPayPoints.enabled:true}")
    private Boolean enabled;

    /**
     * 按钮标题
     */
    @Value("${AliPayPoints.btnTitle:支付并签约}")
    private String btnTitle;

    /**
     * 按钮副标题
     */
    @Value("${AliPayPoints.btnSubTitle:''}")
    private String btnSubTitle;

    /**
     * 按钮图标url
     */
    @Value("${AliPayPoints.btnIcon:''}")
    private String btnIcon;


    /**
     * 支付宝-支付并签约白名单-这里配置userId
     */
    @Value("${AliPayPoints.payAndSign.aliWhiteList:''}")
    private String payAndSignAliWhiteList;

    /**
     * 支付宝-支付并签约百分比
     */
    @Value("${AliPayPoints.payAndSign.aliPercent:-1}")
    private Integer payAndSignAliPercent;

}
