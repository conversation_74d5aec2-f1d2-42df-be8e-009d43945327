package com.t3.ts.pay.center.api.exception;

import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * <AUTHOR>
 */
@Order(0)
@Slf4j
@RestControllerAdvice
public class BusinessExceptionHandler {

    /**
     * @param e 业务异常
     * @return Response
     * @Description 业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Response handleRRException(BusinessException e) {
        log.error(e.getMessage(), e);
        return Response.createError(e.getMsg(), e.getCode(), e.getObj());
    }

    /**
     * @param e 业务异常
     * @return Response
     * @Description 系统未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Response handleException(Exception e) {
        log.error(e.getMessage(), e);
        return Response.createError(ResultErrorEnum.GLOBLE_EXCEPTION);
    }

    /**
     * @param e 业务异常
     * @return Response
     * @Description 系统未知异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.OK)
    public Response handleValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        return Response.createError(e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }

    /**
     * @param e 业务异常
     * @return Response
     * @Description 系统未知异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public Response handleMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        log.error(e.getMessage(), e);
        return Response.createError("不支持的请求内容类型，请设置：Content-Type=application/json;charset=UTF-8,"
                + "错误详情：" + e.getMessage(), ResultErrorEnum.PARAM_FORAMT_ERROR.getMsg());
    }
}
