package com.t3.ts.pay.center.api.controller;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 15:48
 */
@RunWith(MockitoJUnitRunner.class)
public class HealthControllerTest {
    HealthController healthController = new HealthController();

    @Test
    public void testHealthCheck() throws Exception {
//        String result = healthController.healthCheck();
//        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme