package com.t3.ts.pay.center.api.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.dto.AccountSignDtoV2;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.pay.center.api.bo.PayDeskInfoBo;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.business.PayProxyService;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PayConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.AdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.enums.PayChannelEnum;
import com.t3.ts.pay.center.api.exception.BizExceptionUtil;
import com.t3.ts.pay.center.api.rest.MallRest;
import com.t3.ts.pay.center.api.rest.mall.MallOrder;
import com.t3.ts.pay.center.api.rest.mall.MallOrderData;
import com.t3.ts.pay.center.api.rest.mall.PassengerInfoDTO;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.service.RoutePayService;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.remote.constants.*;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.dto.SettlementRechargeDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.SettleType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.settlement.centre.service.SettlementUnifiedService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.*;

/**
 * <AUTHOR>
 * @date: 2019-11-15 21:52
 * @des: 行程支付相关实现类
 */
@Slf4j
@Service("routePayService")
public class RoutePayServiceImpl implements RoutePayService {

    @DubboReference
    private SettlementRechargeService settlementRechargeService;
    @Autowired
    private PayProxyService payProxyService;
    @Autowired
    private PayChannelFactory payChannelFactory;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private SettlementUnifiedService settlementUnifiedService;
    @Autowired
    private MallRest mallRest;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @DubboReference
    private AccountSignService accountSignService;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;


    @Override
    public Response<RechargePayBo> advanceRecharge(AdvanceRechargeDTO advanceRechargeDTO) {
        log.info("RoutePayServiceImpl.advanceRecharge:{}", JSONUtil.toJsonStr(advanceRechargeDTO));
        String settlementUuid = "";
        try {
            settlementUuid = getSettlementId(advanceRechargeDTO);
        } catch (BizException e) {
            log.error("getSettlementId error", e);
            return Response.createError(e.getRes().getMsg(), e.getRes().getCode());
        }

        List<Integer> payTypeList = new ArrayList<>();
        payTypeList.add(advanceRechargeDTO.getPayType());

        //在线支付信息
        Response<String> onlinePaymentResponse =
                travelOnlineCharge(settlementUuid, payTypeList, advanceRechargeDTO);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg(), onlinePaymentResponse.getCode());
        }
        //是否免密支付
        boolean isNoSecret = StringUtils.equals(String.valueOf(onlinePaymentResponse.getCode()),
                PayConstants.NO_SEC_PAY) ? true : false;
        //内部传递对象
        PaymentInfoBo paymentInfoBo = new PaymentInfoBo(isNoSecret, onlinePaymentResponse,
                advanceRechargeDTO.getPassengerUuid(), advanceRechargeDTO.getPassengerMobile());
        paymentInfoBo.setPayAndSign(advanceRechargeDTO.getPayAndSign());
        paymentInfoBo.setSource(advanceRechargeDTO.getSource());
        // 获取真实的支付类型
//        int payType = getPayType(payTypeList);
        int payType = PayUtils.getRealChannelRouting(onlinePaymentResponse, payTypeList);
        log.info("预付款 获取支付路由后的支付类型 payType={}", payType);
        //支付工厂模式调用
        Response<RechargePayBo> payResponse = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg(), payResponse.getCode());
        }
        if (Objects.isNull(payResponse.getData())) {
            return Response.createError(ResultErrorEnum.GET_RECHARGE_IFNO_ERROR);
        }
        RechargePayBo rechargePayBo = payResponse.getData();
        // 增加返回结算id addBy zangzl20200709
        rechargePayBo.setSettlementId(settlementUuid);
        return Response.createSuccess("获取预付款支付信息成功", rechargePayBo);
    }

    @Override
    public Response<PayDeskInfoBo> getPayDeskInfo(String routePlanUuid, String passengerUuid) {
        return null;
    }

    @Override
    public Response<RechargePayBo> launchRoutePay(RoutePayDTO routePayDTO) {
        log.info("launchRoutePay param:{}", JSONObject.toJSONString(routePayDTO));
        Boolean taxiScanPay = routePayDTO.getTaxiScanPay();
        //校验行程状态
        if (!taxiScanPay) {
            Response<RouteInfoDto> checkResponse = getRouteAndCheckStatus(routePayDTO.getOrderUuid());
            if (!checkResponse.isSuccess()) {
                return Response.createError(checkResponse.getMsg(), checkResponse.getCode());
            }
            RouteInfoDto routeInfoDto = checkResponse.getData();
            // 判断是否企业用车敏感订单 低版本用户
            if (switchConfig.getDisputeFlag()
                    && StringUtils.isNotBlank(routePayDTO.getGrayBuild())
                    && !CollectionUtils.isEmpty(routeInfoDto.getLabels())
                    && isDisputeOrder(routePayDTO, routeInfoDto)) {
                return Response.createError(ResultErrorEnum.DISPUTER_ORDER_PAY_BEFORE_NEED_BEFORE);
            }
            // 处理业务类型映射
            dealBizType(routePayDTO, routeInfoDto);
            routePayDTO.setIsElectricCar(routeInfoDto.getIsElectricCar());
            routePayDTO.setPlateNum(routeInfoDto.getPlateNum());
            routePayDTO.setCityCode(routeInfoDto.getCityCode());
            if (null != routeInfoDto.getSource() && routePayDTO.getSource() <= NUM_0) {
                routePayDTO.setSource(routeInfoDto.getSource());
            }
        }

        /**
         * -----------------处理收银券套餐搭售------------
         */
        if (StringUtils.isNotBlank(routePayDTO.getSkuCode())) {
            Response<String> handle = getMallOrderResp(routePayDTO);
            if (!handle.isSuccess() && ResultErrorEnum.MALL_PACKAGE_BUY_UPPER_LIMIT.getMsg().equals(handle.getMsg())) {
                return Response.createError(ResultErrorEnum.MALL_PACKAGE_BUY_UPPER_LIMIT_OUTSIDE);
            } else if (!handle.isSuccess()) {
                return Response.createError(ResultErrorEnum.COUPON_ACTIVITY_ERROR);
            }
            String bizId = handle.getData();
            routePayDTO.setOrderUuid(bizId);
            routePayDTO.setBizType(EnumPayOrderType.COMBINE_PAY.getCode());
        }

        //在线支付信息
        Response<String> onlinePaymentResponse = travelOnlinePayment(routePayDTO);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg(), onlinePaymentResponse.getCode());
        }
        // 余额支付成功
        if (onlinePaymentResponse.getData() == null) {
            RechargePayBo payInfoVo = new RechargePayBo();
            payInfoVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            return Response.createSuccess("获取支付信息成功", payInfoVo);
        }
        //获取支付路由后的支付类型
        int payType = PayUtils.getRealChannelRouting(onlinePaymentResponse, routePayDTO.getPayTypeList());
        log.info("行程支付 获取支付路由后的支付类型 payType={}", payType);
        //是否免密支付
        boolean isNoSecret = false;
        if (PayConstants.NO_SEC_PAY.equals(String.valueOf(onlinePaymentResponse.getCode()))) {
            isNoSecret = true;
        }
        //内部传递对象
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret)
                .paymentResp(onlinePaymentResponse)
                .passengerUuid(routePayDTO.getPassengerUuid())
                .passengerMobile(routePayDTO.getPassengerMobile())
//                .aggPayTypeList(routePayDTO.getAggPayTypeList())
                .taxiScanPay(routePayDTO.getTaxiScanPay())
                .payAndSign(routePayDTO.getPayAndSign())
                .source(routePayDTO.getSource())
                .routePlanUuid(routePayDTO.getOrderUuid())
                .plateNum(routePayDTO.getPlateNum())
                .isElectricCar(routePayDTO.getIsElectricCar())
                .build();
        //支付工厂模式调用：下单成功后处理支付串或者免密支付，调用三方免密支付
        Response<RechargePayBo> payResponse = getPayResponse(payType, paymentInfoBo);
        payResponse.setAttachment(onlinePaymentResponse.getAttachment());
        return payResponse;
    }


    /**
     * 判断敏感订单是否老版本用户
     *
     * @param routePayDTO  routePayDTO
     * @param routeInfoDto readingRoutePlanDto
     * @return Boolean
     */
    private Boolean isDisputeOrder(RoutePayDTO routePayDTO, RouteInfoDto routeInfoDto) {
        List<String> labels = routeInfoDto.getLabels();
        String disputeOrderLabels = switchConfig.getDisputeLabels();
        String companySensitiveLabels = switchConfig.getCompanySensitiveLabels();
        for (String label : labels) {
            boolean scenesA = disputeOrderLabels.contains(label)
                    && Double.parseDouble(routePayDTO.getGrayBuild()) < NUM_530;
            boolean scenesB = companySensitiveLabels.contains(label)
                    && Double.parseDouble(routePayDTO.getGrayBuild()) < NUM_700;
            if (scenesA || scenesB) {
                log.info("orderUuid:{},需要升级后支付", routePayDTO.getOrderUuid());
                return true;
            }
        }
        return false;
    }

    /**
     * 获取支付结果
     *
     * @param payType       支付类型
     * @param paymentInfoBo 支付参数
     * @return 支付结果
     */
    @Override
    public Response<RechargePayBo> getPayResponse(int payType, PaymentInfoBo paymentInfoBo) {


        Response<RechargePayBo> payResponse = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg(), payResponse.getCode());
        }
        RechargePayBo rechargePayVo = payResponse.getData();
        if (rechargePayVo.getPayType() == EnumPayOrderChannel.BALANCE.getCode()) {
            return Response.createSuccess("余额支付成功", rechargePayVo);
        }
        return Response.createSuccess("获取行程支付信息成功", rechargePayVo);
    }

    /**
     * 处理生成订单
     *
     * @param routePayDTO 参数
     * @return bizId
     */
    private Response<String> getMallOrderResp(RoutePayDTO routePayDTO) {
        String skuCode = routePayDTO.getSkuCode();
        String skuUuid = routePayDTO.getSkuUuid();
        Integer useCounponActivity = routePayDTO.getUseCounponActivity();
        Integer useFlag = NUM_1;
        if (null != useCounponActivity && NUM_1 == useCounponActivity) {
            useFlag = NUM_2;
        }
        PassengerInfoDTO passengerInfoDTO = PassengerInfoDTO.builder()
                .uuid(routePayDTO.getPassengerUuid()).mobile(routePayDTO.getPassengerMobile()).build();
        // 新人链路orderTime 给下单时间
        String orderTime = DateUtils.getDateTimeFormat(new Date());
        if (StringUtils.isNotBlank(routePayDTO.getSellType()) && Objects.nonNull(routePayDTO.getOrderTime())) {
            orderTime = DateUtils.getDateTimeFormat(routePayDTO.getOrderTime());
        }
        MallOrder mallOrder = MallOrder.builder()
                .buyNum(NUM_1)
                .cityCode("")
                .skuCode(skuCode)
                .skuUuid(skuUuid)
                .sellType(routePayDTO.getSellType())
                .useFlag(useFlag)
                .orderTime(orderTime)
                .source(NUM_1)
                .bizType(NUM_3)
                .passengerInfoDTO(passengerInfoDTO).build();

        if (useFlag == NUM_2) {
            mallOrder.setCouponId(routePayDTO.getCouponUuid());
        }

        Response<MallOrderData> response = mallRest.mallOrderAdd(mallOrder);
        if (null == response || !response.isSuccess() || null == response.getData()) {
            return Response.createError(response.getMsg());
        }
        MallOrderData mallOrderData = response.getData();
        JSONObject extendParam = new JSONObject();
        extendParam.put("routeUuid", routePayDTO.getOrderUuid());
        extendParam.put("needSettle", true);
        JSONObject generalData = new JSONObject();
        generalData.put("bizType", NUM_0);
        generalData.put("settleType", NUM_0);
        generalData.put("paymentSubject", routePayDTO.getPassengerUuid());
        generalData.put("paymentSubjectType", String.valueOf(NUM_1));
        generalData.put("orderUuid", mallOrderData.getOrderCode());
        generalData.put("orderFare", getBigDecimalValue(mallOrderData.getSkuSellCash()).toPlainString());
        extendParam.put("generalData", generalData);
        com.t3.ts.settlement.centre.dto.UnifiedDto unifiedDto = new com.t3.ts.settlement.centre.dto.UnifiedDto();
        unifiedDto.setSceneType("t3pay.settlement.order.save");
        unifiedDto.setExtendParam(extendParam.toJSONString());
        Response handle = settlementUnifiedService.handle(unifiedDto);
        if (null == handle || !handle.isSuccess()) {
            return Response.createError("券套餐结算失败！");
        }
        return handle;
    }

    /**
     * 获取较大的十进制值
     *
     * @param bigDecimal 大小数
     * @return {@link BigDecimal}
     */
    private BigDecimal getBigDecimalValue(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }


    /**
     * 处理业务类型映射
     *
     * @param routePayDTO         支付参数
     * @param routeInfoDto 行程信息
     */
    private void dealBizType(RoutePayDTO routePayDTO, RouteInfoDto routeInfoDto) {
        Integer typeTrip = routeInfoDto.getTypeTrip();
        // 行程类型（个人/企业）：1 个人;2 企业
        Integer typeEnt = routeInfoDto.getTypeEnt();
        if (null != typeEnt && NUM_1 == typeEnt) {
            // 个人行程
            if (typeTrip != null && typeTrip.intValue() == NUM_8) {
                //包车类型
                routePayDTO.setBizType(EnumPayOrderType.CHARTERED_ONLINE.getCode());
            } else {
                //用车类型：(1:出租车,2：专车，3：拼车，4：快车,5：顺风车）
                Integer typeModule = routeInfoDto.getTypeModule();
                if (typeModule != null && typeModule.intValue() == NUM_1) {
                    //出租车
                    routePayDTO.setBizType(EnumPayOrderType.TAXI_TRAVEL_ONLINE.getCode());
                } else if (typeModule != null && typeModule.intValue() == NUM_2) {
                    //专车
                    routePayDTO.setBizType(EnumPayOrderType.TAILORED_ONLINE.getCode());
                } else if (typeModule != null && typeModule.intValue() == NUM_4) {
                    if (Objects.nonNull(routePayDTO.getPayForOtherType())
                            && PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType()
                            == routePayDTO.getPayForOtherType()) {
                        routePayDTO.setBizType(EnumPayOrderType.PAY_FOR_ANOTHER_OLD_MAN_AUTO_PAY.getCode());
                    } else {
                        //快享
                        routePayDTO.setBizType(EnumPayOrderType.TRAVELONLINE.getCode());
                    }
                } else if (typeModule != null && typeModule == NUM_6) {
                    //惠享
                    if (Objects.nonNull(routePayDTO.getPayForOtherType())
                            && PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType()
                            == routePayDTO.getPayForOtherType()) {
                        routePayDTO.setBizType(EnumPayOrderType.PAY_FOR_ANOTHER_OLD_MAN_AUTO_PAY.getCode());
                    } else {
                        //惠享
                        routePayDTO.setBizType(EnumPayOrderType.CHEAP_ONLINE.getCode());
                    }
                } else if (typeModule != null && typeModule == NUM_7) {
                    //自动驾驶
                    routePayDTO.setBizType(EnumPayOrderType.AUTOPILOT_ONLINE.getCode());
                } else if (typeModule != null && typeModule == EnumTypeModule.AUTOPILOT_V2_RIDE.getCode()) {
                    //自动驾驶v2
                    routePayDTO.setBizType(EnumPayOrderType.AUTOPILOT_V2_ONLINE.getCode());
                } else if (typeModule != null && typeModule == NUM_8) {
                    //三方运力
                    routePayDTO.setBizType(EnumPayOrderType.TRANSPORT_ONLINE.getCode());
                } else if (typeModule != null && typeModule == CommonNumConst.NUM_9) {
                    //三方运力
                    routePayDTO.setBizType(EnumPayOrderType.SHOUYUE_ONLINE.getCode());
                } else if (typeModule != null && typeModule.equals(EnumTypeModule.YANGGUANG_TRAVEL.getTypeMode())) {
                    //三方运力
                    routePayDTO.setBizType(EnumPayOrderType.YANGGUANG_ONLINE.getCode());
                } else if (typeModule != null && typeModule == CommonNumConst.NUM_11) {
                    routePayDTO.setBizType(EnumPayOrderType.WUHAN_TAXI_ONLINE.getCode());
                } else if (typeModule != null && typeModule == CommonNumConst.NUM_14) {
                    routePayDTO.setBizType(EnumPayOrderType.HITCHHIKED_ONLINE.getCode());
                } else if (typeModule != null && typeModule == CommonNumConst.NUM_21) {
                    routePayDTO.setBizType(EnumPayOrderType.HITCH_CAR_ONLINE.getCode());
                } else if (typeModule != null && typeModule.equals(EnumTypeModule.RUQI_TRAVEL.getTypeMode())) {
                    // 如祺出行
                    routePayDTO.setBizType(EnumPayOrderType.RUQI_ONLINE.getCode());
                } else if (switchConfig.getThirdTransport().contains(typeModule)) {
                    routePayDTO.setBizType(EnumPayOrderType.THIRD_TRANSPORT_ONLINE.getCode());
                }
            }
        } else if (null != typeEnt && NUM_2 == typeEnt) {
            //企业行程
            if (typeTrip != null && typeTrip.intValue() == NUM_8) {
                //包车类型
                routePayDTO.setBizType(EnumPayOrderType.CHARTERED_COMPANY_PERSONAL_PAY.getCode());
            } else {
                //用车类型：(1:出租车,2：专车，3：拼车，4：快车,5：顺风车）
                Integer typeModule = routeInfoDto.getTypeModule();
                if (typeModule != null && typeModule.intValue() == NUM_2) {
                    //专车
                    routePayDTO.setBizType(EnumPayOrderType.TAILORED_COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule.intValue() == NUM_4) {
                    //快享
                    routePayDTO.setBizType(EnumPayOrderType.COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule == NUM_6) {
                    //惠享
                    routePayDTO.setBizType(EnumPayOrderType.CHEAP_COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule == NUM_7) {
                    //自动驾驶
                    routePayDTO.setBizType(EnumPayOrderType.AUTOPILOT_COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule == NUM_8) {
                    //三方运力
                    routePayDTO.setBizType(EnumPayOrderType.TRANSPORT_COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule == CommonNumConst.NUM_9) {
                    //三方运力
                    routePayDTO.setBizType(EnumPayOrderType.SHOUYUE_COMPANY_PERSONAL_PAY.getCode());
                } else if (typeModule != null && typeModule.equals(EnumTypeModule.YANGGUANG_TRAVEL.getTypeMode())) {
                    routePayDTO.setBizType(EnumPayOrderType.YANGGUANG_COMPANY_PERSONAL_PAY.getCode());
                }
            }
        }

    }

    /**
     * 在线充值
     *
     * @param settlementId       行程id
     * @param payTypeList        支付类型
     * @param advanceRechargeDTO 充值参数
     * @return 出参
     */
    private Response<String> travelOnlineCharge(String settlementId, List<Integer> payTypeList,
                                                AdvanceRechargeDTO advanceRechargeDTO) {
        if (CollectionUtils.isEmpty(payTypeList)) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR);
        }
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(EnumPayOrderType.RECHARGE.getCode());
        paymentDto.setUserId(advanceRechargeDTO.getPassengerUuid());
        paymentDto.setSettlementId(settlementId);
        boolean noSecretFinal = false;
        if (NumConstants.STR_1.equals(advanceRechargeDTO.getPayAndSign())) {
            //组装获取最终免密支付标识
            noSecretFinal = getNoSecretPre(paymentDto.getUserId(), advanceRechargeDTO.getNoSecret(), payTypeList);
            paymentDto.getExtendParams().put("noSecret", noSecretFinal);
        }
        paymentDto.getExtendParams().put("payAndSign", advanceRechargeDTO.getPayAndSign());
        paymentDto.getExtendParams().put("quit_url", advanceRechargeDTO.getQuitUrl());
        paymentDto.getExtendParams().put("cityCode", advanceRechargeDTO.getCityCode());
        // 云闪付纬度需要的字段
        paymentDto.getExtendParams().put("sub_source", advanceRechargeDTO.getSubSource());
        paymentDto.getExtendParams().put("user_auth_code", advanceRechargeDTO.getAuthCode());
        paymentDto.getExtendParams().put("app_up_identifier", advanceRechargeDTO.getAppUpIdentifier());

        paymentDto.getExtendParams().put("grayVersion", advanceRechargeDTO.getGrayVersion());
        paymentDto.getExtendParams().put("grayBuild", advanceRechargeDTO.getGrayBuild());
        paymentDto.getExtendParams().put("terminal", advanceRechargeDTO.getTerminal());
        paymentDto.getExtendParams().put("userIp", advanceRechargeDTO.getUserIp());

        if (!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(paymentDto.getUserId()).getData()) {
            // 配合招行聚合支付
            if (!noSecretFinal) {
                //非免密才需要做渠聚合渠道转换
                cmbAggHelper.convertAggChannel(payTypeList, advanceRechargeDTO.getAggPayTypeList(), paymentDto.getUserId(), paymentDto);
            }
        }
        // 获取支付方式
        Response<PaymentDto> fullPaymentDto = fullPaymentDto(paymentDto, payTypeList, advanceRechargeDTO.getOpenId(),
                advanceRechargeDTO.getAliAuthCode(), null);
        if (!fullPaymentDto.isSuccess()) {
            return Response.createError(fullPaymentDto.getMsg(), fullPaymentDto.getCode());
        }
        //执行支付
        Response<String> onlinePaymentResponse = payProxyService.pay(paymentDto);
        if (null == onlinePaymentResponse || !onlinePaymentResponse.isSuccess()) {
            return Response.createError(ResultErrorEnum.PAY_ERROR);
        }

        return onlinePaymentResponse;
    }

    /**
     * fullPaymentDto
     *
     * @param paymentDto        paymentDto
     * @param payTypeList       payTypeList
     * @param openId            openId
     * @param aliAuthCode       aliAuthCode
     * @param availableIntegral availableIntegral
     * @return 出参
     */
    public Response<PaymentDto> fullPaymentDto(PaymentDto paymentDto, List<Integer> payTypeList, String openId,
                                               String aliAuthCode, String availableIntegral) {
        // 支付渠道类型
        PaywayEnum[] paywayEnums = PayWayConvert.getPayWayEnum(payTypeList);
        if (null != paywayEnums) {
            List<PaywayEnum> paywayEnumList = new ArrayList<>(Arrays.asList(paywayEnums));
            // 支付宝小程序
            if (paywayEnumList.contains(PaywayEnum.ALIPAY_MINI)) {
                if (StringUtils.isNotBlank(aliAuthCode)) {
                    paymentDto.getExtendParams().put("code", aliAuthCode);
                } else {
                    return Response.createError(ResultErrorEnum.ALI_MINI_PARAM_ERROR);
                }
            }
            // 微信小程序程序
            if (paywayEnumList.contains(PaywayEnum.WECHAT_MINIPROGRAM)) {
                if (StringUtils.isNotBlank(openId)) {
                    paymentDto.getExtendParams().put("openId", openId);
                } else {
                    return Response.createError(ResultErrorEnum.WX_MINI_PARAM_ERROR);
                }
            }
            paymentDto.setPaywayEnums(paywayEnumList.toArray(new PaywayEnum[]{}));
        }
        //如果是老年用车-亲友代付-免密代扣 并且没有支付方式
        if (paymentDto.getBizType().intValue() == EnumPayOrderType.PAY_FOR_ANOTHER_OLD_MAN_AUTO_PAY.getCode()
                && Objects.isNull(paymentDto.getPaywayEnums())) {
            paymentDto.setPaywayEnums(new PaywayEnum[]{});
        }
        return Response.createSuccess(paymentDto);
    }

    /**
     * travelOnlinePayment
     *
     * @param routePayDTO routePayDTO
     * @return 出参
     */
    private Response<String> travelOnlinePayment(RoutePayDTO routePayDTO) {
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(routePayDTO.getBizType());
        paymentDto.setUserId(routePayDTO.getPassengerUuid());
        paymentDto.setBizId(routePayDTO.getOrderUuid());
        paymentDto.setMarketingId(routePayDTO.getCouponUuid());
        paymentDto.setPrivilegeUuid(routePayDTO.getPrivilegeUuid());
        List<Integer> payTypeList = routePayDTO.getPayTypeList();
        //组装获取最终免密支付标识
        boolean noSecretFinal = getNoSecret(paymentDto.getUserId(), routePayDTO.getNoSecret(), payTypeList);
        if (!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(paymentDto.getUserId()).getData()) {
            // 配合招行聚合支付
            if (!noSecretFinal) {
                //非免密才需要做渠聚合渠道转换
                cmbAggHelper.convertAggChannel(payTypeList, routePayDTO.getAggPayTypeList(), paymentDto.getUserId(), paymentDto);
            }
            routePayDTO.setPayTypeList(payTypeList);
        }
        // 构建支付方式
        Response<PaymentDto> fullPaymentDto = fullPaymentDto(paymentDto, routePayDTO.getPayTypeList(),
                routePayDTO.getOpenId(), routePayDTO.getAliAuthCode(), routePayDTO.getAvailableIntegral());

        if (!fullPaymentDto.isSuccess()) {
            return Response.createError(fullPaymentDto.getMsg(), fullPaymentDto.getCode());
        }
        // 是否选择了省心打
        paymentDto.getExtendParams().put("unWarriedArrive", routePayDTO.getUnWarriedArrive());
        paymentDto.getExtendParams().put("payForOtherType", routePayDTO.getPayForOtherType());
        // 支付并签约标识
        paymentDto.getExtendParams().put("payAndSign", routePayDTO.getPayAndSign());
        //收银台发起免密支付标识
        paymentDto.getExtendParams().put("noSecret", noSecretFinal);
        paymentDto.getExtendParams().put("grayVersion", routePayDTO.getGrayVersion());
        paymentDto.getExtendParams().put("grayBuild", routePayDTO.getGrayBuild());
        paymentDto.getExtendParams().put("terminal", routePayDTO.getTerminal());
        paymentDto.getExtendParams().put("quit_url", routePayDTO.getQuitUrl());
        paymentDto.getExtendParams().put("isElectricCar", routePayDTO.getIsElectricCar());
        paymentDto.getExtendParams().put("plateNum", routePayDTO.getPlateNum());
        paymentDto.getExtendParams().put("source", routePayDTO.getSource());
        paymentDto.getExtendParams().put("cityCode", routePayDTO.getCityCode());

        paymentDto.getExtendParams().put("sub_source", routePayDTO.getSubSource());
        paymentDto.getExtendParams().put("user_auth_code", routePayDTO.getAuthCode());
        paymentDto.getExtendParams().put("userIp", routePayDTO.getUserIp());


        //是否使用券套餐里面的券 （只购买不使用的时候，透传到pay-center）
        if (null != routePayDTO.getUseCounponActivity()
                && NumberConstants.NUMBER_2 == routePayDTO.getUseCounponActivity()) {
            paymentDto.getExtendParams().put("useCounponActivity", routePayDTO.getUseCounponActivity());
        }
        paymentDto.getExtendParams().put("skuCode", routePayDTO.getSkuCode());
        //执行支付
        Response<String> payResp = payProxyService.pay(paymentDto);
        if (null == payResp) {
            return Response.createError(ResultErrorEnum.PAY_ERROR);
        }
        return payResp;
    }

    /**
     * 获取是否免密支付标识
     *
     * @param passenger   乘客
     * @param noSecret    不是什么秘密
     * @param payTypeList 支付类型列表
     * @return boolean
     */
    private boolean getNoSecretPre(String passenger, boolean noSecret, List<Integer> payTypeList) {
        if (noSecret) {
            return true;
        }
        PayChannelEnum payChannelEnum = null;
        for (Integer payType : payTypeList) {
            if (PayChannelEnum.matchAnyThirdPay(payType, PayChannelEnum.WEIXIN, PayChannelEnum.ALIPAY,
                    PayChannelEnum.NETCOM, PayChannelEnum.UNIONPAY, PayChannelEnum.WECHATMINIPROGRAM)) {
                payChannelEnum = PayChannelEnum.getPayChannelEnumByPayType(payType);
            }
        }
        if (Objects.isNull(payChannelEnum)) {
            return false;
        }
        boolean isSupportNoSecretPay = false;
        AccountSignParamDto queryDto = new AccountSignParamDto();
        queryDto.setUserId(passenger);
        // wei
        if (PayChannelEnum.WECHATMINIPROGRAM.getPayType().equals(payChannelEnum.getPayType())) {
            queryDto.setPayChannel(PayChannelEnum.WEIXIN.getPayType());
        } else {
            queryDto.setPayChannel(payChannelEnum.getPayType());
        }
        queryDto.setDensityFree(AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode());
        Response queryResponse = accountSignService.getSignContract(queryDto);
        if (queryResponse.isSuccess() && Objects.nonNull(queryResponse.getData())
                && !DateUtils.isBeforeAppointedTime(((AccountSignDtoV2) queryResponse.getData()).getUpdateTime(),
                NumConstants.NUM_5)) {
            isSupportNoSecretPay = true;
        }
        return isSupportNoSecretPay;
    }

    /**
     * 获取是否免密支付标识
     *
     * @param passenger   乘客
     * @param noSecret    不是什么秘密
     * @param payTypeList 支付类型列表
     * @return boolean
     */
    private boolean getNoSecret(String passenger, boolean noSecret, List<Integer> payTypeList) {
        if (noSecret) {
            return true;
        }
        PayChannelEnum payChannelEnum = PayChannelEnum.getThirdPayChannelEnum(payTypeList);
        if (Objects.isNull(payChannelEnum)) {
            return false;
        }
        boolean isSupportNoSecretPay = false;
        AccountSignParamDto queryDto = new AccountSignParamDto();
        queryDto.setUserId(passenger);
        queryDto.setPayChannel(payChannelEnum.getPayType());
        queryDto.setDensityFree(AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode());
        Response queryResponse = accountSignService.getSignContract(queryDto);
        if (queryResponse.isSuccess() && Objects.nonNull(queryResponse.getData())
                && !DateUtils.isBeforeAppointedTime(((AccountSignDtoV2) queryResponse.getData()).getUpdateTime(),
                NumConstants.NUM_5)) {
            isSupportNoSecretPay = true;
        }
        return isSupportNoSecretPay;
    }

    /**
     * 是否是微信小程序支付
     *
     * @param paywayEnums 支付方式列表
     * @return 出参
     */
    public boolean isWMPPay(PaywayEnum[] paywayEnums) {
        return Arrays.asList(paywayEnums).contains(PaywayEnum.WECHAT_MINIPROGRAM);
    }

    /**
     * 校验行程状态
     *
     * @param orderUuid 行程id
     * @return 行程信息
     */
    private Response<RouteInfoDto> getRouteAndCheckStatus(String orderUuid) {
        //获取行程数据
        RouteInfoDto routeInfo = routeDetailClient.getRouteInfo(orderUuid);
        if (routeInfo == null) {
            return Response.createError(ResultErrorEnum.ROUTE_INFO_ERROR);
        }

        //校验行程状态=待支付
        if (!RouteStatus.ROUTE_6.getStatus().equals(routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString())) {
            return Response.createError(ResultErrorEnum.ROUTE_PAYED);
        }
        return Response.createSuccess(routeInfo);
    }

    /**
     * 获取充值结算单uuid
     *
     * @param advanceRechargeDTO AdvanceRechargeDTO
     * @return String
     */
    private String getSettlementId(AdvanceRechargeDTO advanceRechargeDTO) {
        // 顺风车的预付款
        if (null != advanceRechargeDTO.getBizLineFlag()
                && NumberConstants.NUMBER_1 == advanceRechargeDTO.getBizLineFlag()) {
            //先查询预付款流水号是否已经创单（顺风车预付款免密会先创单）
            SettlementGeneralDto generalDto = new SettlementGeneralDto();
            generalDto.setOrderUuid(advanceRechargeDTO.getPrePayNo());
            generalDto.setPaymentSubject(advanceRechargeDTO.getPassengerUuid());
            generalDto.setSettleType(SettleType.GENERAL_PAYMENT.getType());
            generalDto.setBizType(BizType.RECHARGE.getType());
            Response<List<SettlementGeneralDto>> generalRes = settlementGeneralService.selectSettlement(generalDto);
            if (null == generalRes || !generalRes.isSuccess()) {
                BizExceptionUtil.create(ResultErrorEnum.SAVE_RECHARGE_SETTLEMENT_ERROR.getMsg(),
                        ResultErrorEnum.SAVE_RECHARGE_SETTLEMENT_ERROR.getCode());
            }
            if (!CollectionUtils.isEmpty(generalRes.getData())) {
                SettlementGeneralDto settlementRechargeDto = generalRes.getData().get(NumberConstants.NUMBER_0);
                //如果订单是已支付状态
                if (null != settlementRechargeDto.getOrderStatus()
                        && NumberConstants.NUMBER_4 == settlementRechargeDto.getOrderStatus()) {
                    BizExceptionUtil.create(ResultErrorEnum.ROUTE_PAYED.getMsg(),
                            ResultErrorEnum.ROUTE_PAYED.getCode());
                }
                return generalRes.getData().get(NumberConstants.NUMBER_0).getSettleUuid();
            }
        }
        //构建计算单数据对象
        SettlementRechargeDto settlementRechargeDto = new SettlementRechargeDto();
        // 用户uuid
        settlementRechargeDto.setPassengerUuid(advanceRechargeDTO.getPassengerUuid());
        //预付款流水号
        settlementRechargeDto.setAdvanceSerial(advanceRechargeDTO.getPrePayNo());
        // 充值金额
        settlementRechargeDto.setRechargeAmount(advanceRechargeDTO.getRechargeAmount());
        // 来源
        settlementRechargeDto.setSourceType(advanceRechargeDTO.getSourceType());
        settlementRechargeDto.setRoutePlanUuid(advanceRechargeDTO.getRoutePlanUuid());
        // 生成充值结算单
        Response<SettlementRechargeDto> srResponse =
                settlementRechargeService.saveSettlementRecharge(settlementRechargeDto);
        if (!srResponse.isSuccess() || srResponse.getData() == null
                || StringUtils.isBlank(srResponse.getData().getUuid())) {
            BizExceptionUtil.create(ResultErrorEnum.SAVE_RECHARGE_SETTLEMENT_ERROR.getMsg(),
                    ResultErrorEnum.SAVE_RECHARGE_SETTLEMENT_ERROR.getCode());
        }
        return srResponse.getData().getUuid();
    }

}
