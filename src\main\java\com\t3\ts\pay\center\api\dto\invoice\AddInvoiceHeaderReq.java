package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Getter;
import lombok.Setter;

import java.io.*;

/**
 * 迁移 by ivy .2021/09/17 14:30
 *
 * @Author: qul
 * @Date: 2021/4/26 17:06
 * @Description: 添加常用抬头入参
 */
@Getter
@Setter
public class AddInvoiceHeaderReq implements Serializable {

    private static final long serialVersionUID = 3460677883584140660L;

    /**
     * 唯一标识
     */
    private String uuid;

    /**
     * 用户唯一标识
     */
    private String userUuid;

    /**
     * 抬头类型（1：企业单位2：个人/非企业单位）
     */
    private Integer headerType;

    /**
     * 发票抬头
     */
    private String header;

    /**
     * 税号
     */
    private String taxNum;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerTel;

    /**
     * 开户银行
     */
    private String openingBank;

    /**
     * 开户银行账号
     */
    private String bankAccount;

    /**
     * 是否默认抬头 0否 1是
     */
    private Integer defaultFlag = 0;

    /**
     * 邮箱
     */
    private String email;
}
