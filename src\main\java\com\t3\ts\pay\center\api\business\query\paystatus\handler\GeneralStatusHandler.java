package com.t3.ts.pay.center.api.business.query.paystatus.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.pay.center.api.business.query.paystatus.PayStatusDto;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.OrderStatus;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.utils.StringUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 通用结算单支付状态查询
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 14:54
 */
@Slf4j
@Component
public class GeneralStatusHandler implements PayStatusHandler {

    @Autowired
    private T3CacheFactory factory;

    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    @Override
    public String getType() {
        return "general";
    }

    /**
     * 处理程序
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    @Override
    public Response<JSONObject> handler(QueryStatusContext context) {
        if (StringUtils.isEmpty(context.getSettleId())) {
            throw new IllegalArgumentException("输入参数有误");
        }
        PayStatusDto result = new PayStatusDto();
        result.setSettleId(context.getSettleId());
        result.setPayStatus(checkStatus(context));
        return Response.createSuccess("", JSON.parseObject(JSON.toJSONString(result)));
    }

    /**
     * 支付状态，1：初始化，2：待（支付）结算，3：（支付）结算中，4：（支付）结算成功
     *
     * @param context 上下文
     * @return {@link Integer}
     */
    private Integer checkStatus(QueryStatusContext context) {
        boolean checkRedis = checkRedis(context.getSettleId(), factory);
        if (checkRedis) {
            return NumConstants.NUM_3;
        }
        SettlementGeneralDto generalDto = checkAndGetGeneralSettlement(context.getSettleId());
        if (generalDto.getOrderStatus() == OrderStatus.SUCCESS.getStatus()) {
            return NumConstants.NUM_4;
        }
        return NumConstants.NUM_2;
    }

    /**
     * 得到通用结算单
     *
     * @param settleId 解决id
     * @return {@link SettlementGeneralDto}
     */
    public SettlementGeneralDto checkAndGetGeneralSettlement(String settleId) {
        SettlementGeneralDto dto = new SettlementGeneralDto();
        dto.setSettleUuid(settleId);
        Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(dto);
        if (null == response || Boolean.FALSE.equals(response.isSuccess()) || null == response.getData()) {
            throw new IllegalArgumentException("结算单不存在");
        }
        return response.getData().get(0);
    }
}
