<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>t3-parent</artifactId>
        <groupId>com.t3.ts</groupId>
        <version>5.2.7.RELEASE</version>
        <relativePath/>
    </parent>

    <artifactId>pay-center-api</artifactId>
    <version>5.0.0-RELEASE</version>
    <description>支付中心api服务</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>false</skipTests>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>3.0.0</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>3.0.0</version>
            <!--移除swagger-models 1.5.20 依赖，存在Swagger2异常:Illegal DefaultValue null for parameter type integer问题-->
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.t3.middleware.route</groupId>
            <artifactId>route-consul-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.t3.middleware.route</groupId>
            <artifactId>route-dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
        </dependency>

        <dependency>
            <groupId>com.commons.utils</groupId>
            <artifactId>JoinApiTools</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 集成BU依赖 start -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>integrated-center-remote</artifactId>
            <version>0.0.14-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-dubbo-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-ribbon-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>interactive-default-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 集成BU依赖 end -->

        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.5.17</version>
        </dependency>

        <!-- 前台依赖 start -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>operation-center-remote</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>
        <!-- 前台依赖 end -->

        <!-- 三户BU依赖 start -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>passenger-remote</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>auth-center-remote</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <!-- 三户BU依赖 end -->

        <!-- 运管BU依赖 start-->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>travel-config-remote</artifactId>
            <version>0.0.11-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>travel-manager-remote</artifactId>
            <version>0.1.16-SNAPSHOT</version>
        </dependency>
        <!-- 运管BU依赖 end-->

        <!--        <dependency>-->
        <!--            <groupId>com.t3.ts</groupId>-->
        <!--            <artifactId>t3-api-core-config</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.t3.ts</groupId>-->
        <!--            <artifactId>t3-api-route</artifactId>-->
        <!--            <version>2.0.5-RELEASE</version>-->
        <!--        </dependency>-->
        <!--会员配置服务-->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>member-config-remote</artifactId>
            <version>2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-dubbo-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-ribbon-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--会员账户服务-->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>member-account-remote</artifactId>
            <version>0.0.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-dubbo-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>gray-ribbon-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>interactive-default-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>t3-cache-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>pay-center-remote</artifactId>
            <version>5.1.718-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>settlement-finance-remote</artifactId>
            <version>5.1.44-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>kz-data-center-fp-remote</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>finance-center-remote</artifactId>
            <version>5.1.515-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>pay-common</artifactId>
            <version>5.9.3-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>channelmgr-center-remote</artifactId>
            <version>5.0.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>account-center-remote</artifactId>
            <version>5.1.514-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>adapter-center-remote</artifactId>
            <version>5.4.18-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>settlement-center-remote</artifactId>
            <version>5.2.724-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>pay-data-remote</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>withdraw-center-remote</artifactId>
            <version>5.0.46-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>t3-dubbo-core-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <version>3.6.2</version>
        </dependency>

        <!-- GIS服务 -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>gis-position-remote</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>gis-poi-remote</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.t3.ts</groupId>
                    <artifactId>t3-sentinel-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 单元测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>compile</scope>
        </dependency>

        <!--乘客服务-->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>passenger-center-remote</artifactId>
            <version>0.2.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- +++++++++++++++++++++ 企业用车 +++++++++++++++++++  -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>org-manager-remote</artifactId>
            <version>2.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>scene-config-remote</artifactId>
            <version>1.3-SNAPSHOT</version>
        </dependency>
        <!-- api支付模块jar -->

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>route-plan-remote</artifactId>
            <version>0.1.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 发票接口迁移 -->
        <dependency>
            <groupId>com.t3.ts</groupId>
            <artifactId>invoice-center-remote</artifactId>
            <version>5.8.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>1.18</version>
        </dependency>

        <!-- end    -->

        <!--健康检查-->
        <dependency>
            <groupId>com.t3.spring.boot.extend</groupId>
            <artifactId>actuator-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--consul无损接入-->
        <dependency>
            <groupId>com.t3.spring.boot.extend</groupId>
            <artifactId>consul-extend-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <!--                <version>2.3</version>-->
                <configuration>
                    <includes>
                        <include>**/*Test*.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>prepare-unit-test-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-site</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>prepare-it-test-agent</id>
                        <configuration>
                            <propertyName>jacoco.agent.argLine</propertyName>
                            <append>true</append>
                        </configuration>
                        <goals>
                            <goal>prepare-agent-integration</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-site-it</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-integration</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.12.4</version>
                <configuration>
                    <argLine>-Xmx1024m -XX:MaxPermSize=256m</argLine>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src\main\java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src\main\resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.7.8</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
