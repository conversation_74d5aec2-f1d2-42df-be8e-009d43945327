package com.t3.ts.pay.center.api.dto.fare;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:32
 * @description: fareModelRuleDto的简化版
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FareModelRuleConcept implements Serializable {

    private BigDecimal memberDiscount;
    private String memberDiscountVersion;
}
