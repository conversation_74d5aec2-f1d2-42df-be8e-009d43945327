package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.util.PayAbTestUtil;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.AliPayDto;
import com.t3.ts.pay.remote.service.AliPayService;
import com.t3.ts.result.Response;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 支付宝支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("aliPayServiceImpl")
@Slf4j
public class AliPayServiceImpl implements PayChannelService {

    @DubboReference
    private AliPayService aliPayService;

    @Autowired
    private PayAbTestUtil payAbTestUtil;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.ALIPAY.getCode());

        AliPayDto dto = new AliPayDto();
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        dto.setOrderNo(paymentResp.getMsg());
        boolean payAndSignAbTest = payAbTestUtil.aliPayAndSignAbTest(paymentInfoBo.getPassengerUuid());
        log.info("payAndSignAbTest2:{}", payAndSignAbTest);
        if (payAndSignAbTest) {
            dto.setPayAndSign(paymentInfoBo.getPayAndSign());
        }
        dto.setUserId(paymentInfoBo.getPassengerUuid());
        dto.setIsElectricCar(paymentInfoBo.getIsElectricCar());
        dto.setRoutePlanUuid(paymentInfoBo.getRoutePlanUuid());
        dto.setSource(paymentInfoBo.getSource());
        dto.setPlateNum(paymentInfoBo.getPlateNum());
        dto.setOutIp(paymentInfoBo.getOutIp());
        //免密支付
        if (paymentInfoBo.isNoSecret()) {
            // 调用免密支付接口
            Response response = aliPayService.noPwdPay(dto);
            rechargePayVo.setSdk(response.getMsg());
            if (!response.isSuccess()) {
                rechargePayVo.setCode(NumConstants.STR_500);
                rechargePayVo.setSdk("支付宝免密支付失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        } else {
            // 支付宝一般支付
            Response aliPayInfo = aliPayService.normalPay(dto);
            rechargePayVo.setSdk(String.valueOf(aliPayInfo.getData()));
            if (!aliPayInfo.isSuccess() || Objects.isNull(aliPayInfo.getData())) {
                rechargePayVo.setCode(NumConstants.STR_500);
                rechargePayVo.setSdk("获取支付宝支付串失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        }
        return Response.createSuccess(rechargePayVo);
    }
}
