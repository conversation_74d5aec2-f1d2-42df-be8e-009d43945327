package com.t3.ts.pay.center.api.dto.route;

import com.t3.ts.pay.center.api.dto.fare.ModifyDetailBean;
import com.t3.ts.pay.center.api.dto.fare.PrePayDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/21 20:45
 * @description:
 */
@Getter
@Setter
public class FareDetailDto {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 行程ID
     */
    private String journeyId;
    /**
     * 改价详情
     */
    private ModifyDetailBean modifyDetail;
    /**
     * 实际支付费用 = 已支付费用 - 已退款费用
     */
    private String actFare;
    /**
     * 已支付费用
     */
    private String paidFare;
    /**
     * 已退款费用
     */
    private String refundFare;
    /**
     * 企业支付费用
     */
    private String companyPay;
    /**
     * 预付款详情
     */
    private PrePayDto prePayDetail;
    /**
     * 结算总费用
     */
    private String totalFare;
    /**
     * 待支付费用
     */
    private String toPayFare;
    /**
     * 支付信息
     */
    private List<PayChannelBean> detail;

    /**
     * 退款详情
     */
    private RefundDetailDto refundDetail;
}
