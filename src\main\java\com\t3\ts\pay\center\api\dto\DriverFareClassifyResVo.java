package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 司机费用大类ResVO
 *
 * <AUTHOR>
 * @date 2023/03/10
 */
@Data
public class DriverFareClassifyResVo {

    @ApiModelProperty(value = "斗金广告位开关")
    private String recommendPassengerFlag;

    @ApiModelProperty(value = "问题描述1")
    private String questionDesc1;
    @ApiModelProperty(value = "问题描述2")
    private String questionDesc2;
    @ApiModelProperty(value = "问题描述3")
    private String questionDesc3;
}
