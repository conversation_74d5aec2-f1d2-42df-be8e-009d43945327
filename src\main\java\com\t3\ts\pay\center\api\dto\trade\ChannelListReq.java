package com.t3.ts.pay.center.api.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/1 14:07
 * @description:
 */
@Data
@ApiModel
public class ChannelListReq {
    /**
     * 行程订单号
     */
    @ApiModelProperty(value = "行程订单号")
    private String orderUuid;

    // 行程id
    private String journeyId;

    // 用户id
    private String userId;

    // 城市code
    private String cityCode;

    // 使用渠道 0,1:  0标识通用券  1渠道券
    private String useChannel;

    private String terminal;
}
