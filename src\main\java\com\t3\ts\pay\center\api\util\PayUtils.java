package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSON;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.common.http.CityCode;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.dto.channelRouting.PayChannelRealConfigDto;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.*;

/**
 * pay 工具类
 *
 * <AUTHOR>
 * @date 2022-10-26 16:01:01
 */
public class PayUtils {

    /**
     * 获取城市code
     *
     * @param routeInfo 参数
     * @param payTradeReq  参数
     * @return 城市code
     */
    public static String getCityCode(RouteInfoDto routeInfo, PayTradeReq payTradeReq) {
        String orgCode = "";
        if (null != routeInfo) {
            orgCode = CityCode.convertCode(routeInfo.getAreaCode());
        } else {
            orgCode = CityCode.convertCode(payTradeReq.getAdCode());
        }
        return orgCode;
    }

    /**
     * 删除微信类型
     *
     * @param payTypeList 付款类型列表
     * @return {@link List <Integer>}
     */
    public static List<Integer> removeWechatType(List<Integer> payTypeList) {
        Iterator<Integer> iterator = payTypeList.iterator();
        while (iterator.hasNext()) {
            Integer next = iterator.next();
            if (Integer.valueOf(EnumPayOrderChannel.WEIXIN.getCode()).equals(next)) {
                iterator.remove();
            }
        }
        return payTypeList;
    }

    /**
     * 收银台错误信息转换
     *
     * @param response 错误信息
     * @return Response
     */
    public static Response convertErrorResponse(Response response) {
        if (null != response && StringUtils.isNotBlank(response.getMsg())) {
            ResultErrorEnum resultErrorEnum = ResultErrorEnum.getResultErrorEnum(response.getMsg());
            if (null != resultErrorEnum) {
                return Response.createError(resultErrorEnum);
            }
        }
        return Response.createError(ResultErrorEnum.PAY_DESK_INFO_ERROR);
    }

    /**
     * 获取较大的十进制值
     *
     * @param bigDecimal 大小数
     * @return {@link BigDecimal}
     */
    public static BigDecimal getBigDecimal(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 获取较大的十进制值
     *
     * @param d 大小数
     * @return {@link BigDecimal}
     */
    public static BigDecimal getBigDecimal(Double d) {
        if (Objects.isNull(d)) {
            return BigDecimal.ZERO;
        }
        BigDecimal decimal = new BigDecimal(d);
        return decimal.setScale(NumConstants.NUM_2, RoundingMode.HALF_UP);
    }

    public static BigDecimal getBigDecimal(String money) {
        if (Objects.isNull(money)) {
            return BigDecimal.ZERO;
        }
        BigDecimal decimal = new BigDecimal(money);
        return decimal.setScale(NumConstants.NUM_2, RoundingMode.HALF_UP);
    }

    /**
     * 是否大于0
     *
     * @param number 大小数
     * @return {@link BigDecimal}
     */
    public static Boolean isGreaterThanZero(Object number) {
        if (Objects.isNull(number)) {
            return false;
        }
        if (number instanceof BigDecimal) {
            BigDecimal bigDecimal = (BigDecimal) number;
            return bigDecimal.compareTo(BigDecimal.ZERO) > 0;
        }
        if (number instanceof Double) {
            Double aDouble = (Double) number;
            return aDouble > 0;
        }
        return false;
    }



    /**
     * 是否当前版本是否大于等于指定版本
     *
     * @param b1 待比对版本
     * @param b2 指定版本
     * @return boolean true:大于等于 false:小于
     */
    public static boolean compareGrayBuild(String b1, String b2) {
        Integer grayBuild = 0;
        if (StringUtils.isNotBlank(b1)) {
            grayBuild = Integer.parseInt(b1);
        }
        if (grayBuild >= Integer.parseInt(b2)) {
            return true;
        }
        return false;
    }

    /**
     * 生成拼车的提示文案
     *
     * @param routeInfoDto   routePlanDto
     * @return String 拼车提示文案
     */
    public static String parseCarPoolNotice(RouteInfoDto routeInfoDto) {
        StringBuilder sb = new StringBuilder("");
        if (null != routeInfoDto.getFareMethod()) {
            // 计费方式 1 平台计价  2 计价器计价  3 一口价计价
            if (NUM_1 == routeInfoDto.getFareMethod()) {
                sb.append("实时计价 ");
            } else if (NUM_2 == routeInfoDto.getFareMethod()) {
                sb.append("计价器计价 ");
            } else if (NUM_3 == routeInfoDto.getFareMethod()) {
                sb.append("一口价 ");
            }
        }
        if (!CollectionUtils.isEmpty(routeInfoDto.getLabels()) && routeInfoDto.getLabels().contains("817")) {
            sb.append("(停止拼车)");
        } else if (null != routeInfoDto.getCarPoolSuccess()) {
            if (routeInfoDto.getCarPoolSuccess()) {
                if (null != routeInfoDto.getSeatNum() && NUM_0 != routeInfoDto.getSeatNum()) {
                    sb.append(routeInfoDto.getSeatNum()).append("座");
                }
                sb.append("(已拼成)");
            } else {
                sb.append("(未拼成)");
            }
        } else {
            sb.append("(未拼成)");
        }
        return sb.toString();
    }

    /**
     * 获取 Double
     *
     * @param d d
     * @return Double
     */
    public static Double getDouble(Double d) {
        if (Objects.isNull(d)) {
            return Double.valueOf(NumConstants.STR_0);
        }
        return d;
    }


    public static boolean payRoutingSuccess(Response<String> paymentResponse) {
        //支付路由替换方法
        if(paymentResponse.getAttachment() == null){
            return false;
        }
        Map<String, String> attachment = paymentResponse.getAttachment();
        PaymentDto paymentDto = JSON.parseObject(attachment.get("paymentDto"),PaymentDto.class);
        if(paymentDto == null || paymentDto.getExtendParams() == null){
            return false;
        }
        PayChannelRealConfigDto newRealChannelDto = JSON.parseObject(paymentDto.getExtendParams().get("readChannelDto")+""
                ,PayChannelRealConfigDto.class);
        if(newRealChannelDto != null && StringUtils.isNotBlank(newRealChannelDto.getRealChannelCode())){
            return true;
        }
        return false;
    }
    /**
     * 获取真实支付渠道
     * @param paymentResponse unifiedPaymentFacade.pay(paymentDto) 的返回值
     * @param payTypeList
     * @return
     */
    public static Integer getRealChannelRouting(Response<String> paymentResponse,List<Integer> payTypeList) {

        //支付方式
        int payType = getThirdPayChannel(payTypeList);

        //支付路由替换方法
        if(paymentResponse.getAttachment() == null){
            return payType;
        }
        Map<String, String> attachment = paymentResponse.getAttachment();
        PaymentDto paymentDto = JSON.parseObject(attachment.get("paymentDto"),PaymentDto.class);
        if(paymentDto == null || paymentDto.getExtendParams() == null){
            return payType;
        }
        PayChannelRealConfigDto readChannelDto = JSON.parseObject(paymentDto.getExtendParams().get("readChannelDto")+""
                ,PayChannelRealConfigDto.class);
        if(readChannelDto == null || StringUtils.isBlank(readChannelDto.getRealChannelCode())){
            return payType;
        }
        return  Integer.valueOf(readChannelDto.getRealChannelCode());
    }

    /**
     * 获取三方支付类型
     * 优惠券和预付款应该是直接自动支付了
     * @param payChannelList 支付列表
     * @return 出参
     */
    public static Integer getThirdPayChannel(List<Integer> payChannelList) {
        if (CollectionUtils.isEmpty(payChannelList)) {
            return null;
        }
        return payChannelList.stream()
                .filter(payType -> EnumPayOrderChannel.INTEGRAL.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.BALANCE.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.PAY_ACCOUNT_SALARY.getCode() != payType)
                .findFirst().orElse( null);
    }



    /**
     * 获取终端 1 APP移动端、2 司机APP移动端 、8微信小程序、14：支付宝小程序、19：H5、0 全选
     * 这个背景有点深远：
     * 作用：查营销最优券的入参
     * 来源：
     * terminal 分为两个部分
     * 1、原始自定义：使用终端 1 APP移动端、2 司机APP移动端 、8微信小程序、14：支付宝小程序、19：H5、0 全选
     * 2、后续按照和出行保持一致：t3_trip.t_source 表里的
     *
     * @param grayVersion grayVersion
     * @return terminal
     */
    public static String getTerminal(String grayVersion, Integer userChannel) {
        //透传微信九宫格的终端201
        if(userChannel != null && userChannel == NumConstants.NUM_201){
            return userChannel + "";
        }
        if (grayVersion == null) {
            return "0";
        }
        if (grayVersion.startsWith(CommonNumConst.WX)) {
            return NumConstants.STR_8;
        } else if (grayVersion.startsWith(CommonNumConst.AX)) {
            return NumConstants.STR_14;
        } else if (grayVersion.startsWith(CommonNumConst.H5)) {
            return NumConstants.STR_19;
        } else if (grayVersion.startsWith(CommonNumConst.DOUYIN)) {
            return NumConstants.STR_89;
        } else if (grayVersion.startsWith(CommonNumConst.Driver_APP_Android)
                    || grayVersion.startsWith(CommonNumConst.Driver_APP_IOS)) {
            return NumConstants.STR_2;
        } else {
            return NumConstants.STR_1;
        }
    }
    public static String getTerminal(String grayVersion) {
        return PayUtils.getTerminal(grayVersion, null);
    }

    /**
     * 获取端 1.app 8 小程序 0 通用
     *
     * @param userChannel userChannel
     * @return userChannel
     */
    public static String changeForCoupon(Integer userChannel) {
        if (userChannel.equals(NumConstants.NUM_2)) {
            return Integer.toString(NumConstants.NUM_8);
        }
        return Integer.toString(userChannel);
    }

}
