package com.t3.ts.pay.center.api.business.common;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.pay.center.api.bo.CashierDeskBaseFareBo;
import com.t3.ts.pay.center.api.bo.DriverInfoBo;
import com.t3.ts.pay.center.api.cache.RoutePlanKey;
import com.t3.ts.pay.center.api.constants.CharteredAppConstants;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.chartered.CashierDeskProVo;
import com.t3.ts.pay.center.api.dto.chartered.PayDeskInfoVo;
import com.t3.ts.pay.center.api.dto.vo.IntegralMsgVO;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.dto.IntegralConfigDto;
import com.t3.ts.pay.remote.dto.SettlementInfo;
import com.t3.ts.pay.remote.dto.marketing.IntegralDeductDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.result.Response;
import com.t3.ts.travel.manager.resource.dto.res.CarDto;
import com.t3.ts.travel.manager.resource.dto.res.DriverResDto;
import com.t3.ts.travel.manager.resource.service.CarService;
import com.t3.ts.travel.manager.resource.service.DriverService;
import com.t3.ts.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 包车/企业用车抽取的公共方法
 *
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: CommonPaymentBusiness
 * @Package com.t3.ts.pay.center.api.business.common
 * @date 2021/9/15 16:59
 */
@Slf4j
@Component
public class CommonPaymentBusiness {
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private DriverService driverService;
    @DubboReference
    private CarService carService;
    @Resource
    private T3CacheFactory cacheFactory;
    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQueryFacade;

    /**
     * 获取司机信息
     *
     * @param routePlanUuid routePlanUuid
     * @param stopWatch     stopWatch
     * @return DriverInfoBo
     */
    public DriverInfoBo getDriverInfo(String routePlanUuid, StopWatch stopWatch) {
        DriverInfoBo driverInfoBo = new DriverInfoBo();

        stopWatch.start("/api/passenger/read/v1/getRouteDetail");
        log.info("cashier or enterprise getDriverInfo queryRouteInfo: {}", routePlanUuid);
        RouteInfoDto routeInfo = routeDetailClient.getRouteInfoWeak(routePlanUuid);
        stopWatch.stop();
        log.info("traceId {} /api/passenger/read/v1/getRouteDetail,Request={} , Resposne={}",
                MDC.get(CharteredAppConstants.LOG_TRACE_ID), routePlanUuid,
                routeInfo);

        driverInfoBo.setRouteStatus(routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString());

        stopWatch.start("com.t3.ts.travel.manager.resource.service.DriverService.findDriverByUuid");

        if (routeInfo == null) {
            return driverInfoBo;
        }

        // 获取司机信息
        Response<DriverResDto> driver = driverService.findDriverByUuid(routeInfo.getDriverUuid());
        stopWatch.stop();
        if (driver.getData().getName() == null) {
            driverInfoBo.setDriverName("司机师傅");
        } else {
            driverInfoBo.setDriverName(driver.getData().getName().substring(0, 1) + "师傅");
        }
        stopWatch.start("com.t3.ts.travel.manager.resource.service.CarService.findByUuid");
        // 车牌
        Response<CarDto> carDtoResponse = carService.findByUuid(routeInfo.getCarUuid());
        stopWatch.stop();
        if (Boolean.TRUE.equals(carDtoResponse.isSuccess()) && !Objects.isNull(carDtoResponse.getData())) {
            driverInfoBo.setPlateNum(carDtoResponse.getData().getPlateNum());
        }
        return driverInfoBo;
    }

    /**
     * 获取支付方式
     *
     * @param payTypeList 车费支付方式
     * @return List<String>
     */
    public List<String> getPayWay(List<Integer> payTypeList) {
        PaywayEnum[] paywayEnum = PayWayConvert.getPayWayEnum(payTypeList);
        List<String> payWay = Lists.newArrayList();
        // 支付方式
        if (null != paywayEnum) {
            for (PaywayEnum anEnum : paywayEnum) {
                if (Objects.nonNull(anEnum)) {
                    payWay.add(anEnum.getCode());
                }
            }
        }
        // 这里追加上余额方式  上送支付查可支付金额需要
        if (!payWay.contains(PaywayEnum.BALANCE.getCode())) {
            payWay.add(PaywayEnum.BALANCE.getCode());
        }
        return payWay;
    }

    /**
     * 获取三方支付渠道
     * minusOne 没有支付方式
     *
     * @param payTypeList 车费支付方式
     * @return payChannel
     */
    public Integer getPayChannel(List<Integer> payTypeList) {
        List<Integer> payChannelList = Lists.newArrayList();
        payChannelList.addAll(payTypeList);
        final int minusOne = -1;
        payChannelList.removeIf(next -> EnumPayOrderChannel.BALANCE.getCode() == next
                || EnumPayOrderChannel.INTEGRAL.getCode() == next || minusOne == next);
        Integer payChannel = null;
        if (!CollectionUtils.isEmpty(payChannelList)) {
            payChannel = payChannelList.get(0);
        }
        return payChannel;
    }

    /**
     * 是否预付支付尾款 1尾款 2普通
     *
     * @param prPayed 预付款已支付
     * @return Integer
     */
    public Integer getPayDeskType(BigDecimal prPayed) {
        if (prPayed.compareTo(BigDecimal.ZERO) > 0) {
            return 1;
        } else {
            return NumConstants.NUM_2;
        }
    }

    /**
     * 初始化积分抵扣限制提示
     *
     * @param msg  提示文案
     * @param key1 文案中数字1
     * @param key2 文案中数字2
     * @return 提示文案
     */
    public IntegralMsgVO initIntegralMsg(String msg, String key1, String key2) {
        IntegralMsgVO integralMsgVO = new IntegralMsgVO();
        integralMsgVO.setMsg(msg);
        integralMsgVO.setKey1(key1 + "");
        String fullMsg = "";
        if (Objects.nonNull(key2)) {
            integralMsgVO.setKey2(key2 + "");
            fullMsg = String.format(msg, key1, key2);
        } else {
            fullMsg = String.format(msg, key1);
        }
        integralMsgVO.setFullMsg(fullMsg);
        return integralMsgVO;
    }

    /**
     * 余额信息
     *
     * @param baseFareBo     基础金额
     * @param balancePayable 余额可支付金额
     * @param payTypeList    支付方式列表
     * @param deductCost     积分抵扣
     * @return CashierDeskProVo
     */
    public CashierDeskProVo getBalanceInfo(CashierDeskBaseFareBo baseFareBo, BigDecimal balancePayable,
                                           List<Integer> payTypeList, BigDecimal deductCost) {
        // 用户钱包余额（赠币+礼品卡）
        BigDecimal balanceTotal = baseFareBo.getBalanceTotal();
        // 附加费
        BigDecimal additionalFee = baseFareBo.getAdditionalFee();
        CashierDeskProVo cashierDeskProVo = new CashierDeskProVo();
        if (additionalFee.compareTo(BigDecimal.ZERO) > 0) {
            if (balanceTotal.compareTo(BigDecimal.ZERO) > 0) {
                if (balancePayable.compareTo(BigDecimal.ZERO) > 0) {
                    // 余额可以支付的应付金额
                    BigDecimal diff = baseFareBo.getRemainPay().subtract(baseFareBo.getAdditionalFee());
                    if (payTypeList.contains(EnumPayOrderChannel.INTEGRAL.getCode())) {
                        diff = diff.subtract(deductCost);
                    }
                    if (diff.compareTo(balancePayable) > 0 && balancePayable.compareTo(balanceTotal) < 0) {
                        cashierDeskProVo.setBalanceMsg(
                                "当前账号余额中包含赠送币" + (baseFareBo.getGiftCardCanPayService() ? "/礼品卡" : "")
                                        + "，余额支付" + balancePayable + "元，剩余费用需要通过第三方支付");
                        cashierDeskProVo.setBalanceFlag(Boolean.TRUE);
                        cashierDeskProVo.setAvailableBalance(balancePayable);
                    } else if (diff.compareTo(balancePayable) <= 0) {
                        cashierDeskProVo.setBalanceMsg(
                                "当前账号余额中包含赠送币" + (baseFareBo.getGiftCardCanPayService() ? "/礼品卡" : "")
                                        + "，余额支付" + balancePayable + "元");
                        cashierDeskProVo.setBalanceFlag(Boolean.TRUE);
                        cashierDeskProVo.setAvailableBalance(balancePayable);
                    }
                } else {
                    cashierDeskProVo.setBalanceMsg("当前账户余额中包含赠送币" + (
                            baseFareBo.getGiftCardCanPayService() ? "/礼品卡" : "") + "，余额不可用于支付，需通过第三方支付");
                }
            } else {
                cashierDeskProVo.setBalanceMsg("余额不足");
            }
        } else if (balanceTotal.compareTo(BigDecimal.ZERO) > 0) {
            cashierDeskProVo.setBalanceFlag(Boolean.TRUE);
        } else if (balanceTotal.compareTo(BigDecimal.ZERO) <= 0) {
            cashierDeskProVo.setBalanceMsg("余额不足");
        }
        log.info("计算余额信息结果是 {}", JSON.toJSONString(cashierDeskProVo));
        return cashierDeskProVo;
    }

    /**
     * 最终支付费用
     *
     * @param baseFareBo     基础费用
     * @param payTypeList    支付方式
     * @param balancePayable 余额可支付金额
     * @param deductCost     积分可抵扣费用
     * @return finalFare
     */
    public BigDecimal getFinalFare(CashierDeskBaseFareBo baseFareBo,
                                   List<Integer> payTypeList,
                                   BigDecimal balancePayable,
                                   BigDecimal deductCost) {
        BigDecimal finalFare = baseFareBo.getRemainPay();
        if (payTypeList.contains(EnumPayOrderChannel.BALANCE.getCode())) {
            finalFare = baseFareBo.getRemainPay().subtract(balancePayable);
        }
        if (payTypeList.contains(EnumPayOrderChannel.INTEGRAL.getCode())) {
            finalFare = finalFare.subtract(deductCost);
        }
        log.info("最终支付费用计算为 {}, 支付方式为 {}, 余额可支付 {}, 积分可抵扣 {}", finalFare,
                JSON.toJSONString(payTypeList), balancePayable, deductCost);
        if (finalFare.compareTo(BigDecimal.ZERO) < 0) {
            finalFare = BigDecimal.ZERO;
        }
        return finalFare;
    }

    /**
     * 获取支付时可抵扣积分
     *
     * @param passengerUuid 乘客uuid
     * @return 可抵扣积分
     */
    public String deductPoints4Pay(String passengerUuid) {
        String availableIntegral = "";
        List<PayDeskInfoVo> payDeskInfoVos = null;
        try {
            payDeskInfoVos = cacheFactory.ExpireHash()
                    .getHash(RoutePlanKey.DEDUCT_POINTS_KEY, PayDeskInfoVo.class, passengerUuid);
        } catch (Exception e) {
            log.error("ExpireHashService.getHash key: {}, field: {}，Error: {}", RoutePlanKey.DEDUCT_POINTS_KEY,
                    passengerUuid, e);
        }
        if (!CollectionUtils.isEmpty(payDeskInfoVos)) {
            availableIntegral = payDeskInfoVos.get(0).getAvailableIntegral();
        }
        log.info("hash get key: {}，field: {}， value: {}", RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid,
                JSON.toJSONString(payDeskInfoVos));
        return availableIntegral;
    }

    /**
     * 根据产品线和业务线查询T币抵扣配置(仅查配置)
     *
     * @param bizType 产品线
     * @param bizLine 业务线
     * @return Response
     */
    public Response<IntegralDeductDto> getIntegralDeductInfo(Integer bizType, Integer bizLine) {
        try {
            if (null != bizType && null != bizLine) {
                Response<IntegralDeductDto> response =
                        unifiedPaymentQueryFacade.getIntegralDeductInfo(bizLine, bizType);
                if (null != response && response.getSuccess() && null != response.getData()) {
                    IntegralDeductDto dto = response.getData();
                    if (null == dto.getEffectTime()) {
                        return Response.createSuccess(dto);
                    } else if (dto.getEffectTime() <= DateUtils.getSystemTime().getTime()) {
                        return Response.createSuccess(dto);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("【告警信息】：{}", "Tbi抵扣规则查询接口失败");
        }
        log.warn("配置超过有效期或查无配置或传参问题");
        return Response.createError();
    }

    /**
     * 根据产品线和业务线查询T币抵扣配置(待金额计算)
     *
     * @param bizType 产品线
     * @param bizLine 业务线
     * @param userId  用户userId
     * @param payAmt  待支付金额
     * @return Response<IntegralDeductDto>
     */
    public Response<IntegralDeductDto> getIntegralDeductInfo(Integer bizType, Integer bizLine,
                                                             String userId, BigDecimal payAmt) {
        IntegralConfigDto integralConfigDto = new IntegralConfigDto();
        List<SettlementInfo> list = new ArrayList<>();
        SettlementInfo info = new SettlementInfo();
        info.setTypeModule(bizType);
        info.setExpandBizLine(bizLine);
        info.setOrderFare(payAmt);
        list.add(info);
        integralConfigDto.setCond(list);
        integralConfigDto.setUserId(userId);
        //不需要查用户是否开了T币免密
        integralConfigDto.setSignSwitch(false);
        Response<List<IntegralDeductDto>> response =
                unifiedPaymentQueryFacade.getIntegralDeductInfos(integralConfigDto);
        if (null != response && response.getSuccess() && !CollectionUtils.isEmpty(response.getData())) {
            List<IntegralDeductDto> confList = response.getData();
            return Response.createSuccess(confList.get(0));
        }
        return Response.createError();
    }

}
