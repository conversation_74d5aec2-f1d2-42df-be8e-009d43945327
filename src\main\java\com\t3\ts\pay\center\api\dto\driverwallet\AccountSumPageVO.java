package com.t3.ts.pay.center.api.dto.driverwallet;

import com.t3.ts.finance.center.dto.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class AccountSumPageVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("uuid")
    private String uuid;
    @ApiModelProperty("1收支维度 2费用项")
    private Integer bizType;
    @ApiModelProperty("用户id")
    private String userId;
    @ApiModelProperty("账户类型,3:t3司机,4:出租车司机")
    private Integer accountType;
    @ApiModelProperty("1日2周3月")
    private Integer dateType;
    @ApiModelProperty("日期2022-08-01")
    private Date startDate;
    @ApiModelProperty("日期2022-08-07")
    private Date endDate;
    @ApiModelProperty("汇总金额-分")
    private String sumAmount;
    @ApiModelProperty("费用大类,10000-订单收入、20000-任务奖励 、、20020-租金扣款 等")
    private String fareClassify;
    @ApiModelProperty("费用大类描述,10000-订单收入、20000-任务奖励 、、20020-租金扣款 等")
    private String fareClassifyDesc;
    @ApiModelProperty("收支类型,1收入,2支出,3收支")
    private Integer changedType;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("日期")
    private String dateStr;


}
