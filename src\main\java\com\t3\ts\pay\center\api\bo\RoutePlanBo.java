package com.t3.ts.pay.center.api.bo;

import com.t3.ts.route.plan.dto.RoutePlanReqDto;

import java.math.BigDecimal;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * @Author: YJ
 * @Date: 2020/4/10 23:11
 * @Description:  1.0
 */
public class RoutePlanBo extends RoutePlanReqDto {

    private static final long serialVersionUID = -3437589366732532060L;

    /**
     * 可开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 发票主体代码
     */
    private Integer invoiceSubjectCode;
    /**
     * 发票来源（0 平台开票）
     */
    private Integer invoiceSource;
    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    private Integer transportType;

    /**
     * 打车类型（1：出租车，2：专车，3：拼车，4：快车，5：货的，6：骑手，7：搬家）
     */
    private Integer	typeModule;

    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    private Integer	typeEnt;
    /**
     * 订单行程类型(1用车;2日租;3半日租;4接机;5送机;6接站;7送站;8包车)
     */
    private Integer	typeTrip;

    /**
     * 开票主体
     */
    private String invoiceSubjectName;

    /**
     * 开票内容
     */
    private String invoiceContent;

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Integer getInvoiceSubjectCode() {
        return invoiceSubjectCode;
    }

    public void setInvoiceSubjectCode(Integer invoiceSubjectCode) {
        this.invoiceSubjectCode = invoiceSubjectCode;
    }

    public Integer getInvoiceSource() {
        return invoiceSource;
    }

    public void setInvoiceSource(Integer invoiceSource) {
        this.invoiceSource = invoiceSource;
    }

    @Override
    public Integer getTransportType() {
        return transportType;
    }

    @Override
    public void setTransportType(Integer transportType) {
        this.transportType = transportType;
    }

    public Integer getTypeModule() {
        return typeModule;
    }

    public void setTypeModule(Integer typeModule) {
        this.typeModule = typeModule;
    }

    public Integer getTypeEnt() {
        return typeEnt;
    }

    public void setTypeEnt(Integer typeEnt) {
        this.typeEnt = typeEnt;
    }

    public Integer getTypeTrip() {
        return typeTrip;
    }

    public void setTypeTrip(Integer typeTrip) {
        this.typeTrip = typeTrip;
    }

    public String getInvoiceSubjectName() {
        return invoiceSubjectName;
    }

    public void setInvoiceSubjectName(String invoiceSubjectName) {
        this.invoiceSubjectName = invoiceSubjectName;
    }

    public String getInvoiceContent() {
        return invoiceContent;
    }

    public void setInvoiceContent(String invoiceContent) {
        this.invoiceContent = invoiceContent;
    }
}
