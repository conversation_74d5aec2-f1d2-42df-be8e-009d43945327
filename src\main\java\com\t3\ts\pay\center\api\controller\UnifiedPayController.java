package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.service.impl.RouteService;
import com.t3.ts.pay.center.api.util.ResponseUtil;
import com.t3.ts.pay.remote.exception.PayException;
import com.t3.ts.pay.remote.exception.PayExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/pay/api/v1/unified")
public class UnifiedPayController {

    /**
     * The Route service.
     */
    @Autowired
    private RouteService routeService;

    /**
     * Unified.
     *
     * @param request  the request
     * @param response the response
     */
    @PostMapping(value = {"/aggregate/**", "/pay/**", "/settlement/**", "/account/**"})
    public void unified(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("UnifiedPayController.unified接收到请求");
            routeService.route(request, response);
        } catch (PayException e) {
            log.warn("UnifiedPayController请求错误，错误信息：", e);
            ResponseUtil.writeFailResponse(response, e.getCode(), e.getErrorMessage());
        } catch (Exception e) {
            log.error("UnifiedPayController请求错误，错误信息：", e);
            ResponseUtil.writeFailResponse(response, PayExceptionCode.SYSTEM_ERROR);
        }
    }
}
