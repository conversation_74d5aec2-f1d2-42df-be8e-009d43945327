package com.t3.ts.pay.center.api.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 迁移 by ivy .2021/09/17 14:20
 *
 * @Author: hupo
 * @Date: 2019.11.1
 * @Description: 发票金额
 */
@Data
public class InvoiceFareBo {
    /**
     * 附加费用
     */
    private BigDecimal additionalMoney;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 开票金额
     */
    private BigDecimal billMoney;

    /**
     * @return {@link InvoiceFareBo}
     */
    public static InvoiceFareBo newInstance() {
        return new InvoiceFareBo();
    }

}
