package com.t3.ts.pay.center.api.business.query.paystatus.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.pay.center.api.business.query.paystatus.PayStatusDto;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.service.SrOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 行程支付状态查询
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 14:09
 */
@Slf4j
@Component
public class RoutePayStatusHandler implements PayStatusHandler {

    @Autowired
    private T3CacheFactory factory;
    @DubboReference
    private SrOrderService srOrderService;

    @Override
    public String getType() {
        return "route";
    }

    @Override
    public Response<JSONObject> handler(QueryStatusContext context) {
        log.info("RoutePayStatusHandler.handler : {}", JSON.toJSONString(context));
        PayStatusDto payStatusDto = new PayStatusDto();
        if (StringUtils.isBlank(context.getOrderUuid())) {
            return Response.createSuccess(payStatusDto);
        }
        payStatusDto.setRoutePlanUuid(context.getOrderUuid());
        try {
            Response<SrOrderDto> orderDtoResponse = srOrderService.querySrOrderByRoutePlanId(context.getOrderUuid());
            if (null != orderDtoResponse && orderDtoResponse.isSuccess() && orderDtoResponse.getData() != null) {
                SrOrderDto data = orderDtoResponse.getData();
                // 结算单状态
                Integer orderStatus = data.getOrderStatus();

                if (checkRedis(context.getSettleId(), factory)) {
                    log.info("RoutePayStatusHandler.handler redis exist");
                    // 锁存在，说明乘客有正在支付中的动作
                    payStatusDto.setPayStatus(NumConstants.NUM_3_INT);
                } else {
                    // 锁不存在，说明乘客目前没有在支付，根据结算单状态返回该笔行程的支付状态
                    if (orderStatus != null && NumConstants.NUM_3_INT == orderStatus) {
                        orderStatus = NumConstants.NUM_2_INT;
                    }
                    payStatusDto.setPayStatus(orderStatus);
                }
            } else {
                return Response.createError("行程[" + context.getOrderUuid() + "]结算单不存在");
            }
        } catch (Exception e) {
            log.error("payStatusQuery error", e);
        }
        return Response.createSuccess("", JSONObject.parse(JSON.toJSONString(payStatusDto)));
    }
}
