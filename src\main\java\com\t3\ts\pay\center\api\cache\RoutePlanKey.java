package com.t3.ts.pay.center.api.cache;

import java.io.Serializable;

/**
 * 创建行程redis key
 *
 * <AUTHOR>
 * @date 2019-04-24
 */
public class RoutePlanKey implements Serializable {

    /**
     * 重新叫车、行程预付款关系、预付款倒计时 = 15 分钟
     */
    public static final int CACHE_COUNTDOWN_MINUTE = 15;

    /**
     * v2版本重新叫车缓存时间 = 30 分钟
     */
    public static final int CACHE_THIRTY_MINUTE = 30;

    /**
     * 重新叫车 存入参
     */
    public static final String CALL_AGAIN = "API:PASSENGER:CALLAGAIN:";

    /**
     * 预付款金额
     */
    public static final String ROUTE_SERIAL = "API:PASSENGER:SERIAL:";
    /**
     * 确认用车创建申请单缓存
     */
    public static final String REQUISITION_UUID = "API:ENTERPRISE:REQUISITIONUUID:";
    /**
     * 企业更改行程缓存
     */
    public static final String ROUTE_PLAN_UUID = "API:ENTERPRISE:ROUTEPLANUUID:";
    /**
     * 预付款流水号（key：行程单号,value：支付流水号）
     */
    public static final String ROUTE_ADVANCESERIAL = "API:PASSENGER:ADVANCESERIAL:";

    public static final String ROUTE_REMAIN = "API:PASSENGER:REMAIN:";

    /**
     * 倒计时使用参数
     */
    public static final String COUNTDOWN_TIME = "API:PASSENGER:COUNTDOWN:";

    /**
     * 正计时使用参数
     */
    public static final String POSITIVE_TIME = "API:PASSENGER:POSITIVE:";

    /**
     * 抵扣积分
     */
    public static final String DEDUCT_INTEGRAL = "API:PASSENGER:DEDUCTINTEGRAL:";

    /**
     * 正计时使用参数
     */
    public static final String CC_CALL_ESTIMATE_REQ = "API:PASSENGER:ESTIMATEREQ:";

    /**
     * 预估车型列表(设备号)
     */
    public static final String VALUATION_MODELS_DEVICE = "API:PASSENGER:VALUATIONMODELSDEVICE:";
    /**
     * 预估车型列表(订单号)
     */
    public static final String VALUATION_MODELS_ORDER = "API:PASSENGER:VALUATIONMODELSORDER:";
    /**
     * 重新预估参数(设备号)
     */
    public static final String VALUATION_AGAIN_DEVICE = "API:PASSENGER:VALUATIONAGAINDEVICE:";
    /**
     * 重新预估参数(订单号)
     */
    public static final String VALUATION_AGAIN_ORDER = "API:PASSENGER:VALUATIONAGAINORDER:";
    /**
     * 预付款追加车型
     */
    public static final String ADVANCE_CHARGE_MODELS = "API:PASSENGER:ADVANCEMODELS:";
    /**
     * 收银台积分抵扣明细缓存(包车)
     */
    public static final String CHARTERED_DEDUCT_POINTS_KEY = "API:CHARTERED:INTEGRAL";


    /**
     * 收银台积分抵扣明细缓存(企业用车)
     */
    public static final String DEDUCT_POINTS_KEY = "API:ENTERPRISE:INTEGRAL";
}
