package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/27
 */
@Getter
@Setter
@ApiModel("绑定卡信息VO")
public class BindCardInfoVo {

    private String accountId;
    private String accountType;
    private String bankLocation;
    @ApiModelProperty(value = "银行名称")
    private String bankName;
    private Integer bindStatus;
    private Date bindTime;
    @ApiModelProperty(value = "银行卡号")
    private String cardNo;
    @ApiModelProperty(value = "银行卡类型，1储蓄卡，2信用卡")
    private Integer cardType;
    private String createTime;
    @ApiModelProperty(value = "姓名")
    private String name;
}
