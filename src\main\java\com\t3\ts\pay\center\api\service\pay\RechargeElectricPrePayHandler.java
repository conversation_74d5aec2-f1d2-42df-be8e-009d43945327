package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.dto.book.AccountBookFreezeDto;
import com.t3.ts.account.center.service.AccountBookService;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.account.manager.dto.req.FreezeBalanceReqDto;
import com.t3.ts.account.manager.service.freeze.BalanceFreezeService;
import com.t3.ts.pay.center.api.dto.ChargeOrderDto;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.common.constant.settle.SettleStatusEnum;
import com.t3.ts.pay.common.exception.BizExceptionEnum;
import com.t3.ts.pay.common.exception.BizExceptionUtil;
import com.t3.ts.pay.common.util.MoneyUtils;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.SettleType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 充电预付款支付
 * <AUTHOR>
 */
@Slf4j
@Component
public class RechargeElectricPrePayHandler extends CommonPayHandler {
    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @DubboReference
    private AccountBookService accountBookService;
    @DubboReference
    private AccountService accountService;
    @Autowired
    private MarketingRest marketingRest;
    @DubboReference
    private BalanceFreezeService balanceFreezeService;

    @Override
    public String getType() {
        return "rechargeElectricPrePay";
    }


    @Override
    public void checkSettlement(PayContext context) {
        SettlementGeneralDto dto = new SettlementGeneralDto();
        dto.setBizTypes(super.getBizTypeList(context));
        dto.setOrderUuid(context.getOrderId());
        SettlementGeneralDto settlementGeneralDto = getSettlement(dto);
        if(settlementGeneralDto == null){
            this.addSettlement(context.getUserId(), context.getPayChannelList(), context.getOrderId());
            settlementGeneralDto = getSettlement(dto);
        }
        if (settlementGeneralDto == null) {
            BizExceptionUtil.create("创建预付款结算单失败");
        }
        context.getExtendParam().put("inner_bizId", settlementGeneralDto.getOrderUuid());
        context.getExtendParam().put("inner_settleId", settlementGeneralDto.getSettleUuid());
        context.getExtendParam().put("bizType", settlementGeneralDto.getBizType());
        context.setSettleId(settlementGeneralDto.getSettleUuid());
        log.debug("RechargeElectricPrePayHandler checkSettlement context={}", JSON.toJSONString(context));
    }

    private SettlementGeneralDto getSettlement(SettlementGeneralDto dto) {
        SettlementGeneralDto settlementGeneralDto = null;
        Response<List<SettlementGeneralDto>> listResponse = settlementGeneralService.selectSettlement(dto);
        if (listResponse.isSuccess() && CollectionUtil.isNotEmpty(listResponse.getData())) {
            settlementGeneralDto = listResponse.getData().stream()
                    .filter(dto1 -> dto1.getOrderStatus() < SettleStatusEnum.SETTLED.getCode())
                    .findFirst().orElse(null);
            if (settlementGeneralDto == null) {
                BizExceptionUtil.create("结算单已结算");
            }
        }
        return settlementGeneralDto;
    }

    private void addSettlement(String userId, List<Integer> payTypeList, String orderId) {
        //判断乘客是否选择了充电卡
        //查充电业务单
        ChargeOrderDto chargeOrderDto = marketingRest.queryChargingElectric(orderId);
        long insureFare = 0L;
        if(chargeOrderDto.getInsureFare() != null){
            insureFare = (long)MoneyUtils.yuanToFen(chargeOrderDto.getInsureFare());
        }
        //订单金额
        BigDecimal totalFare = chargeOrderDto.getAdvanceMoney();
        //待支付金额
        long remainFare = (long) MoneyUtils.yuanToFen(totalFare);
        // 查礼品卡
        Response<AccountDto> acctResp = accountService.simpleGetAccount(userId);
        if (acctResp == null || !acctResp.isSuccess() || acctResp.getData() == null) {
            BizExceptionUtil.create(BizExceptionEnum.PARAM_ERROR.getCode(), "账户账本不存在");
        }
        AccountDto accountDto = acctResp.getData();
        if (payTypeList.contains(EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode())) {
            // 查冻结记录
            AccountBookFreezeDto accountBookFreezeDto = new AccountBookFreezeDto();
            accountBookFreezeDto.setUserId(userId);
            accountBookFreezeDto.setSceneUuid(orderId);
            Response<PageResult<AccountBookFreezeDto>> accountBookFreeze = accountBookService.getAccountBookFreeze(accountBookFreezeDto);
            long totalFreezeFare=accountBookFreeze.getData().getList().stream().mapToLong(AccountBookFreezeDto::getFreezeAmount).sum();
            remainFare = remainFare - totalFreezeFare;
            long chargingElectricityCardFreeze = remainFare - insureFare;
            if (chargingElectricityCardFreeze > 0 && accountDto.getChargingElectricityCard() > 0) {
                //账户余额可用性检查：查询礼品卡是否可用，默认不可用
                boolean chargingElectricityCardCanUse = marketingRest.querySupportCard(orderId);
                if(chargingElectricityCardCanUse){
                    //充电卡冻结金额
                    chargingElectricityCardFreeze = Math.min(accountDto.getChargingElectricityCard(), chargingElectricityCardFreeze);
                    FreezeBalanceReqDto freezeBalanceReqDto = new FreezeBalanceReqDto();
                    //冻结单号
                    freezeBalanceReqDto.setOrderUuid(orderId);
                    freezeBalanceReqDto.setFreezeAmount(chargingElectricityCardFreeze);
                    freezeBalanceReqDto.setUserId(userId);
                    freezeBalanceReqDto.setAccountType(accountDto.getAccountType());
                    //冻结场景
                    freezeBalanceReqDto.setBizType(BizType.DRIVER_CHARGING_PAY.getType());
                    freezeBalanceReqDto.setRemark("充电预付款，冻结充电卡");
                    freezeBalanceReqDto.setBookType(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType());
                    Response<List<com.t3.ts.account.manager.dto.resp.AccountBookFreezeDto>>  freezeResponse = balanceFreezeService.freezeBalance(freezeBalanceReqDto);
                    if (!freezeResponse.isSuccess()) {
                        BizExceptionUtil.create(BizExceptionEnum.PARAM_ERROR.getCode(), "冻结充电卡失败");
                    }
                    //冻结成功 待支付金额减少
                    remainFare = remainFare - chargingElectricityCardFreeze;
                }
            }
        }
        //剩下要付的三方，生成预付款结算单
        BigDecimal remainFareYuan = MoneyUtils.fenToYuan(remainFare);
        SettlementGeneralDto settlementGeneralDto = new SettlementGeneralDto();
        settlementGeneralDto.setBizType(BizType.DRIVER_PRE_PAY.getType());
        settlementGeneralDto.setSettleType(SettleType.GENERAL_PAYMENT.getType());
        settlementGeneralDto.setPaymentSubject(userId);
        settlementGeneralDto.setPaymentSubjectType(accountDto.getAccountType());
        settlementGeneralDto.setOrderUuid(orderId);
        settlementGeneralDto.setBindOrderUuid(orderId);
        settlementGeneralDto.setTotalFare(remainFareYuan);
        settlementGeneralDto.setOrderFare(remainFareYuan);
        Response<String> settlementResponse = settlementGeneralService.addSettlement(settlementGeneralDto);
        if(!settlementResponse.isSuccess()){
            BizExceptionUtil.create(BizExceptionEnum.PARAM_ERROR.getCode(), "创建预付款结算单失败");
        }
        settlementGeneralDto.setSettleUuid(settlementResponse.getData());
        // 使用迭代器遍历并移除所有匹配项
        payTypeList.removeIf(payType -> EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode() == payType);
    }
}
