package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.operation.center.constant.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.utils.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1000;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_2;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_24;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_3;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_60;

/**
 * 有效日期
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
public final class DateUtils {

    /**
     * 设置日期1格式
     */
    public static final String FORMAT_DATE_1 = "yyyy-MM-dd";
    /**
     * 违约
     */
    public static final String DEFULT = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日志
     */
    private static final Logger LOG = LoggerFactory.getLogger(DateUtils.class);
    /**
     * 设置日期格式
     */
    private static final String FORMAT_DATE = "yyyyMMdd";
    /**
     * 格式化时间
     */
    private static final String FORMAT_TIME = "HHmmss";
    /**
     * %1-%2
     */
    private static final String FORMAT_DATETIME = "yyyyMMddHHmmss";
    /**
     * 设置月份格式
     */
    private static final String FORMAT_MONTH = "yyyyMM";

    public static final String END_TIME = " 02:00:00";

    /**
     * 日期跑龙套
     */
    private DateUtils() {

    }

    /**
     * 将字符串格式yyyyMMdd的字符串转为日期，格式"yyyy-MM-dd"
     *
     * @param date 日期字符串
     * @return 返回格式化的日期
     */
    public static String strDateTimeString(String date) {
        try {
            Date d = org.apache.commons.lang3.time.DateUtils.parseDate(date);
            return new SimpleDateFormat(FORMAT_DATE_1).format(d);
        } catch (ParseException e) {
            LOG.error(e.getMessage());
        }
        return null;
    }

    /**
     * 将字符串格式yyyyMMdd的字符串转为日期，格式"yyyy-MM-dd"
     *
     * @param date 日期字符串
     * @return 返回格式化的日期
     */
    public static String strToDateFormat(String date) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(FORMAT_DATE);
            formatter.setLenient(false);
            Date newDate = formatter.parse(date);
            formatter = new SimpleDateFormat(FORMAT_DATE_1);
            return formatter.format(newDate);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 解析日期
     * 解析字符串为时间
     *
     * @param source  字符串
     * @param pattern 字符串格式
     * @return 时间
     */
    public static Date parseDate(String source, String pattern) {
        if (StringUtils.isBlank(source)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);

            return sdf.parse(source);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 把字符串日期转换为f指定格式的Data对象
     *
     * @param strDate ,f
     * @param f       f
     * @return 返回格式化的日期
     */
    public static Date format(String strDate, String f) {
        Date d = null;
        if ("".equals(strDate)) {
            return null;
        } else {
            try {
                d = getFormatter(f).parse(strDate);
            } catch (ParseException pex) {
                return null;
            }
        }
        return d;
    }

    /**
     * 获取一个简单的日期格式化对象
     *
     * @param parttern xx
     * @return 一个简单的日期格式化对象
     */
    private static SimpleDateFormat getFormatter(String parttern) {
        return new SimpleDateFormat(parttern, Locale.CHINA);
    }

    /**
     * 获取日期字符串
     *
     * @return {@link String}
     */
    public static String getDateFormat() {
        return new SimpleDateFormat(FORMAT_DATE).format(new Date());
    }

    /**
     * 获取时间字符串
     *
     * @return {@link String}
     */
    public static String getTimeFormat() {
        return new SimpleDateFormat(FORMAT_TIME).format(new Date());
    }

    /**
     * 获取日期时间字符串
     *
     * @return {@link String}
     */
    public static String getDateTimeFormat() {
        return new SimpleDateFormat(FORMAT_DATETIME).format(new Date());
    }

    /**
     * 得到月格式
     *
     * @return {@link String}
     */
    public static String getMonthFormat() {
        return new SimpleDateFormat(FORMAT_MONTH).format(new Date());
    }

    /**
     * 获取日期字符串
     *
     * @param date 日期
     * @return {@link String}
     */
    public static String getDateFormat(Date date) {
        return new SimpleDateFormat(FORMAT_DATE).format(date);
    }

    /**
     * 获取时间字符串
     *
     * @param date 日期
     * @return {@link String}
     */
    public static String getTimeFormat(Date date) {
        return new SimpleDateFormat(FORMAT_TIME).format(date);
    }

    /**
     * 获取日期时间字符串
     *
     * @param date 日期
     * @return {@link String}
     */
    public static String getDateTimeFormat(Date date) {
        return new SimpleDateFormat(DEFULT).format(date);
    }

    /**
     * 获取时间字符串
     *
     * @param date 日期
     * @return {@link String}
     */
    public static String getMonthFormat(Date date) {
        return new SimpleDateFormat(FORMAT_MONTH).format(date);
    }

    /**
     * 获取时间戳
     *
     * @param date 日期
     * @param time 时间
     * @return {@link Long}
     */
    public static Long getDateTimeLong(String date, String time) {
        String dateTime = date + " " + time;
        try {
            Date d = org.apache.commons.lang3.time.DateUtils.parseDate(dateTime, "yyyy-MM-dd HH:mm");
            return d.getTime();
        } catch (ParseException e) {
            LOG.error(e.getMessage());
        }
        return null;
    }


    /**
     * String 转其它格式String日期
     *
     * @param date    日期
     * @param pattern 模式
     * @return {@link String}
     */
    public static String stringParseDate(String date, String pattern) {
        try {
            Date d = org.apache.commons.lang3.time.DateUtils.parseDate(date, pattern);
            return DateFormatUtils.format(d, pattern);
        } catch (ParseException e) {
            LOG.error(e.getMessage());
        }
        return date;
    }

    /**
     * 翻译日期
     *
     * @param time 时间
     * @return {@link String}
     */
    public static String translateDate(Long time) {
        if (time == null) {
            return "";
        }
        long oneDay = NUM_24 * NUM_60 * NUM_60 * CommonNumConst.NUM_1000L;
        Calendar current = Calendar.getInstance();
        // 今天
        Calendar today = Calendar.getInstance();
        today.set(Calendar.YEAR, current.get(Calendar.YEAR));
        today.set(Calendar.MONTH, current.get(Calendar.MONTH));
        today.set(Calendar.DAY_OF_MONTH, current.get(Calendar.DAY_OF_MONTH));
        // Calendar.HOUR——12小时制的小时数 Calendar.HOUR_OF_DAY——24小时制的小时数
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);

        Date dt = new Date(time);
        SimpleDateFormat fmt = new SimpleDateFormat("HH:mm");
        String dtStr = fmt.format(dt);

        long todayStartTime = today.getTimeInMillis();

        if (time >= todayStartTime && time < todayStartTime + oneDay) {
            // today
            return "今天" + dtStr;
        } else if (time >= todayStartTime + oneDay
                && time < todayStartTime + oneDay * NUM_2) {
            // yesterday
            return "明天" + dtStr;
        } else if (time >= todayStartTime + oneDay * NUM_2
                && time < todayStartTime + oneDay * NUM_3) {
            // the day before
            // yesterday
            return "后天" + dtStr;
        } else {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = new Date(time);
            return dateFormat.format(date) + " " + dtStr;
        }
    }

    /**
     * 得到毫第二
     *
     * @param offset 抵消
     * @return {@link Long}
     */
    public static Long getMilliSecond(Integer offset) {
        return LocalDateTime.now().plusMinutes(offset).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 获取当前时间到第二天凌晨的毫秒数
     *
     * @return int
     */
    public static int getMillisecondsNextEarlyMorning() {

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return (int) (cal.getTimeInMillis() - System.currentTimeMillis());
    }

    /**
     * 半小时后得到日期
     *
     * @param date   日期
     * @param minute 一分钟
     * @return {@link Long}
     */
    public static Long getDateAfterHalfHour(Date date, Integer minute) {
        long time = date.getTime();
        Long result = time + minute * NUM_60 * NUM_1000;
        return result;
    }

    /**
     * 用于计算两个日期之间的间隔 （原时间差工具类跨年计算错误）
     *
     * @param first   第一个日期(取最近的时间)
     * @param second  第二个日期（较远的时间）
     * @param formart formart
     * @return 返回天数差
     */
    public static Integer longOfTwoDate(Date first, Date second, String formart) {
        Integer day = null;
        try {
            /**
             * 原有的时间在转化成String然后再次解析为Date
             * 目的: 清除时间原本自带的小时毫秒便于计算
             */
            //格式化时间
            SimpleDateFormat format = new SimpleDateFormat(formart);
            //将时间转化成String类型
            String fDate = format.format(first);
            String sDate = format.format(second);
            //解析String为Date
            Date date1 = format.parse(fDate);
            Date date2 = format.parse(sDate);
            //两日期相减，所得毫秒数 除以1000得秒 除以3600得小时 除24得天数得到两个日期的间隔
            day = (int) ((date1.getTime() - date2.getTime()) / (NUM_1000 * CommonNumConst.NUM_3600 * NUM_24));

        } catch (Exception e) {
            e.printStackTrace();
            LOG.error("DateUtils longOfTwoDate ERROR, msg={}", JSONObject.toJSONString(e.getMessage()));
        }
        //返回天数
        return day;
    }


    /**
     * 是生效日期
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime1   当前时间
     * @param beginTime1 开始时间
     * @param endTime1   结束时间
     * @return boolean
     * <AUTHOR>
     */
    public static boolean isEffectiveDate(Date nowTime1, String beginTime1, String endTime1) {
        try {
            String format = "HH:mm";
            SimpleDateFormat sf = new SimpleDateFormat("HH:mm");
            String now = sf.format(nowTime1);
            Date nowTime = new SimpleDateFormat(format).parse(now);
            Date beginTime = new SimpleDateFormat(format).parse(beginTime1);
            Date endTime = new SimpleDateFormat(format).parse(endTime1);
            Calendar date = Calendar.getInstance();
            date.setTime(nowTime);
            Calendar begin = Calendar.getInstance();
            begin.setTime(beginTime);
            Calendar end = Calendar.getInstance();
            end.setTime(endTime);

            if (end.getTimeInMillis() < begin.getTimeInMillis()) {
                end.add(Calendar.DATE, 1);
            }
            if (date.getTimeInMillis() < begin.getTimeInMillis() && date.getTimeInMillis() < end.getTimeInMillis()) {
                date.add(Calendar.DATE, 1);
            }
            return date.getTimeInMillis() >= begin.getTimeInMillis() && date.getTimeInMillis() <= end.getTimeInMillis();
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 获取指定时间加几个月后的时间
     *
     * @param currentDate 当前日期
     * @param num         加的几个月
     * @return {@link Date}
     */
    public static Date getDateAddMonth(Date currentDate, int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.add(Calendar.MONTH, num);
        return calendar.getTime();
    }

    /**
     * 判断 指定时间是否在当期时间的i分钟之前
     *
     * @param time 指定时间
     * @param i    分钟数
     * @return boolean
     */
    public static boolean isBeforeAppointedTime(Date time, Integer i) {
        if (Objects.isNull(time)) {
            return true;
        }
        String dateTime = DateUtils.getDateTimeFormat(time);
        if (dateTime.compareTo(obtainMinutes(NumConstants.NUM_1, i)) < 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取当时间前/后的时间(小时)
     *
     * @param i     指定距离当前时间的分钟数
     * @param state 状态  0.当月 1.前  2.后
     * @return {@link String}
     */
    public static String obtainMinutes(Integer state, Integer i) {
        LocalDateTime date = null;
        //type 类型 0.月 1.天 2.小时 3.分钟 4.秒
        date = getLocalDateTime(state, NumConstants.NUM_3, i);
        //获取该月份的最后一天
        String minutes = date.format(DateTimeFormatter.ofPattern(DEFULT));
        return minutes;
    }

    /**
     * 得到当地日期时间
     *
     * @param state 状态
     * @param type  类型
     * @param i     我
     * @return {@link LocalDateTime}
     */
    private static LocalDateTime getLocalDateTime(Integer state, Integer type, Integer i) {
        LocalDateTime date;
        if (state == 0) {
            date = LocalDateTime.now();
        } else if (state == NumConstants.NUM_1) {
            if (type == 0) {
                //获取月
                date = LocalDateTime.now().minusMonths(i);
            } else if (type == NumConstants.NUM_1) {
                //获取天
                date = LocalDateTime.now().minusDays(i);
            } else if (type == NumConstants.NUM_2) {
                //获取小时
                date = LocalDateTime.now().minusHours(i);
            } else if (type == NumConstants.NUM_3) {
                //获取分钟
                date = LocalDateTime.now().minusMinutes(i);
            } else {
                //获取秒
                date = LocalDateTime.now().minusSeconds(i);
            }
        } else {
            if (type == 0) {
                //获取月
                date = LocalDateTime.now().plusMonths(i);
            } else if (type == NumConstants.NUM_1) {
                //获取天
                date = LocalDateTime.now().plusDays(i);
            } else if (type == NumConstants.NUM_2) {
                //获取小时
                date = LocalDateTime.now().plusHours(i);
            } else if (type == NumConstants.NUM_3) {
                //获取分钟
                date = LocalDateTime.now().plusMinutes(i);
            } else {    //获取秒
                date = LocalDateTime.now().plusSeconds(i);
            }
        }
        return date;
    }

    /**
     * 获取当前时间到指定时刻前的毫秒数
     *
     * @param hour 指定时刻的小时
     * @param min  指定时刻的分钟
     * @param sec  指定时刻的秒
     * @param mill 指定时刻的毫秒
     * @return long
     */
    public static long getMillsecBeforeMoment(int hour, int min, int sec, int mill) {
        return getMillisecBetweenDate(new Date(), getMoment(hour, min, sec, mill));
    }

    /**
     * 获取两个日期之间的毫秒数
     *
     * @param before 之前
     * @param after  后
     * @return long
     */
    public static long getMillisecBetweenDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return afterTime - beforeTime;
    }

    /**
     * 获取当天的某一时刻Date
     *
     * @param hour 24小时
     * @param min  分钟
     * @param sec  秒
     * @param mill 毫秒
     * @return {@link Date}
     */
    public static Date getMoment(int hour, int min, int sec, int mill) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, min);
        calendar.set(Calendar.SECOND, sec);
        calendar.set(Calendar.MILLISECOND, mill);
        return calendar.getTime();
    }

    /**
     * 一天开始时间
     *
     * @param date date
     * @return return
     */
    public static Date getDayStartTimeNew(Date date) {
        String dateStr = format2String(date, FORMAT_DATE_1) + " 00:00:00";
        return format(dateStr, DEFULT);
    }

    /**
     * format2字符串
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format2String(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 有一天结束时间
     *
     * @param date 日期
     * @return {@link Date}
     * @param:
     * @return:
     * @Description:获取当天结束时间
     * @Author: huhongchang
     * @Date: 2019/1/8 16:01
     */
    public static Date getDayEndTime(Date date) {
        String dateStr = format2String(date, FORMAT_DATE_1) + " 23:59:59";
        return format(dateStr, DEFULT);
    }

    /**
     * 设置日期结束时间
     *
     * @param date 日期
     * @return 日期开始时间
     */
    public static Date getReleaseDate(Date date) {
        if (null == date) {
            return null;
        }
        return parseDate(format2String(date, FORMAT_DATE_1) + END_TIME, DEFULT);
    }

    /**
     * 得到一天剩下的时间
     * 获取一天中剩余的时间（秒数）
     *
     * @param currentDate 当前日期
     * @return {@link Integer}
     */
    public static Integer getDayRemainingTime(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }
}
