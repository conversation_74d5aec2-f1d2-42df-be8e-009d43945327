package com.t3.ts.pay.center.api.business.query.paystatus.handler;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.cache.exception.T3CacheException;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.constants.RedisKeyConstant;
import com.t3.ts.result.Response;

/**
 * Description: 查询支付状态接口
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/29/0029 14:10
 */
public interface PayStatusHandler {

    /**
     * 查询类型
     *
     * @return {@link String}
     */
    String getType();

    /**
     * 处理程序
     *
     * @param context 上下文
     * @return {@link Response<String>}
     */
    Response<JSONObject> handler(QueryStatusContext context);

    /**
     * 检查当前redis中是否存在结算单
     *
     * @param settleId 结算单
     * @param factory  redis工厂
     * @return boolean
     */
    default boolean checkRedis(String settleId, T3CacheFactory factory) {
        String lockKey = RedisKeyConstant.PAY_CENTER_SETTLE_ID_KEY + settleId;
        try {
            return factory.Lock().containsLock(lockKey);
        } catch (T3CacheException e) {
        }
        return false;
    }
}
