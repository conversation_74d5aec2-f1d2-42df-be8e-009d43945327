package com.t3.ts.pay.center.api.controller;

import com.t3.ts.pay.center.api.business.EnterprisePaymentBusiness;
import com.t3.ts.pay.center.api.constants.EnterpriseConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.dto.chartered.PayTradeCharteredReq;
import com.t3.ts.pay.center.api.dto.enterprise.AdvanceSerialReq;
import com.t3.ts.pay.center.api.dto.enterprise.PayReq;
import com.t3.ts.pay.center.api.dto.enterprise.RechargeReq;
import com.t3.ts.pay.center.api.dto.enterprise.RequisitionUuidReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 企业用车相关支付接口
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: EnterpriseApi
 * @Package com.t3.ts.pay.center.api.controller
 * @date 2021/9/15 10:45
 */
@RestController
@Slf4j
@RequestMapping("/api/payment/v1/enterprise")
@Api("Enterprise-Api")
public class EnterpriseApi extends BaseApi {

    @Autowired
    private EnterprisePaymentBusiness enterprisePaymentBusiness;
    /**
     * 企业用车收银台pro
     * @param req req
     * @return Response
     */
    @PostMapping("/cashierDeskPro")
    @ApiOperation(value = "收银台pro", notes = "收银台pro")
    public Response cashierDeskPro(@RequestBody PayTradeCharteredReq req) {
        if (ObjectUtils.isEmpty(req.getRoutePlanUuid())) {
            return Response.createError("参数行程ID不能为空");
        }
        return enterprisePaymentBusiness.cashierDeskPro(req);
    }

    /**
     * @param payReq  payReq
     * @param request request
     * @return com.t3.ts.result.Response
     * @author: qul
     * @description: 企业用车支付接口
     * @date: 2020/3/2 11:49
     */
    @PostMapping("/payForAllBusiness")
    @ApiOperation(value = "个人支付", notes = "个人支付")
    public Response payForPackage(@RequestBody PayReq payReq, HttpServletRequest request) {
        if (payReq.getRoutePlanUuid() == null) {
            return Response.createError("参数行程ID不能为空");
        }
        if (getUserUid(request) == null) {
            return Response.createError("未获取到乘客ID");
        }
        if (payReq.getPayTypeList() == null || CollectionUtils.isEmpty(payReq.getPayTypeList())) {
            return Response.createError("支付渠道参数异常");
        }
        return enterprisePaymentBusiness.payForAllBusiness(
                payReq, getUserUid(request), payReq.getBusinessType().toString());
    }

    /**
     * 企业用车预付款支付接口
     * @param req req
     * @param request request
     * @return Response
     */
    @PostMapping("/recharge")
    @ApiOperation(value = "企业个人支付充值", notes = "企业个人支付充值")
    public Response recharge(@RequestBody RechargeReq req, HttpServletRequest request) {
        if (req.getSettlementId() == null) {
            return Response.createError("充值结算ID不能为空");
        }
        if (req.getRechargePayType() == null) {
            return Response.createError("充值支付方式不能为空");
        }
        if (getUserUid(request) == null) {
            return Response.createError("未获取到乘客ID");
        }
        return enterprisePaymentBusiness.recharge(req, getUserUid(request));
    }

    /**
     * 企业用车预付款结算单状态
     * @param req req
     * @param request request
     * @return Response
     */
    @PostMapping("/querySettlementIsSuccess")
    @ApiOperation(value = "查询是否支付成功", notes = "查询是否支付成功")
    public Response querySettlementIsSuccess(@RequestBody AdvanceSerialReq req, HttpServletRequest request) {
        if (getUserUid(request) == null) {
            return Response.createError("未获取到乘客ID");
        }
        if (req.getAdvanceSerial() == null) {
            return Response.createError("预付款流水号参数不能为空");
        }
        if (req.getSettlementId() == null) {
            return Response.createError("充值结算单参数不能为空");
        }
        return enterprisePaymentBusiness.querySettlementIsSuccess(
                getUserUid(request), req.getAdvanceSerial(), req.getSettlementId());
    }

    /**
     * 企业用车预付款支付状态查询（创建订单）
     * @param req req
     * @return return
     */
    @PostMapping("/checkIsCreateRequisition")
    @ApiOperation(value = "验证申请单是否创建成功", notes = "验证申请单是否创建成功")
    public Response checkIsCreateApplication(@RequestBody RequisitionUuidReq req) {
        // 参数校验
        if (StringUtils.isBlank(req.getRequisitionUuid())) {
            return Response.createError(EnterpriseConstants.NON_REQUSITION_ID);
        }
        return enterprisePaymentBusiness.checkIsCreateApplication(req.getRequisitionUuid());
    }

    /**
     * 企业用车预付款支付状态查询（变更行程）
     * @param req req
     * @return return
     */
    @PostMapping("/checkIsModifyRoutePlan")
    @ApiOperation(value = "验证行程是否更改成功", notes = "验证行程是否更改成功")
    public Response checkIsModifyRoutePlan(@RequestBody RoutePlanUuidReq req) {
        if (StringUtils.isEmpty(req.getRoutePlanUuid())) {
            return Response.createError(PassengerConstants.ROUTE_PLAN_UUID_NOT_EXIST);
        }
        return enterprisePaymentBusiness.checkIsModifyRoutePlan(req.getRoutePlanUuid());
    }
}
