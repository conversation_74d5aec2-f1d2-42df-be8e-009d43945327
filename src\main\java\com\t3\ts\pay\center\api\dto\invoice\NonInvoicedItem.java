package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Data;

import java.io.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NonInvoicedItem.java
 * @Description 不可开票金额项
 * @createTime 2022年01月19日 09:01:00
 */
@Data
public class NonInvoicedItem implements Serializable {

    private static final long serialVersionUID = 2208320175970562504L;

    
    // 类型名称
    private String item;

    // 类型-用于防重
    private String type;

    // 不可开票金额，带元
    private String amount;


}
