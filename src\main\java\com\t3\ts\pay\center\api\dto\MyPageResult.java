package com.t3.ts.pay.center.api.dto;

import com.t3.ts.result.PageResult;

/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * @param <T>
 * <AUTHOR>
 * @Date: 2020/6/29 20:57
 * @Description: 1.0
 */
public class MyPageResult<T> extends PageResult<T> {

    private static final long serialVersionUID = 4078453729327572242L;

    // 是否跨账号支付
    private Boolean crossAccountPayment = Boolean.FALSE;

    // 底部点击文案
    private String tips;

    // 下次查询月份索引
    private String nextIndex;

    // 列表是否已触底
    private Boolean endFlag = false;

    public Boolean getEndFlag() {
        return endFlag;
    }

    public void setEndFlag(Boolean endFlag) {
        this.endFlag = endFlag;
    }

    public String getNextIndex() {
        return nextIndex;
    }

    public void setNextIndex(String nextIndex) {
        this.nextIndex = nextIndex;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public Boolean getCrossAccountPayment() {
        return crossAccountPayment;
    }

    public void setCrossAccountPayment(Boolean crossAccountPayment) {
        this.crossAccountPayment = crossAccountPayment;
    }
}
