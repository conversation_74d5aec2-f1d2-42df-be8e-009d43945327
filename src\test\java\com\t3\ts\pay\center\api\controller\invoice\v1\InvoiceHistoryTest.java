package com.t3.ts.pay.center.api.controller.invoice.v1;

import com.google.common.collect.Lists;
import com.t3.ts.invoice.center.dto.PassengerInvoiceDto;
import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceHistoryReq;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import junit.framework.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

/**
 * @Author: ivy
 * @Date: 2021/9/17 17:22
 * @Description: 获取发票开票历史test类    /api/passenger/v1/invoice/historyList
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class InvoiceHistoryTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void invoiceHistoryTest() {
        InvoiceHistoryReq invoiceReq = new InvoiceHistoryReq();
        invoiceReq.setCurrPage(0);
        invoiceReq.setPageSize(10);
        ArrayList<Integer> in = Lists.newArrayList();
        in.add(2);
        in.add(1);
        invoiceReq.setInvoiceClassList(in);
        Response<PageResult<PassengerInvoiceDto>> pageResultResponse =
                invoiceBusiness.queryInvoiceHistoryList(invoiceReq, "967f0d3ae5cf4760923bef29d1255bea");
        Assert.assertTrue(pageResultResponse.isSuccess());
    }
}
