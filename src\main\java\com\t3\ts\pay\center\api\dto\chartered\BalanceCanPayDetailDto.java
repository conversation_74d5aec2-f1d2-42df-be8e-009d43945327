package com.t3.ts.pay.center.api.dto.chartered;

import lombok.Data;

/**
 * <AUTHOR>
 * @dete 2020.10.26
 */
@Data
public class BalanceCanPayDetailDto {
    /**
     * 礼品卡能支付费用  单位元
     */
    private String giftCardCanPay;
    /**
     * 赠送币能支付费用  单位元
     */
    private String giftMoneyCanPay;
    /**
     * 礼品卡是否支持附加费支付
     */
    private Boolean giftCardCanPayService;
    /**
     * 积分能支付费用  单位元
     */
    private String integralCanPay;

}
