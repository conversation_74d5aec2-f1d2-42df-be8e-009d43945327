package com.t3.ts.pay.center.api.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/8 20:25
 * @param <T> This describes my type parameter
 * <p>
 * 由于前端解析问题，数组不能直接返回。
 * 用这个对象包一层
 */
@ApiModel
@Data
public class DataListVo<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("集合")
    private List<T> list;

    @ApiModelProperty(value = "配置信息")
    private Map<String, Object> configMap;

    public DataListVo(List<T> list) {
        this.list = list;
    }

    public DataListVo(List<T> list, Map<String, Object> configMap) {
        this.list = list;
        this.configMap = configMap;
    }
}
