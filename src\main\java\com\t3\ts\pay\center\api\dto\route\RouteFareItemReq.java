package com.t3.ts.pay.center.api.dto.route;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RouteFareItemReq implements Serializable {

    /**
     * 行程uuid
     */
    private String routePlanUuid;

    /**
     * 费用对象
     */
    private Integer consumerType;

    /**
     * 行程uuid集合，批量查询时使用
     */
    private List<String> routePlanUuidList;

    /**
     * Description: 构造器<br>
     *
     * @param routePlanUuid <br>
     * @return null <br>
     * <AUTHOR> <br>
     * @taskId <br>
     */
    public RouteFareItemReq(String routePlanUuid) {
        this.routePlanUuid = routePlanUuid;
    }

    /**
     * Description: 构造器<br>
     *
     * @param routePlanUuidList <br>
     * @return null <br>
     * <AUTHOR> <br>
     * @taskId <br>
     */
    public RouteFareItemReq(List<String> routePlanUuidList) {
        this.routePlanUuidList = routePlanUuidList;
    }
}
