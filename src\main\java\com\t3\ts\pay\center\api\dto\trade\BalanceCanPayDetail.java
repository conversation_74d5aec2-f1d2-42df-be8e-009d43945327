package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:25
 * @des 1.0
 */

@Data
public class BalanceCanPayDetail {

    // 礼品卡能支付费用
    private BigDecimal giftCardCanPay;

    // 赠送币能支付费用
    private BigDecimal giftMoneyCanPay;
    //个人充值本金支付费用
    private BigDecimal rechargeMoneyCanPay;
    //充值赠金支付费用
    private BigDecimal rechargeGiftMoneyCanPay;

    // 礼品卡是否支持附加费支付
    private Boolean giftCardCanPayService;

    // 积分能支付费用  单位元
    private BigDecimal integralCanPay;
    /**
     * 企业礼品卡支付费用
     */
    private BigDecimal companyGiftCardCanPay;

    // 礼品卡是否支持节日费支付
    private Boolean giftCardCanPayFestival;
    private Boolean giftCardCanPayCrossCity;
    private Boolean giftCardCanPayDispatch;
}
