package com.t3.ts.pay.center.api.controller.invoice.v3;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/17 17:22
 * @Description: 提交开票test类    /api/passenger/v3/invoice/billing
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
@Slf4j
public class InvoiceBillingTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void billingTest() {
//        InvoiceRelevantFormV3 invoiceForm = new InvoiceRelevantFormV3();
//        invoiceForm.setSendType(2);
//        invoiceForm.setInvoicePassengerUuid(StringUtils.buildUUID().replaceAll("-",""));
//        invoiceForm.setInvoiceMoney(new BigDecimal(10));
//        invoiceForm.setInvoiceOrderUuid("");
//        invoiceForm.setVirtualPassengerUuid("");
//        invoiceForm.setTransportType(1);
//
//        T3Transport t3Transport = new T3Transport();
//        t3Transport.setOrderUuid("123123123123");
//        List<OrderToCode> orderToCodes = new ArrayList<>();
//        OrderToCode orderToCode = new OrderToCode();
//        orderToCode.setOrderUuid("433334234");
//        orderToCode.setInvoiceSubjectCode(2);
//        orderToCode.setMoney(new BigDecimal(10));
//        orderToCodes.add(orderToCode);
//        t3Transport.setOrderToCodes(orderToCodes);
//        t3Transport.setMoney(new BigDecimal(10));
//        invoiceForm.setT3Transport(t3Transport);
//        invoiceForm.setUuid("");
//        invoiceForm.setPassengerMobile("***********");
//        invoiceForm.setType(2);
//        invoiceForm.setPayType(0);
//        invoiceForm.setHeaderType(1);
//        invoiceForm.setHeader("胡建德测试");
//        invoiceForm.setMoney(new BigDecimal(10));
//        invoiceForm.setContent("测试");
//        invoiceForm.setRecipient("胡建德");
//        invoiceForm.setMobile("***********");
//        invoiceForm.setArea("南京");
//        invoiceForm.setDetailAddress("南京");
//        invoiceForm.setRemark("测试");
//        invoiceForm.setLogisticsOrderNo("**********");
//        invoiceForm.setStatus(0);
//        invoiceForm.setOrderUuid("********");
//        invoiceForm.setLogisticsCompany("京东物流");
//        invoiceForm.setInvoiceType(1);
//        invoiceForm.setEmail("<EMAIL>");
//        invoiceForm.setRegisterAddress("南京江宁");
//        invoiceForm.setRegisterTel("***********");
//        invoiceForm.setOpeningAccount("*********");
//        invoiceForm.setOpeningAccount("招商");
//        invoiceForm.setTaxNum("***************");
//        invoiceForm.setBillType(1);
//        Response response = invoiceBusiness.invoiceBilling(invoiceForm, invoiceForm.getInvoicePassengerUuid());
//        log.info("开篇结果：{}", JSONUtil.toJsonStr(response));
    }
}
