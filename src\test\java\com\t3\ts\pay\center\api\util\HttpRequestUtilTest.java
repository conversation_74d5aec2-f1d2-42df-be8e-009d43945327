package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSONObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Map;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2020/5/15, 0015 9:26
 */
@RunWith(MockitoJUnitRunner.class)
public class HttpRequestUtilTest {
    @Mock
    Logger log;
    @InjectMocks
    HttpRequestUtil httpRequestUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetJSONParamFromJson() throws Exception {
        try {
            JSONObject result = HttpRequestUtil.getJSONParamFromJson(null);
            Assert.assertTrue(true);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void testGetMapParamFromForm() throws Exception {
        try {
            Map<String, Object> result = HttpRequestUtil.getMapParamFromForm(null);
            Assert.assertTrue(true);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme