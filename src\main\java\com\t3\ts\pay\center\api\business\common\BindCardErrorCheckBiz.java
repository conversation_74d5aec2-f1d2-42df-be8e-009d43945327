package com.t3.ts.pay.center.api.business.common;

import com.t3.ts.pay.center.api.cache.ApiCacheKeyUtils;
import com.t3.ts.pay.center.api.cache.RedisCacheServiceV2;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.util.DateUtils;
import com.t3.ts.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 卡绑定错误检查
 *
 * <AUTHOR>
 * @date 2021/08/24
 */
@Component
public class BindCardErrorCheckBiz {

    private static final Integer ONE_HOUR = 3600;

    @Autowired
    private RedisCacheServiceV2 redisCacheServiceV2;

    @Autowired
    private SwitchConfig switchConfig;

    /**
     * 标记错误次数
     *
     * @param driverUuid 司机uuid
     */
    public void incError(String driverUuid) {
        redisCacheServiceV2.setValueIncr(String.format(ApiCacheKeyUtils.BIND_CARD_ERROR, driverUuid), 1,
                DateUtils.getDayRemainingTime(new Date()));
    }

    /**
     * 超出错误次数限制
     *
     * @param driverUuid 司机uuid
     */
    public boolean checkOutOfErrorLimit(String driverUuid) {
        Integer count = redisCacheServiceV2.getValue(String.format(ApiCacheKeyUtils.BIND_CARD_ERROR, driverUuid), Integer.class);
        if (count != null && count > switchConfig.getBindCardErrCount()) {
            return false;
        }
        return true;
    }

    /**
     * 设置四要素验证 标识
     * @param driverUuid 司机uuid
     * @param cardNo 银行卡号
     */
    public void setVerifyBankCardInfo(String driverUuid, String cardNo) {
        redisCacheServiceV2.setValue(String.format(ApiCacheKeyUtils.VERIFY_BANK_CARD_INFO, driverUuid), cardNo, ONE_HOUR);
    }

    /**
     * 校验司机是否进行过四要素验证
     *
     * @param driverUuid 司机uuid
     * @param cardNo 银行卡号
     * @return true:进行过 false：没有
     */
    public boolean checkVerifyBankCardInfo(String driverUuid, String cardNo) {
        String redisCardNo = redisCacheServiceV2.getValue(String.format(ApiCacheKeyUtils.VERIFY_BANK_CARD_INFO, driverUuid), String.class);
        if (StringUtils.isNotBlank(redisCardNo) && redisCardNo.equals(cardNo)) {
            return true;
        }
        return false;
    }

    /**
     * 约约司机身份信息确认
     * @param idCardNo String
     * @return String
     */
    public String getConfirmToken(String idCardNo) {
        String uuid = StringUtils.buildUUID();
        redisCacheServiceV2.setValue(String.format(ApiCacheKeyUtils.YUEYUE_CONFIRM_TOKEN, uuid), idCardNo, ONE_HOUR);
        return uuid;
    }

    /**
     * 获取司机身份信息
     *
     * @param confirmToken getIdCardNoByConfirmToken
     * @return String
     */
    public String getIdCardNoByConfirmToken(String confirmToken) {
        return redisCacheServiceV2.getValue(String.format(ApiCacheKeyUtils.YUEYUE_CONFIRM_TOKEN, confirmToken), String.class);
    }
}
