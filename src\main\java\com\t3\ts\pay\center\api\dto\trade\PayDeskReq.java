package com.t3.ts.pay.center.api.dto.trade;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/23 10:53
 * @des 1.0
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayDeskReq {

    // 收银台类型
    private String type;

    // 行程id
    private String orderId;

    // 用户id
    private String userId;

    // 支付方式
    private List<String> payWay;

    // 可用积分数量
    private double integral;

    // 优惠券id
    private String couponId;

    // 使用省心打标记 1表示使用省心打
    private Integer unWarriedArrive;

    // 第三方支付渠道
    private Integer payChannel;

    // 券适用类型 0 不使用 1 使用最优券 2 使用指定券 3券活动
    private String useCouponType;


    private String cityCode;

    /**
     * 0 不使用（需返回信息）   1 使用（返回信息且使用）  null （不使用）
     */
    private Integer couponActivityFlag;
    /**
     * 初始化收银台 传 -1    ，
     * 亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择
     */
    private Integer payForOtherType;

    private Boolean taxiScanPay = false;

    /**
     * 权益卡uuid
     */
    private String privilegeUuid;

    /**
     * 卡使用类型 0 不使用 1 使用最优卡 2 使用指定卡
     */
    private String usePrivilegeType;

    /**
     * 渠道来源
     */
    private Integer userChannel;

    /**
     * 版本号
     */
    private String grayVersion;

    /**
     * 版本号
     */
    private String grayBuild;

    /**
     * 是否使用新券包信息
     */
    private Boolean useNewCouponPackage;
    /**
     * 是否需要返回可用券数量信息
     */
    private Boolean needCouponNum = false;
}
