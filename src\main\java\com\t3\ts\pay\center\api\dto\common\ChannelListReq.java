package com.t3.ts.pay.center.api.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/1 14:07
 * @description:
 */
@Data
@ApiModel
public class ChannelListReq {
    /**
     * 行程订单号
     */
    @ApiModelProperty(value = "行程订单号")
    private String orderUuid;

    // 行程id
    @ApiModelProperty(value = "行程id")
    private String journeyId;

    // 用户id
    @ApiModelProperty(value = "用户id")
    private String userId;

    // 城市code
    @ApiModelProperty(value = "城市code")
    private String cityCode;

    // 区域code
    @ApiModelProperty(value = "区域code，用于替换上面的城市code")
    private String adCode;

    // 手机号
    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "产品线 1：出租车  2:专享 4:快享 6:惠享 7:自动驾驶")
    private String productLine;

    @ApiModelProperty(value = "业务线 1：普通打车 2：包车 4：顺风车 5：老年用车 6:企业用车 7:尊享")
    private String expandBizLine;

    @ApiModelProperty(value = "乘客上次支付的类型")
    private Integer lastPayType;

    //云闪付标志
    @ApiModelProperty(value = "是否云闪付, 默认否")
    private Boolean quickPass = false;

    //预付款标志
    @ApiModelProperty(value = "是否预付款, 默认否")
    private Boolean prePay = false;

    @ApiModelProperty(value = "app免密支付新体验，默认false，true表示是新版本")
    private Boolean useNewWxSign = false;

    @ApiModelProperty(value = "支付场景，参考BizType静态类")
    private Integer bizType;

    @ApiModelProperty(value = "使用有一网通app，默认false，true表示可以引导一网通签约")
    private Boolean hasNetComApp = false;

}
