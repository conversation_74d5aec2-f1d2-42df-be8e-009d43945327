package com.t3.ts.pay.center.api.enums;

import lombok.Getter;

/**
 * 迁移 by ivy .2021/09/17 10:40
 * @Author:  qul
 * @Date:  2021/4/26 11:23
 */
@Getter
public enum InvoiceClassEnum {

    //发票类型(1.出行服务 2.商城)
    ROUTE(1, "出行服务"),
    MALL(2, "商城"),
    CHARGING_FEE(8, "电费");

    /**
     * 类型代码
     */
    private Integer type;

    /**
     * 类型名称
     */
    private String name;

    /**
     * 实名认证枚举构造
     *
     * @param type 类型代码
     * @param name 类型名称
     */
    InvoiceClassEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

}
