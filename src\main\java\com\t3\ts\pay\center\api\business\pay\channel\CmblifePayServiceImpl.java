package com.t3.ts.pay.center.api.business.pay.channel;

import cn.hutool.core.util.StrUtil;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 结果参数拼装
 * @createTime 2021年11月22日 14:12:00
 */
@Service("cmblifePayServiceImpl")
@Slf4j
public class CmblifePayServiceImpl implements PayChannelService {

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        Response<String> paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        // 有引号 replace掉
        String payString = paymentResp.getData();
        RechargePayBo rechargePayVo = new RechargePayBo();
        if (StrUtil.isNotBlank(payString)) {
            String sdk = payString.replaceAll("\"", "");
            rechargePayVo.setSdk(sdk);
        }
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.CMBLIFE.getCode());
        return Response.createSuccess(rechargePayVo);
    }
}
