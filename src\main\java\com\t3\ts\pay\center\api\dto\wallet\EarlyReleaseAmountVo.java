package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 *
 * @ClassName: EarlyReleaseAmountVo
 * @Package com.t3.ts.pay.center.api.dto.wallet
 * @version v1.0.0
 * <AUTHOR>
 * @date 2025/6/19 15:22
 */
@Data
public class EarlyReleaseAmountVo {

    private BigDecimal withdrawAmount = BigDecimal.ZERO;
    private BigDecimal rentAmount = BigDecimal.ZERO;
    private BigDecimal totalAmount = BigDecimal.ZERO;
}
