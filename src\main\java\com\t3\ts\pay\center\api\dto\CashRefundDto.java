package com.t3.ts.pay.center.api.dto;

import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 退款
 *
 * <AUTHOR>
 * @date 2022-03-29
 */
@Getter
@Setter
public class CashRefundDto {

    /**
     * 订单号
     */
    private final String orderUuid = StringUtils.buildUUID();

    /**
     * 金额
     */
    private BigDecimal orderFare;

    /**
     * 金额
     */
    private BigDecimal totalFare;

    /**
     * 金额
     */
    private BigDecimal payAmount;

    /**
     * user_id
     */
    private String incomeSubject;

    /**
     * user_id
     */
    private String paymentSubject;

    /**
     * 乘客
     */
    private final Integer paymentSubjectType = 1;

    /**
     * 乘客
     */
    private final Integer incomeSubjectType = 1;

    /**
     * 结算类型 - 退款
     */
    private final Integer settleType = 1;

    /**
     * 业务类型
     */
    private final Integer bizType = BizType.CASH_RECHARGE_REFUND.getType();
}
