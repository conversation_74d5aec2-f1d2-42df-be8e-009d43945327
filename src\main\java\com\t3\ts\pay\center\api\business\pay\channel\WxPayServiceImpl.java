package com.t3.ts.pay.center.api.business.pay.channel;

import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.util.PayAbTestUtil;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.WxPayDto;
import com.t3.ts.pay.remote.service.WxPayService;
import com.t3.ts.result.Response;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("wxPayServiceImpl")
@Slf4j
public class WxPayServiceImpl implements PayChannelService {

    @DubboReference
    private WxPayService wxPayService;

    @Autowired
    private PayAbTestUtil payAbTestUtil;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        log.info("PayChannelService.WxPayServiceImpl.paymentInfo:{}", JSONUtil.toJsonStr(paymentInfoBo));
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.WEIXIN.getCode());

        WxPayDto dto = new WxPayDto();
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        dto.setOrderNo(paymentResp.getMsg());
        dto.setUserId(paymentInfoBo.getPassengerUuid());
        boolean payAndSignAbTest = payAbTestUtil.wxPayAndSignAbTest(paymentInfoBo.getPassengerUuid());
        log.info("payAndSignAbTest1:{}", payAndSignAbTest);
        if (payAndSignAbTest) {
            dto.setPayAndSign(paymentInfoBo.getPayAndSign());
        }

        //免密支付
        if (paymentInfoBo.isNoSecret()) {
            // 调用免密支付接口
            Response response = wxPayService.noPwdPay(dto);
            rechargePayVo.setSdk(response.getMsg());
            if (!response.isSuccess()) {
                rechargePayVo.setSdk("微信免密支付失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        } else {
            // 微信一般支付
            dto.setSource(paymentInfoBo.getSource());
            Response weiXinPayInfo = wxPayService.pay(dto);
            rechargePayVo.setSdk(String.valueOf(weiXinPayInfo.getData()));
            if (!weiXinPayInfo.isSuccess() || Objects.isNull(weiXinPayInfo.getData())) {
                rechargePayVo.setSdk("获取微信支付串失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }
        }
        rechargePayVo.setOrderNo(dto.getOrderNo());
        return Response.createSuccess(rechargePayVo);
    }
}
