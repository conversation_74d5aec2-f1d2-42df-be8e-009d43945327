package com.t3.ts.pay.center.api.dto.wallet;

import com.t3.ts.account.center.dto.flow.PayAccountFlowsDto;
import com.t3.ts.pay.center.api.constants.NumConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * FreezeBalanceResVo
 *
 * <AUTHOR>
 * @date 2022/06/07
 */
@Data
@ApiModel("冻结明细VO")
public class FreezeRecordResVo {

    @ApiModelProperty(value = "变更类型（3:冻结; 4:解冻）")
    private Integer changedType;

    @ApiModelProperty(value = "冻结金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "冻结日期")
    private Date date;

    @ApiModelProperty(value = "冻结原因")
    private String reason;

    /**
     * buildFreezeCordeResVo
     *
     * @param payAccountFlowsDto PayAccountFlowsDto
     * @return FreezeRecordResVo
     */
    public static FreezeRecordResVo buildFreezeCordeResVo(PayAccountFlowsDto payAccountFlowsDto) {
        FreezeRecordResVo resVo = new FreezeRecordResVo();
        resVo.setAmount(new BigDecimal(payAccountFlowsDto.getChangedFreezeBalance()).movePointLeft(
                NumConstants.NUM_2).setScale(NumConstants.NUM_2, RoundingMode.HALF_UP));
        resVo.setDate(payAccountFlowsDto.getCreateTime());
        resVo.setReason(payAccountFlowsDto.getRemark());
        resVo.setChangedType(payAccountFlowsDto.getChangedType());
        return resVo;
    }

}
