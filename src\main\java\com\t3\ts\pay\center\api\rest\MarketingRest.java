package com.t3.ts.pay.center.api.rest;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.ChargeOrderDto;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.pay.center.api.dto.wallet.QuotaDto;
import com.t3.ts.pay.center.api.service.ResponseConverter;
import com.t3.ts.result.Response;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 * 营销Rest接口封装
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MarketingRest extends BaseRest implements InitializingBean, ResponseConverter {

    @Resource
    private SlbConfig slbConfig;
    private OkHttpClient okHttpClient = null;
    private OkHttpClient okHttpClient2s = null;
    @Resource
    private SwitchConfig switchConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(500, TimeUnit.MILLISECONDS)
                .writeTimeout(500, TimeUnit.MILLISECONDS)
                .readTimeout(500, TimeUnit.MILLISECONDS)
                .build();
        okHttpClient2s = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(2000, TimeUnit.MILLISECONDS)
                .writeTimeout(2000, TimeUnit.MILLISECONDS)
                .readTimeout(2000, TimeUnit.MILLISECONDS)
                .build();
    }
    /**
     * 按乘客uuid获取用户组
     *
     * @param passengerUuid 乘客uuid
     * @return {@link List<String>}
     */
    public List<String> getUserGroupByPassengerUuid(String passengerUuid) {
        JSONObject entity = new JSONObject();
        entity.put("uuid", passengerUuid);
        try {
            String postHttp = sendPostHttpClient(okHttpClient,
                    slbConfig.getMarketing() + "/userGroup" + "/queryUserGroup", entity.toJSONString());
            if (StringUtils.isNotBlank(postHttp)) {
                Response resp = JSONObject.parseObject(postHttp, Response.class);
                if (null != resp && resp.isSuccess() && null != resp.getData()) {
                    return JSONUtil.parseArray(resp.getData()).toList(String.class);
                }
            }
        } catch (Exception e) {
            log.error("getUserGroupByPassengerUuid error", e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据ID获取用户信息
     *
     * @param passengerUuid 乘客uuid
     * @return {@link JSONObject} view  AccountInfoDto.class
     */
    public JSONObject getMemberInfo(String passengerUuid) {
        JSONObject entity = new JSONObject();
        entity.put("userId", passengerUuid);
        entity.put("memberSysCode", "T3000001");
        String postHttp = sendPostHttp(slbConfig.getMember() + "/account/info/getUserAccount",
                entity.toJSONString());
        if (StringUtils.isNotBlank(postHttp)) {
            JSONObject object = JSON.parseObject(postHttp);
            return object.getJSONObject("data");
        }
        return new JSONObject();
    }

    /**
     * 根据ID获取用户信息
     *
     * @param cityCode 城市code
     * @param userId   乘客uuid
     * @return {@link JSONObject} view  AccountInfoDto.class
     */
    public boolean getRechargeSwitch(String cityCode, String userId) {
        JSONObject entity = new JSONObject();
        entity.put("cityCode", cityCode);
        entity.put("userId", userId);
        try {
            String postHttp = sendPostHttp(slbConfig.getMember() + "/recharge/getSwitch",
                    entity.toJSONString());
            log.info("MarketingRest.getRechargeSwitch,request:{},response:{}", entity.toJSONString(), postHttp);
            if (StringUtils.isNotBlank(postHttp)) {
                Response resp = JSONObject.parseObject(postHttp, Response.class);
                if (resp.isSuccess() && ObjectUtil.isNotNull(resp.getData())) {
                    return (boolean) resp.getData();
                }
            }
            return false;
        } catch (Exception e) {
            log.warn("MarketingRest.getRechargeSwitch post failed", e);
            return false;
        }
    }

    /**
     * 获取用户专享信息
     *
     * @param passengerUuid 乘客uuid
     * @param cityCode      cityCode
     * @return {@link JSONObject} view  AccountInfoDto.class
     */
    public JSONObject showAppGuide(String passengerUuid, String cityCode) {
        JSONObject entity = new JSONObject();
        entity.put("passengerUuid", passengerUuid);
        entity.put("cityCode", cityCode);
        try {
            String postHttp = sendPostHttp(slbConfig.getMarketing() + "/migration/showAppGuide",
                    entity.toJSONString());
            log.info("MarketingRest.showAppGuide,request:{},response:{}", passengerUuid, postHttp);
            if (StringUtils.isNotBlank(postHttp)) {
                JSONObject object = JSON.parseObject(postHttp);
                return object.getJSONObject("data");
            }
        } catch (Exception e) {
            log.error("MarketingRest.showAppGuide fail,passengerUuid:{}", passengerUuid);
        }
        return new JSONObject();
    }

    /**
     * 获取乘推乘冒泡信息
     *
     * @param dto dto
     * @return JSONObject
     */
    public JSONObject getRecommendPassenger(QueryDto dto) {
        JSONObject entity = new JSONObject();
        entity.put("userId", dto.getUserId());
        entity.put("cityCode", dto.getAdCode());
        entity.put("source", dto.getSource());
        entity.put("mediumType", getTerminal(dto.getGrayVersion()));

        try {
            String postHttp = sendPostHttp(slbConfig.getMarketing() + "/api/marketing/side/bar/recommendPassenger",
                    entity.toJSONString());
            log.info("MarketingRest.recommendPassenger,request:{},response:{}", entity, postHttp);
            if (StringUtils.isNotBlank(postHttp)) {
                JSONObject object = JSON.parseObject(postHttp);
                return object.getJSONObject("data");
            }
        } catch (Exception e) {
            log.error("MarketingRest.recommendPassenger fail,passengerUuid:{}", entity);
        }
        return new JSONObject();
    }

    /**
     * 获取端 1.app 8 小程序 0 通用
     *
     * @param grayVersion grayVersion
     * @return terminal
     */
    private int getTerminal(String grayVersion) {
        if (grayVersion == null) {
            return 0;
        }
        if (grayVersion.startsWith(CommonNumConst.WX)) {
            return NumConstants.NUM_2;
        } else if (grayVersion.startsWith(CommonNumConst.AX)) {
            return NumConstants.NUM_3;
        } else {
            return NumConstants.NUM_1;
        }
    }

    /**
     * 查询借钱额度
     * @param passengerUuid passengerUuid
     * @return QuotaDto
     */
    public QuotaDto quotaStatusQuery(String passengerUuid) {
        // 加个开关，防止供给侧接口出问题
        if (!switchConfig.isQuotaSwitch()) {
            return null;
        }
        try {
            JSONObject entity = new JSONObject();
            entity.put("passengerUuid", passengerUuid);
            // 设置2s 超时
            String postHttp = sendPostHttpClient(okHttpClient2s, slbConfig.getMarketingAssistGateway()
                            + "/api/finance/quotaStatusQueryByPassengerUuid",
                    entity.toJSONString());
            if (StringUtils.isNotBlank(postHttp)) {
                Response<QuotaDto> response = JSONObject.parseObject(postHttp,
                        new TypeReference<Response<QuotaDto>>() {});
                log.info("MarketingRest.quotaStatusQueryByPassengerUuid,request:{},response:{}",
                        entity.toJSONString(), JSON.toJSONString(response));
                if (null != response) {
                    return response.getData();
                }
            }
        } catch (Exception e) {
            log.warn("MarketingRest.quotaStatusQueryByPassengerUuid error", e);
        }
        return null;
    }

    public boolean querySupportCard(String orderUuid) {
        JSONObject entity = new JSONObject();
        entity.put("orderSeq", orderUuid);
        try {
            String postHttp = sendPostHttpClient(okHttpClient,
                    slbConfig.getMarketingAssistGateway() + "/api/charge/querySupportCard",
                    entity.toJSONString());
            log.info("MarketingRest.querySupportCard,request:{},response:{}", entity.toJSONString(), postHttp);
            if (StringUtils.isNotBlank(postHttp)) {
                Response resp = JSONObject.parseObject(postHttp, Response.class);
                if (resp.isSuccess() && ObjectUtil.isNotNull(resp.getData())) {
                    return (boolean) resp.getData();
                }
            }
            return false;
        } catch (Exception e) {
            log.warn("MarketingRest.querySupportCard post failed", e);
            return false;
        }
    }

    /**
     * 查询司机充电订单详情
     * @param orderUuid orderUuid
     * @return
     */
    public ChargeOrderDto queryChargingElectric(String orderUuid) {
        JSONObject entity = new JSONObject();
        entity.put("orderSeq", orderUuid);
        try {
            String postHttp = sendPostHttpClient(okHttpClient,
                    slbConfig.getMarketingAssistGateway() + "/api/charge/queryOrderDetail",
                    entity.toJSONString());
            log.info("MarketingRest.queryChargingElectric,request:{},response:{}", entity.toJSONString(), postHttp);
            if (StringUtils.isNotBlank(postHttp)) {
                Response resp = JSON.parseObject(postHttp, Response.class);
                if (resp.isSuccess() && ObjectUtil.isNotNull(resp.getData())) {
                    ChargeOrderDto chargeOrderDto = JSON.parseObject(JSON.toJSONString(resp.getData()), ChargeOrderDto.class);
                    return chargeOrderDto;
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("MarketingRest.querySupportCard post failed", e);
            return null;
        }
    }


}
