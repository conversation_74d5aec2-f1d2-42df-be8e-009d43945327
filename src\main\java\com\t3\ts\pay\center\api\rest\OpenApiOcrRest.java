package com.t3.ts.pay.center.api.rest;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.t3.ts.integrated.dto.security.BankCardAuthenticationReqDto;
import com.t3.ts.integrated.dto.security.BankCardAuthenticationRespDto;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2023/10/11
 */
@Component
@Slf4j
public class OpenApiOcrRest extends BaseRest {

    /**
     * 银行卡四要素实名认证
     */
    private static final String BANK_CARD_AUTHENTICATION = "/center/v1/security/bankCardAuthentication";
    @Resource
    private SlbConfig slbConfig;

    /**
     * 银行四要素校验
     *
     * @param bankCardAuthenticationReqDto bankCardAuthenticationReqDto
     * @return BankCardAuthenticationRespDto
     */
    public Response<BankCardAuthenticationRespDto> bankCardAuthentication(
            BankCardAuthenticationReqDto bankCardAuthenticationReqDto) {

        String postHttp = sendPostHttp(slbConfig.getOpenPortal() + BANK_CARD_AUTHENTICATION,
                JSONObject.toJSONString(bankCardAuthenticationReqDto));
        if (StrUtil.isNotBlank(postHttp)) {
            Response<BankCardAuthenticationRespDto> response = JSONObject.parseObject(postHttp,
                    new TypeReference<Response<BankCardAuthenticationRespDto>>() {
                    });
            return response;
        }
        return null;
    }
}
