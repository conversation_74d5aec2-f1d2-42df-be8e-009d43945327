package com.t3.ts.pay.center.api.controller.invoice.v3;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/17 17:22
 * @Description: 获取发票详情test类    /api/passenger/v3/invoice/list
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class InvoiceListTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void getInfoTest() {
//        InvoiceRelevantReq invoiceReq = new InvoiceRelevantReq();
//        invoiceReq.setCurrPage(0);
//        invoiceReq.setPageSize(10);
//        invoiceReq.setType(2);
//        invoiceBusiness.getInvoiceList(invoiceReq, "2d53a198cef9455a891c82956ee8bf46");
    }
}
