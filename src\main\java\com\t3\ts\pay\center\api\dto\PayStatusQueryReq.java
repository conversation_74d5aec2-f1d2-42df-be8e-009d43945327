package com.t3.ts.pay.center.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class PayStatusQueryReq {

    /**
     * userIds 逗号分隔
     */
    @ApiModelProperty(value = "用户ID集合(逗号分隔,企业用车需要将企业用户ID和个人用户ID都传入)")
//    @NotBlank(message = "userIds不能为空")
    private String userIds;

    /**
     * 外部业务单号 - 行程ID  预付款ID 等
     */
    @ApiModelProperty(value = "业务单号-行程ID、预付款ID等")
    @NotBlank(message = "orderUuid不能为空")
    private String orderUuid;
    /**
     * 结算ID
     */
    private String settleId;
    /**
     * 类型
     */
    private String type;
}
