package com.t3.ts.pay.center.api.util;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.context.ContextUtil;
import com.t3.ts.pay.center.api.config.PayCenterInterceptorConfig;
import com.t3.ts.pay.center.api.config.interceptor.PassengerTokenInterceptor;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * HttpRequest工具
 *
 * <AUTHOR>
 * @date 19 /7/11
 */
@Slf4j
public class HttpRequestUtil {

    private static final String MICRO_MESSENGER = "micromessenger";
    private static final String ALI_PAY_CLIENT = "alipayclient";

    /**
     * HttpRequest中的body转换成Json (content-type=application/json)
     *
     * @param request the request
     * @return json param from json
     */
    public static JSONObject getJSONParamFromJson(HttpServletRequest request) {
        JSONObject jsonParam = null;
        try {
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));

            StringBuilder sb = new StringBuilder();
            String line = null;
            while ((line = streamReader.readLine()) != null) {
                sb.append(line);
            }
            jsonParam = JSONObject.parseObject(sb.toString());
        } catch (Exception e) {
            log.error("HttpRequestUtil.getJSONParam error: {}", e.getMessage(), e);
        }
        return jsonParam;
    }

    /**
     * Gets map param from form.
     *
     * @param request the request
     * @return the map param from form
     */
    public static Map<String, Object> getMapParamFromForm(HttpServletRequest request) {
        Map<String, Object> resultMap = null;

        try {
            if (!CollectionUtils.isEmpty(request.getParameterMap())) {
                resultMap = new HashMap<>(CommonNumConst.NUM_16);
                for (String key : request.getParameterMap().keySet()) {
                    resultMap.put(key, request.getParameter(key));
                }

                return resultMap;
            }

            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));

            StringBuilder sb = new StringBuilder();
            String line = null;
            while ((line = streamReader.readLine()) != null) {
                sb.append(line);
            }
            resultMap = getQueryParams(sb.toString());
        } catch (Exception e) {
            log.error("HttpRequestUtil.getMapParamFromForm error: {}", e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * Gets query params.
     *
     * @param url the url
     * @return the query params
     */
    private static Map<String, Object> getQueryParams(String url) {
        try {
            Map<String, Object> params = new HashMap<>(CommonNumConst.NUM_16);
            String[] splits = url.split("&");
            for (String param : splits) {
                String[] pair = param.split("=");
                String key = URLDecoder.decode(pair[0], "UTF-8");
                String value = "";
                if (pair.length > 1) {
                    value = URLDecoder.decode(pair[1], "UTF-8");
                }
                params.put(key, value);
            }
            return params;
        } catch (UnsupportedEncodingException e) {
            log.error("HttpRequestUtil.getQueryParams error: {}", e.getMessage(), e);
        }

        return new HashMap<>(CommonNumConst.NUM_16);
    }

    /**
     * 解析请求头中UA信息
     *
     * @param request the request
     * @return the integer
     */
    public static Integer parseUa(HttpServletRequest request) {
        String agent = request.getHeader("user-agent").toLowerCase();
        return getChannel(agent);
    }

    /**
     * 解析请求头中UA信息
     *
     * @return the integer
     */
    public static Integer parseUa() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attr.getRequest();
        String agent = request.getHeader("user-agent").toLowerCase();
        return getChannel(agent);
    }

    /**
     * 得到渠道
     *
     * @param agent 代理
     * @return {@link Integer}
     */
    public static Integer getChannel(String agent) {
        if (agent.contains(MICRO_MESSENGER)) {
            return EnumPayOrderChannel.WEIXIN_JSAPI.getCode();
        } else if (agent.contains(ALI_PAY_CLIENT)) {
            return EnumPayOrderChannel.ALIPAY_JSAPI.getCode();
        } else {
            return 0;
        }
    }
    /**
     * 获取用户uid
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getUserUid(HttpServletRequest request) {
        if (SpringUtils.getBean(PayCenterInterceptorConfig.class).isInterceptorEnabled()) {
            Object uuid = request.getAttribute("uuid");
            return uuid != null ? uuid.toString() : null;
        }
        return ContextUtil.getUserId();
    }

    /**
     * 让用户移动
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getUserMobile(HttpServletRequest request) {
        Object mobile = request.getAttribute(PassengerConstants.REQUEST_ATTRIBUTE_PASSENGER_MOBILE);
        return null != mobile ? mobile.toString() : null;
    }

    /**
     * 获取grayBuild版本号
     *
     * @param request http请求
     * @return String
     */
    public static String getGrayBuild(HttpServletRequest request) {
        return request.getHeader("grayBuild");
    }

    /**
     * 获取 grayVersion 版本号
     *
     * @param request http请求
     * @return String
     */
    public static String getGrayVersion(HttpServletRequest request) {
        return request.getHeader("grayVersion");
    }

    /**
     * 得到指纹设备令牌
     * 获取设备指纹token
     *
     * @param request 请求
     * @return {@link String}
     */
    public static String getDeviceFingerPrintToken(HttpServletRequest request) {

        return request.getHeader(PassengerTokenInterceptor.REQUEST_HEADER_DEVICE_FINGERPRINT_TOKEN);
    }
}
