package com.t3.ts.pay.center.api.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.dto.driverwallet.ProtocolDto;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户协议服务REST接口
 * 提供用户协议相关操作，包括获取未签署协议和签署协议功能
 * 通过OkHttpClient与外部服务通信，集成配置中心实现动态参数配置
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProtocolServiceRest extends BaseRest implements InitializingBean {

    @Autowired
    private SlbConfig slbConfig;

    private OkHttpClient okHttpClientTimeOut = null;

    /**
     * 客户端连接超时时间
     */
    @Value(value = "${platform.switch.okClient.protocol.connectTimeout:100}")
    private Integer okClientConnectTimeout;

    /**
     * 客户端写超时时间
     */
    @Value(value = "${platform.switch.okClient.protocol.writeTimeout:100}")
    private Integer okClientWriteTimeout;

    /**
     * 客户端读超时时间
     */
    @Value(value = "${platform.switch.okClient.protocol.readTimeout:100}")
    private Integer okClientReadTimeout;


    /**
     * 初始化OkHttpClient实例
     * 配置连接超时、写超时、读超时参数
     * 通过Spring的InitializingBean接口在bean初始化后执行
     */
    @Override
    public void afterPropertiesSet() {
        okHttpClientTimeOut = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(okClientConnectTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(okClientWriteTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(okClientReadTimeout, TimeUnit.MILLISECONDS)
                .build();
    }


    /**
     * 获取用户未签署的协议列表
     * 
     * @param userId 用户ID
     * @return 协议信息列表（ProtocolDto）
     *         当请求失败或无数据时返回null
     */
    public List<ProtocolDto> getUserUnSignAgreements(String userId, List<Integer> agreementTypes, Integer agreementObject) {
        try {
            // 构建请求参数
            JSONObject param = new JSONObject();
            //钱包代扣协议编码 89
            param.put("agreementTypes", agreementTypes);
            param.put("agreementObject", agreementObject);
            param.put("userUuid", userId);
            
            // 构建请求URL并发送HTTP POST请求
            String url = slbConfig.getKzDataCenterGateway() + "/agreementVersion/getUserUnSignAgreements";
            String postHttp = sendPostHttpClient(okHttpClientTimeOut, url, JSON.toJSONString(param));
            
            // 处理响应数据
            if (StringUtils.isBlank(postHttp)) {
                return null;
            }
            JSONObject response = JSONObject.parseObject(postHttp, JSONObject.class);
            JSONArray dataList = response.getJSONArray("data");
            if (CollectionUtils.isEmpty(dataList)) {
                return null;
            }
            List<ProtocolDto> protocolDtoList = new ArrayList<>();
            for (Object a : dataList) {
                // 解析单个协议数据
                JSONObject json = JSON.parseObject(JSON.toJSONString(a), JSONObject.class);
                if(!agreementTypes.contains(json.getInteger("agreementType"))){
                    continue;
                }
                ProtocolDto protocolDto = new ProtocolDto();
                protocolDto.setCode(json.getString("agreementCode"));
                protocolDto.setUrl(json.getString("protocolLink"));
                protocolDto.setName(json.getString("agreementName"));
                protocolDtoList.add(protocolDto);
            }
            return protocolDtoList;
        } catch (Exception e) {
            log.error("ProtocolServiceRest getUserUnSignAgreements error {}", e.getMessage());
        }
        return null;
    }


    /**
     * 用户勾选签约协议
     *
     * @param agreementCodes 协议编码列表
     * @param userId 用户ID
     * @return Boolean 操作结果
     *         异常情况下返回false
     */
    public Boolean signAgreement(String userId, String driverMobile,
                                 List<String> agreementCodes, Integer agreementType, Integer agreementObject) {
        try {
            JSONObject param = new JSONObject();
            param.put("agreementCodes", agreementCodes);
            param.put("agreementType", agreementType);
            param.put("agreementObject", agreementObject);
            param.put("userUuid", userId);
            param.put("mobile", driverMobile);
            String url = slbConfig.getKzDataCenterGateway() + "/agreementVersion/driver/signAgreement";
            String postHttp = sendPostHttpClient(okHttpClientTimeOut, url, JSON.toJSONString(param));
            
            // 解析响应结果
            if (StringUtils.isNotBlank(postHttp)) {
                JSONObject responseBody = JSON.parseObject(postHttp);
                return responseBody.getBoolean("data");
            }
        } catch (Exception e) {
            log.error("ProtocolServiceRest signAgreement error {}", e.getMessage());
        }
        return false;
    }
}
