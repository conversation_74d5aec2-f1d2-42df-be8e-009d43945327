package com.t3.ts.pay.center.api.service.invoice;

import com.t3.ts.pay.center.api.dto.invoice.AddInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.invoice.DelInvoiceHeaderReq;
import com.t3.ts.pay.center.api.dto.vo.invoice.InvoiceHeaderVO;
import com.t3.ts.result.Response;

/**
 * @Author: ivy
 * @Date: 2021/10/11 19:02
 * @Description: 常用抬头接口
 */
public interface InvoiceHeaderService {

    /**
     * saveOrUpdateInvoiceHeader
     *
     * @param req      请求
     * @param userUuid 乘客id
     * @return 处理结果
     */
    Response<?> saveOrUpdateInvoiceHeader(AddInvoiceHeaderReq req, String userUuid);

    /**
     * addInvoiceHeader
     *
     * @param req 请求
     * @return 处理结果
     */
    Response<?> addInvoiceHeader(AddInvoiceHeaderReq req);

    /**
     * deleteInvoiceHeader
     *
     * @param req      请求
     * @param userUuid 乘客id
     * @return 处理结果
     */
    Response<?> deleteInvoiceHeader(DelInvoiceHeaderReq req, String userUuid);

    /**
     * queryInvoiceHeader
     *
     * @param req      请求
     * @param userUuid 乘客id
     * @return 处理结果
     */
    Response<?> queryInvoiceHeader(AddInvoiceHeaderReq req, String userUuid);



    /**
     * 查询一年内最近三次的开票成功发票抬头信息
     *
     * @param headerType 头类型
     * @param userUid    用户uid
     * @return {@link Response<  InvoiceHeaderVO  >}
     */
    Response<InvoiceHeaderVO> queryInvoiceHeader(Integer headerType, String userUid);
}
