package com.t3.ts.pay.center.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.service.AccountUnifiedService;
import com.t3.ts.pay.center.api.util.Constants;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.service.SettlementUnifiedService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletResponse;

import static org.mockito.Mockito.*;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 16:20
 */
public class RouteServiceTest {
    @Mock
    UnifiedService unifiedService;
    @Mock
    AccountUnifiedService accountUnifiedService;
    @Mock
    SettlementUnifiedService settlementUnifiedService;
    @Mock
    Logger log;
    @InjectMocks
    RouteService routeService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testRoute() throws Exception {
        MockHttpServletRequest mockedRequest = Mockito.mock(MockHttpServletRequest.class);
        HttpServletResponse mockResponse = Mockito.mock(HttpServletResponse.class);
        routeService.route(mockedRequest, mockResponse);
    }

    @Test
    public void testDoRoute() throws Exception {
        MockHttpServletRequest mockedRequest = Mockito.mock(MockHttpServletRequest.class);
        HttpServletResponse mockResponse = Mockito.mock(HttpServletResponse.class);

        Response response = new Response("", "", true);
        when(unifiedService.handle(any())).thenReturn(response);
        routeService.doRoute(mockedRequest, mockResponse, new JSONObject(), "t3pay.trade");
    }

    @Test
    public void testDoRoute1() throws Exception {
        MockHttpServletRequest mockedRequest = Mockito.mock(MockHttpServletRequest.class);
        HttpServletResponse mockResponse = Mockito.mock(HttpServletResponse.class);

        Response response = new Response("", "", true);
        when(unifiedService.handle(any())).thenReturn(response);
        routeService.doRoute(mockedRequest, mockResponse, new JSONObject(), Constants.ACCOUNT_CENTER_PREFIX);
    }

    @Test
    public void testDoRoute2() throws Exception {
        MockHttpServletRequest mockedRequest = Mockito.mock(MockHttpServletRequest.class);
        HttpServletResponse mockResponse = Mockito.mock(HttpServletResponse.class);

        Response response = new Response("", "", true);
        when(unifiedService.handle(any())).thenReturn(response);
        routeService.doRoute(mockedRequest, mockResponse, new JSONObject(), Constants.SETTLEMENT_CENTER_PREFIX);
    }

    @Test
    public void testCheckMethod() throws Exception {
        routeService.checkMethod("1");
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme