/**
 * <AUTHOR>
 * @date ：Created in 2021/4/8 16:12
 * @description：微信支付分配置
 */

package com.t3.ts.pay.center.api.config.valueconfig;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/8 16:12
 */
@Getter
@Setter
@Component
public class WechatPayPointsConfig {
    /**
     * 开关是否打开
     */
    @Value("${wechatPayPoints.enabled}")
    private Boolean enabled;

    /**
     * 按钮标题
     */
    @Value("${wechatPayPoints.btnTitle}")
    private String btnTitle;

    /**
     * 按钮副标题
     */
    @Value("${wechatPayPoints.btnSubTitle}")
    private String btnSubTitle;
    
    /**
     * 按钮图标url
     */
    @Value("${wechatPayPoints.btnIcon}")
    private String btnIcon;


    /**
     * 微信支付分-支付并签约白名单-这里配置userId
     */
    @Value("${wechatPayPoints.payAndSign.aliWhiteList:''}")
    private String payAndSignAliWhiteList;

    /**
     * 微信支付分-支付并签约百分比
     */
    @Value("${wechatPayPoints.payAndSign.aliPercent:-1}")
    private Integer payAndSignAliPercent;
    /**
     * 预付款引导开关是否打开
     */
    @Value("${wechatPayPoints.prePay.enabled:true}")
    private Boolean prePayEnabled;
}
