package com.t3.ts.pay.center.api.util;

import com.t3.ts.pay.center.api.config.interceptor.HttpTraceLogInterceptor;
import com.t3.ts.rpc.sentinel.thread.async.T3ThreadPoolExecutor;
import com.t3.ts.utils.StringUtils;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date: 2019-09-04 19:56
 * @des: 线程池工具类：解决加入：Mdc日志（traceId）
 */
public class ThreadPoolMdcUtil {

    /**
     * 如果没有跟踪id，则设置跟踪id
     */
    public static void setTraceIdIfAbsent() {
        if (MDC.get(HttpTraceLogInterceptor.TRACE_ID) == null) {
            MDC.put(HttpTraceLogInterceptor.TRACE_ID, StringUtils.buildUUID());
        }
    }

    /**
     * 设置跟踪id
     */
    public static void setTraceId() {
        MDC.put(HttpTraceLogInterceptor.TRACE_ID, StringUtils.buildUUID());
    }

    /**
     * 设置跟踪id
     *
     * @param traceId 跟踪id
     */
    public static void setTraceId(String traceId) {
        MDC.put(HttpTraceLogInterceptor.TRACE_ID, traceId);
    }

    /**
     * 包
     * @param <T> This describes my type parameter
     * @param callable 可赎回
     * @param context  上下文
     * @return {@link Callable<T>}
     */
    public static <T> Callable<T> wrap(final Callable<T> callable, final Map<String, String> context) {
        return () -> {
            if (context == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(context);
            }
            setTraceIdIfAbsent();
            try {
                return callable.call();
            } finally {
                MDC.clear();
            }
        };
    }

    /**
     * 包
     *
     * @param runnable 可运行
     * @param context  上下文
     * @return {@link Runnable}
     */
    public static Runnable wrap(final Runnable runnable, final Map<String, String> context) {
        return () -> {
            if (context == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(context);
            }
            setTraceIdIfAbsent();
            try {
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }

    /**
     * 线程池执行器mdc包装器
     *
     * <AUTHOR>
     * @date 2020/10/26
     */
    public static class ThreadPoolExecutorMdcWrapper extends T3ThreadPoolExecutor {
        /**
         * 线程池执行器mdc包装器
         *
         * @param poolName        池名称
         * @param corePoolSize    核心池大小
         * @param maximumPoolSize 最大池大小
         * @param keepAliveTime   保持生存时间
         * @param unit            单元
         * @param workQueue       工作队列
         */
        public ThreadPoolExecutorMdcWrapper(
                String poolName, int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
                BlockingQueue<Runnable> workQueue) {
            super(poolName, corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
        }

        /**
         * 线程池执行器mdc包装器
         *
         * @param corePoolSize    核心池大小
         * @param maximumPoolSize 最大池大小
         * @param keepAliveTime   保持生存时间
         * @param unit            单元
         * @param workQueue       工作队列
         * @param threadFactory   工厂螺纹
         */
        public ThreadPoolExecutorMdcWrapper(
                int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
                BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
        }

        /**
         * 执行
         *
         * @param task 任务
         */
        @Override
        public void execute(Runnable task) {
            super.execute(ThreadPoolMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
        }

        /**
         * 提交
         *
         * @param task   任务
         * @param result 结果
         * @return {@link Future<T>}
         */
        @Override
        public <T> Future<T> submit(Runnable task, T result) {
            return super.submit(ThreadPoolMdcUtil.wrap(task, MDC.getCopyOfContextMap()), result);
        }

        /**
         * 提交
         *
         * @param task 任务
         * @return {@link Future<T>}
         */
        @Override
        public <T> Future<T> submit(Callable<T> task) {
            return super.submit(ThreadPoolMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
        }

        /**
         * 提交
         *
         * @param task 任务
         * @return {@link Future<?>}
         */
        @Override
        public Future<?> submit(Runnable task) {
            return super.submit(ThreadPoolMdcUtil.wrap(task, MDC.getCopyOfContextMap()));
        }
    }
}
