package com.t3.ts.pay.center.api.business;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.result.Response;

/**
 * <AUTHOR>
 * @date: 2019-11-13 15:21
 * @des: 支付通道服务接口
 */
public interface PayChannelService {

    /**
     * 获取充值支付信息
     * @param paymentInfoBo 入参
     * @return 出参
     */
    Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo);
}
