package com.t3.ts.pay.center.api.controller.invoice.v1;

import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/18 16:41
 * @Description: 查询发票公告通知test类    /api/passenger/v1/invoice/queryUserNotice
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
public class QueryUserNoticeTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void getInfoTest() {
//        Response<NoticeUserVo> noticeUserVoResponse = invoiceBusiness.queryUserNotice("320100");
//        Assert.assertTrue(noticeUserVoResponse.isSuccess());
    }
}
