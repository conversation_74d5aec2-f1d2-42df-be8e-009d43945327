package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.bo.PayDeskInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.dto.ChartedAdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.result.Response;

/**
 * <AUTHOR>
 * @date: 2019-11-15 21:34
 * @des: 行程支付相关接口类
 */
public interface CharterRoutePayService {


    /**
     * 获取预付款（充值）支付信息
     *
     * @param advanceRechargeDTO advanceRechargeDTO
     * @return RechargePayBo
     */
    Response<RechargePayBo> advanceRecharge(ChartedAdvanceRechargeDTO advanceRechargeDTO);

    /**
     * 获取收银台信息（不支持企业用车）
     *
     * @param routePlanUuid 行程uuid
     * @param passengerUuid 乘客uuid
     * @return Response
     */
    Response<PayDeskInfoBo> getPayDeskInfo(String routePlanUuid, String passengerUuid);

    /**
     * 发起行程支付
     *
     * @param routePayDTO routePayDTO
     * @return RechargePayBo
     */
    Response<RechargePayBo> launchRoutePay(RoutePayDTO routePayDTO);
}
