package com.t3.ts.pay.center.api.util;

import com.google.common.collect.Lists;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Description: 支付方式转换
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/28/0028 10:17
 */
public class PayWayConvert {

    /**
     * 支付方式转换成枚举
     *
     * @param payTypeList 支付类型列表
     * @return {@link PaywayEnum[]}
     */
    public static PaywayEnum[] getPayWayEnum(List<Integer> payTypeList) {
        if (CollectionUtils.isEmpty(payTypeList)) {
            return null;
        }
        // 支付渠道类型
        List<PaywayEnum> payWayEnumList = new ArrayList<>();
        for (Integer payTpe : payTypeList) {
            if (payTpe.equals(EnumPayOrderChannel.ALIPAY.getCode())) {
                payWayEnumList.add(PaywayEnum.ALIPAY_APP);
            } else if (payTpe.equals(EnumPayOrderChannel.ALIPAY_MINI.getCode())) {
                payWayEnumList.add(PaywayEnum.ALIPAY_MINI);
            } else if (payTpe.equals(EnumPayOrderChannel.ALIPAY_H5.getCode())) {
                payWayEnumList.add(PaywayEnum.ALIPAY_H5);
            } else if (payTpe.equals(EnumPayOrderChannel.WEIXIN.getCode())) {
                payWayEnumList.add(PaywayEnum.WECHAT_APP);
            } else if (payTpe.equals(EnumPayOrderChannel.BALANCE.getCode())) {
                payWayEnumList.add(PaywayEnum.BALANCE);
            } else if (payTpe.equals(EnumPayOrderChannel.NETCOM.getCode())) {
                payWayEnumList.add(PaywayEnum.CMBCONENET_H5);
            } else if (payTpe.equals(EnumPayOrderChannel.NETCOM_MINI_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.CMBCONENET_MINI);
            } else if (payTpe.equals(EnumPayOrderChannel.UNIONPAY.getCode())) {
                payWayEnumList.add(PaywayEnum.UNION_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode())) {
                payWayEnumList.add(PaywayEnum.WECHAT_MINIPROGRAM);
            } else if (payTpe.equals(EnumPayOrderChannel.INTEGRAL.getCode())) {
                payWayEnumList.add(PaywayEnum.INTEGRAL);
            } else if (payTpe.equals(EnumPayOrderChannel.CMB_AGGREGATE_WX_MINI_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.CMB_AGGREGATE_WX_MINI_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.CMB_AGGREGATE_WX_UNIFIED_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.CMB_AGGREGATE_ALI_APP_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.UNION_QUICK_PASS_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.UNION_QUICK_PASS_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.UNION_JS_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.UNION_JS_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.WEIXIN_H5.getCode())) {
                payWayEnumList.add(PaywayEnum.WEIXIN_H5);
            } else if (payTpe.equals(EnumPayOrderChannel.WEIXIN_JSAPI.getCode())) {
                payWayEnumList.add(PaywayEnum.WEIXIN_JSAPI);
            } else if (payTpe.equals(EnumPayOrderChannel.CMBLIFE.getCode())) {
                payWayEnumList.add(PaywayEnum.CMBLIFE);
            } else if (payTpe.equals(EnumPayOrderChannel.DOUYIN_MINI_PAY.getCode())) {
                payWayEnumList.add(PaywayEnum.DOUYIN_MINI_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode())) {
                payWayEnumList.add(PaywayEnum.CHARGING_ELECTRICITY_CARD_PAY);
            } else if (payTpe.equals(EnumPayOrderChannel.PAY_ACCOUNT_SALARY.getCode())) {
                payWayEnumList.add(PaywayEnum.DRIVER_SALARY_PAY);
            }
        }
        return payWayEnumList.toArray(new PaywayEnum[payWayEnumList.size()]);
    }

    /**
     * 得到payway枚举
     *
     * @param enumPayOrderChannel enum订单支付通道
     * @return {@link PaywayEnum[]}
     */
    public static PaywayEnum[] getPayWayEnum(Integer enumPayOrderChannel) {
        return getPayWayEnum(Lists.newArrayList(enumPayOrderChannel));
    }

    /**
     * 得到第三方支付类型
     *
     * @param payTypeList 支付类型列表
     * @return int
     */
    public static Integer getThirdType(List<Integer> payTypeList) {
        if (CollectionUtils.isEmpty(payTypeList)) {
            return null;
        }
        Optional<Integer> optional = payTypeList.parallelStream().filter(payType -> EnumPayOrderChannel.INTEGRAL.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.BALANCE.getCode() != payType)
                .findFirst();
        return optional.orElse(null);
    }

    /**
     * 是否是出租车扫码付
     *
     * @param payOrderType payOrderType
     * @return boolean
     */
    public static boolean isTaxiScanPay(Integer payOrderType) {
        return payOrderType != null && EnumPayOrderType.DRIVER_RECOMMEND_PASSENGER_PAY_ROUTE.getCode() == payOrderType;
    }

    /**
     * 支付方式转化
     *
     * @param payTypeList payTypeList
     * @return List<String>
     */
    public static List<String> getPayWay(List<Integer> payTypeList) {
        PaywayEnum[] paywayEnum = PayWayConvert.getPayWayEnum(payTypeList);
        List<String> payWay = Lists.newArrayList();
        // 支付方式
        if (null != paywayEnum) {
            for (PaywayEnum anEnum : paywayEnum) {
                if (Objects.nonNull(anEnum)) {
                    payWay.add(anEnum.getCode());
                }
            }
        }
        return payWay;
    }
}
