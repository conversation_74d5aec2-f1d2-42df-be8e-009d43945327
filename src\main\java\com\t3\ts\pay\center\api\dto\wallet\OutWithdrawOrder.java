package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class OutWithdrawOrder {

    @NotBlank(message = "身份标识不能为空")
    private String confirmToken;

    @NotBlank(message = "支付宝账号姓名不能为空")
    private String accountName;

    @NotBlank(message = "支付宝账号不能为空")
    private String alipayNo;

    @NotBlank(message = "请填写正确的手机号")
    @Length(min = 11, max = 11, message = "请填写正确的手机号")
    private String mobile;

    @NotNull(message = "平台提现金额不能为空")
    private BigDecimal platformAmount;

    @NotNull(message = "奖励金额不能为空")
    private BigDecimal awardAmount;
}
