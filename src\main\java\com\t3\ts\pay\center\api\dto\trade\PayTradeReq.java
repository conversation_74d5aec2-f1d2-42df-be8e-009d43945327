package com.t3.ts.pay.center.api.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: YJ
 * @Date: 2019/5/10 14:26
 * @Version: v1
 * @Description: 乘客支付车费入参
 */
@Data
@ApiModel
public class PayTradeReq implements Serializable {

    private static final long serialVersionUID = -2474279118814825081L;

    /**
     * 乘客uuid
     */
    private String passengerUuid;
    /**
     * 订单(行程)Uuid
     */
    @ApiModelProperty(value = "订单(行程)Uuid")
    @NotBlank(message = "订单(行程)Uuid不能为空")
    private String orderUuid;

    /**
     * 优惠券uuid
     */
    @ApiModelProperty(value = "优惠券uuid")
    private String couponUuid;

    /**
     * 微信小程序code-微信小程序支付
     */
    @ApiModelProperty(value = "微信小程序code-微信小程序支付")
    private String code;

    /**
     * 券适用类型 0 不使用 1 使用最优券 2 使用指定券
     */
    @ApiModelProperty(value = "券适用类型 0 不使用 1 使用最优券 2 使用指定券")
    private String useCouponType;

    // 使用省心打标记 1表示使用省心打
    @ApiModelProperty(value = "使用省心打标记 1表示使用省心打")
    private Integer unWarriedArrive;

    /**
     * 使用券套餐里面的优惠券 1表示使用
     */
    @ApiModelProperty(value = "0表示不勾选 1表示购买券套餐并使用券，2表示只购买券套餐", required = false)
    private Integer useCounponActivity;

    /**
     * 车费支付方式【包含余额+一种第三方支付】
     */
    @ApiModelProperty(value = "车费支付方式【包含余额+一种第三方支付】")
    private List<Integer> payTypeList;


    /**
     * 出租车扫码支付
     */
    @ApiModelProperty(value = "出租车扫码支付")
    private Boolean taxiScanPay = false;


    /**
     * {@link com.t3.ts.pay.remote.constants.EnumPayOrderType}
     */
    @ApiModelProperty(value = "支付订单类型", required = true)
    private Integer payOrderType;


    /**
     * 支付渠道是否为乘客选择  true-乘客选择  false-推荐|记忆
     * 余额充足时：
     * 自动拉起收银台--支付方式列表返回余额
     * 乘客选中支付方式--支付方式列表返回乘客选中的支付方式
     */
    @ApiModelProperty(value = "支付渠道是否为乘客选择")
    private Boolean passengerChooseFlag = false;

    /**
     * 应用类型   特定为小程序新增-2
     */
    private Integer applicationType;
    /**
     * 亲友代付标识  初始化收银台 传 -1
     */
    @ApiModelProperty(value = "亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择")
    private Integer payForOtherType;

    /**
     * 区域code
     */
    @ApiModelProperty(value = "区域code，用于替换上面的城市code")
    private String adCode;
    /**
     * 权益卡uuid
     */
    @ApiModelProperty(value = "权益卡uuid")
    private String privilegeUuid;

    /**
     * 卡使用类型 0 不使用 1 使用最优卡 2 使用指定卡
     */
    @ApiModelProperty(value = "卡使用类型 0 不使用 1 使用最优卡 2 使用指定卡")
    private String usePrivilegeType;
    /**
     * 版本类型  P_a_ 安卓   P_i_ 苹果 示例 "grayversion":"P_a_4.0.4" - 必须有
     */
    private String grayVersion;
    /**
     * 版本号  "graybuild":"851" - 必须有
     */
    private String grayBuild;
    /**
     * 执行支付的终端 - 必须生成
     */
    private String terminal;

    /**
     * 用户支付时使用的终端，类似下单打车时的终端 Srorder.extInfo.subSource
     * 微信APP 九宫格入口 userChannel= 201
     */
    @ApiModelProperty(value = "用户支付时使用的终端")
    private Integer userChannel;
    /**
     * 是否可用新券包信息
     * 为了兼容 - 自动驾驶收银台 , 都是使用老的券搭售
     */
    @ApiModelProperty(value = "收银台是否可以使用新券包")
    private Boolean useNewCouponPackage = false;




}
