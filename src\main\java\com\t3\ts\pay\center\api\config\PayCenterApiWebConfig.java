package com.t3.ts.pay.center.api.config;

import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.t3.ts.converter.T3HttpMessageConverterProvider;
import com.t3.ts.pay.center.api.config.interceptor.ApiInterceptor;
import com.t3.ts.pay.center.api.config.interceptor.DriverTokenInterceptor;
import com.t3.ts.pay.center.api.config.interceptor.HttpTraceLogInterceptor;
import com.t3.ts.pay.center.api.config.interceptor.PassengerTokenInterceptor;
import java.util.ArrayList;
import java.util.List;
import com.t3.ts.pay.center.api.config.interceptor.RateLimitInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.cbor.MappingJackson2CborHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.smile.MappingJackson2SmileHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * The type Web security config.
 *
 * @author: caodongfeng
 * @date: 2019 -09-03 10:58
 */
@Order(1)
@Configuration
public class PayCenterApiWebConfig implements WebMvcConfigurer {

    @Autowired
    private HttpTraceLogInterceptor httpTraceLogInterceptor;

    @Autowired
    private ApiInterceptor apiInterceptor;

    @Autowired
    private PassengerTokenInterceptor passengerTokenInterceptor;

    @Autowired
    private DriverTokenInterceptor driverTokenInterceptor;
    @Autowired
    private RateLimitInterceptor rateLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(apiInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/public/**");

        registry.addInterceptor(passengerTokenInterceptor)
                .addPathPatterns("/api/passenger/**", "/api/invoice/**", "/api/payment/**", "/api/wallet/**",
                        "/pay/api/v1/account/**", "/api/pay/v2/sign/**", "/api/mall/order/**", "/api/privilege/**")
                .excludePathPatterns("");

        registry.addInterceptor(driverTokenInterceptor)
                .addPathPatterns("/api/driver/**","/api/finance/payAccountAnalyseSum/**")
                .excludePathPatterns("");

        registry.addInterceptor(rateLimitInterceptor)
                .addPathPatterns("/api/driver/wallet/getPayAccountAnalyseDetailPage",
                        "/api/driver/wallet/getPayAccountAnalyseSumByDay",
                        "/api/finance/payAccountAnalyseSum/v2/query/ratioTrend",
                        "/api/finance/payAccountAnalyseSum/v2/query/flowCount",
                        "/api/driver/wallet/payAccountAnalyseDwySum")
                .excludePathPatterns("");


        //非拦截路径
        List<String> excludePathPatterns = new ArrayList<>();
        excludePathPatterns.add("/healthCheck");

        InterceptorRegistration httpTraceLogInterceptorRegistration = registry.addInterceptor(httpTraceLogInterceptor);
        httpTraceLogInterceptorRegistration.addPathPatterns("/**");
        httpTraceLogInterceptorRegistration.excludePathPatterns(excludePathPatterns);


    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 默认 FastJson UTC时间格式
        converters.removeIf((converter) -> {
            return converter instanceof MappingJackson2HttpMessageConverter
                    || converter instanceof MappingJackson2SmileHttpMessageConverter
                    || converter instanceof MappingJackson2CborHttpMessageConverter
                    || converter instanceof FastJsonHttpMessageConverter;
        });
        if (this.useSpringDefaultHttpMessageConverter()) {
            converters.add(T3HttpMessageConverterProvider.supportSpringDefaultHttpMessageConverterTimestamp());
        } else if (this.fastJsonSerializationByTimestamp()) {
            converters.add(T3HttpMessageConverterProvider.fastJsonHttpMessageConverterTimestamp());
        } else {
            converters.add(T3HttpMessageConverterProvider.fastJsonHttpMessageConverterUTC());
        }
    }

    /**
     * 使用spring默认转换
     *
     * @return 出参
     */
    public boolean useSpringDefaultHttpMessageConverter() {
        // 下期迭代该配置将会失效，默认使用：FastJson
        return true;
    }

    /**
     * 使用fastjson默认转换
     *
     * @return 出参
     */
    public boolean fastJsonSerializationByTimestamp() {
        return false;
    }


}
