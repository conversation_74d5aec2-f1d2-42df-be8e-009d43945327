package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 * @ClassName UnWarriedArriveDetail.java
 * <AUTHOR>
 * @version 1.0.0
 * @Description 省心打折扣明细
 * @createTime 2021年07月09日 10:46:00
 */
@Data
public class UnWarriedArriveDetail {

    //省心打可抵扣金额
    private BigDecimal unWarriedArriveDisAmount;

    //最高折扣金额
    private BigDecimal maxDeductAmount;

    //折扣
    private BigDecimal discount;

    //省心打相对优惠券节省的价格

    private BigDecimal subCouponAmount;

}
