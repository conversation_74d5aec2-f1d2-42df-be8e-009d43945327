

package com.t3.ts.pay.center.api.service;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * 转换
 *
 * <AUTHOR>
 */
public interface ResponseConverter {

    /**
     * 数据转换
     *
     * @param response 参数
     * @param clazz    参数
     * @param <T>      参数
     * @return 参数
     */
    default <T> Response<T> convertData(Response response, Class<T> clazz) {
        if (response.isSuccess() && !Objects.isNull(response.getData())) {
            T tDto = null;
            if (response.getData() instanceof String) {
                try {
                    tDto = JSONObject.parseObject((String) response.getData(), clazz);
                } catch (JSONException var5) {
                    tDto = (T) response.getData();
                }
            } else {
                tDto = JSONObject.parseObject(JSONObject.toJSONString(response.getData()), clazz);
            }

            return Response.createSuccess(tDto);
        } else {
            return response;
        }
    }


    /**
     * 数据转换
     *
     * @param response 参数
     * @param clazz    参数
     * @param <T>      参数
     * @return 参数
     */
    default <T> Response<List<T>> convertListData(Response response, Class<T> clazz) {
        if (response.isSuccess() && !Objects.isNull(response.getData())) {
            List<T> listDto = null;
            if (response.getData() instanceof String) {
                try {
                    listDto = JSONObject.parseArray((String) response.getData(), clazz);
                } catch (JSONException var5) {
                    listDto = (List) response.getData();
                }
            } else {
                listDto = JSONObject.parseArray(JSONObject.toJSONString(response.getData()), clazz);
            }

            return Response.createSuccess(listDto);
        } else {
            return response;
        }
    }


    /**
     * 数据转换
     *
     * @param response 参数
     * @param clazz    参数
     * @param <T>      参数
     * @return 参数
     */
    default <T> Response<PageResult<T>> convertPageData(Response response, Class<T> clazz) {
        if (response.isSuccess() && !Objects.isNull(response.getData())) {
            PageResult<T> listVo = new PageResult();
            PageResult<JSONObject> pageResultResp = null;
            if (response.getData() instanceof String) {
                pageResultResp = (PageResult) JSONObject.parseObject((String) response.getData(), PageResult.class);
            } else {
                pageResultResp = (PageResult) JSONObject
                        .parseObject(JSONObject.toJSONString(response.getData()), PageResult.class);
            }

            List<JSONObject> resultList = pageResultResp.getList();
            if (!CollectionUtils.isEmpty(resultList)) {
                listVo.setCurrPage(pageResultResp.getCurrPage());
                listVo.setHasMore(pageResultResp.isHasMore());
                listVo.setPageSize(pageResultResp.getPageSize());
                listVo.setTotalCount(pageResultResp.getTotalCount());
                Iterator var6 = resultList.iterator();

                while (var6.hasNext()) {
                    JSONObject jsonObj = (JSONObject) var6.next();
                    T routeListDto = JSONObject.parseObject(jsonObj.toJSONString(), clazz);
                    listVo.getList().add(routeListDto);
                }
            }

            return Response.createSuccess(listVo);
        } else {
            return response;
        }
    }
}
