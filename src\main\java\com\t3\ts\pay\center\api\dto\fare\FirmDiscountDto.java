package com.t3.ts.pay.center.api.dto.fare;

/**
 * @Author:<PERSON><PERSON><PERSON>
 * @Date${DATTE}-22:08
 */

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * @version 1.0
 * @interface FirmDiscountDto
 * @Description 企业折扣策略
 * <AUTHOR>
 * @Date 2019/5/30 22:08
 */
public class FirmDiscountDto implements Serializable {
    DecimalFormat df = new DecimalFormat("#.00");

    //开始时间
    private String startTime;

    //结束时间
    private String endTime;

    //企业折扣率比如8.0 即为8折
    private BigDecimal discount;

    //企业直减
    private BigDecimal mimus;

    /**
     * 得到折扣
     *
     * @return {@link BigDecimal}
     */
    public BigDecimal getDiscount() {
        if (discount == null) {
            discount = BigDecimal.TEN;
        }
        return discount;
    }

    /**
     * 设定的折扣
     *
     * @param discount 折扣
     */
    public void setDiscount(BigDecimal discount) {
        if (discount == null) {
            discount = BigDecimal.TEN;
        } else {
            discount = new BigDecimal(df.format(discount));
        }
        this.discount = discount;
    }

    /**
     * 得到会
     *
     * @return {@link BigDecimal}
     */
    public BigDecimal getMimus() {
        if (mimus == null) {
            mimus = BigDecimal.ZERO;
        }
        return mimus;
    }

    /**
     * 组会
     *
     * @param mimus 会
     */
    public void setMimus(BigDecimal mimus) {
        if (mimus == null) {
            mimus = BigDecimal.ZERO;
        } else {
            mimus = new BigDecimal(df.format(mimus));
        }
        this.mimus = mimus;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
