package com.t3.ts.pay.center.api.bo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * <AUTHOR>
 * @Date: 2021-09-17
 */
@Getter
@Setter
public class InvoiceRouteBo implements Serializable {
    private static final long serialVersionUID = -2324195477982077977L;

    private String uuid;
    /**
     * 行程id
     */
    private String routeUuid;
    /**
     * 发票id
     */
    private String invoiceUuid;
    /**
     * 乘客id
     */
    private String passengerUuid;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单模块类型（1：出租车，2：专享，3：拼车，4：快享，5：顺风车，6：骑手，7：搬家）
     */
    private Integer typeModule;
    /**
     * 城市
     */
    private String city;
    /**
     * 出发时间
     */
    private Date deparTime;
    /**
     * 起点
     */
    private String originAddress;
    /**
     * 终点
     **/
    private String destAddress;
    /**
     * 里程
     */
    private Double trip;
    /**
     * 支付金额
     */
    private BigDecimal payMoney;
    /**
     * 附加费用（不可开票费用=附加服务费用+高速费+路桥费+停车费+其他费）
     */
    private BigDecimal additionalMoney;

    /**
     * 状态（1：有效，2：作废）
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人id
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改者
     */
    private String updater;
    /**
     * 修改时间
     */
    private Date updatedTime;

    /**
     * 行程类型（个人/企业）：1 个人；2 企业
     */
    private Integer typeEnt;

    /**
     * add 2020年2月19日17:26:58
     * 行程结束时间
     */
    private Date arriveTime;
    /**
     * 开票金额
     */
    private BigDecimal amount;
    /**
     * 订单行程类型(1.用车 8 包车)
     */
    private Integer typeTrip;

    /**
     * 运力类型(1 T3运力,2 东风运力,3 一汽运力)
     */
    private Integer transportType;

    private static final int NUM_2 = 2;
    private static final int NUM_3 = 3;
    private static final int NUM_4 = 4;
    private static final int NUM_6 = 6;
    private static final int NUM_7 = 7;

    /**
     * @return {@link String}
     */
    public String getBillMoney() {
        if (null == getPayMoney() || BigDecimal.ZERO.compareTo(getPayMoney()) >= 0) {
            return BigDecimal.ZERO.setScale(NUM_2).toString();
        }
        BigDecimal actualFare = getPayMoney();
        BigDecimal additionalFare = null == getAdditionalMoney() ? BigDecimal.ZERO : getAdditionalMoney();
        return actualFare.subtract(additionalFare).setScale(NUM_2).toString();
    }

    /**
     * 得到类型模块名称
     *
     * @return {@link String}
     */
    public String getTypeModuleName() {
        if (null == getTypeModule()) {
            return null;
        }
        String typeModuleName = "快享";
        switch (getTypeModule()) {
            case 1:
                typeModuleName = "出租车";
                break;
            case NUM_2:
                typeModuleName = "专车";
                break;
            case NUM_3:
                typeModuleName = "拼车";
                break;
            case NUM_4:
                typeModuleName = "快车";
                break;
            case NUM_6:
                typeModuleName = "惠享";
                break;
            case NUM_7:
                typeModuleName = "自动驾驶";
                break;
            default:
                typeModuleName = "快享";
        }
        return typeModuleName;
    }
}
