package com.t3.ts.pay.center.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.bo.PayDeskInfoBo;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.PayConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.ChartedAdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.service.CharterRoutePayService;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.combina.PayDeskInfoService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.dto.SettlementRechargeDto;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.travel.config.operate.service.CityService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 包车相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service("charterRoutePayService")
public class CharterRoutePayServiceImpl implements CharterRoutePayService {

    @DubboReference
    private SettlementRechargeService settlementRechargeService;
    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;
    @DubboReference
    private PayDeskInfoService payDeskInfoService;
    @Resource
    private PayChannelFactory payChannelFactory;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private CityService cityService;

    @Override
    public Response<RechargePayBo> advanceRecharge(ChartedAdvanceRechargeDTO advanceRechargeDTO) {
        //构建计算单数据对象
        SettlementRechargeDto settlementRechargeDto = new SettlementRechargeDto();
        // 用户uuid
        settlementRechargeDto.setPassengerUuid(advanceRechargeDTO.getPassengerUuid());
        //预付款流水号
        settlementRechargeDto.setAdvanceSerial(advanceRechargeDTO.getPrePayNo());
        // 充值金额
        settlementRechargeDto.setRechargeAmount(advanceRechargeDTO.getRechargeAmount());
        // 生成充值结算单
        Response<SettlementRechargeDto> srResponse =
                settlementRechargeService.saveSettlementRecharge(settlementRechargeDto);
        if (!srResponse.isSuccess() || srResponse.getData() == null
                || StringUtils.isBlank(srResponse.getData().getUuid())) {
            return Response.createError("生成充值结算单失败", ResultErrorEnum.PARAM_NULL_ERROR);
        }
        SettlementRechargeDto settlementRechargeResponseDto = srResponse.getData();
        List<Integer> payTypeList = Arrays.asList(advanceRechargeDTO.getPayType());
        //在线支付信息
        Response<String> onlinePaymentResponse =
                travelOnlineCharge(settlementRechargeResponseDto.getUuid(), payTypeList, advanceRechargeDTO);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg(), onlinePaymentResponse.getCode());
        }
        //是否免密支付
        boolean isNoSecret = StringUtils.equals(String.valueOf(onlinePaymentResponse.getCode()),
                PayConstants.NO_SEC_PAY) ? true : false;
        //内部传递对象
        PaymentInfoBo paymentInfoBo =
                new PaymentInfoBo(isNoSecret, onlinePaymentResponse, advanceRechargeDTO.getPassengerUuid(),
                        advanceRechargeDTO.getPassengerMobile());
        //支付工厂模式调用
        Response<RechargePayBo> payResponse =
                payChannelFactory.paymentInfo(advanceRechargeDTO.getPayType(), paymentInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg());
        }
        if (Objects.isNull(payResponse.getData())) {
            return Response.createError("获取预付款支付信息失败，返回data为空");
        }
        RechargePayBo rechargePayBo = payResponse.getData();
        // 增加返回结算id addBy zangzl20200709
        rechargePayBo.setSettlementId(settlementRechargeResponseDto.getUuid());
        return Response.createSuccess("获取预付款支付信息成功", rechargePayBo);
    }

    @Override
    public Response<PayDeskInfoBo> getPayDeskInfo(String routePlanUuid, String passengerUuid) {
        return null;
    }

    @Override
    public Response<RechargePayBo> launchRoutePay(RoutePayDTO routePayDTO) {
        log.info("launchRoutePay param:{}", JSONObject.toJSONString(routePayDTO));
        //校验行程状态
        Response checkResponse = getRouteAndCheckStatus(routePayDTO.getOrderUuid());
        if (!checkResponse.isSuccess()) {
            return Response.createError(checkResponse.getMsg(), checkResponse.getCode());
        }
        //在线支付信息
        Response<String> onlinePaymentResponse = travelOnlinePayment(routePayDTO);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg(), onlinePaymentResponse.getCode());
        }
        // 余额支付成功
        if (onlinePaymentResponse.getData() == null) {
            RechargePayBo payInfoVo = new RechargePayBo();
            payInfoVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            return Response.createSuccess("获取支付信息成功", payInfoVo);
        }
        //支付方式
        int payType = getPayType(routePayDTO.getPayTypeList());
        //是否免密支付
        boolean isNoSecret = StringUtils.equals(String.valueOf(onlinePaymentResponse.getCode()),
                PayConstants.NO_SEC_PAY) ? true : false;
        //内部传递对象
        PaymentInfoBo paymentInfoBo =
                new PaymentInfoBo(isNoSecret, onlinePaymentResponse, routePayDTO.getPassengerUuid(),
                        routePayDTO.getPassengerMobile());
        //支付工厂模式调用
        Response<RechargePayBo> payResponse = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        if (!payResponse.isSuccess()) {
            return Response.createError(payResponse.getMsg());
        }
        RechargePayBo rechargePayVo = payResponse.getData();
        if (rechargePayVo.getPayType() == EnumPayOrderChannel.BALANCE.getCode()) {
            return Response.createSuccess("余额支付成功", rechargePayVo);
        }
        return Response.createSuccess("获取行程支付信息成功", rechargePayVo);
    }

    /**
     * 在线充值
     *
     * @param settlementId       settlementId
     * @param payTypeList        payTypeList
     * @param advanceRechargeDTO advanceRechargeDTO
     * @return Response
     */
    private Response<String> travelOnlineCharge(String settlementId, List<Integer> payTypeList,
                                                ChartedAdvanceRechargeDTO advanceRechargeDTO) {
        if (CollectionUtils.isEmpty(payTypeList)) {
            return Response.createError("支付渠道参数异常", ResultErrorEnum.PARAM_NULL_ERROR);
        }
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(EnumPayOrderType.RECHARGE.getCode());
        paymentDto.setUserId(advanceRechargeDTO.getPassengerUuid());
        paymentDto.setSettlementId(settlementId);

        Response<PaymentDto> fullPaymentDto = fullPaymentDto(paymentDto, payTypeList, advanceRechargeDTO.getOpenId());
        if (!fullPaymentDto.isSuccess()) {
            return Response.createError(fullPaymentDto.getMsg(), fullPaymentDto.getCode());
        }
        if (advanceRechargeDTO.getPayType().equals(EnumPayOrderChannel.ALIPAY_MINI.getCode())) {
            Map<String, Object> extendParams = new HashMap<>(1);
            extendParams.put("code", advanceRechargeDTO.getAliAuthCode());
            paymentDto.setExtendParams(extendParams);
        }
        //执行支付
        Response<String> onlinePaymentResponse = unifiedPaymentFacade.pay(paymentDto);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg());
        }
        return onlinePaymentResponse;
    }

    /**
     * @param paymentDto  paymentDto
     * @param payTypeList payTypeList
     * @param openId      openId
     * @return Response
     */
    private Response<PaymentDto> fullPaymentDto(PaymentDto paymentDto, List<Integer> payTypeList, String openId) {
        // 支付渠道类型
        PaywayEnum[] paywayEnums = getPaywayEnum(payTypeList);
        paymentDto.setPaywayEnums(paywayEnums);
        /**
         * 支持小程序
         */
        if (isWMPPay(paywayEnums)) {
            if (StringUtils.isNotBlank(openId)) {
                Map<String, Object> params = new HashMap<>(CommonNumConst.NUM_16);
                params.put("openId", openId);
                paymentDto.setExtendParams(params);
            } else {
                return Response.createError("支付渠道参数异常", ResultErrorEnum.PARAM_NULL_ERROR);
            }
        }
        return Response.createSuccess(paymentDto);
    }

    /**
     * 在线支付
     *
     * @param routePayDTO routePayDTO
     * @return Response
     */
    private Response<String> travelOnlinePayment(RoutePayDTO routePayDTO) {
        if (CollectionUtils.isEmpty(routePayDTO.getPayTypeList())) {
            return Response.createError("支付渠道参数异常", ResultErrorEnum.PARAM_NULL_ERROR);
        }
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(routePayDTO.getBizType());
        paymentDto.setUserId(routePayDTO.getPassengerUuid());
        paymentDto.setBizId(routePayDTO.getOrderUuid());
        paymentDto.setMarketingId(routePayDTO.getCouponUuid());

        Response<PaymentDto> fullPaymentDto =
                fullPaymentDto(paymentDto, routePayDTO.getPayTypeList(), routePayDTO.getOpenId());
        if (!fullPaymentDto.isSuccess()) {
            return Response.createError(fullPaymentDto.getMsg(), fullPaymentDto.getCode());
        }
        if (routePayDTO.getPayTypeList().contains(EnumPayOrderChannel.INTEGRAL.getCode())) {
            Map<String, Object> extendParams = new HashMap<>(1);
            extendParams.put("integralNum", StringUtils.isNotBlank(routePayDTO.getAvailableIntegral())
                    ? Integer.valueOf(routePayDTO.getAvailableIntegral()) : 0);
            paymentDto.setExtendParams(extendParams);
        }
        if (routePayDTO.getPayTypeList().contains(EnumPayOrderChannel.ALIPAY_MINI.getCode())) {
            Map<String, Object> extendParams = new HashMap<>(1);
            extendParams.put("code", routePayDTO.getAliAuthCode());
            paymentDto.setExtendParams(extendParams);
        }
        //执行支付
        Response<String> onlinePaymentResponse = unifiedPaymentFacade.pay(paymentDto);
        if (!onlinePaymentResponse.isSuccess()) {
            return Response.createError(onlinePaymentResponse.getMsg());
        }
        return onlinePaymentResponse;
    }

    /**
     * @param paywayEnums paywayEnums
     * @return boolean
     */
    public boolean isWMPPay(PaywayEnum[] paywayEnums) {
        return Arrays.asList(paywayEnums).contains(PaywayEnum.WECHAT_MINIPROGRAM);
    }

    /**
     * @param payTypeList payTypeList
     * @return PaywayEnum
     */
    private PaywayEnum[] getPaywayEnum(List<Integer> payTypeList) {
        if (CollectionUtils.isEmpty(payTypeList)) {
            return null;
        }
        // 支付渠道类型
        if (payTypeList != null && payTypeList.size() > 0) {
            PaywayEnum[] paywayEnums = new PaywayEnum[payTypeList.size()];
            for (int i = 0; i < payTypeList.size(); i++) {
                Integer payTpe = payTypeList.get(i);
                if (payTpe.equals(EnumPayOrderChannel.ALIPAY.getCode())) {
                    paywayEnums[i] = PaywayEnum.ALIPAY_APP;
                } else if (payTpe.equals(EnumPayOrderChannel.WEIXIN.getCode())) {
                    paywayEnums[i] = PaywayEnum.WECHAT_APP;
                } else if (payTpe.equals(EnumPayOrderChannel.BALANCE.getCode())) {
                    paywayEnums[i] = PaywayEnum.BALANCE;
                } else if (payTpe.equals(EnumPayOrderChannel.NETCOM.getCode())) {
                    paywayEnums[i] = PaywayEnum.CMBCONENET_H5;
                } else if (payTpe.equals(EnumPayOrderChannel.UNIONPAY.getCode())) {
                    paywayEnums[i] = PaywayEnum.UNION_PAY;
                } else if (payTpe.equals(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode())) {
                    paywayEnums[i] = PaywayEnum.WECHAT_MINIPROGRAM;
                } else if (payTpe.equals(EnumPayOrderChannel.INTEGRAL.getCode())) {
                    paywayEnums[i] = PaywayEnum.INTEGRAL;
                } else if (payTpe.equals(EnumPayOrderChannel.ALIPAY_MINI.getCode())) {
                    paywayEnums[i] = PaywayEnum.ALIPAY_MINI;
                }
            }
            return paywayEnums;
        }
        return null;
    }

    /**
     * 校验行程状态
     *
     * @param orderUuid orderUuid
     * @return 行程信息
     */
    private Response getRouteAndCheckStatus(String orderUuid) {
        log.info("charterRoutePay queryRouteInfo: {}", orderUuid);
        //获取行程数据
        RouteInfoDto routeInfo = routeDetailClient.getRouteInfo(orderUuid);
        if (routeInfo == null) {
            return Response.createError("获取行程信息失败");
        }
        //校验行程状态=待支付
        if (!RouteStatus.ROUTE_6.getStatus().equals(routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString())) {
            return Response.createError("订单为非待支付状态");
        }
        return Response.createSuccess(routeInfo);
    }

    /**
     * 获取支付类型
     *
     * @param payTypeList payTypeList
     * @return int
     */
    private int getPayType(List<Integer> payTypeList) {
        return payTypeList.parallelStream().filter(payType -> EnumPayOrderChannel.INTEGRAL.getCode() != payType)
                .filter(payType -> EnumPayOrderChannel.BALANCE.getCode() != payType)
                .findFirst().get();
    }
}
