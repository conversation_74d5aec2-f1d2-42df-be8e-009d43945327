package com.t3.ts.pay.center.api.controller.invoice.v3;

import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.PayCenterApiServer;
import com.t3.ts.pay.center.api.business.invoice.InvoiceV3Business;
import com.t3.ts.pay.center.api.dto.MyPageResult;
import com.t3.ts.pay.center.api.dto.invoice.InvoiceRoutReq;
import com.t3.ts.pay.center.api.dto.vo.InvoiceRouteV3Vo;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: ivy
 * @Date: 2021/9/17 17:22
 * @Description: 提交开票test类    /api/passenger/v3/invoice/billing
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PayCenterApiServer.class)
@Slf4j
public class InvoiceBillingByRouteTest {

    @Autowired
    private InvoiceV3Business invoiceBusiness;

    @Test
    public void billingTest() {
        InvoiceRoutReq req = new InvoiceRoutReq();
        req.setNextIndex("202110");
        Response<?> response =
                invoiceBusiness.billingByRoute(req, "2d53a198cef9455a891c82956ee8bf46");
        log.info("开篇结果：{}", JSONUtil.toJsonStr(response));
    }
}
