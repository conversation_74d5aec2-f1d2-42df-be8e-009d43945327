package com.t3.ts.pay.center.api.dto.common;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/24 18:14
 * @des 1.0
 */

@Data
public class CanUseCoupon {

    private String userId;
    private long accountNewId;
    private String activityId;
    private String activityName;
    private String activityType;
    private int cancelReason;
    private String cityCode;
    private String couponId;
    private int couponMoney;
    private String couponName;
    private int couponType;
    private long createTime;
    private String creator;
    private String decutionAmount;
    private String endRailIds;
    private long expiryDateBegin;
    private long expiryDateEnd;
    private String extendValue;
    private String journeyId;
    private int optVersion;
    private String periodBegin;
    private String periodEnd;
    private int recordStatus;
    private int sourceType;
    private String startRailIds;
    private int status;
    private String templateCode;
    private String templateName;
    private String templateUuid;
    private long updateTime;
    private String updater;
    private String useCustomer;
    private String useLimit;
    private String useTrip;
    private String useType;
    private String useVehicle;
    private String uuid;
    private String terminal;
}
