package com.t3.ts.pay.center.api.dto.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @author: hemiaojun
 * @Date: 2019-03-03
 * @Description:发送发票或行程单接参
 */

@ApiModel
public class InvoiceSendReq implements Serializable {

    /**
     * 申请来源(1:app申请 2:客服申请)--- 客服申请
     */
    private Integer applySource = 1;

    private static final long serialVersionUID = 4408979731709759344L;

    /**
     * 发票id
     */
    @ApiModelProperty(value = "发票id")
    private String uuid;

    /**
     * 接收发票的邮箱
     */
    @ApiModelProperty(value = "接收发票的邮箱")
    @NotNull(message = "请输入邮箱")
    @Pattern(regexp = "^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$",
             message = "请输入正确的邮箱")
    private String email;

    /**
     * 乘客手机号
     */
    @ApiModelProperty(value = "乘客手机号")
    private String passengerMobile;

    @ApiModelProperty(value = "发票类别(1 出行服务,2 商城)")
    private Integer invoiceClass;

    public String getPassengerMobile() {
        return passengerMobile;
    }

    public void setPassengerMobile(String passengerMobile) {
        this.passengerMobile = passengerMobile;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getApplySource() {
        return applySource;
    }

    public void setApplySource(Integer applySource) {
        this.applySource = applySource;
    }

    public Integer getInvoiceClass() {
        return invoiceClass;
    }

    public void setInvoiceClass(Integer invoiceClass) {
        this.invoiceClass = invoiceClass;
    }
}
