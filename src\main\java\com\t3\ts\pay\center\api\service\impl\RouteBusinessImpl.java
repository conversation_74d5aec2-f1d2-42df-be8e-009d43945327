package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.pay.center.api.rest.RouteReadRest;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 出行相关接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RouteBusinessImpl implements RouteBusiness {

    @Resource
    private RouteReadRest routeReadRest;

    @Override
    public Response<PassengerRouteFareItemsVo> getPassengerRouteFareItems(RouteFareItemReq req) {
        Response response = routeReadRest.getRouteFareItems(req);
        return convertData(response, PassengerRouteFareItemsVo.class);
    }

    @Override
    public Response getEditRoutePointByCache(String routePlanUuid) {
        Response response = routeReadRest.getEditRoutePointByCache(new RoutePlanUuidReq(routePlanUuid));
        return response;
    }
}
