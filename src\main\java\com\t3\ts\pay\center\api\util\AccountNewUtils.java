package com.t3.ts.pay.center.api.util;

import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.pay.common.exception.BizExceptionUtil;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 分库分表下账号工具类
 *
 * <AUTHOR>
 * @date 2020/4/26.
 */
@Slf4j
@Component
public class AccountNewUtils {

    @DubboReference
    private AccountService accountService;


    /**
     * 获取司机的AccountType
     *
     * @return 非出租车司机为3，出租车司机为4
     */
    public Integer getAccountType(String userId) {
        Response<AccountDto> acctResp = accountService.simpleGetAccount(userId);
        if (acctResp == null || !acctResp.isSuccess() || acctResp.getData() == null) {
            log.error("getAccountType 账户不存在 userId={}", userId);
            return null;
        }
        AccountDto accountDto = acctResp.getData();
        return accountDto.getAccountType();
    }
    public Integer getAccountTypeE(String userId) {
        Integer accountType = accountService.getAccountTypeCache(userId);
        if (accountType == null) {
            log.error("getAccountTypeE 账户不存在 userId={}", userId);
            BizExceptionUtil.create("账户不存在");
            return null;
        }
        return accountType;
    }

    public boolean setAccountType(SettlementGeneralDto settlementGeneral) {
        if (settlementGeneral.getPaymentSubjectType() != null
                && PayAccountTypeEnum.isContain(settlementGeneral.getPaymentSubjectType(),
                PayAccountTypeEnum.DRIVER_TYPE, PayAccountTypeEnum.TAXI_TYPE)) {
            Integer accountType = this.getAccountType(settlementGeneral.getPaymentSubject());
            if(accountType != null){
                settlementGeneral.setPaymentSubjectType(accountType);
                return true;
            }
        } else if (settlementGeneral.getIncomeSubjectType() != null
                && PayAccountTypeEnum.isContain(settlementGeneral.getIncomeSubjectType(),
                PayAccountTypeEnum.DRIVER_TYPE, PayAccountTypeEnum.TAXI_TYPE)) {
            Integer accountType = this.getAccountType(settlementGeneral.getIncomeSubject());
            if(accountType != null){
                settlementGeneral.setIncomeSubjectType(accountType);
                return true;
            }
        }
        return false;
    }


}
