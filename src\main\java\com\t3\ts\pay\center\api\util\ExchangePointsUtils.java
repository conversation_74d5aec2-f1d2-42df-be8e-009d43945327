package com.t3.ts.pay.center.api.util;

import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <b>
 * ExchangePointsUtils
 * 2020/6/30
 * 积分换算工具类
 * </b>
 *
 * <AUTHOR>
 */
@Slf4j
public class ExchangePointsUtils {

    public static final int PRECISION = 2;

    /**
     * 转换金钱为积分
     *
     * @param money        费用
     * @param scale        兑换比例
     * @param roundingMode 舍入方式
     * @return 积分数
     */
    public static final BigDecimal changeMoney2Points(BigDecimal money, String scale, RoundingMode roundingMode) {
        checkParam(money, scale, "费用");
        BigDecimal points;
        try {
            points = money.divide(new BigDecimal(scale), 0, roundingMode);
        } catch (Exception e) {
            log.error("费用转换积分失败: {}", e);
            throw new RuntimeException("费用转换积分失败");
        }
        return points;
    }

    /**
     * 转换积分为金钱
     *
     * @param points 积分
     * @param scale  转换比例
     * @return 抵扣金额
     */
    public static final BigDecimal changePoints2Money(BigDecimal points, String scale) {
        checkParam(points, scale, "积分");
        BigDecimal money;
        try {
            money = points.multiply(new BigDecimal(scale)).setScale(PRECISION, RoundingMode.DOWN);
        } catch (Exception e) {
            log.error("积分转换金额失败: {}", e);
            throw new RuntimeException("积分转换金额失败");
        }
        return money;
    }

    /**
     * 参数校验
     *
     * @param money        费用|积分
     * @param scale        兑换比例
     * @param exchangeDesc 描述，辅助提示
     */
    private static void checkParam(BigDecimal money, String scale, String exchangeDesc) {
        if (StringUtils.isBlank(scale)) {
            log.error("兑换比例为空，无法转换");
            throw new RuntimeException("兑换比例为空，无法转换");
        }
        if (null == money) {
            log.error(exchangeDesc + "为空，无法转换");
            throw new RuntimeException(exchangeDesc + "为空，无法转换");
        }
    }
}
