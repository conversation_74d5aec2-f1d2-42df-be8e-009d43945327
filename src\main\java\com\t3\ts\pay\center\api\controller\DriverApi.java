package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.dto.book.AccountBookFreezeDto;
import com.t3.ts.account.center.service.AccountBookService;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.account.manager.dto.req.FreezeBalanceReqDto;
import com.t3.ts.account.manager.service.freeze.BalanceFreezeService;
import com.t3.ts.context.ContextUtil;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.DriverConstants;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.*;
import com.t3.ts.pay.center.api.dto.driverwallet.DriveProtocolDto;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskDto;
import com.t3.ts.pay.center.api.dto.driverwallet.DriverPayDeskVo;
import com.t3.ts.pay.center.api.dto.driverwallet.ProtocolDto;
import com.t3.ts.pay.center.api.dto.mall.MallPayResp;
import com.t3.ts.pay.center.api.enums.PayChannelEnum;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.rest.ProtocolServiceRest;
import com.t3.ts.pay.center.api.service.DriverPayService;
import com.t3.ts.pay.center.api.service.RoutePayService;
import com.t3.ts.pay.center.api.util.*;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.common.exception.BizExceptionEnum;
import com.t3.ts.pay.common.exception.BizExceptionUtil;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.common.util.MoneyUtils;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.GeneralSettlePayDto;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.SettleType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 司机api
 *
 * <AUTHOR>
 * @date 2022/11/08
 */
@RestController
@Slf4j
@RequestMapping("/api/driver")
@Api("Driver-Api")
public class DriverApi extends BaseApi {


    private static final String T3DSIGN = "t3DSign";

    @Autowired
    private CmbAggHelper cmbAggHelper;

    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;

    @DubboReference
    private SettlementGeneralService settlementGeneralService;

    @DubboReference
    private UnifiedService unifiedService;

    @Autowired
    private SwitchConfig switchConfig;

    private RoutePayService routePayService;

    @Autowired
    private PayChannelFactory payChannelFactory;


    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;

    @Autowired
    private MallApi mallApi;

    @DubboReference
    private AccountBookService accountBookService;
    @DubboReference
    private AccountService accountService;
    @Autowired
    private MarketingRest marketingRest;
    @DubboReference
    private BalanceFreezeService balanceFreezeService;
    @Autowired
    private DriverPayService driverPayService;

    @Autowired
    private ProtocolServiceRest protocolServiceRest;


    @ApiOperation(value = "获取司机支付配置", notes = "获取司机支付配置")
    @PostMapping(value = "/get/config")
    public Response<DriverPayConfigVo> getConfig() {
        DriverPayConfigVo dto = new DriverPayConfigVo();
        dto.setPayV2Key(switchConfig.getPayV2PublicKey());
        return Response.createSuccess(dto);
    }

    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping(value = "/pay/v2")
    public Response payV2(@RequestBody @Validated DriverPaymentReq req,
                                              HttpServletRequest request) {
        if(req.getBizType() == null){
            return Response.createError("缺少支付业务类型");
        }
        try {
            if (switchConfig.isPayOrderTypeNotUseChannel(req.getBizType(), req.getPayChannels())) {
                BizExceptionUtil.create("不支持当前支付渠道",
                        ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
            }
            DriverPaymentResVo driverPaymentResVo = new DriverPaymentResVo();
            Response response2 = Response.createSuccess(driverPaymentResVo);
            if (req.getBizType().equals(EnumPayOrderType.DRIVER_FREE_COMMISSION_CARD_V2_PAY.getCode())
                    || req.getBizType().equals(EnumPayOrderType.DRIVER_ELECTRIC_CARD_PAY.getCode())
                    || req.getBizType().equals(EnumPayOrderType.DRIVER_ELECTRIC_COUPON_PAY.getCode())) {
                // 商城支付
                this.adapterMallPay(req, request, driverPaymentResVo);
            } else {
                // 调用司机支付
                response2 = this.pay(req, request);
            }
            //加密
            if (switchConfig.isEncrypt()) {
                //对结果进行加密
                log.info("payV2 Encrypt is open");
                response2 = this.encryptResp(response2, request);
            }
            return response2;
        }  catch (BizException e) {
            log.error("payV2 bizException,information:", e);
            return e.getRes();
        } catch (Exception e) {
            log.error("payV2 exception,information:", e);
            return Response.createError("服务异常", ResultErrorEnum.PAY_ERROR.getCode());
        }
    }

    private void adapterMallPay(DriverPaymentReq req, HttpServletRequest request, DriverPaymentResVo driverPaymentResVo) {
        PayOrderSureReq payOrderSureReq = new PayOrderSureReq();
        payOrderSureReq.setOrderCode(req.getOrderId());
        payOrderSureReq.setLatitude(req.getLatitude());
        payOrderSureReq.setLongitude(req.getLongitude());
        if (!CollectionUtils.isEmpty(req.getPayChannels())) {
            payOrderSureReq.setPayChannels(req.getPayChannels());
            //过滤出三方支付用于后续各种校验
            Integer thirdPayChannel = PayUtils.getThirdPayChannel(req.getPayChannels());
            if(thirdPayChannel == null){
                thirdPayChannel = NumConstant.NUM_2;
            }
            payOrderSureReq.setPayOrderChannel(thirdPayChannel);
        } else {
            payOrderSureReq.setPayOrderChannel(req.getPayChannel());
        }
        Response<?> response = mallApi.mallPayV1(payOrderSureReq, request);
        if(!response.isSuccess() || response.getData() == null){
            BizExceptionUtil.create(response.getMsg());
        }
        Object data = response.getData();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
        driverPaymentResVo.setCmbMiniAppId(jsonObject.getString("cmbMiniAppId"));
        driverPaymentResVo.setStatus(response.getCode());
        driverPaymentResVo.setPayType(jsonObject.getInteger("payType"));
        driverPaymentResVo.setSecretKey(jsonObject.getString("sdk"));
    }

    /**
     * 收银台支付
     *
     * @param req     DriverPaymentReq
     * @param request HttpServletRequest
     * @return DriverPaymentResVo
     */
    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping(value = "/pay")
    public Response<DriverPaymentResVo> pay(@RequestBody @Validated DriverPaymentReq req,
                                            HttpServletRequest request) {
        log.info("/api/driver/pay start req={}", JSON.toJSONString(req));
        try {
        String driverUuid = getUserUid(request);
        req.setDriverUuid(driverUuid);
        List<Integer> payTypeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(req.getPayChannels())) {
            payTypeList.addAll(req.getPayChannels());
        } else {
            payTypeList.add(req.getPayChannel());
        }
        req.setPayTypeList(payTypeList);
        if (EnumPayOrderType.isContain(req.getBizType(), EnumPayOrderType.DRIVER_CHARGING_PILE_PAY)) {
            req.setBizType(EnumPayOrderType.DRIVER_RECHARGE.getCode());
        }
        if (EnumPayOrderType.isContain(req.getBizType(), EnumPayOrderType.DRIVER_CHARGING_PILE_PAY_AF)) {
            req.setBizType(EnumPayOrderType.DRIVER_CHARGING_PAY_AFTER.getCode());
        }
        GeneralSettlePayDto generalSettlePayDto = driverPayService.getGeneralSettlePayDto(null, req.getBizType());
        if (EnumPayOrderType.isContain(req.getBizType(), EnumPayOrderType.DRIVER_RECHARGE)) {
            this.addSettlement(req, generalSettlePayDto);
            if (req.isNotNeedPay()) {
                RechargePayBo payInfoVo = new RechargePayBo();
                payInfoVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
                return Response.createSuccess("充电预支付完成", payInfoVo);
            }
        }
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizId(req.getOrderId());
        paymentDto.setUserId(driverUuid);
        paymentDto.setBizType(req.getBizType());
        // 填充支付类型
        //其实里面是查结算单
        fillBizType(paymentDto, generalSettlePayDto);
        if(!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(paymentDto.getUserId()).getData()) {
        cmbAggHelper.convertAggChannelForDriver(payTypeList, getCmbAggPayTypeList(), driverUuid, paymentDto);
        }
        paymentDto.setPaywayEnums(PayWayConvert.getPayWayEnum(payTypeList));
        paymentDto.getExtendParams().put("wechatAppId", switchConfig.getDriverWxAppId());

        String grayVersion = getGrayVersion(request);
        paymentDto.getExtendParams().put("grayVersion", grayVersion);
        //司机端没有GrayBuild ,要不默认都支持吧
        paymentDto.getExtendParams().put("grayBuild", getGrayBuild(request));
        paymentDto.getExtendParams().put("terminal", PayUtils.getTerminal(grayVersion));
        paymentDto.getExtendParams().put("userIp", NetworkUtil.getOutIp(request));

        //支付
        Response<String> payRes = unifiedPaymentFacade.pay(paymentDto);
        if (!payRes.isSuccess()) {
            return Response.createSuccess(payRes.getMsg(), payRes.getCode());
        }
        // 余额支付成功
        if (payRes.getData() == null) {
            RechargePayBo payInfoVo = new RechargePayBo();
            payInfoVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            return Response.createSuccess("获取支付信息成功", payInfoVo);
        }
        log.info("payTypeList={}, payRes={}", JSON.toJSONString(payTypeList), JSON.toJSONString(payRes));
        //如果路由成功不走老流程
        if (!PayUtils.payRoutingSuccess(payRes)
                && !payRes.getCode().equals(NumConstants.NUM_202)
                && !cmbAggHelper.isCmbAggPay(PayWayConvert.getPayWayEnum(payTypeList))) {
            DriverPaymentResVo resVo = new DriverPaymentResVo();
            resVo.setPayType(payTypeList.get(0));
            resVo.setSecretKey(payRes.getData());
            return Response.createSuccess(resVo);
        }
        int payType = PayUtils.getRealChannelRouting(payRes, payTypeList);
        log.info("司机支付 获取支付路由后的支付类型 payType={}", payType);
        if (payRes.getData() == null) {
            DriverPaymentResVo resVo = new DriverPaymentResVo();
            resVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            return Response.createSuccess("支付成功", resVo);
        }
        PaymentInfoBo paymentInfoBo = new PaymentInfoBo(false, payRes, driverUuid, null);
        //支付工厂模式调用，这里面如果是翼支付就会把payType转换成47：招行聚合
        Response<RechargePayBo> response = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        if (!response.isSuccess() || null == response.getData()) {
            return Response.createSuccess(response.getMsg(), response.getCode());
        }
        DriverPaymentResVo resVo = buildPayResVo(response.getData());
        return Response.createSuccess(resVo);
        } catch (BizException e) {
            log.error("支付业务异常", e);
            return e.getRes();
        } catch (Exception e) {
            log.error("支付未知异常", e);
            return Response.createError("支付异常", BizExceptionEnum.PARAM_ERROR.getCode());
        }
    }

    private void addSettlement(DriverPaymentReq req,GeneralSettlePayDto generalSettlePayDto) {
        String userId = req.getDriverUuid();
        List<Integer> payTypeList = req.getPayTypeList();
        //判断乘客是否选择了充电卡
        String orderUuid=req.getOrderId();
        SettlementGeneralDto checkDto = new SettlementGeneralDto();
        checkDto.setOrderUuid(orderUuid);
        checkDto.setSettleType(SettleType.GENERAL_PAYMENT.getType());
        if(generalSettlePayDto != null){
            checkDto.setBizType(generalSettlePayDto.getBizType());
        }
        Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(checkDto);
        if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            //结算单已经存在
            log.warn("DriverApi addSettlement 结算单已经存在 SettlementGeneralDto={}", response.getData());
            //预付款时候充电卡只用于冻结，不用于支付，移除充电卡支付
            payTypeList.removeIf(payType -> EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode() == payType);
            if (response.getData().get(0).getTotalFare().compareTo(BigDecimal.ZERO)<=0) {
                req.setNotNeedPay(true);
            }
            return;
        }
        //查充电业务单
        ChargeOrderDto chargeOrderDto = marketingRest.queryChargingElectric(orderUuid);
        //订单金额
        BigDecimal totalFare = chargeOrderDto.getAdvanceMoney();
        long insureFare = 0L;
        if(chargeOrderDto.getInsureFare() != null){
            insureFare = (long)MoneyUtils.yuanToFen(chargeOrderDto.getInsureFare());
        }
        //待支付金额
        long remainFare = (long)MoneyUtils.yuanToFen(totalFare);
        // 查礼品卡
        Response<AccountDto> acctResp = accountService.simpleGetAccount(userId);
        if (acctResp == null || !acctResp.isSuccess() || acctResp.getData() == null){
            BizExceptionUtil.create("账户账本不存在");
        }
        AccountDto accountDto = acctResp.getData();
        if (payTypeList.contains(EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode())
                && req.getBizType() != null
                && req.getBizType().equals(EnumPayOrderType.DRIVER_RECHARGE.getCode())) {

            // 查冻结记录
            AccountBookFreezeDto accountBookFreezeDto = new AccountBookFreezeDto();
            accountBookFreezeDto.setUserId(userId);
            accountBookFreezeDto.setSceneUuid(orderUuid);
            Response<PageResult<AccountBookFreezeDto>> accountBookFreeze = accountBookService.getAccountBookFreeze(accountBookFreezeDto);
            long totalFreezeFare=accountBookFreeze.getData().getList().stream().mapToLong(AccountBookFreezeDto::getFreezeAmount).sum();
            remainFare = remainFare - totalFreezeFare;
            // 可以充电卡冻结金额 要减去保险费
            long chargingElectricityCardFreeze = remainFare - insureFare;
            if (chargingElectricityCardFreeze > 0 && accountDto.getChargingElectricityCard() > 0) {
                //账户余额可用性检查：查询礼品卡是否可用，默认不可用
                boolean chargingElectricityCardCanUse = marketingRest.querySupportCard(orderUuid);
                if(chargingElectricityCardCanUse){
                    //充电卡冻结金额
                    chargingElectricityCardFreeze = Math.min(accountDto.getChargingElectricityCard(), chargingElectricityCardFreeze);
                    FreezeBalanceReqDto freezeBalanceReqDto = new FreezeBalanceReqDto();
                    //冻结单号
                    freezeBalanceReqDto.setOrderUuid(orderUuid);
                    freezeBalanceReqDto.setFreezeAmount(chargingElectricityCardFreeze);
                    freezeBalanceReqDto.setUserId(userId);
                    freezeBalanceReqDto.setAccountType(accountDto.getAccountType());
                    //冻结场景
                    freezeBalanceReqDto.setBizType(BizType.DRIVER_CHARGING_PAY.getType());
                    freezeBalanceReqDto.setRemark("充电预付款，冻结充电卡");
                    freezeBalanceReqDto.setBookType(BookTypeEnum.CHARGING_ELECTRICITY_CARD.getType());
                    Response<List<com.t3.ts.account.manager.dto.resp.AccountBookFreezeDto>>  freezeResponse = balanceFreezeService.freezeBalance(freezeBalanceReqDto);
                    if (!freezeResponse.isSuccess()) {
                        BizExceptionUtil.create("冻结充电卡失败");
                    }
                    //冻结成功 待支付金额减少
                    remainFare = remainFare - chargingElectricityCardFreeze;
                }
            }
        }
        //剩下要付的三方，生成预付款结算单,加回保费
        BigDecimal remainFareYuan = MoneyUtils.fenToYuan(remainFare);
        SettlementGeneralDto settlementGeneralDto = new SettlementGeneralDto();
        settlementGeneralDto.setBizType(BizType.DRIVER_PRE_PAY.getType());
        settlementGeneralDto.setSettleType(SettleType.GENERAL_PAYMENT.getType());
        settlementGeneralDto.setPaymentSubject(userId);
        settlementGeneralDto.setPaymentSubjectType(accountDto.getAccountType());
        settlementGeneralDto.setOrderUuid(orderUuid);
        settlementGeneralDto.setBindOrderUuid(orderUuid);
        //预付款只是预付款，不区分保费还是订单费还是停车费
        settlementGeneralDto.setTotalFare(remainFareYuan);
        settlementGeneralDto.setOrderFare(remainFareYuan);
        Response<String> settlementResponse = settlementGeneralService.addSettlement(settlementGeneralDto);
        if(!settlementResponse.isSuccess()){
            BizExceptionUtil.create(BizExceptionEnum.PARAM_ERROR.getCode(), "创建预付款结算单失败");
        }
        //预付款时候充电卡只用于冻结，不用于支付，移除充电卡支付
        payTypeList.removeIf(payType -> EnumPayOrderChannel.CHARGING_ELECTRICITY_CARD.getCode() == payType);
        if (remainFareYuan.compareTo(BigDecimal.ZERO)<=0) {
            req.setNotNeedPay(true);
        }
    }

    /**
     * 查询支付结果
     *
     * @param req DriverPaymentReq
     * @return DriverPaymentResVo
     */
    @ApiOperation(value = "查询支付结果", notes = "查询支付结果")
    @PostMapping(value = "/queryPayResult")
    public Response<DriverPaymentResVo> queryPayResult(@RequestBody DriverPaymentReq req) {
        DriverPaymentResVo resVo = new DriverPaymentResVo();
        SettlementGeneralDto settlementGeneralDto = new SettlementGeneralDto();
        settlementGeneralDto.setOrderUuid(req.getOrderId());
        Response<List<SettlementGeneralDto>> response = settlementGeneralService.selectSettlement(settlementGeneralDto);
        if (response.isSuccess() && !CollectionUtils.isEmpty(response.getData())) {
            resVo.setStatus(response.getData().get(0).getOrderStatus());
        }
        return Response.createSuccess(resVo);
    }

    /**
     * 查询渠道列表
     *
     * @param request HttpServletRequest
     * @return Response<DriverChannelListResVo>
     */
    @ApiOperation(value = "查询渠道列表", notes = "查询渠道列表")
    @PostMapping(value = "/channel/list")
    public Response<DriverChannelListResVo> queryDriverChannelList(HttpServletRequest request) {
        try {
            DriverChannelListResVo resVo = driverPayService.queryDriverChannelList(null, request);
            return Response.createSuccess(resVo);
        } catch (Exception e) {
            return Response.createError(ResultErrorEnum.CHANNEL_LIST_ERROR);
        }
    }


    /**
     * 司机app使用的聚合支付
     *
     * @return List<Integer>
     */
    private List<Integer> getCmbAggPayTypeList() {
        List<Integer> aggPayTypeList = new ArrayList<>();
        aggPayTypeList.add(PayChannelEnum.WEIXIN.getPayType());
        aggPayTypeList.add(PayChannelEnum.ALIPAY.getPayType());
        aggPayTypeList.add(PayChannelEnum.CMB_AGGREGATE_WX_MINI_PAY.getPayType());
        return aggPayTypeList;
    }

    /**
     * 拼装返回信息
     *
     * @param bo      RechargePayBo
     */
    private DriverPaymentResVo buildPayResVo(RechargePayBo bo) {
        DriverPaymentResVo resVo= new DriverPaymentResVo();
        //微信聚合需要用到
        resVo.setCmbMiniAppId(bo.getCmbMiniAppId());
        if (cmbAggHelper.isCmbAggPay(PayWayConvert.getPayWayEnum(bo.getPayType()))) {
            resVo.setSecretKey(bo.getSdk() + "%26source%3DT3Driver");
        }else {
            resVo.setSecretKey(bo.getSdk());
        }
        resVo.setPayType(bo.getPayType());
        return resVo;
    }

    /**
     * 填充支付类型
     * 配置化功能弱化支付类型
     *
     * @param paymentDto paymentDto
     * @param generalSettlePayDto generalSettlePayDto
     */
    private void fillBizType(PaymentDto paymentDto, GeneralSettlePayDto generalSettlePayDto) {
        if (StringUtils.isEmpty(paymentDto.getBizId()) || StringUtils.isEmpty(paymentDto.getUserId())) {
            return;
        }
        if(generalSettlePayDto != null){
            paymentDto.setBizType(generalSettlePayDto.getPayOrderType());
            paymentDto.setSettlementBizType(generalSettlePayDto.getBizType());
        }
    }



    /**
     * 司机端收银台信息
     * @param dto
     * @param request
     * @return
     */
    @PostMapping("/v1/pay/desk/info")
    public Response<DriverPayDeskVo> deskInfo(@RequestBody @Validated DriverPayDeskDto dto, HttpServletRequest request) {
        return driverPayService.deskInfo(dto, request);
    }


    /**
     * 对结果进行加密
     *
     * @param response 当前响应体
     * @return 加密后响应体
     */
    public Response<MallPayResp> encryptResp(Response response, HttpServletRequest request) throws Exception {

        String t3DSign =request.getHeader(T3DSIGN);
        log.info("使用AES密钥加密前t3DSign={}", t3DSign);
        if (StringUtils.isEmpty(t3DSign)) {
            return response;
        }
        //解析出ase秘钥，为空直接阻断
        String payV2PrivateKey = switchConfig.getPayV2PrivateKey();
        String aesKey = RSAUtils.decrypt(t3DSign, payV2PrivateKey);
        if (StringUtils.isEmpty(aesKey)) {
          return response;
        }
        String respSign = StringUtils.buildUUID();
        //使用AES密钥对结果进行加密生成content
        String jsonResp = JSON.toJSONString(response.getData());
        log.info("使用AES密钥加密前aesKey={} jsonResp={}", aesKey, jsonResp);
        String content = AESUtil.encryptByECB(jsonResp, aesKey);
        log.info("使用AES密钥加密后={}", content);
        //构建响应结果
        MallPayResp mallPayResp = MallPayResp.builder()
                .respSign(respSign)
                .respSignVer(switchConfig.getPayV2Version())
                .content(content)
                .build();

        return Response.createSuccess(mallPayResp);
    }


    /**
     * 司机签约
     * @param dto dto
     * @return Response
     */
    @PostMapping("/agreementVersion/getUserUnSignAgreements")
    public Response<Boolean> getUserUnSignAgreements(@RequestBody DriveProtocolDto dto) {
        try{
            String userId = ContextUtil.getUserId();
            //2表示司机端
            Integer agreementObject = DriverConstants.AGREEMENT_OBJECT_DRIVER;
            //89-钱包代扣协议
            Integer agreementType = DriverConstants.AGREEMENT_TYPE_WALLET;
            List<ProtocolDto> userUnSignAgreements = protocolServiceRest.getUserUnSignAgreements(userId,
                    Lists.newArrayList(agreementType),
                    agreementObject);
            return Response.createSuccess(userUnSignAgreements);
        } catch (Exception e) {
            log.error("司机查询钱包代扣协议异常",e);
        }
        return Response.createSuccess();
    }

    /**
     * 司机签约
     * @param dto dto
     * @return Response
     */
    @PostMapping("/agreementVersion/signAgreement")
    public Response<Boolean> signAgreement(@RequestBody @Validated DriveProtocolDto dto) {
        try {
            String userId = ContextUtil.getUserId();
            String driverMobile = RequestContextHelper.getDriverMobile();
            if (CollectionUtils.isEmpty(dto.getCodes())) {
                return Response.createSuccess("无签约协议编码");
            }
            //2表示司机端
            Integer agreementObject = DriverConstants.AGREEMENT_OBJECT_DRIVER;
            //89-钱包代扣协议
            Integer agreementType = DriverConstants.AGREEMENT_TYPE_WALLET;
            protocolServiceRest.signAgreement(userId, driverMobile, dto.getCodes(),
                    agreementType, agreementObject);
        } catch (Exception e) {
            log.error("司机签约钱包代扣协议异常",e);
        }
        return Response.createSuccess("签约成功");
    }
}
