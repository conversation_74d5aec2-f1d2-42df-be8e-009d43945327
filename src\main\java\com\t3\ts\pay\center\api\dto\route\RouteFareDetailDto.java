package com.t3.ts.pay.center.api.dto.route;

import lombok.Data;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/23 17:06
 * @description:
 */
@Data
@ToString
public class RouteFareDetailDto implements Serializable {

    /**
     * 里程费用明细
     */
    private List<SplitTimeMileageFare> splitMileages;

    /**
     * 时长费用明细
     */
    private List<SplitTimeFare> splitTimes;

    /**
     * Description:
     * CreateDate: 2020/12/31
     * <br/>
     *
     * @return boolean
     * <AUTHOR>
     **/
    public boolean notEmpty() {
        return !CollectionUtils.isEmpty(splitMileages) || !CollectionUtils.isEmpty(splitTimes);
    }
}
