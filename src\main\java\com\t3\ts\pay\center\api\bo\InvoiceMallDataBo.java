package com.t3.ts.pay.center.api.bo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 迁移 by ivy .2021/09/17 13:40
 *
 * @Author: ivy
 * @Date: 2021-09-17 10:00
 */
@Getter
@Setter
public class InvoiceMallDataBo implements Serializable {

    private static final long serialVersionUID = 3528770258716222233L;
    /**
     * 订单ID
     */
    private String orderNo;
    /**
     * 城市CODE
     */
    private String cityCode;
    /**
     * 城市名
     */
    private String cityName;
    /**
     * 城市名
     */
    private String city;
    /**
     * 用户手机号
     */
    private String userMobile;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 用户id
     */
    private String userUuid;
    /**
     * 订单创建时间
     */
    private Date orderCreateTime;
    /**
     * 业务类型 0积分商城 7 商城礼品卡
     */
    private Integer bizType;
    /**
     * 可开票金额
     */
    private BigDecimal amount;
    /**
     * 开票状态 0-待开票 1-已开票
     */
    private Integer invoiceStatus;
    /**
     * 开票次数
     */
    private Integer invoiceTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商品数量
     */
    private Integer skuNum;

    /**
     * 商品名称
     */
    private String skuName;

    private Integer invoiceClass;

    private Integer invoiceSubjectCode;

    private String groupKey;

    private Date completeTime;
}
