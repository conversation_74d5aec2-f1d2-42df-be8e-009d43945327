package com.t3.ts.pay.center.api.dto.driverwallet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public class AccountSumDwyVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("yj1-归于收入 yj2-归于支出")
    private Double incomeAmount = 0D;

    @ApiModelProperty("收入描述")
    private String incomeDesc;

    @ApiModelProperty("收入小类")
    private List<IncomeCodeClassifyVo> incomeList = new ArrayList<>();

    @ApiModelProperty("支出")
    private Double payOutAmount = 0D;

    @ApiModelProperty("支出描述")
    private String payOutDesc;
    @ApiModelProperty("支出小类")
    private List<IncomeCodeClassifyVo> payOutList = new ArrayList<>();


    @Data
    public static class IncomeCodeClassifyVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 业绩指标编码
         */
        @ApiModelProperty("业绩指标编码")
        private String classIncomeCode;

        /**
         * 业绩指标描述
         */
        @ApiModelProperty("业绩指标描述")
        private String classIncomeDesc;

        /**
         * 汇总金额-分
         */
        @ApiModelProperty("汇总金额-分")
        private Double sumAmount = 0D;

    }


}
