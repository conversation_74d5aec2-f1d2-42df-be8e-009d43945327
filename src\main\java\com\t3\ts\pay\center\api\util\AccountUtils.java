package com.t3.ts.pay.center.api.util;

import com.t3.ts.account.center.dto.AccountCompanyDto;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.utils.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1024;

/**
 * The type Account utils.
 *
 * <AUTHOR>
 * @date 2019 /2/22.
 */
public class AccountUtils {

    /**
     * The Money times one hundred.
     */
    public static final int MONEY_TIMES_ONE_HUNDRED = 100;
    /**
     * The Money scale two.
     */
    public static final int MONEY_SCALE_TWO = 2;
    public static final int MONEY_SCALE_FOUR = 4;

    /**
     * 统计总的余额
     *
     * @param company 公司信息
     * @return 总余额 available quota
     */
    public static long getAvailableQuota(AccountCompanyDto company) {
        return company.getAvailableCredit() + company.getCashMoney() + company.getGiftMoney();
    }

    /**
     * Gets integral num.
     *
     * @param amount        the amount
     * @param integralValue the integral value
     * @return the integral num
     */
    public static int getIntegralNum(Integer amount, String integralValue) {
        return null == amount ? 0 : (amount / toIntAmount(new BigDecimal(integralValue)));
    }

    /**
     * 统计总的余额
     *
     * @param account 用户账户对象
     * @return 总余额 int
     */
    public static int calBalanceSum(AccountDto account) {
        return account.getAccountCash() + account.getAccountTravelMoney() + account.getAccountGiftMoney();
    }

    /**
     * 统计总的充电补贴
     *
     * @param account the account
     * @return int
     */
    public static int calChargeSubsidySum(AccountDto account) {
        return (null == account.getChargePileSubsidy() ? 0 : account.getChargePileSubsidy())
                + (null == account.getOtherChargeSubsidy() ? 0 : account.getOtherChargeSubsidy())
                + (null == account.getOperatorRecharge() ? 0 : account.getOperatorRecharge());
    }

    /**
     * Check balance available boolean.
     *
     * @param userPayAmount the user pay amount
     * @param account       the account
     * @return the boolean
     */
    boolean checkBalanceAvailable(BigDecimal userPayAmount, AccountDto account) {
        BigDecimal bUserPayAmount = userPayAmount.setScale(MONEY_SCALE_TWO);
        int iUserPayAmount = bUserPayAmount.multiply(BigDecimal.valueOf(MONEY_TIMES_ONE_HUNDRED)).intValue();
        return iUserPayAmount <= calBalanceSum(account);
    }

    /**
     * Check pay grater balance boolean.
     *
     * @param userPayAmount the user pay amount
     * @param account       the account
     * @return the boolean
     */
    boolean checkPayGraterBalance(BigDecimal userPayAmount, AccountDto account) {
        BigDecimal bUserPayAmount = userPayAmount.setScale(MONEY_SCALE_TWO);
        int iUserPayAmount = bUserPayAmount.multiply(BigDecimal.valueOf(MONEY_TIMES_ONE_HUNDRED)).intValue();
        return iUserPayAmount > calBalanceSum(account);
    }

    /**
     * To int amount integer.
     *
     * @param amount the amount
     * @return the integer
     */
    public static Integer toIntAmount(BigDecimal amount) {
        if (amount == null) {
            return 0;
        }
        return amount.setScale(MONEY_SCALE_TWO, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(MONEY_TIMES_ONE_HUNDRED)).intValue();
    }

    /**
     * Gets decimal string.
     *
     * @param bigDecimal the big decimal
     * @return the decimal string
     */
    public static String getDecimalString(BigDecimal bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO.toPlainString() : bigDecimal.toPlainString();
    }

    /**
     * To double string string.
     *
     * @param amount the amount
     * @return the string
     */
    public static String toDoubleString(int amount) {
        return new BigDecimal(amount)
                .divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }

    /**
     * int来大小数
     *
     * @param amount 金额
     * @return {@link BigDecimal}
     */
    public static BigDecimal intToBigDecimal(Integer amount) {
        if (amount == null) {
            return new BigDecimal(NumConstants.STR_0);
        }
        return new BigDecimal(amount)
                .divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO, BigDecimal.ROUND_DOWN);
    }

    /**
     * To double string string.
     *
     * @param amount the amount
     * @return the string
     */
    public static String toDoubleString(long amount) {
        return new BigDecimal(amount)
                .divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO, BigDecimal.ROUND_DOWN)
                .toPlainString();
    }

    /**
     * Int to big decimal big decimal.
     *
     * @param amount the amount
     * @return the big decimal
     */
    public static BigDecimal intToBigDecimal(int amount) {
        return new BigDecimal(amount)
                .divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO, BigDecimal.ROUND_DOWN);
    }

    /**
     * 计算现金含量
     *
     * @param cashMoney 现金金额
     * @param giftMoney 赠送金额
     * @return 含量 cash percent
     */
    public static String getCashPercent(long cashMoney, long giftMoney) {
        BigDecimal cashRate = new BigDecimal(cashMoney).divide(new BigDecimal(cashMoney + giftMoney), MONEY_SCALE_FOUR,
                BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(MONEY_TIMES_ONE_HUNDRED)).setScale(MONEY_SCALE_TWO);
        return cashRate.toPlainString();
    }

    /**
     * Gets cash percent.
     *
     * @param company the company
     * @return the cash percent
     */
    public static String getCashPercent(AccountCompanyDto company) {
        long cashMoney = company.getCashMoney() + company.getAvailableCashMoney();
        long giftMoney = company.getGiftMoney() + company.getAvailableGiftMoney();
        if ((cashMoney + giftMoney) <= 0) {
            return "100.00";
        }
        BigDecimal cashRate = new BigDecimal(cashMoney).divide(new BigDecimal(cashMoney + giftMoney), MONEY_SCALE_FOUR,
                BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(MONEY_TIMES_ONE_HUNDRED)).setScale(MONEY_SCALE_TWO);
        return cashRate.toPlainString();
    }

    /**
     * 根据hash配置判断 是否支持此业务
     *
     * @param uuid      判断依据
     * @param whitelist 支持白名单
     * @param blacklist 支持黑名单
     * @param percent   支持百分比
     * @return boolean
     */
    public static boolean switchOpenByHash(String uuid, List<String> blacklist,
                                           List<String> whitelist, Integer percent) {
        if (StringUtils.isBlank(uuid)) {
            return false;
        }
        if (!CollectionUtils.isEmpty(blacklist) && blacklist.contains(uuid)) {
            return false;
        }
        if (!CollectionUtils.isEmpty(whitelist) && whitelist.contains(uuid)) {
            return true;
        }
        int hashCode = uuid.hashCode();
        if (hashCode == Integer.MIN_VALUE) {
            hashCode = Integer.MAX_VALUE;
        }
        int hashCodeAbs = Math.abs(hashCode);
        int index = hashCodeAbs % NUM_1024;
        if (index <= percent) {
            return true;
        }
        return false;
    }
}
