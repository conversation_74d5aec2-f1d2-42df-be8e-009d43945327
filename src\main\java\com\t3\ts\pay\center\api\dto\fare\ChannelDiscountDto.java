package com.t3.ts.pay.center.api.dto.fare;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:12
 * @description:
 */
public class ChannelDiscountDto extends BaseDiscountDto {

    @Getter
    @Setter
    List<ChannelDiscountItemDto> discountItemDtoList;

    @Getter
    @Setter
    private BigDecimal finalDiscount;

    //只有在系统一口价 并且是预置附加费场景才有值
    @Getter
    @Setter
    private BigDecimal otherFare;

    /**
     * 一口价场景的一口价费用
     */
    @Getter
    @Setter
    private BigDecimal orderFare;

    @JSONField(serialize = false, deserialize = false)
    private static final BigDecimal MAX_DISCOUNT = new BigDecimal(2);

    public ChannelDiscountItemDto matchCarLevel(Integer carLevel) {
        for (ChannelDiscountItemDto discountItemDto : discountItemDtoList) {
            if (discountItemDto.matchCarLevel(carLevel)) {
                this.finalDiscount = discountItemDto.getUseChannelDiscount() ? discountItemDto.getDicount() : null;
                return discountItemDto;
            }
        }
        return null;
    }

    public boolean validDiscount() {
        return finalDiscount != null
                && (finalDiscount.compareTo(BigDecimal.ZERO) > 0 && finalDiscount.compareTo(MAX_DISCOUNT) < 0);
    }

    /**
     * 用于交易管理平台获取折扣详情
     *
     * @return ChannelDiscountItemDto
     */
    public ChannelDiscountItemDto discountDetailAfterCreateOrder() {
        if (CollectionUtils.isEmpty(discountItemDtoList)) {
            return null;
        }
        return discountItemDtoList.get(0);
    }

}
