package com.t3.ts.pay.center.api.util;

import com.t3.ts.cache.t3.type.T3CacheFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 分布式锁-工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LockUtils {

    /**
     * The constant LOCK_TIME.
     */
    private static final int LOCK_TIME = 5000;

    /**
     * The constant LOCK_TIME.
     */
    private static final int LOCK_TIME_2000 = 2000;

    /**
     * The constant WAIT_TIME.
     */
    private static final int WAIT_TIME = 100;
    /**
     * The constant RE_COUNT.
     */
    private static final int RE_COUNT = 10;

    @Autowired
    private T3CacheFactory t3CacheFactory;


    /**
     * 加锁
     * 一定配合finally中unlock使用
     *
     * @param lockKey  关键
     * @param factory  工厂
     * @param lockTime 锁定时间
     * @return boolean
     */
    public static boolean lock(String lockKey, T3CacheFactory factory, int lockTime) {
        try {
            return factory.getLockService().lock(lockKey, lockTime);
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{}", lockKey, e);
        }
        return false;
    }

    /**
     * 锁
     * 一定配合finally中unlock使用
     *
     * @param lockKey 锁定键
     * @param factory 工厂
     * @return boolean
     */
    public static boolean lock(String lockKey, T3CacheFactory factory) {
        return lock(lockKey, factory, LOCK_TIME_2000);
    }

    /**
     * 释放锁
     *
     * @param lockKey 关键
     * @param factory 工厂
     */
    public static void unLock(String lockKey, T3CacheFactory factory) {
        try {
            factory.getLockService().unLock(lockKey);
        } catch (Exception e) {
            log.error("LockUtils.unLock failed!, lockKey:{}", lockKey, e);
        }
    }

    /**
     * 手动上锁，指定锁value值 如果上锁失败，每 间隔 waitTime ms重试，重试reCount次数
     *
     * @param lockKey    锁Key值
     * @param lockValue  锁value值
     * @param expireTime 锁过期时间，单位:ms
     * @param waitTime   每次请求加锁等待时间，单位:ms
     * @param reCount    重试次数
     * @return boolean 加锁是否成功
     */
    public boolean lockAndWait(String lockKey, String lockValue, int expireTime, int waitTime, int reCount) {
        try {
            return t3CacheFactory.getLockService().lockAndWait(lockKey, lockValue, expireTime, waitTime, reCount);
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{},lockValue:{}", lockKey, lockValue, e);
        }
        return false;
    }

    /**
     * 手动上锁，指定锁value值 ,锁定5秒
     *
     * @param lockKey   锁Key值
     * @param lockValue 锁value值
     * @return boolean 加锁是否成功
     */
    public boolean lock(String lockKey, String lockValue) {
        try {
            return t3CacheFactory.getLockService().lock(lockKey, lockValue, LOCK_TIME);
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{},lockValue:{}", lockKey, lockValue, e);
        }
        return false;
    }

    /**
     * 手动上锁，指定锁value值 ,锁定5秒，如果上锁失败，每 间隔 100 ms重试，重试10次数
     *
     * @param lockKey   锁Key值
     * @param lockValue 锁value值
     * @return boolean 加锁是否成功
     */
    public boolean lockAndWait(String lockKey, String lockValue) {
        try {
            return t3CacheFactory.getLockService().lockAndWait(lockKey, lockValue, LOCK_TIME, WAIT_TIME, RE_COUNT);
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{},lockValue:{}", lockKey, lockValue, e);
        }
        return false;
    }

    /**
     * 手动上锁，指定锁value值 ,锁定5秒
     *
     * @param lockKey 锁Key值
     * @return boolean 加锁是否成功
     */
    public boolean lock(String lockKey) {
        try {
            return t3CacheFactory.getLockService().lock(lockKey, LOCK_TIME);
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{}", lockKey, e);
        }
        //redis异常时兜底返回true（弱依赖改造） --by ligq
        return true;
    }

    /**
     * 释放锁
     *
     * @param lockKey   键
     * @param lockValue 值
     * @return 释放成功或者失败
     */
    public Boolean unLock(String lockKey, String lockValue) {
        try {
            return t3CacheFactory.getLockService().unLock(lockKey, lockValue);
        } catch (Exception e) {
            log.error("LockUtils.unLock failed!, lockKey:{}", lockKey, e);
        }
        return false;
    }

    /**
     * 释放锁
     *
     * @param lockKey 键
     * @return 释放成功或者失败
     */
    public Boolean unLock(String lockKey) {
        try {
            return t3CacheFactory.getLockService().unLock(lockKey);
        } catch (Exception e) {
            log.error("LockUtils.unLock failed!, lockKey:{}", lockKey, e);
        }
        return false;
    }

    /**
     * 核心链路 降级使用锁处理
     *
     * @param lockKey    锁定键
     * @param expireTime 到期时间 单位 毫秒
     * @return {@link Boolean}
     */
    public Boolean lockByCoreLink(String lockKey, int expireTime) {
        Boolean isLock = true;
        try {
            isLock = t3CacheFactory.getLockService().lock(lockKey, expireTime);
            if (isLock == null) {
                log.error("LockUtils.lockByCoreLink failed return null!, lockKey:{} 降级处理 expireTime={}", lockKey, expireTime);
                isLock = true;
            }
        } catch (Exception e) {
            log.error("LockUtils.lockByCoreLink failed!, lockKey:{} 降级处理", lockKey, e);
        }
        return isLock;
    }
    /**
     * 手动上锁，指定锁value值 ,锁定5秒
     *
     * @param lockKey   锁Key值
     * @param lockValue 锁value值
     * @return boolean 加锁是否成功
     */
    public boolean lockByCoreLink(String lockKey, String lockValue) {
        Boolean isLock = true;
        try {
            isLock = t3CacheFactory.getLockService().lock(lockKey, lockValue, LOCK_TIME);
            if (isLock == null) {
                log.error("LockUtils.lockByCoreLink failed return null!, lockKey:{} 降级处理 lockValue={}",
                        lockKey, lockValue);
                isLock = true;
            }
        } catch (Exception e) {
            log.error("LockUtils.lock failed!, lockKey:{},lockValue:{}", lockKey, lockValue, e);
        }
        return isLock;
    }

    /**
     * 核心链路 降级使用锁处理
     *
     * @param lockKey    锁定键
     * @return {@link Boolean}
     */
    public Boolean lockByCoreLink(String lockKey) {
        // lock_time 单位 毫秒  默认锁定5s
        return lockByCoreLink(lockKey, LOCK_TIME);
    }
    /**
     * 核心链路 降级使用锁处理
     *
     * @param lockKey    锁定键
     * @param lockValue  锁的价值
     * @param expireTime 到期时间
     * @param waitTime   等待时间
     * @param reCount    重新计算
     * @return boolean
     */
    public Boolean lockAndWaitByCoreLink(String lockKey, String lockValue, int expireTime, int waitTime, int reCount) {
        Boolean isLock = true;
        try {
            isLock = t3CacheFactory.getLockService().lockAndWait(lockKey, lockValue, expireTime, waitTime, reCount);
            if (isLock == null) {
                log.error("LockUtils.lockByCoreLink failed return null!, lockKey:{},lockValue:{} 降级处理", lockKey, lockValue);
                isLock = true;
            }
        } catch (Exception e) {
            log.error("LockUtils.lockByCoreLink failed!, lockKey:{},lockValue:{} 降级处理", lockKey, lockValue, e);
        }
        return isLock;
    }


}
