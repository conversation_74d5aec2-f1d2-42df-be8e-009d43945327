package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/7/24 9:22
 * @des 1.0
 */

@Data
public class Payed {

    // 当前支付总费用
    private BigDecimal amountPayed;

    // 礼品卡已抵扣
    private BigDecimal giftCardPayed;

    // 省心打已支付
    private BigDecimal unWarriedArrivePay;

    // 积分已抵扣
    private BigDecimal integralPayed;

    private BigDecimal balancePay;

    private BigDecimal couponPay;

    private BigDecimal privilegePay;

    private BigDecimal prPayed;

    /**
     * 企业支付已经支付的总金额
     */
    private BigDecimal companyPayedAmount;
    /**
     * 企业优惠券已经抵扣的金额
     */
    private BigDecimal companyCouponAmount;
    /**
     * 充值本金已支付金额
     */
    private BigDecimal rechargeCashPay;
    /**
     * 充值赠金已支付金额
     */
    private BigDecimal rechargeGiftPay;
    /**
     * 预付款已支付金额
     */
    private BigDecimal giftCashPay;
    /**
     * 企业礼品卡已支付金额
     */
    private BigDecimal companyGiftCardPayed;
}
