package com.t3.ts.pay.center.api.dto.chartered;

import lombok.Data;

import java.util.List;

/**
 * 收银台pro入参
 * <AUTHOR>
 * @date 2020.10.22
 */
@Data
public class PayTradeCharteredReq {

    /**
     * 行程Uuid
     */
    private String routePlanUuid;
    /**
     * 优惠券uuid,企业用车不用
     */
    private String couponUuid;
    /**
     * 券适用类型 0 不使用 1 使用最优券 2 使用指定券,企业用车不用
     */
    private String useCouponType;
    /**
     * 车费支付方式【包含余额+一种第三方支付】
     * -1 没选支付方式
     */
    private List<Integer> payTypeList;

}
