package com.t3.ts.pay.center.api.exception;

import com.t3.ts.enums.BaseEnumInterface;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;

/**
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private static final Integer DEFAULT_EXCEPTION_CODE = 500;

    private Integer code;
    private String msg;
    private Object obj;


    /**
     * @param resultErrorEnum 错误信息
     */
    public BusinessException(ResultErrorEnum resultErrorEnum) {
        super(resultErrorEnum.getMsg());
        this.msg = resultErrorEnum.getMsg();
        this.code = resultErrorEnum.getCode();
    }


    /**
     * @param msg
     */
    public BusinessException(String msg) {
        super(msg);
        this.msg = msg;
        this.code = DEFAULT_EXCEPTION_CODE;
    }

    /**
     * @param msg
     * @param e
     */
    public BusinessException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = DEFAULT_EXCEPTION_CODE;
    }

    /**
     * @param msg
     * @param code
     */
    public BusinessException(String msg, Integer code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    /**
     * @param msg
     * @param code
     * @param obj
     */
    public BusinessException(String msg, Integer code, Object obj) {
        super(msg);
        this.msg = msg;
        this.code = code;
        this.obj = obj;
    }

    /**
     * @param msg
     * @param code
     * @param e
     */
    public BusinessException(String msg, Integer code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    /**
     * @param enumInterface
     */
    public BusinessException(BaseEnumInterface enumInterface) {
        super(enumInterface.getMsg());
        this.msg = enumInterface.getMsg();
        this.code = enumInterface.getCode();
    }

    public String getMsg() {
        return msg;
    }

    public int getCode() {
        return code;
    }

    public Object getObj() {
        return obj;
    }

}
