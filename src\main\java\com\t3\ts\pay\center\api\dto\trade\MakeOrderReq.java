package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.io.Serializable;

/**
 * 支付宝聚合支付下单请求接口
 */
@Data
public class MakeOrderReq implements Serializable {

    /**
     * 支付串
     */
    private String payString;

    /**
     * 三方用户编码
     */
    private String userCode;
    /**
     * 支付并签约标识
     */
    private String payAndSign;

    /**
     * 用户ip
     */
    private String userIp;

}
