package com.t3.ts.pay.center.api.controller;

import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.business.PaymentBusiness;
import com.t3.ts.pay.center.api.business.PrePaymentBusiness;
import com.t3.ts.pay.center.api.business.common.CommonBusiness;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.PayStatusQueryReq;
import com.t3.ts.pay.center.api.dto.PrepaymentStatusResVo;
import com.t3.ts.pay.center.api.dto.RoutePaymentReq;
import com.t3.ts.pay.center.api.dto.common.ChannelListReq;
import com.t3.ts.pay.center.api.dto.route.RouteOrderQueryReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.trade.MakeOrderReq;
import com.t3.ts.pay.center.api.dto.trade.PayDeskVoV3;
import com.t3.ts.pay.center.api.dto.trade.PayStatusQueryReqV2;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.center.api.dto.trade.RechargeReq;
import com.t3.ts.pay.center.api.dto.vo.DataListVo;
import com.t3.ts.pay.center.api.dto.vo.PayWayVo;
import com.t3.ts.pay.center.api.dto.vo.RechargePayVo;
import com.t3.ts.pay.center.api.enums.SpuTypeEnum;
import com.t3.ts.pay.center.api.rest.MallRest;
import com.t3.ts.pay.center.api.rest.mall.RechargeCheckResp;
import com.t3.ts.pay.center.api.util.NetworkUtil;
import com.t3.ts.pay.common.exception.BizExceptionUtil;
import com.t3.ts.pay.remote.constants.PayChannelEnum;
import com.t3.ts.pay.remote.dto.PayStatusDto;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PassengerAPi.java
 * @Description TODO
 * @createTime 2021年06月08日 15:15:00
 */
@RestController
@Slf4j
@RequestMapping("/api/payment")
@Api("Passenger-Api")
public class PassengerAPi extends BaseApi {

    @Autowired
    private PaymentBusiness paymentBusiness;

    @Autowired
    private CommonBusiness commonBusiness;

    @Autowired
    private MallRest mallRest;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private PrePaymentBusiness prePaymentBusiness;
    @Value("${giftcard.channel.showali:true}")
    private boolean giftcardChannelShowali;


    /**
     * 获取支付渠道列表
     *
     * @param channelListReq 频道列表请求
     * @param request        请求
     * @return {@link Response}
     * @throws Exception 异常
     */
    @PostMapping("/v3/channel/list")
    @ApiOperation(value = "获取乘客支付类型列表", notes = "获取乘客支付类型列表")
    public Response<DataListVo> getPayChannelList(
            @RequestBody @Validated ChannelListReq channelListReq, HttpServletRequest request) throws Exception {
        return commonBusiness.channelList(channelListReq, getUserUid(request));
    }


    /**
     * 获取支付渠道列表
     *
     * @param channelListReq 频道列表请求
     * @param request        请求
     * @return {@link Response}
     * @throws Exception 异常
     */
    @PostMapping("/v4/channel/list")
    @ApiOperation(value = "获取乘客支付类型列表", notes = "获取乘客支付类型列表")
    public Response<DataListVo> channelList(
            @RequestBody @Validated ChannelListReq channelListReq, HttpServletRequest request) throws Exception {
        validateChannelListParam(channelListReq);

        String userId = getUserUid(request);
        Response<DataListVo> response = commonBusiness.channelList(channelListReq, userId);

        if(ObjectUtils.isEmpty(response) || !response.isSuccess()) {
            return response;
        }
        DataListVo dataListVo = response.getData();
        if(ObjectUtils.isEmpty(dataListVo)) {
            return response;
        }
        List<PayWayVo> allList = dataListVo.getList();
        if(CollectionUtils.isEmpty(allList)) {
            return response;
        }

        if(BizType.SHOPPING_MALL.getType() != channelListReq.getBizType()
                || CollectionUtils.isEmpty(switchConfig.getGiftCardNotUseChannel())) {
            return response;
        }
        if(!isGiftCard(channelListReq.getOrderUuid(), userId)) {
            return response;
        }
        //删除支付宝
        Iterator<PayWayVo> iterator = allList.iterator();
        while (iterator.hasNext()) {
            PayWayVo payWayVo = iterator.next();
            if(switchConfig.getGiftCardNotUseChannel().contains(payWayVo.getPayChannel())) {
                iterator.remove();
            }
        }
        return response;
    }

    /**
     * 校验参数
     * @param channelListReq 渠道列表
     */
    private void validateChannelListParam(ChannelListReq channelListReq) {
        if(ObjectUtils.isEmpty(channelListReq) || ObjectUtils.isEmpty(channelListReq.getBizType())) {
            BizExceptionUtil.create(ResultErrorEnum.PARAM_NULL_ERROR.getCode(), "业务类型入参必填");
        }
        if(ObjectUtils.isEmpty(channelListReq.getOrderUuid())) {
            BizExceptionUtil.create(ResultErrorEnum.PARAM_NULL_ERROR.getCode(), "订单号入参必填");
        }
    }

    /**
     * 判断礼品卡类型
     * @param orderUuid 订单号
     * @return 是否礼品卡
     */
    private boolean isGiftCard(String orderUuid, String userId) {
        try {
            log.info("isGiftCard orderUuid:{}, userId:{}", orderUuid, userId);
            if(StringUtils.isEmpty(orderUuid)) {
                return false;
            }
            // 订单校验
            Response<?> mallRes = mallRest.rechargeCheck(orderUuid, userId, null);
            if (null != mallRes && !mallRes.isSuccess()) {
                return false;
            }
            if (null != mallRes && mallRes.isSuccess() && mallRes.getCode().equals(CommonNumConst.NUM_10001)) {
                return false;
            }
            RechargeCheckResp res = null;
            if (null != mallRes && mallRes.getSuccess() && null != mallRes.getData()) {
                res = JSONUtil.toBean(JSONUtil.toJsonStr(mallRes.getData()), RechargeCheckResp.class);
            }
            if (null == res || res.getPayCash() == null || res.getPayCash().compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }
            final Integer spuType = res.getSpuType();
            if (spuType == SpuTypeEnum.CARD.getCode().intValue()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.info("isGiftCard error,information:", e);
            return false;
        }
    }

    /**
     * 获取付款台信息
     *
     * @param payTradeReq 支付交易请求
     * @param request     请求
     * @return {@link Response}
     */
    @PostMapping("/v3/pay/desk/info")
    @ApiOperation(value = "获取收银台信息", notes = "获取收银台信息")
    public Response<PayDeskVoV3> getPayDeskInfo4V3(@RequestBody @Validated PayTradeReq payTradeReq,
                                                   HttpServletRequest request) {
        payTradeReq.setGrayBuild(getGrayBuild(request));
        payTradeReq.setGrayVersion(getGrayVersion(request));
        return paymentBusiness.getPayDeskInfoV3(payTradeReq, getUserUid(request));
    }


    /**
     * 行程付款
     *
     * @param routePaymentReq 行程付款申请
     * @param request         参数
     * @return {@link Response<RechargePayVo>}
     */
    @PostMapping("/v1/route/pay")
    @ApiOperation(value = "收银台支付接口", notes = "收银台支付接口")
    public Response<RechargePayVo> routePay(
            @RequestBody @Validated RoutePaymentReq routePaymentReq,
            HttpServletRequest request) {
        routePaymentReq.setGrayBuild(getGrayBuild(request));
        routePaymentReq.setGrayVersion(getGrayVersion(request));
        routePaymentReq.setUserIp(NetworkUtil.getOutIp(request));
        return paymentBusiness.routePay(routePaymentReq, getUserUid(request), getUserMobile(request));
    }


    /**
     * 查询支付状态
     *
     * @param queryReq 查询请求
     * @param request  请求
     * @return {@link Response}
     */
    @PostMapping("/v1/payStatusQuery")
    @ApiOperation(value = "查询订单支付状态", notes = "查询订单支付状态")
    public Response<PayStatusDto> payStatusQuery4Special(@RequestBody @Valid PayStatusQueryReq queryReq,
                                                         HttpServletRequest request) {
        queryReq.setUserIds(getUserUid(request));
        Integer payStatus = paymentBusiness.payStatusQuery(queryReq.getOrderUuid());
        PayStatusDto payStatusDto = new PayStatusDto();
        payStatusDto.setPayStatus(payStatus);
        payStatusDto.setRoutePlanUuid(queryReq.getOrderUuid());
        return Response.createSuccess(payStatusDto);
    }

    /**
     * 预付款支付-充值
     *
     * @param rechargeReq 充值请求
     * @param request     请求
     * @return {@link Response}
     */
    @PostMapping("/v2/pay/recharge")
    @ApiOperation(value = "乘客充值", notes = "乘客充值")
    public Response recharge(@RequestBody RechargeReq rechargeReq, HttpServletRequest request) {
        rechargeReq.setGrayBuild(getGrayBuild(request));
        rechargeReq.setGrayVersion(getGrayVersion(request));
        if (StringUtils.isEmpty(rechargeReq.getRoutePlanUuid())) {
            rechargeReq.setRoutePlanUuid(rechargeReq.getOrderUuid());
        }
        rechargeReq.setUserIp(NetworkUtil.getOutIp(request));
        return prePaymentBusiness.recharge(rechargeReq, getUserUid(request));
    }


    /**
     * 支付宝聚合支付下单接口
     *
     * @param makeOrderReq 下单请求
     * @param request      请求
     * @return {@link Response}
     */
    @PostMapping("/v2/pay/makeOrder")
    @ApiOperation(value = "用户下单", notes = "用户下单")
    public Response makeOrder(@RequestBody MakeOrderReq makeOrderReq, HttpServletRequest request) {
        makeOrderReq.setUserIp(NetworkUtil.getOutIp(request));
        return paymentBusiness.makeOrder(makeOrderReq);
    }

    /**
     * 预付款支付情况查询
     *
     * @param queryReq 查询请求
     * @param request  请求
     * @return {@link Response}
     */
    @PostMapping("/v2/prePayStatusQuery")
    @ApiOperation(value = "预付款支付情况查询", notes = "预付款支付情况查询")
    public Response prePayStatusQuery(@RequestBody @Valid PayStatusQueryReqV2 queryReq, HttpServletRequest request) {
        queryReq.setUserIds(getUserUid(request));
        return prePaymentBusiness.prePayStatusQuery(queryReq);
    }

    /**
     * 预付款支付情况查询（后端发起的自动支付，端上没有settlementId）
     *
     * @param queryReq 查询请求
     * @param request  请求
     * @return {@link Response}
     */
    @PostMapping("/v1/prePayStatusQueryByOrderUuid")
    @ApiOperation(value = "预付款免密支付结果查询", notes = "预付款免密支付结果查询")
    public Response<PrepaymentStatusResVo> prePayStatusQueryByOrderUuid(@RequestBody PayStatusQueryReqV2 queryReq,
                                                                        HttpServletRequest request) {
        queryReq.setUserIds(getUserUid(request));
        return prePaymentBusiness.prePayStatusQueryByOrderUuid(queryReq);
    }

    /**
     * 预付款支付状态查询（变更行程）
     *
     * @param routePlanUuidReq 路由计划UUID请求
     * @return {@link Response}
     */
    @PostMapping("/v2/route/checkRoutePlanEdit")
    @ApiOperation(value = "预付款支付状态查询（变更行程）", notes = "预付款支付状态查询（变更行程）")
    public Response checkRoutePlanEdit(@RequestBody RoutePlanUuidReq routePlanUuidReq) {
        return prePaymentBusiness.checkRoutePlanEdit(routePlanUuidReq.getRoutePlanUuid());
    }

    /**
     * 根据支付单号查询行程单号
     *
     * @param routeOrderQueryReq 查询请求
     * @param request  请求
     * @return {@link Response}
     */
    @PostMapping("/v1/pay/queryRoutePlanUuid")
    @ApiOperation(value = "根据支付单号查询行程单号", notes = "根据支付单号查询行程单号")
    public Response queryRoutePlanUuid(@RequestBody RouteOrderQueryReq routeOrderQueryReq, HttpServletRequest request) {
        routeOrderQueryReq.setUserId(getUserUid(request));
        return paymentBusiness.queryRoutePlanUuid(routeOrderQueryReq);
    }

    /**
     * 根据行程单号查询蚂蚁森林能量
     *
     * @param routePlanUuidReq 查询请求
     * @param request  请求
     * @return {@link Response}
     */
    @PostMapping("/v1/pay/queryAliEcoRecycle")
    @ApiOperation(value = "根据行程单号查询蚂蚁森林能量", notes = "根据行程单号查询蚂蚁森林能量")
    public Response queryAliEcoRecycle(@RequestBody RoutePlanUuidReq routePlanUuidReq, HttpServletRequest request) {
        return paymentBusiness.queryAliEcoRecycle(routePlanUuidReq.getRoutePlanUuid(), getUserUid(request));
    }

}
