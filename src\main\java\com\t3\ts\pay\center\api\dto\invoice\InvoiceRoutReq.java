package com.t3.ts.pay.center.api.dto.invoice;

import com.t3.ts.pay.center.api.dto.PageReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.*;
import java.util.List;

/**
 * 迁移 by ivy .2021/09/17 10:40
 *
 * @Author: lijb1
 * @Date: 2020/10/26
 * @Description: 发票流程请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class InvoiceRoutReq extends PageReq implements Serializable {

    private static final long serialVersionUID = 4071729521333012703L;

    private String uuid;

    /**
     * 开始月份
     */
    @ApiModelProperty(value = "开始月份--格式yyyyMM")
    private String startMonth;

    /**
     * 月末
     */
    @ApiModelProperty(value = "结束--格式yyyyMM")
    private String endMonth;

    /**
     * 查询时间开始
     */
    @ApiModelProperty(value = "查询时间开始，格式yyyyMMdd")
        private String startDay;

    @ApiModelProperty(value = "查询时间截止，格式yyyyMMdd")
    private String endDay;

    @ApiModelProperty(value = "城市条件，如南京320100")
    private String cityCode;

    @ApiModelProperty(value = "订单类型  0积分商城 7 商城礼品卡")
    private Integer bizType;

    @ApiModelProperty(value = "行程id列表")
    private List<String> routePlanUuids;

    @ApiModelProperty(value = "被代交车人虚拟乘客id")
    private String virtualPassengerUuid;

    /**
     * 下次查询月份索引
     */
    private String nextIndex;
    /**
     * 用户类型，默认1
     */
    private Integer accountType = 1;

    /**
     * 订单id
     */
    private String orderNo;
}
