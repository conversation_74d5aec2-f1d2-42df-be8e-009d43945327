package com.t3.ts.pay.center.api.service;

import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.result.Response;

/**
 * 出行服务业务实现
 *
 * <AUTHOR>
 */
public interface RouteBusiness extends ResponseConverter {

    /**
     * 查询订单费用详情
     *
     * @param req 参数
     * @return 出参
     */
    Response<PassengerRouteFareItemsVo> getPassengerRouteFareItems(RouteFareItemReq req);

    /**
     * getEditRoutePointByCache
     *
     * @param routePlanUuid 行程计划Uuid
     * @return 出参
     * @throws
     * @Description: 查询预付款变更行程
     * <AUTHOR>
     * @date 2021/9/8 10:52
     */
    Response getEditRoutePointByCache(String routePlanUuid);
}
