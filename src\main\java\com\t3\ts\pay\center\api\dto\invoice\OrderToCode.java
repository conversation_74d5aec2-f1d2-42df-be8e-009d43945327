package com.t3.ts.pay.center.api.dto.invoice;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> z<PERSON>ming<PERSON><PERSON>
 * @date : 2020/10/29
 * @Title : OrderToInvoiceSubjectCode
 * @Package : com.t3.ts.app.api.third.invoice.req
 * @Description : 订单id和开票主体code对应关系实体类
 */
@Data
public class OrderToCode implements Serializable {
    /**
     * 行程订单uuid
     */
    private String orderUuid;
    /**
     * 开票主体code
     */
    private Integer invoiceSubjectCode;

    /**
     * 单个行程的money
     */
    private BigDecimal money;
}
