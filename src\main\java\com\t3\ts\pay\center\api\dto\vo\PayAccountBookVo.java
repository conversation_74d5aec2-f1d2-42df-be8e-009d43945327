/**
 * <AUTHOR>
 * @date ：Created in 2021/4/27 15:09
 * @description：
 */

package com.t3.ts.pay.center.api.dto.vo;

import com.t3.ts.account.center.dto.PayAccountBookDto;
import com.t3.ts.pay.center.api.util.MoneyConvert;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 迁移 by ivy .2021/09/17 14:35
 *
 * @Author: qul
 * @Date: 2021/4/27 15:09
 */
@Getter
@Setter
public class PayAccountBookVo implements Serializable {

    private static final long serialVersionUID = 2008229294490541037L;

    private Integer bookStatus;

    /**
     * 总余额=可用余额+冻结余额
     */
    private String totalBalance;

    /**
     * 可用余额
     */
    private String availableBalance;

    /**
     * 冻结余额
     */
    private String freezeBalance;

    /**
     * 可提现金额
     */
    private String withdrawalBalance;


    public PayAccountBookVo() {
    }

    /**
     * @param dto PayAccountBookDto
     */
    public PayAccountBookVo(PayAccountBookDto dto) {
        this.setBookStatus(dto.getBookStatus());
        this.setAvailableBalance(MoneyConvert.longOrNullConvertBig(dto.getAvailableBalance()).toPlainString());
        this.setFreezeBalance(MoneyConvert.longOrNullConvertBig(dto.getFreezeBalance()).toPlainString());
        this.setWithdrawalBalance(MoneyConvert.longOrNullConvertBig(dto.getWithdrawalBalance()).toPlainString());
        this.setTotalBalance(MoneyConvert.longOrNullConvertBig(dto.getTotalBalance()).toPlainString());
    }
}
