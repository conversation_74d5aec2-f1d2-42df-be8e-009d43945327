package com.t3.ts.pay.center.api.dto.wallet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;


/**
 * 帐户余额 ResVo
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
@Getter
@Setter
@ApiModel("账户余额VO")
public class AccountBalanceResVo {
    /**
     * 可用余额
     */
    @ApiModelProperty(value = "可用余额")
    private String availableBalance = "0";
    /**
     * bookStatus
     */
    private String bookStatus;
    /**
     * 账本类型
     */
    private Integer bookType;
    private String createTime;
    /**
     * 冻结余额
     */
    @ApiModelProperty(value = "手动冻结余额")
    private String freezeBalance;

    @ApiModelProperty(value = "手动冻结余额原因")
    private String freezeReason;
    private String remark;
    /**
     * 总余额
     */
    @ApiModelProperty(value = "总余额")
    private String totalBalance = "0";

    private String updateTime;
    /**
     * 可提现余额
     */
    @ApiModelProperty(value = "可提现余额")
    private String withdrawalBalance = "0";

    @ApiModelProperty(value = "提现说明方案")
    private String drawCashTip;

    @ApiModelProperty(value = "账户冻结原因")
    private String acctFreezeReason;

    @ApiModelProperty(value = "硬件保证金")
    private String hardAwreCashDeposit = "0";

    @ApiModelProperty(value = "提现中金额")
    private String withdrawingBalance;

    @ApiModelProperty(value = "核算中金额")
    private String accountingBalance;

    /**
     * 最近可解冻金额日期
     */
    @ApiModelProperty(value = "最近可解冻金额日期")
    private Date unfreezeDate;
    /**
     * 最近可解冻金额
     */
    @ApiModelProperty(value = "最近可解冻金额")
    private String unfreezeAmount;

    @ApiModelProperty(value = "待入账金额")
    private String prepareBalance;

    @ApiModelProperty(value = "租金代扣金额")
    private String rentAmount;

    @ApiModelProperty(value = "提现中冻结金额")
    private String withdrawFreezeAmt;

    @ApiModelProperty(value = "入账中说明")
    private String prepareTip;
}
