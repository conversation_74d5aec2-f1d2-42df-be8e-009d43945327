package com.t3.ts.pay.center.api.dto.chartered;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020.10.22
 */
@Data
public class FareDetailDto {
    /**
     * 附加费
     */
    private String serviceFare;
    /**
     * 订单费
     */
    private String orderFare;
    /**
     * 行程取消费
     */
    private BigDecimal cancelFee;
    /**
     * 剩余应付金额
     */
    private String remainPay;
    /**
     * 行程总费用
     */
    private String totalFare;
}
