package com.t3.ts.pay.center.api.rest.risk;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.pay.center.api.rest.BaseRest;
import com.t3.ts.pay.center.api.service.ResponseConverter;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 营销Rest接口封装
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class RiskCheckRest extends BaseRest implements InitializingBean, ResponseConverter {

    private OkHttpClient okHttpClient = null;
    @Autowired
    private SlbConfig slbConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(500, TimeUnit.MILLISECONDS)
                .writeTimeout(500, TimeUnit.MILLISECONDS)
                .readTimeout(500, TimeUnit.MILLISECONDS)
                .build();
    }

    /**
     * 调用风控
     *
     * @param checkReqDto json
     * @return boolean
     */
    public Response unifiedVerify(RiskCheckReqDto checkReqDto) {
        String url = slbConfig.getRiskGateway() + "/validate/unifiedVerify";
        String postReturn = sendPostHttpClient(okHttpClient, url, JSON.toJSONString(checkReqDto));
        if (StringUtils.isBlank(postReturn)) {
            return null;
        }
        return JSON.parseObject(postReturn, Response.class);
    }
}
