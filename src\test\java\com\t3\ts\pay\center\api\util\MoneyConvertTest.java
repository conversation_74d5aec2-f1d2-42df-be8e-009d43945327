package com.t3.ts.pay.center.api.util;

import java.math.BigDecimal;
import org.junit.Assert;
import org.junit.Test;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:02
 */
public class MoneyConvertTest {

    @Test
    public void testIntOrNullConvertBig() throws Exception {
        BigDecimal result = MoneyConvert.intOrNullConvertBig(Integer.valueOf(0));
        Assert.assertEquals(new BigDecimal(0), result);
    }

    @Test
    public void testLongOrNullConvertBig() throws Exception {
        BigDecimal result = MoneyConvert.longOrNullConvertBig(Long.valueOf(1));
        Assert.assertEquals(new BigDecimal(0), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
