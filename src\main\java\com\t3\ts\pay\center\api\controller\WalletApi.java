package com.t3.ts.pay.center.api.controller;

import com.t3.ts.account.center.dto.PersonSuperviseAccountDto;
import com.t3.ts.pay.center.api.business.WalletBusiness;
import com.t3.ts.pay.center.api.dto.BasePageReq;
import com.t3.ts.pay.center.api.dto.CashRefundDto;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.pay.center.api.dto.vo.PayAccountBookVo;
import com.t3.ts.pay.center.api.dto.wallet.AmountWalletVo;
import com.t3.ts.pay.center.api.dto.wallet.ThirdWalletVo;
import com.t3.ts.pay.common.constant.account.AccountTypeEnum;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.remote.dto.CashRefundQueryDto;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import com.t3.ts.withdraw.center.enums.WithdrawCardType;
import com.t3.ts.withdraw.center.remote.dto.BindCardDto;
import com.t3.ts.withdraw.center.remote.service.BindCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WalletApi.java
 * @Description TODO
 * @createTime 2021年08月02日 18:29:00
 */
@RestController
@Slf4j
@RequestMapping("/api/wallet")
@Api("Wallet-Api")
public class WalletApi extends BaseApi {

    @Autowired
    private WalletBusiness walletBusiness;
    @DubboReference
    private BindCardService bindCardService;

    /**
     * 余额流水详情
     *
     * @param basePageReq 基本页要求
     * @param request     请求
     * @return {@link Response}
     */
    @PostMapping("/account/flow/passenger")
    @ApiOperation(value = "余额流水详情", notes = "余额流水详情")
    public Response getAccountFlowPassenger(@RequestBody BasePageReq basePageReq, HttpServletRequest request) {
        return walletBusiness.getAccountFlowPassenger(basePageReq, getUserUid(request));
    }

    /**
     * 查询账户信息
     *
     * @param request  请求
     * @param queryDto 请求
     * @return {@link Response}
     */
    @PostMapping("/accountInfo")
    @ApiOperation(value = "获取乘客钱包账户信息", notes = "获取乘客钱包账户信息")
    public Response<AmountWalletVo> accountInfo(@RequestBody(required = false) QueryDto queryDto,
                                                HttpServletRequest request) {
        if (Objects.isNull(queryDto)) {
            queryDto = new QueryDto();
        }
        queryDto.setUserId(getUserUid(request));
        queryDto.setAdCode(queryDto.getAdCode());
        queryDto.setGrayVersion(getGrayVersion(request));
        return walletBusiness.accountInfo(queryDto);
    }

    /**
     * 查询乘客钱包三方信息，包含
     * 1.乘客借款额度查询
     *
     * @param request  请求
     * @param queryDto 请求
     * @return {@link Response}
     */
    @PostMapping("/third")
    @ApiOperation(value = "获取乘客钱包三方信息", notes = "获取乘客钱包三方信息")
    public Response<ThirdWalletVo> thirdInfo(@RequestBody(required = false) QueryDto queryDto,
                                             HttpServletRequest request) {
        if (Objects.isNull(queryDto)) {
            queryDto = new QueryDto();
        }
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.thirdInfo(queryDto);
    }

    /**
     * 现金余额流水明细
     *
     * @param request  request
     * @param queryDto queryDto
     * @return Response
     */
    @PostMapping("/account/cash/flow/passenger")
    @ApiOperation(value = "现金余额流水详情", notes = "现金余额流水详情")
    public Response getAccountCashFlowPassenger(HttpServletRequest request, @RequestBody QueryDto queryDto) {
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.getAccountCashFlowPassenger(queryDto);
    }

    /**
     * 查询账户信息
     *
     * @param request  请求
     * @param queryDto queryDto
     * @return {@link Response}
     */
    @PostMapping("/account/bindCardInfo")
    @ApiOperation(value = "获取乘客绑卡信息", notes = "获取乘客绑卡信息")
    public Response bindCardInfo(HttpServletRequest request, @RequestBody QueryDto queryDto) {
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.bindCardInfo(queryDto);
    }


    /**
     * 查询账户信息
     *
     * @param request      请求
     * @param bindCardInfo 绑卡信息
     * @return {@link Response}
     */
    @PostMapping("/account/bindCard")
    @ApiOperation(value = "乘客绑卡", notes = "乘客绑卡")
    public Response bindCard(HttpServletRequest request, @RequestBody PersonSuperviseAccountDto bindCardInfo) {
        bindCardInfo.setUserId(getUserUid(request));
        return walletBusiness.bindCard(bindCardInfo);
    }

    /**
     * 获取乘客钱包账户信息
     *
     * @param request  请求
     * @param queryDto queryDto
     * @return {@link Response}
     */
    @PostMapping("/account/cash/detail")
    @ApiOperation(value = "获取乘客钱包账户信息", notes = "获取乘客钱包账户信息")
    public Response<AmountWalletVo> cashDetail(HttpServletRequest request, @RequestBody QueryDto queryDto) {
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.cashDetail(queryDto);
    }


    /**
     * 获取乘客钱包账户信息
     *
     * @param request  请求
     * @param queryDto queryDto
     * @return {@link Response}
     */
    @PostMapping("/book/balance")
    @ApiOperation(value = "获取乘客指定账本余额信息", notes = "获取乘客指定账本余额信息")
    public Response<PayAccountBookVo> bookBalance(HttpServletRequest request, @RequestBody QueryDto queryDto) {
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.bookBalance(queryDto);
    }

    /**
     * 开户
     *
     * @param request  请求
     * @param queryDto queryDto
     * @return {@link Response}
     */
    @PostMapping("/account/cash/openAcc")
    @ApiOperation(value = "开户", notes = "获取乘客钱包账户信息")
    public Response<?> personOpenAcc(HttpServletRequest request, @RequestBody QueryDto queryDto) {
        queryDto.setUserId(getUserUid(request));
        return walletBusiness.openAcc(queryDto);
    }

    /**
     * 充值退款
     *
     * @param request       request
     * @param cashRefundDto cashRefundDto
     * @return Response
     */
    @PostMapping("/v2/account/cash/refund")
    @ApiOperation(value = "充值退款", notes = "充值退款")
    public Response cashRefund(HttpServletRequest request, @RequestBody CashRefundDto cashRefundDto) {
        String userUid = getUserUid(request);
        cashRefundDto.setIncomeSubject(userUid);
        cashRefundDto.setPaymentSubject(userUid);
        return walletBusiness.cashRefund(cashRefundDto);
    }

    /**
     * 充值退款
     *
     * @param request request
     * @param dto     dto
     * @return Response
     */
    @PostMapping("/v2/account/cash/refundQuery")
    @ApiOperation(value = "充值退款（查询）", notes = "充值退款（查询）")
    public Response<PageResult<CashRefundQueryDto>> cashRefundQuery(HttpServletRequest request,
                                                                    @RequestBody CashRefundQueryDto dto) {
        String userUid = getUserUid(request);
        dto.setUserId(userUid);
        return walletBusiness.cashRefundQuery(dto);
    }


    /**
     * 开户
     *
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/account/prePay/timeLine")
    @ApiOperation(value = "预付款时间轴", notes = "预付款时间轴")
    public Response<?> prePayTimeLine(HttpServletRequest request) {
        return walletBusiness.prePayTimeLine(getUserUid(request));
    }

    /**
     * 开户
     *
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/account/prePay/refund")
    @ApiOperation(value = "用户手动退款", notes = "用户手动退款")
    public Response<?> prePayRefund(HttpServletRequest request) {
        return walletBusiness.prePayRefund(getUserUid(request), getUserMobile(request));
    }


    /**
     * 绑卡或更新卡
     *
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/bindCard")
    @ApiOperation(value = "绑定或更新银行卡", notes = "绑定或更新银行卡")
    public Response bindOrUpdateCard(HttpServletRequest request,
                             @RequestBody BindCardDto dto) {
        if (StringUtils.isEmpty(dto.getCardNo())) {
            return Response.createError("卡号不能为空");
        }
        //完善用户信息
        String userUid = getUserUid(request);
        if (StringUtils.isEmpty(userUid)) {
            return Response.createError("获取登录信息失败");
        }
        dto.setUserId(userUid);
        dto.setInterBankNum("");
        //默认乘客
        dto.setAccountType(AccountTypeEnum.PASSENGER_TYPE.getType());
        //类型不传默认支付宝
        if (dto.getCardType() == null) {
            dto.setCardType(WithdrawCardType.ALI_PAY.getType());
            dto.setBankName(WithdrawCardType.ALI_PAY.getDesc());
            dto.setBankLocation(WithdrawCardType.ALI_PAY.getDesc());
        } else if (dto.getCardType().equals(WithdrawCardType.ALI_PAY.getType())) {
            dto.setBankName(WithdrawCardType.ALI_PAY.getDesc());
            dto.setBankLocation(WithdrawCardType.ALI_PAY.getDesc());
        }
        return bindCardService.bindOrUpdate(dto);
    }

    /**
     * 绑卡或更新卡
     *
     * @param request 请求
     * @return {@link Response}
     */
    @PostMapping("/queryCard")
    @ApiOperation(value = "绑定或更新银行卡", notes = "绑定或更新银行卡")
    public Response<BindCardDto> queryCard(HttpServletRequest request) {
        //完善用户信息
        String userUid = getUserUid(request);
        if (StringUtils.isEmpty(userUid)) {
            return Response.createError("获取登录信息失败");
        }
        BindCardDto dto = new BindCardDto();
        dto.setUserId(userUid);
        //默认乘客
        dto.setAccountType(AccountTypeEnum.PASSENGER_TYPE.getType());
        return bindCardService.queryBind(dto);
    }

}
