package com.t3.ts.pay.center.api.service.pay;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;

/**
 * Description: 支付适配接口
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 16:34
 */
public interface PayHandler {

    /**
     * 当前类适配的支付类型
     *
     * @return {@link String}
     */
    String getType();

    /**
     * 当前类适配的业务类型
     * @param context context
     * @return {@link Integer}
     */
    Integer getBizType(PayContext context);

    /**
     * 执行
     *
     * @param context 上下文
     * @return {@link Response}
     */
    default Response<JSONObject> process(PayContext context) {

        checkParam(context);

        checkSettlement(context);

        checkPayWay(context);

        PaymentDto param = createPaymentParam(context);

        Response<String> response = doPay(param);

        if (!response.isSuccess()) {
            return Response.createError(response.getMsg());
        }

        routePayChannel(response, context);

        return processResponse(context);
    }

    /**
     * 检查入参
     *
     * @param context 上下文
     */
    default void checkParam(PayContext context) {
        if (StringUtils.isEmpty(context.getUserId()) || StringUtils.isEmpty(context.getType())
                || StringUtils.isEmpty(context.getMessage())) {
            throw new IllegalArgumentException("用户|类型不能为空");
        }
    }

    /**
     * 检查支付方式
     *
     * @param context 上下文
     */
    void checkPayWay(PayContext context);

    /**
     * 检查结算单
     *
     * @param context 上下文
     */
    void checkSettlement(PayContext context);

    /**
     * 创建支付参数
     *
     * @param context 上下文
     * @return {@link PaymentDto}
     */
    PaymentDto createPaymentParam(PayContext context);

    /**
     * 进行支付
     *
     * @param dto dto
     * @return {@link Response<String>}
     */
    Response<String> doPay(PaymentDto dto);

    /**
     * 根据不同渠道路由
     *
     * @param payInfo 支付信息
     * @param context 上下文
     */
    void routePayChannel(Response<String> payInfo, PayContext context);

    /**
     * 组织返回信息
     *
     * @param context 上下文
     * @return {@link Response}
     */
    Response<JSONObject> processResponse(PayContext context);
}
