package com.t3.ts.pay.center.api.dto.fare;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/22 10:10
 * @description:
 */
@Data
public class MemberDiscountDto implements Serializable {

    //预估的时候多车型
    //下单的时候fareModelJson只保留当前下单车型数据
    private List<MemberDiscountItemDto> itemDtos;

    //最终使用的折扣
    private BigDecimal finalDiscount;

    //最终使用的版本
    private String finalVersion;

}
