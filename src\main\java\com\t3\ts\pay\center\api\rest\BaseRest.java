package com.t3.ts.pay.center.api.rest;

import com.t3.ts.pay.center.api.config.PayCenterApiWebConfig;
import com.t3.ts.pay.center.api.config.valueconfig.SlbConfig;
import com.t3.ts.pay.common.util.JSONFilterUtils;
import com.t3.ts.pay.common.util.JSONLogUtils;
import com.t3.ts.rpc.sentinel.http.client.HttpSync;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseRest.java
 * @Description TODO
 * @createTime 2021年06月23日 20:10:00
 */
@Slf4j
public class BaseRest implements InitializingBean {
    @Autowired
    private SlbConfig slbConfig;

    private OkHttpClient okHttpClient = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        okHttpClient = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectTimeout(slbConfig.getHttpConnectTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(slbConfig.getHttpWriteTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(slbConfig.getHttpReadTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }

    /**
     * 发送post http
     *
     * @param url  url
     * @param data 报文
     * @return {@link String}
     */
    protected String sendPost(String url, String data) {
        String response = HttpSync.post(okHttpClient, url, data);
        log.info("sendHttp response url:{}  data:{} resp:{}",
                url, data, JSONLogUtils.getDefaultString(response));
        return response;
    }


    /**
     * @param url  url
     * @param data 报文
     * @return {@link String}
     */
    public String sendPostHttp(String url, String data) {
        log.info("sendHttp request url:{}  data:{}", url,
                JSONLogUtils.getLogObjString(data, JSONFilterUtils.getFilterByExcludes("phone")));
        String response = HttpSync.post(url, data);
        log.info("sendHttp response url:{} data:{}", url,
                JSONLogUtils.getLogObjString(response, JSONFilterUtils.getFilterByExcludes("phone")));
        return response;
    }


    /**
     * @param client client
     * @param url    url
     * @param data   报文
     * @return {@link String}
     */
    public String sendPostHttpClient(OkHttpClient client, String url, String data) {
        String response = HttpSync.post(client, url, data);
        log.info("sendHttp response url:{} req:{} response:{}", url,
                JSONLogUtils.getLogObjString(data, JSONFilterUtils.getFilterByExcludes("phone","mobile")),
                JSONLogUtils.getLogObjString(response, JSONFilterUtils.getFilterByExcludes("phone","mobile")));
        return response;
    }

}
