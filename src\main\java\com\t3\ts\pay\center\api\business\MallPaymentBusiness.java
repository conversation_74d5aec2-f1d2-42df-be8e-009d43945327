package com.t3.ts.pay.center.api.business;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.business.wechat.Code2SessionEnum;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.constants.RiskTerminalTypeEnum;
import com.t3.ts.pay.center.api.controller.BaseApi;
import com.t3.ts.pay.center.api.dto.PayOrderSureReq;
import com.t3.ts.pay.center.api.dto.mall.MallPayContext;
import com.t3.ts.pay.center.api.enums.SpuTypeEnum;
import com.t3.ts.pay.center.api.exception.BizExceptionEnum;
import com.t3.ts.pay.center.api.exception.BizExceptionUtil;
import com.t3.ts.pay.center.api.rest.MallRest;
import com.t3.ts.pay.center.api.rest.mall.RechargeCheckResp;
import com.t3.ts.pay.center.api.rest.risk.RiskCheckReqDto;
import com.t3.ts.pay.center.api.rest.risk.RiskCheckReqParamDto;
import com.t3.ts.pay.center.api.rest.risk.RiskCheckRest;
import com.t3.ts.pay.center.api.util.BigDecimalUtils;
import com.t3.ts.pay.center.api.util.MoneyUtils;
import com.t3.ts.pay.center.api.util.NetworkUtil;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.common.constant.pay.PayConstant;
import com.t3.ts.pay.common.exception.BizException;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.PayChannelEnum;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.dto.ali.ZhimaOrderParam;
import com.t3.ts.pay.remote.service.UnifiedPaymentFacade;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.pay.remote.service.ZhimaService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.dto.shopping.SettlementShoppingAddDto;
import com.t3.ts.settlement.centre.dto.sr.SettlementGeneralExtInfoDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.enums.SettleType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import com.t3.ts.settlement.centre.service.SettlementShoppingService;
import com.t3.ts.utils.DateUtils;
import com.t3.ts.utils.StringUtils;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.STR_203;

/**
 * 商城收银台
 *
 * <AUTHOR>
 * @date 2022/02/22 10:57
 */
@Component
@Slf4j
public class MallPaymentBusiness {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    /**
     * 礼品卡购买风控场景
     */
    private static final String RISK_GIFTCARD_PAY_SCENE_TYPE = "GIFTCARD_PAY";

    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private MallRest mallRest;
    @DubboReference
    private SettlementShoppingService settlementShoppingService;
    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @DubboReference
    private UnifiedPaymentFacade unifiedPaymentFacade;
    @DubboReference
    private ZhimaService zhimaService;
    @Autowired
    private PayChannelFactory payChannelFactory;
    @Autowired
    private SwitchConfig switchConfig;
    @Autowired
    private RiskCheckRest riskCheckRest;
    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;

    /**
     * @param req 请求
     * @return Response
     */
    public Response<?> mallPay(PayOrderSureReq req, HttpServletRequest request) {
        try {
            checkParam(req);
            return cashPay(req, request);
        } catch (BizException e) {
            return Response.createError(e.getRes().getMsg(), e.getRes().getCode());
        }
    }

    /**
     * 礼品卡购买接入风控 -- 可降级
     *
     * @param req 入参
     */
    private void riskCheck(PayOrderSureReq req, HttpServletRequest request) {
        Response response = null;
        try {
            //构建风控参数
            RiskCheckReqDto riskCheckReqDto = RiskCheckReqDto.builder()
                    .paramMap(getRiskCheckParamDto(req, request))
                    .build();

            //风控校验
            response = riskCheckRest.unifiedVerify(riskCheckReqDto);

            if (ObjectUtils.isEmpty(response) || response.isSuccess()) {
                return;
            }
        } catch (Exception e) {
            log.error("riskCheck error,information: ", e);
            //调用接口异常直接返回
            return;
        }
        BizExceptionUtil.create(response.getMsg(), ResultErrorEnum.RISK_ERROR.getCode());

    }

    /**
     * 获取风控校验参数
     *
     * @return
     */
    private RiskCheckReqParamDto getRiskCheckParamDto(PayOrderSureReq req, HttpServletRequest request) {
        Date date = new Date();
        String grayVersion = BaseApi.getGrayVersion(request);
        Integer payChannel = req.getPayOrderChannel();
        return RiskCheckReqParamDto
                .builder()
                .payTime(date.getTime())
                .payDate(DateUtils.format(date))
                .userId(req.getUserId())
                .mobile(BaseApi.getUserMobile(request))
                .userAgent(BaseApi.getUserAgent(request))
                .appVersion(grayVersion)
                .ip(NetworkUtil.getOutIp(request))
                .longitude(req.getLongitude())
                .latitude(req.getLatitude())
                .riskDeviceToken(BaseApi.getRiskDeviceToken(request))
                .terminalType(RiskTerminalTypeEnum.getTypeByGrayVersion(grayVersion))
                .giftCardNumber(req.getOrderCode())
                .payChannel(ObjectUtils.isEmpty(payChannel) ? null : payChannel.toString())
                .sceneType(RISK_GIFTCARD_PAY_SCENE_TYPE)
                .build();
    }

    /**
     * 现金充值处理
     *
     * @param req req
     * @return Response
     */
    public Response<?> cashPay(PayOrderSureReq req, HttpServletRequest request) {
        logger.info("MallPaymentBusiness.cashPay:{}", JSON.toJSONString(req));
        // 1、调用营销接口 校验商城订单并返回商品信息
        RechargeCheckResp mallOrder = mallCheck(req);

        // 2、仅积分支付 直接返回
        if (mallOrder.getIntegralOnly()) {
            return Response.createSuccess();
        }
        // 3、增加礼品卡校验
        giftCardCheck(req, request, mallOrder.getSpuType());

        // 4、预付处理
        Response<?> response = prePay(req, mallOrder);
        if (response != null) {
            return response;
        }
        // 5、新增订单
        String settleUuid = addSettlement(req, mallOrder);

        // 6、调用支付
        Response<?> pay = pay(req, mallOrder, settleUuid);

        // 7、更新订单支付信息
        notifyMarket(pay.getSuccess(), req, settleUuid);

        return pay;
    }

    /**
     * 预付创单
     *
     * @param req       req
     * @param mallOrder mallOrder
     */
    private Response<?> prePay(PayOrderSureReq req, RechargeCheckResp mallOrder) {
        Response<?> res = null;
        if (StringUtils.isBlank(req.getPayTypeName())) {
            return res;
        }
        if (req.getPayTypeName().equals("prePay")) {
            if (EnumPayOrderChannel.ZHIMA_MALL_SCORE.getCode() != req.getPayOrderChannel()) {
                return Response.createError(BizExceptionEnum.PRE_CHANNEL_ERROR);
            }
            res = createZhima(req, mallOrder);
        }
        if (req.getPayTypeName().equals("cancel")) {
            res = zhimaService.cancelZhimaOrder(req.getOrderCode(), false);
        }
        // 更新订单支付信息
        if (res != null) {
            notifyMarket(res.getSuccess(), req, req.getCode());
        }
        return res;
    }

    /**
     * 新增通用订单
     *
     * @param req       req
     * @param mallCheck mallCheck
     */
    private String addSettlement(PayOrderSureReq req, RechargeCheckResp mallCheck) {
        Response<String> response;
        if (SpuTypeEnum.COUPON.getCode().intValue() == mallCheck.getSpuType()) {
            response = settlementShopping(req, mallCheck);
        } else {
            response = addGeneralSettlement(req, mallCheck);
        }
        if (ObjectUtil.isNull(response) || !response.isSuccess()) {
            BizExceptionUtil.create("生成结算单失败");
        }
        return response.getData();
    }

    /**
     * 礼品卡安全校验
     *
     * @param req     入参
     * @param request request
     * @param spuType spuType
     */
    private void giftCardCheck(PayOrderSureReq req, HttpServletRequest request, Integer spuType) {
        if (spuType != SpuTypeEnum.CARD.getCode().intValue()) {
            return;
        }
        //支付渠道校验（不可能支付宝时报错）
        if (!CollectionUtils.isEmpty(switchConfig.getGiftCardNotUseChannel())
                && switchConfig.getGiftCardNotUseChannel().contains(req.getPayOrderChannel())) {
            PayChannelEnum parse = PayChannelEnum.parse(req.getPayOrderChannel());
            BizExceptionUtil.create(switchConfig.getGiftcardChannelUsealiDesc()+parse.getDesc(),
                    ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        //风控校验
        riskCheck(req, request);
    }

    /**
     * 调用营销
     *
     * @param req 入参
     * @return {@link RechargeCheckResp}
     */
    public RechargeCheckResp mallCheck(PayOrderSureReq req) {
        RechargeCheckResp mallOrder = new RechargeCheckResp();
        String payTypeName = req.getPayTypeName();
        Response<?> res = mallRest.rechargeCheck(req.getOrderCode(), req.getUserId(), payTypeName);
        // 失败的抛出异常
        if (Objects.isNull(res) || !res.isSuccess() || Objects.isNull(res.getData())) {
            BizExceptionUtil.create(res.getMsg(), ResultErrorEnum.PAY_ERROR.getCode());
        }
        if (Objects.nonNull(res) && res.getCode().equals(CommonNumConst.NUM_10001)) {
            mallOrder.setIntegralOnly(Boolean.TRUE);
            return mallOrder;
        }
        mallOrder = JSONUtil.toBean(JSONUtil.toJsonStr(res.getData()), RechargeCheckResp.class);
        if (!BigDecimalUtils.greaterThanZero(mallOrder.getPayCash()) && !payTypeName.equals("cancel")) {
            BizExceptionUtil.create(res.getMsg(), ResultErrorEnum.PAY_ERROR.getCode());
        }
        return mallOrder;
    }

    /**
     * 调用支付
     *
     * @param req          req
     * @param mallOrder    mallOrder
     * @param settlementId settlementId
     * @return {@link Response}
     */
    private Response<?> pay(PayOrderSureReq req, RechargeCheckResp mallOrder, String settlementId) {
        if (req.getPayOrderChannel().equals(EnumPayOrderChannel.ZHIMA_MALL_SCORE.getCode())) {
            // 芝麻分不处理
            return Response.createSuccess();
        }
        MallPayContext context = MallPayContext.builder()
                .mallOrder(mallOrder)
                .req(req)
                .settlementId(settlementId)
                .build();
        // 1、组装 paymentDto
        PaymentDto paymentDto = buildPaymentDto(context);

        // 3、调用支付
        Response<String> payResponse = unifiedPaymentFacade.pay(paymentDto);

        if (Objects.isNull(payResponse) || !payResponse.isSuccess()) {
            return Response.createError(ResultErrorEnum.GET_RECHARGE_IFNO_ERROR);
        }
        // 余额支付成功
        if (payResponse.getData() == null) {
            RechargePayBo payInfoVo = new RechargePayBo();
            payInfoVo.setPayType(EnumPayOrderChannel.BALANCE.getCode());
            return Response.createSuccess("获取支付信息成功", payInfoVo);
        }
        // 4、非202 直接返回
        //如果路由成功不走老流程
        boolean payRoutingSuccess = PayUtils.payRoutingSuccess(payResponse);
        log.info("如果路由成功不走老流程 payRoutingSuccess={}", payRoutingSuccess);
        if (!payRoutingSuccess) {
            Response<RechargePayBo> aggResponse = takeNot202PayBo(context, payResponse);
            if (Objects.nonNull(aggResponse)) {
                return aggResponse;
            }
        }
        //获取支付路由后的支付类型
        int payType = PayUtils.getRealChannelRouting(payResponse, context.getPayTypeList());
        log.info("商城 获取支付路由后的支付类型 payType={}", payType);
        // 5、再次请求支付
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(false)
                .paymentResp(payResponse)
                .passengerUuid(req.getUserId())
                .outIp(req.getOutIp())
                .build();
        Response<RechargePayBo> response = payChannelFactory.paymentInfo(payType, paymentInfoBo);

        // 6、返回
        RechargePayBo rechargePayBo = response.getData();
        rechargePayBo.setSettlementId(context.getSettlementId());
        packageAggPayBo(rechargePayBo);
        return Response.createSuccess("获取商城支付信息成功", rechargePayBo);
    }

    /**
     * 芝麻分创单
     *
     * @param req       req
     * @param mallOrder mallOrder
     */
    private Response<?> createZhima(PayOrderSureReq req, RechargeCheckResp mallOrder) {
        ZhimaOrderParam param = new ZhimaOrderParam();
        param.setUserId(req.getUserId());
        param.setBizId(req.getOrderCode());
        param.setTotalAmount(MoneyUtils.toIntAmount(mallOrder.getPayCash()));
        param.setServiceTime(new Date());
        param.setZhimaType(req.getPayOrderChannel());
        logger.info("mall.createZhimaOrder:{}", param);
        return zhimaService.createZhimaOrder(param);
    }

    /**
     * 通知营销 订单状态
     *
     * @param success      success
     * @param req          req
     * @param settlementId settlementId
     */
    private void notifyMarket(Boolean success, PayOrderSureReq req, String settlementId) {
        if (success) {
            mallRest.updatePayInfo(req, settlementId);
        }
    }

    /**
     * 非202请求直接返回
     *
     * @param context     上下文
     * @param payResponse payResponse
     * @return {@link Response}
     */
    private Response<RechargePayBo> takeNot202PayBo(MallPayContext context, Response<String> payResponse) {
        try {
            PayOrderSureReq req = context.getReq();
            if (payResponse.getCode().equals(NumConstants.NUM_202)
                    || cmbAggHelper.isCmbAggPay(context.getPayWayEnum())) {
                return null;
            }
            RechargePayBo rechargePayBo = new RechargePayBo();
            rechargePayBo.setPayType(req.getPayOrderChannel());
            if (req.getPayOrderChannel().equals(EnumPayOrderChannel.UNION_JS_PAY.getCode())) {
                final String payUrl = String.valueOf(payResponse.getData());
                rechargePayBo.setSdk(URLDecoder.decode(payUrl, StandardCharsets.UTF_8.toString()));
            } else {
                rechargePayBo.setSdk(payResponse.getData());
            }
            rechargePayBo.setSettlementId(context.getSettlementId());
            return Response.createSuccess("获取商城支付信息成功", rechargePayBo);
        } catch (Exception e) {
            BizExceptionUtil.create(BizExceptionEnum.SYSTEM_ERROR);
        }
        return null;
    }

    /**
     * 组装支付dto
     *
     * @param context context
     * @return {@link PaymentDto}
     */
    private PaymentDto buildPaymentDto(MallPayContext context) {
        PayOrderSureReq req = context.getReq();
        RechargeCheckResp mallOrder = context.getMallOrder();
        Integer payOrderChannel = req.getPayOrderChannel();
        PaymentDto paymentDto = new PaymentDto();
        //设置支付业务类型PayOrderType
        setPayBizType(mallOrder.getSpuType(), paymentDto);
        paymentDto.setUserId(req.getUserId());
        paymentDto.setBizId(req.getOrderCode());
        paymentDto.setSettlementId(context.getSettlementId());

        List<Integer> payTypeList = new ArrayList<>() ;
        if(!CollectionUtils.isEmpty(req.getPayChannels())){
            payTypeList.addAll(req.getPayChannels());
        } else {
            payTypeList.add(payOrderChannel);
        }
        // 支付渠道类型
        PaywayEnum[] paywayEnums = PayWayConvert.getPayWayEnum(payTypeList);
        if (null != paywayEnums) {
            List<PaywayEnum> paywayEnumList = new ArrayList<>(Arrays.asList(paywayEnums));
            if (paywayEnumList.contains(PaywayEnum.ALIPAY_H5)
                    || Arrays.asList(paywayEnums).contains(PaywayEnum.WECHAT_MINIPROGRAM)) {
                paymentDto.getExtendParams().put("openId", req.getCode());
            } else if (paywayEnumList.contains(PaywayEnum.ALIPAY_JSAPI)) {
                paymentDto.getExtendParams().put("code", req.getCode());
            } else if (paywayEnumList.contains(PaywayEnum.ALIPAY_MINI)) {
                paymentDto.getExtendParams().put("code", req.getCode());
            } else if (paywayEnumList.contains(PaywayEnum.UNION_JS_PAY)) {
                paymentDto.getExtendParams().put("sub_source", req.getSubSource());
                paymentDto.getExtendParams().put("user_auth_code", req.getCode());
            } else {
                // 获取微信openId
                Response<String> wxResponse = cmbAggHelper.getWxOpenId(req.getOpenId(), req.getCode(), paywayEnums);
                // 返回code been used，则返回错误
                if (!wxResponse.isSuccess() && Code2SessionEnum.NONE.getCode().equals(wxResponse.getCode())) {
                    // 如果获取微信openId失败了，直接返回
                    BizExceptionUtil.create(ResultErrorEnum.WX_MINI_USER_ERROR.getMsg(),
                            ResultErrorEnum.WX_MINI_USER_ERROR.getCode());
                }
                paymentDto.getExtendParams().put("openId", wxResponse.getData());
            }
            paymentDto.setPaywayEnums(paywayEnumList.toArray(new PaywayEnum[]{}));
        }
        paymentDto.getExtendParams().put("quit_url", req.getQuitUrl());
        paymentDto.getExtendParams().put("wap_url", req.getWapUrl());
        paymentDto.getExtendParams().put("wap_name", req.getWapName());
        paymentDto.getExtendParams().put(PayConstant.OUT_IP, req.getOutIp());
        paymentDto.getExtendParams().put("terminal", req.getTerminal());
        paymentDto.getExtendParams().put("grayVersion", req.getGrayVersion());
        paymentDto.getExtendParams().put("grayBuild", req.getGrayBuild());
        paymentDto.getExtendParams().put("userIp", req.getOutIp());

        context.setPayTypeList(payTypeList);
        return paymentDto;
    }

    /**
     * 支付宝聚合支付封装
     *
     * @param rechargePayBo 返回体
     */
    private void packageAggPayBo(RechargePayBo rechargePayBo) {
        // 支付宝聚合支付需要还原支付渠道
        if (null != rechargePayBo.getPayType()
                && EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode() == rechargePayBo.getPayType()) {
            rechargePayBo.setPayType(EnumPayOrderChannel.ALIPAY.getCode());
        }
        // 只有 203 标识灰度版本
        if (EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode() == rechargePayBo.getPayType()
                && STR_203.equals(rechargePayBo.getCode())) {
            //微信聚合支付跳转微信小程序有额外的落地页 需要在sdk上额外拼装参数
            String sdk = rechargePayBo.getSdk() + "%26scene%3D" + "MALL_PAY";
            rechargePayBo.setSdk(sdk);
            rechargePayBo.setCmbMiniAppId(switchConfig.getCmbAggT3MiniAppId());
        }
    }

    /**
     * 校验参数
     *
     * @param req req
     */
    private void checkParam(PayOrderSureReq req) {
        if (Strings.isNullOrEmpty(req.getUserId())) {
            BizExceptionUtil.create("用户uuid不能为空", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        if (Strings.isNullOrEmpty(req.getOrderCode())) {
            BizExceptionUtil.create("订单编码不能为空", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
        if (null == req.getPayOrderChannel() && CollectionUtils.isEmpty(req.getPayChannels())) {
            BizExceptionUtil.create("支付渠道不能为空2", ResultErrorEnum.PARAMETER_IS_ILLEGAL.getCode());
        }
    }

    /**
     * @param req       req
     * @param mallCheck mallCheck
     * @return {@link Response<String>}
     */
    public Response<String> settlementShopping(PayOrderSureReq req, RechargeCheckResp mallCheck) {
        SettlementShoppingAddDto settlementShoppingAddDto = new SettlementShoppingAddDto();
        settlementShoppingAddDto.setBizType(BizType.SHOPPING_MALL.getType());
        settlementShoppingAddDto.setSettleType(SettleType.SHOPPING_PAYMENT.getType());
        settlementShoppingAddDto.setUserId(req.getUserId());
        settlementShoppingAddDto.setAccountType(1);
        settlementShoppingAddDto.setOrderUuid(req.getOrderCode());
        settlementShoppingAddDto.setTotalFare(mallCheck.getPayCash());
        // 芝麻分自动支付
        settlementShoppingAddDto.setPayChannel(req.getPayOrderChannel().toString());
        return addSettlementShopping(settlementShoppingAddDto);
    }

    /**
     * @param req       req
     * @param mallCheck mallCheck
     * @return {@link Response<String>}
     */
    public Response<String> addGeneralSettlement(PayOrderSureReq req, RechargeCheckResp mallCheck) {
        SettlementGeneralDto settlementGeneralDto = new SettlementGeneralDto();
        // 获取对应的业务类型
        SpuTypeEnum spuTypeEnum = Arrays.stream(SpuTypeEnum.values())
                .filter(type -> type.getCode().equals(mallCheck.getSpuType()))
                .findFirst()
                .orElseThrow(() -> new BizException(BizExceptionEnum.SYSTEM_ERROR.getMsg(),
                        BizExceptionEnum.SYSTEM_ERROR.getCode(), null));

        settlementGeneralDto.setBizType(spuTypeEnum.getBizType());
        settlementGeneralDto.setSettleType(SettleType.SHOPPING_PAYMENT.getType());
        settlementGeneralDto.setPaymentSubject(req.getUserId());
        settlementGeneralDto.setPaymentSubjectType(CommonNumConst.NUM_1);
        if (req.getAccountType() != null) {
            settlementGeneralDto.setPaymentSubjectType(req.getAccountType());
        }
        settlementGeneralDto.setOrderUuid(req.getOrderCode());
        settlementGeneralDto.setTotalFare(mallCheck.getPayCash());
        settlementGeneralDto.setOrderFare(mallCheck.getPayCash());
        SettlementGeneralExtInfoDto extInfoDto = new SettlementGeneralExtInfoDto();
        extInfoDto.setSpuType(spuTypeEnum.getCode());
        extInfoDto.setSpuTypeMsg(spuTypeEnum.getMsg());
        settlementGeneralDto.setExtInfo(JSON.toJSONString(extInfoDto));
        return settlementGeneralService.addSettlement(settlementGeneralDto);
    }

    /**
     * 添加解决购物
     * rpc
     *
     * @param settlementShoppingAddDto 解决购物添加dto
     * @return {@link Response<String>}
     */
    private Response<String> addSettlementShopping(SettlementShoppingAddDto settlementShoppingAddDto) {
        return settlementShoppingService.addSettlementShopping(settlementShoppingAddDto);
    }

    /**
     * 设置支付业务类型
     *
     * @param spuType spu类型
     * @param dto     dto
     */
    private void setPayBizType(Integer spuType, PaymentDto dto) {
        if (spuType == SpuTypeEnum.COUPON.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_45);
        } else if (spuType == SpuTypeEnum.CARD.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_74);
        } else if (spuType == SpuTypeEnum.GOODS.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_75);
        } else if (spuType == SpuTypeEnum.CODE.getCode().intValue()
                || spuType == SpuTypeEnum.OUT_INTERESTS.getCode().intValue()) {
            // 119 购买CDK-商城
            dto.setBizType(CommonNumConst.NUM_119);
        } else if (spuType == SpuTypeEnum.PAY_MEMBER.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_73);
        } else if (spuType == SpuTypeEnum.RECHARGE.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_142);
        } else if (spuType == SpuTypeEnum.INTEREST_CARD.getCode().intValue()) {
            dto.setBizType(CommonNumConst.NUM_168);
        } else if (SpuTypeEnum.BIG_LUCKY_CARD.getCode().equals(spuType)) {
            dto.setBizType(EnumPayOrderType.SHOPPING_DAFU_PAY.getCode());
        } else if (SpuTypeEnum.LIFE_SERVICE.getCode().equals(spuType)) {
            dto.setBizType(EnumPayOrderType.LIFE_SERVICE_PAY.getCode());
        } else if (SpuTypeEnum.NO_COMMISSION.getCode().equals(spuType)) {
            dto.setBizType(NumConstants.NUM_432);
        } else if (SpuTypeEnum.DRIVER_NO_COMMISSION.getCode().equals(spuType)
                || SpuTypeEnum.DRIVER_FLOW_ACCELERATOR_CARD.getCode().equals(spuType)
                || SpuTypeEnum.DRIVER_NO_COMMISSION_INTEREST.getCode().equals(spuType)) {
            dto.setBizType(EnumPayOrderType.DRIVER_FREE_COMMISSION_CARD_V2_PAY.getCode());
        } else if (SpuTypeEnum.DRIVER_COUPON.getCode().equals(spuType)) {
            dto.setBizType(EnumPayOrderType.DRIVER_ELECTRIC_COUPON_PAY.getCode());
        } else if (SpuTypeEnum.DRIVER_RECHARGE_GIFT_CARD.getCode().equals(spuType)) {
            dto.setBizType(EnumPayOrderType.DRIVER_ELECTRIC_CARD_PAY.getCode());
        }
    }
}
