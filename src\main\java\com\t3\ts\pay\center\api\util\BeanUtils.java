package com.t3.ts.pay.center.api.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/14 10:27
 */
@Slf4j
public final class BeanUtils {

    private BeanUtils() {
    }

    /**
     * 属性拷贝
     *
     * @param source      被拷贝的对象
     * @param targetClass 目的对象的Class
     * @param <T>         泛型
     * @return 目标对象
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        T target = null;
        try {
            if (Number.class.isAssignableFrom(targetClass)) {
                return (T) source;
            }
            target = targetClass.newInstance();
            org.springframework.beans.BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.warn("", e);
        }
        return target;
    }


    /**
     * 深属性拷贝
     *
     * @param source      被拷贝的对象
     * @param targetClass 目的对象的Class
     * @param <T>         泛型
     * @return 目标对象
     */
    public static <T> T deepCopyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        return JSONObject.parseObject(JSONObject.toJSONString(source), targetClass);
    }

    /**
     * 属性拷贝
     *
     * @param source 被拷贝的对象
     * @param target 目的对象
     * @param <T>    泛型
     * @return 目标对象
     */
    public static <T> T copyProperties(Object source, T target) {
        if (source == null) {
            return null;
        }
        try {
            if (Number.class.isAssignableFrom(target.getClass())) {
                return (T) source;
            }
            org.springframework.beans.BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.warn("", e);
        }
        return target;
    }

    /**
     * List属性拷贝
     *
     * @param sourceList  被拷贝的对象
     * @param targetClass 目的对象的Class
     * @param <T>         泛型
     * @return 目标对象
     */
    public static <T> List<T> copyListProperties(List<?> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        ArrayList<T> ts = new ArrayList<>(sourceList.size());
        sourceList.forEach(item -> {
            ts.add(copyProperties(item, targetClass));
        });
        return ts;
    }


    /**
     * List属性深拷贝
     *
     * @param sourceList  被拷贝的对象
     * @param targetClass 目的对象的Class
     * @param <T>         泛型
     * @return 目标对象
     */
    public static <T> List<T> deepCopyListProperties(List<?> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        return JSONArray.parseArray(JSONObject.toJSONString(sourceList), targetClass);
    }

    /**
     * 获取父类class
     *
     * @param clazz 类型
     * @return 类型
     */
    public static Class getSuperClass(Class clazz) {
        if (Objects.isNull(clazz) || isPrimitive(clazz) || clazz.equals(Object.class) || clazz.isInterface()) {
            return null;
        }
        Class suClass = clazz.getSuperclass();
        return suClass.isPrimitive() || suClass.equals(Object.class) ? null : suClass;
    }

    /**
     * 获取对象所有属性（包括父类）
     *
     * @param clazz            类型
     * @param annotationsClass 注解类型
     * @return 属性
     */
    public static Field[] getFieldClass(Class clazz, Class<? extends Annotation>... annotationsClass) {
        if (Objects.isNull(clazz) || isPrimitive(clazz) || clazz.equals(Object.class)) {
            return null;
        }
        List<Field> fieldList = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
        Class suClass = getSuperClass(clazz);
        while (Objects.nonNull(suClass) && Boolean.FALSE.equals(suClass.isInterface())) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(suClass.getDeclaredFields())));
            suClass = getSuperClass(suClass);
        }
        if (ArrayUtil.isNotEmpty(annotationsClass)) {
            Iterator<Field> it = fieldList.iterator();
            while (it.hasNext()) {
                if (Boolean.FALSE.equals(hasAnnotations(it.next(), annotationsClass))) {
                    it.remove();
                }
            }
        }
        return fieldList.toArray(new Field[fieldList.size()]);
    }

    /**
     * @param field           属性
     * @param annotationClass 注解类型
     * @return 是否
     */
    public static boolean hasAnnotations(Field field, Class... annotationClass) {
        if (Objects.isNull(field) || ArrayUtil.isEmpty(annotationClass)) {
            return false;
        }
        for (Class clazz : annotationClass) {
            if (Objects.nonNull(clazz) && field.isAnnotationPresent(clazz)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断基本数据类型
     *
     * @param clazz 类型
     * @return 结果
     */
    public static boolean isPrimitive(Class clazz) {
        if (Objects.isNull(clazz)) {
            return false;
        }
        if (clazz.isPrimitive()) {
            return true;
        }
        return Character.class.equals(clazz) || Boolean.class.equals(clazz) || Byte.class.equals(clazz)
                || Short.class.equals(clazz) || Integer.class.equals(clazz) || Long.class.equals(clazz)
                || Float.class.equals(clazz) || Double.class.equals(clazz);
    }

    /**
     * 获取属性值
     *
     * @param object    object
     * @param fieldName fieldName
     * @return field values
     */
    public static Object[] getFieldValue(Object object, String[] fieldName) {
        if (ArrayUtil.isEmpty(fieldName)) {
            return new Object[0];
        }
        Object[] values = new Object[fieldName.length];
        for (int i = 0; i < fieldName.length; i++) {
            if (Objects.nonNull(object)) {
                values[i] = ReflectUtil.getFieldValue(object, fieldName[i]);
            }
        }
        return values;
    }

}
