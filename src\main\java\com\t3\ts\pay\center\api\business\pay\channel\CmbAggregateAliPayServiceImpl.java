package com.t3.ts.pay.center.api.business.pay.channel;


import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 支付宝聚合支付
 *
 * <AUTHOR>
 */
@Service("cmbAggregateAliPayServiceImpl")
@Slf4j
public class CmbAggregateAliPayServiceImpl implements PayChannelService {

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {
        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }
        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode());
        rechargePayVo.setSdk(JSONUtil.toJsonStr(paymentResp.getData()));
        return Response.createSuccess(rechargePayVo);
    }


}
