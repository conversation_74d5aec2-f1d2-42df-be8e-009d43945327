package com.t3.ts.pay.center.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.service.AccountUnifiedService;
import com.t3.ts.pay.center.api.dto.QueryDto;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 14:00
 */
public class GiveControllerTest {
    @Mock
    AccountUnifiedService accountUnifiedService;
    @Mock
    Logger log;
    @InjectMocks
    GiveController giveController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExpireGiveDetail() throws Exception {
        Response<JSONObject> result = giveController.expireGiveDetail(new QueryDto(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGiveDetail() throws Exception {
        Response<JSONObject> result = giveController.giveDetail(new QueryDto(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetDeviceFingerPrintToken() throws Exception {
        String result = GiveController.getDeviceFingerPrintToken(null);
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
