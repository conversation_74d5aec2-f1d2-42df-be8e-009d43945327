package com.t3.ts.pay.center.api.util;

import com.t3.ts.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金钱转换工具类
 *
 * <AUTHOR>
 */
public final class MoneyUtils {

    private static final int MONEY_TIMES_ONE_HUNDRED = 100;
    private static final int MONEY_SCALE_TWO = 2;

    private MoneyUtils() {
    }

    /**
     * 分转元
     *
     * @param money 1
     * @return {@link BigDecimal}
     */
    public static BigDecimal fenToYuan(BigDecimal money) {
        return money.divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO, BigDecimal.ROUND_DOWN);
    }

    /**
     * 分转元
     *
     * @param money 1
     * @return {@link BigDecimal}
     */
    public static String fenToYuan(String money) {
        if(StringUtils.isBlank(money)){
            return "0";
        }
        return new BigDecimal(money)
                .divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED),
                MONEY_SCALE_TWO,
                BigDecimal.ROUND_DOWN).toPlainString();
    }

    /**
     * @description 分转元
     * @param money 钱(分)
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @date 2021/7/10
     */
    public static BigDecimal fenToYuan(Integer money) {
        BigDecimal x = new BigDecimal(money);
        return fenToYuan(x);
    }

    /**
     * @description 分转元
     * @param money 钱(分)
     * @return String
     * <AUTHOR>
     * @date 2021/7/10
     */
    public static String fenToYuan(Long money) {
        if (null == money) {
            return null;
        }
        BigDecimal x = new BigDecimal(money);
        return null != fenToYuan(x) ? String.valueOf(fenToYuan(x)) : null;
    }

    /**
     * @description 分转元
     * @param money 钱(分)
     * @return String
     * <AUTHOR>
     * @date 2021/7/10
     */
    public static Double fenToYuanDouble(Double money) {
        if (null == money) {
            return 0D;
        }
        BigDecimal x = new BigDecimal(money);
        return null != fenToYuan(x) ? fenToYuan(x).doubleValue() : 0D;
    }

    /**
     * @description 分转元
     * @param money 钱(分)
     * @return String
     * <AUTHOR>
     * @date 2021/7/10
     */
    public static Double fenToYuanDouble(Long money) {
        if (null == money) {
            return 0D;
        }
        BigDecimal x = new BigDecimal(money);
        return null != fenToYuan(x) ? fenToYuan(x).doubleValue() : 0D;
    }

    /**
     * @param amount 1
     * @return {@link BigDecimal}
     */
    public static BigDecimal intToBigDecimal(int amount) {
        return new BigDecimal(amount).divide(new BigDecimal(MONEY_TIMES_ONE_HUNDRED), MONEY_SCALE_TWO,
                BigDecimal.ROUND_DOWN);
    }

    /**
     * @param amount 1
     * @return {@link Integer}
     */
    public static Integer toIntAmount(BigDecimal amount) {
        if (amount == null) {
            return 0;
        }
        return amount.setScale(MONEY_SCALE_TWO, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(MONEY_TIMES_ONE_HUNDRED)).intValue();
    }

    /**
     * @param amount 1
     * @return {@link Integer}
     */
    public static Long toLongAmount(BigDecimal amount) {
        if (amount == null) {
            return 0L;
        }
        return amount.setScale(MONEY_SCALE_TWO, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(MONEY_TIMES_ONE_HUNDRED)).longValue();
    }

    /**
     *  元转分
     * @param yuan value
     * @return Long
     */
    public static Long yuanToFen(String yuan) {
        return yuan == null  ? null : new BigDecimal(yuan).multiply(new BigDecimal(MONEY_TIMES_ONE_HUNDRED))
                .toBigInteger().longValue();
    }

    /**
     * 分转元
     * @param fen 钱(分)
     * @return BigDecimal 钱(元)
     */
    public static BigDecimal fenToYuan2(Long fen) {
        if (null == fen) {
            return null;
        }
        BigDecimal x = new BigDecimal(fen);
        return fenToYuan(x);
    }
}
