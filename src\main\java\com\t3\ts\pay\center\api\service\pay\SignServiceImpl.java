package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.t3.ts.pay.center.api.dto.trade.PaySignReq;
import com.t3.ts.pay.center.api.util.CommonUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.NumConstant;
import com.t3.ts.account.center.dto.AccountSignChannelDto;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.account.center.service.ThirdSignService;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.sign.SignButtonVo;
import com.t3.ts.pay.center.api.dto.trade.PayChannelReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeReq;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeStatusVo;
import com.t3.ts.pay.center.api.dto.trade.SecretFreeVo;
import com.t3.ts.pay.center.api.dto.trade.SignStatusByChannelVo;
import com.t3.ts.pay.center.api.dto.vo.DataListVo;
import com.t3.ts.pay.center.api.dto.vo.SecretPayFreeVo;
import com.t3.ts.pay.center.api.enums.SignTerminalEnum;
import com.t3.ts.pay.center.api.rest.PassengerFeignClient;
import com.t3.ts.pay.center.api.service.SignService;
import com.t3.ts.pay.common.constant.PayChannelEnum;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Description: 支付服务
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:04
 */
@Component
@Slf4j
public class SignServiceImpl implements SignService {

    private static final String ZHIMA_PREFIX = "alipays://platformapi/startapp?appId=********&url=";

    private static final String ZHIMA_SIGN_STR =
            "https://render.alipay.com/p/yuyan/180020010000706007/index.html?signStr=";

    @Autowired
    private PayHandlerFactory factory;

    @DubboReference
    private ThirdSignService thirdSignService;

    @DubboReference
    private AccountSignService accountSignService;
    @Autowired
    private PassengerFeignClient passengerFeignClient;

    @Override
    public Response<?> openSecretFree(SecretFreeReq req) {
        try {
            AccountSignParamDto dto = buildSignDto(req);
            Response<?> response = thirdSignService.signContract(dto);
            if (response.getSuccess() && Objects.nonNull(response.getData())) {
                SecretFreeVo secretFreeVo = new SecretFreeVo();
                setSdk(dto.getPayChannel(), req.getSignTerminal(), secretFreeVo, response);
                return Response.createSuccess(secretFreeVo);
            }
        } catch (Exception e) {
            log.error("SignService.openSecretFree fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> closeSecretFree(SecretFreeReq req) {
        try {
            AccountSignParamDto dto = buildSignDto(req);
            return thirdSignService.cancelSign(dto);
        } catch (Exception e) {
            log.error("SignService.closeSecretFree fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> querySignStatus(String userId) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(userId);
            Response<List<AccountSignChannelDto>> response = accountSignService.getSignChannelStatus(param);
            Map<Integer, Integer> channelMap = new HashMap<>(NumConstant.NUM_8);
            if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                List<AccountSignChannelDto> list = response.getData();
                for (AccountSignChannelDto accountSign : list) {
                    Integer channelInfo = status2Integer(accountSign);
                    channelMap.put(accountSign.getPayChannel(), channelInfo);
                }

                //获取用户签约点击情况
                JSONObject json = new JSONObject();
                json.put("identityType", "1");
                json.put("userId", userId);
                json.put("typeList", Collections.singletonList(PassengerConstants.WXALREADYSIGN));
                JSONObject signObj = passengerFeignClient.getUserExtend(json);

                SecretFreeStatusVo secretFreeStatusVo = new SecretFreeStatusVo();
                // 将数据转换为vo
                convertStatusVo(channelMap, secretFreeStatusVo, signObj);

                return Response.createSuccess("获取免密开通状态成功", secretFreeStatusVo);
            }
        } catch (Exception e) {
            log.error("SignService.querySignStatus fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> priority(SecretFreeReq req) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(req.getUserId());
            param.setPayChannel(req.getPayChannel());
            param.setPriority(req.getPriority());
            return accountSignService.setSignChannelPriority(param);
        } catch (Exception e) {
            log.error("SignService.priority fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> querySignStatusByChannel(PayChannelReq req) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(req.getUserId());
            param.setPayChannel(req.getPayOrderChannel());
            Response<AccountSignChannelDto> response = accountSignService.getSignStatusByChannel(param);
            if (response.getSuccess() && ObjectUtil.isNotNull(response.getData())) {
                AccountSignChannelDto data = response.getData();
                SignStatusByChannelVo signStatusByChannel = new SignStatusByChannelVo();
                signStatusByChannel.setStatus(data.getDensityFree().equals(1) ? data.getDensityFree() : 0);
                signStatusByChannel.setFirstFlag(data.getPriority().equals(1));
                return Response.createSuccess(signStatusByChannel);
            }
        } catch (Exception e) {
            log.error("SignService.querySignStatusByChannel fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> getThirdSignStatus(PaySignReq req) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(req.getUserId());
            param.setPayChannel(req.getPayChannel());
            return thirdSignService.getThirdSignStatus(param);
        } catch (Exception e) {
            log.error("SignService.querySignStatusByChannel fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    @Override
    public Response<?> querySignList(String userUid) {
        try {
            AccountSignParamDto param = new AccountSignParamDto();
            param.setUserId(userUid);
            Response<List<AccountSignChannelDto>> response = accountSignService.getSignChannelStatus(param);

            Map<Integer, String> channelMap = new HashMap<>(NumConstant.NUM_8);
            if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                List<AccountSignChannelDto> list = response.getData();
                for (AccountSignChannelDto accountSign : list) {
                    String channelInfo = status2String(accountSign);
                    channelMap.put(accountSign.getPayChannel(), channelInfo);
                }
            }
            return Response.createSuccess("查询成功", new DataListVo<>(getSignList(channelMap)));
        } catch (Exception e) {
            log.error("SignService.querySignList fail!{}", ExceptionUtil.getMessage(e));
        }
        return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
    }

    /**
     * 组装
     *
     * @param channelMap channelMap
     * @return ArrayList
     */
    private ArrayList<SecretPayFreeVo> getSignList(Map<Integer, String> channelMap) {
        ArrayList<SecretPayFreeVo> secretFreeVos = Lists.newArrayList();
        ArrayList<SecretPayFreeVo> secretFreeVosTemp1 = Lists.newArrayList();
        ArrayList<SecretPayFreeVo> secretFreeVosTemp2 = Lists.newArrayList();
        ArrayList<SecretPayFreeVo> secretFreeVosTemp3 = Lists.newArrayList();
        for (Integer key : channelMap.keySet()) {
            String[] split = channelMap.get(key).split("");
            SecretPayFreeVo secretFreeVo = new SecretPayFreeVo();
            secretFreeVo.setPayChannel(key);
            secretFreeVo.setStatus(Integer.parseInt(split[0]));
            secretFreeVo.setFisrtFlag(StringUtils.equals("1", split[1]));
            secretFreeVo.setSort(getSortValue(key));
            if (secretFreeVo.isFisrtFlag()) {
                secretFreeVosTemp1.add(secretFreeVo);
            } else if (Integer.valueOf(1).equals(secretFreeVo.getStatus())) {
                secretFreeVosTemp2.add(secretFreeVo);
            } else {
                secretFreeVosTemp3.add(secretFreeVo);
            }
        }
        secretFreeVosTemp2.sort(Comparator.comparing(SecretPayFreeVo::getSort));
        secretFreeVosTemp3.sort(Comparator.comparing(SecretPayFreeVo::getSort));
        secretFreeVos.addAll(secretFreeVosTemp1);
        secretFreeVos.addAll(secretFreeVosTemp2);
        secretFreeVos.addAll(secretFreeVosTemp3);
        return secretFreeVos;
    }

    /**
     * 组装返回惨呼
     *
     * @param channelMap         channelMap
     * @param secretFreeStatusVo vo
     * @param signObj            用户侧记录的签约点击情况
     */
    private void convertStatusVo(Map<Integer, Integer> channelMap, SecretFreeStatusVo secretFreeStatusVo,
                                 JSONObject signObj) {
        for (Integer key : channelMap.keySet()) {
            Integer value = channelMap.get(key);
            boolean isSign = NumConstant.NUM_1 == value;
            if (EnumPayOrderChannel.ALIPAY.getCode() == key.intValue()) {
                // 支付宝
                secretFreeStatusVo.setAliPay(value);
            } else if (EnumPayOrderChannel.WEIXIN.getCode() == key.intValue()) {
                // 微信
                secretFreeStatusVo.setWxPay(value);
                secretFreeStatusVo.setShowNoPassword(CommonUtils.isShowSign(isSign, null == signObj ? null
                        : JSONObject.parseObject(signObj.getString(PassengerConstants.WXALREADYSIGN),
                        SignButtonVo.class)));
            } else if (EnumPayOrderChannel.NETCOM.getCode() == key.intValue()) {
                // 一网通
                secretFreeStatusVo.setNetcomPay(value);
            } else if (EnumPayOrderChannel.UNIONPAY.getCode() == key.intValue()) {
                // 银联
                secretFreeStatusVo.setUnionPay(value);
            } else if (EnumPayOrderChannel.ZHIMA_SCORE.getCode() == key) {
                secretFreeStatusVo.setZhima(value);
            } else if (EnumPayOrderChannel.ZHIMA_MALL_SCORE.getCode() == key) {
                secretFreeStatusVo.setZhimaMall(value);
            }
        }
    }


    /**
     * 组装请求的参数
     *
     * @param req req
     * @return AccountSignParamDto
     */
    private AccountSignParamDto buildSignDto(SecretFreeReq req) {
        AccountSignParamDto dto = new AccountSignParamDto();
        dto.setUserId(req.getUserId());
        dto.setPayChannel(Integer.valueOf(req.getSecretFreeType()));
        if (null != req.getNewUser()) {
            dto.setNewUser(req.getNewUser());
        }
        dto.setDeviceToken(req.getDeviceToken());
        dto.setSignTerminal(req.getSignTerminal());
        dto.setIp(dto.getIp());
        dto.setSignApp(req.getSignApp());
        dto.setCancelBackLink(req.getCancelBackLink());
        dto.setReturnBackLink(req.getReturnBackLink());
        JSONObject info = new JSONObject();
        info.put("advanceSerial", req.getAdvanceSerial());
        info.put("sence", req.getSence());
        dto.setExtendInfo(info);
        return dto;
    }

    /**
     * 设置SDK
     *
     * @param payChannel   payChannel
     * @param signTer      signTer
     * @param secretFreeVo secretFreeVo
     * @param response     response
     */
    private void setSdk(Integer payChannel, String signTer, SecretFreeVo secretFreeVo, Response response) {
        try {
            secretFreeVo.setSecretFreeType(payChannel.toString());
            if (payChannel == PayChannelEnum.NETCOM.getCode()) {
                secretFreeVo.setNetcomPaySdk(response.getData());
            }
            if (payChannel == PayChannelEnum.UNIONPAY.getCode()) {
                secretFreeVo.setUnionPaySdk(JSONUtil.toJsonStr(response.getData()));
            }
            if (payChannel == PayChannelEnum.ALIPAY.getCode()) {
                secretFreeVo.setAliPaySdk(JSONUtil.toJsonStr(response.getData()));
            }
            if (payChannel == PayChannelEnum.ZHIMA.getCode()
                    || payChannel == PayChannelEnum.ZHIMA_MALL.getCode()) {
                String openUrl = JSONUtil.toJsonStr(response.getData());
                // 支付宝需要区分端
                if (StringUtils.isNotBlank(openUrl)) {
                    if (signTer.equals(SignTerminalEnum.IOS.getCode())
                            || signTer.equals(SignTerminalEnum.ANDROID.getCode())
                            || signTer.equals(SignTerminalEnum.HARMONY_OS.getCode())) {
                        int index = openUrl.indexOf("?");
                        String signStr = openUrl.substring(index + 1);
                        secretFreeVo.setZhimaSdk(ZHIMA_PREFIX + URLEncoder.encode(ZHIMA_SIGN_STR
                                + URLEncoder.encode(signStr, StandardCharsets.UTF_8.toString()), StandardCharsets.UTF_8.toString()));
                    } else {
                        secretFreeVo.setZhimaSdk(JSONUtil.toJsonStr(openUrl));
                    }
                }
            }
            if (payChannel == PayChannelEnum.WECHAT.getCode()) {
                if (ObjectUtil.isNotNull(response.getData())) {
                    JSONObject jsonObject = JSON.parseObject(response.getData().toString());
                    String value = ObjectUtil.isNotNull(jsonObject.get("apply_permissions_token")) ? jsonObject.get(
                            "apply_permissions_token").toString() : "";
                    secretFreeVo.setWxPaySdk("apply_permissions_token=" + value);
                }
            }
        } catch (Exception e) {
            log.warn("组装签约参数失败:{}", JSONUtil.toJsonStr(secretFreeVo));
        }
    }

    /**
     * @param dto 参数
     * @return map
     */
    private Integer status2Integer(AccountSignChannelDto dto) {
        if (AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode() == dto.getDensityFree()) {
            return NumConstant.NUM_1;
        } else {
            return NumConstant.NUM_0;
        }
    }

    /**
     * 签约状态和优先级映射为字符串
     *
     * @param dto dto
     * @return String
     */
    private String status2String(AccountSignChannelDto dto) {
        StringBuilder channelInfo = new StringBuilder("");
        if (AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode() == dto.getDensityFree()) {
            channelInfo.append("1");
        } else {
            channelInfo.append("0");
        }
        if (dto.getPriority() == NumConstant.NUM_1) {
            channelInfo.append("1");
        } else {
            channelInfo.append("0");
        }
        return channelInfo.toString();
    }

    /**
     * 获得的价值
     *
     * @param key 关键
     * @return int
     */
    private int getSortValue(Integer key) {
        switch (key) {
            case NumConstants.NUM_1:
                // 支付宝
                return NumConstants.NUM_2;
            case NumConstants.NUM_2:
                // 微信
                return NumConstants.NUM_5;
            case NumConstants.NUM_3:
                // 一网通
                return NumConstants.NUM_4;
            case NumConstants.NUM_8:
                // 银联
                return NumConstants.NUM_3;
            default:
                break;
        }
        return NumConstants.NUM_6;
    }
}
