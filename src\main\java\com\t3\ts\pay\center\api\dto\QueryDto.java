package com.t3.ts.pay.center.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/8/24/0024 15:35
 */

@Data
public class QueryDto implements Serializable {
    /**
     * The constant serialVersionUID.
     */
    private static final long serialVersionUID = 1220181165534058120L;
    /**
     * 当前页
     */
    private Integer currentPage;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 账户类型
     * {@link com.t3.ts.account.center.constants.PayAccountTypeEnum}
     */
    private Integer accountType;
    /**
     * 支入支出类型  1.收入 2.支出 3.全部
     */
    private String changedType;
    /**
     * 业务类型 142：充值-本金 159：充值-赠金
     */
    private Integer[] flowTypes;

    private Integer[] bookTypes;
    /**
     * @see com.t3.ts.account.center.constants.BookTypeEnum
     */
    private Integer bookType;

    private String adCode;

    /**
     * 请求来源
     * 1：首页侧边栏-乘推乘icon气泡
     * 3：钱包页-打车金处乘推乘气泡入口
     * 2：省钱中心页-打车金处乘推乘气泡
     * 4：打车金页-乘推乘气泡入口
     */
    private String source;

    /**
     * 媒介类型  0 无媒介通用  1 APP 2 微信小程序 3支付宝小程序
     */
    private int mediumType;

    private String grayVersion;
}
