package com.t3.ts.pay.center.api.enums;

import lombok.Getter;

/**
 * 迁移 by ivy .2021/09/17 14:35
 *
 * @Author: qul
 * @Date: 2021/5/7 17:05
 */
@Getter
public enum InvoiceHeaderTypeEnum {

    // 抬头类型（1：企业单位2：个人/非企业单位）
    TYPE_ENTERPRISE(1, "企业单位"),
    TYPE_PERSON(2, "个人/非企业单位");

    private final Integer type;
    private final String msg;

    /**
     * 用户类型枚举
     *
     * @param type 类型
     * @param msg  消息
     */
    InvoiceHeaderTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

}
