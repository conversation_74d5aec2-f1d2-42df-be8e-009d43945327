package com.t3.ts.pay.center.api.config.interceptor;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.t3.ts.pay.common.http.driver.dto.res.DriverResDto;
import com.t3.ts.result.Response;
import java.io.PrintWriter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.t3.ts.travel.manager.resource.service.DriverService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/3 12:11
 */
@Component
@Slf4j
public class DriverTokenInterceptor extends HandlerInterceptorAdapter {

    private static final String DRIVER_URL = "/api/driver/";

    private static final String DRIVER_INVOICE = "/api/driver/invoice";

    public static final ThreadLocal<DriverResDto> THREAD_LOCAL_DRIVER = new ThreadLocal<>();

    /**
     * 前处理
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理程序
     * @return boolean* @throws Exception 异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        return true;
    }

    /**
     * 从打印
     * 输出错误
     *
     * @param out      出
     * @param response 响应
     */
    private void outPrint(PrintWriter out, Response response) {
        out.println(JSON.toJSON(response));
    }

    /**
     * 处理后
     *
     * @param request      请求
     * @param response     响应
     * @param o            o
     * @param modelAndView 模型和视图
     * @throws Exception 异常
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object o,
                           ModelAndView modelAndView) throws Exception {

    }

    /**
     * 完成后
     *
     * @param request  请求
     * @param response 响应
     * @param o        o
     * @param e        e
     * @throws Exception 异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o,
                                Exception e) throws Exception {
        THREAD_LOCAL_DRIVER.remove();
    }


}
