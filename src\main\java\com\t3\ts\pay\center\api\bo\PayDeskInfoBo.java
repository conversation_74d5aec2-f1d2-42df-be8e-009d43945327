package com.t3.ts.pay.center.api.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date: 2019-11-13 11:06
 * @des: 收银台数据展示对象
 */
@Data
public class PayDeskInfoBo {

    /**
     * 行程id
     */
    private String orderUuid;

    /**
     * 服务司机uuid
     */
    private String actualDriverUuid;

    /**
     * 车辆uuid
     */
    private String carUuid;

    /**
     * 钱包余额
     */
    private BigDecimal balance;

    /**
     * 总费用
     */
    private BigDecimal totalFare;

    /**
     * 折扣价格
     */
    private BigDecimal discountFare;

    /**
     * 待支付费用
     */
    private BigDecimal actualFare;

    /**
     * 余额已支付费用
     */
    private BigDecimal amountPayed;

    /**
     * 优惠已抵扣
     */
    private BigDecimal couponPayed;

    /**
     * 优惠卷id
     */
    private String couponId;

    /**
     * 优惠价
     */
    private BigDecimal couponMoney;

    /**
     * 优惠卷数量
     */
    private Integer couponCount;

    /**
     * 是否预付款
     */
    private Boolean prePay;

    /**
     * 预付款金额
     */
    private BigDecimal prePayFare;

    /**
     * 附加费
     */
    private BigDecimal additionalFee;

    /**
     * 赠送币
     */
    private BigDecimal giftCurrency;

    /**
     * 取消费
     */
    private BigDecimal cancelFee;

    /**
     * 超时取消费
     */
    private BigDecimal timeOutFee;

    /**
     * 余额可支付费用
     */
    private BigDecimal availableBalance;

    /**
     * 礼品卡是否支持附加费支付
     */
    private Boolean giftCardCanPayService;
    /**
     * 积分已抵扣
     */
    private BigDecimal integralPayed;

    /**
     * 城市code
     */
    private String cityCode;
}
