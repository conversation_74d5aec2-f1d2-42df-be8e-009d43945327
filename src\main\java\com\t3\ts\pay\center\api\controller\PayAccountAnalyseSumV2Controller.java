package com.t3.ts.pay.center.api.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.constants.BookTypeEnum;
import com.t3.ts.account.center.constants.PayAccountTypeEnum;
import com.t3.ts.account.center.dto.flow.PayAccountFlowsDto;
import com.t3.ts.account.center.dto.flow.PayAccountFlowsQueryDto;
import com.t3.ts.account.center.dto.flow.PayAccountFlowsQueryPageDto;
import com.t3.ts.account.center.service.UnifiedAccountFacade;
import com.t3.ts.finance.center.service.FareItemSumConfigV2Service;
import com.t3.ts.finance.center.sumanalyse.PayAccountAnalyseSumV2Service;
import com.t3.ts.finance.center.sumanalyse.dto.AnalyseRatioTrendVo;
import com.t3.ts.finance.center.sumanalyse.dto.FareItemSumConfigTabVo;
import com.t3.ts.finance.center.sumanalyse.dto.FareItemSumConfigV2CacheDto;
import com.t3.ts.finance.center.sumanalyse.dto.FareItemSumConfigV2Dto;
import com.t3.ts.finance.center.sumanalyse.dto.PayAccountAnalyseSumFlowQueryDto;
import com.t3.ts.finance.center.sumanalyse.dto.PayAccountAnalyseSumV2QueryDto;
import com.t3.ts.finance.center.sumanalyse.dto.PayAccountAnalyseSumV2Vo;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.dto.driverwallet.AccountSumFareItemDto;
import com.t3.ts.pay.center.api.dto.wallet.AccountFlowReqV2Dto;
import com.t3.ts.pay.center.api.dto.wallet.AccountFlowResVo;
import com.t3.ts.pay.center.api.dto.wallet.GraySwitchDto;
import com.t3.ts.pay.center.api.service.DriverWalletDwySumService;
import com.t3.ts.pay.center.api.util.AccountNewUtils;
import com.t3.ts.pay.center.api.util.MoneyUtils;
import com.t3.ts.pay.common.date.DateUtils;
import com.t3.ts.pay.common.num.NumConstant;
import com.t3.ts.pay.common.util.BeanUtils;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 司机流水周期汇总 接口层
 *
 * <AUTHOR>
 * @date 2022 -09-19
 */
@Api(tags = "司机流水周期汇总v2 API")
@Slf4j
@RestController
@RequestMapping(value = "/api/finance/payAccountAnalyseSum")
public class PayAccountAnalyseSumV2Controller {

    private static final Cache<String, FareItemSumConfigV2CacheDto> LOCAL_CACHE =
            CacheBuilder.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS)
                    .maximumSize(100).build();
    public static final String FARE_ITEM_SUM_CONFIG_V2_CACHE_KEY = "FARE_ITEM_SUM_CONFIG_V2_CACHE_KEY";

    @DubboReference
    private PayAccountAnalyseSumV2Service payAccountAnalyseSumV2Service;
    @DubboReference
    private FareItemSumConfigV2Service fareItemSumConfigV2Service;

    @DubboReference
    private UnifiedAccountFacade unifiedAccountFacade;

    @Autowired
    private DriverWalletDwySumService driverWalletDwySumService;

    @Autowired
    private AccountNewUtils accountNewUtils;



    /**
     * 司机钱包明细-开关接口
     * 提供给司机钱包前端界面使用-判断是否跳转新页面
     * 灰度全部结束后可以删除-需要前端先删除调用此开关的地方
     * @param dto dto
     * @return Boolean true-走新页面 其他老页面
     */
    @ApiOperation("司机钱包明细-开关接口")
    @PostMapping("/v2/getSumGraySwitch")
    public Response<Boolean>  getSumGraySwitch(@RequestBody GraySwitchDto dto) {
        return Response.createSuccess(true);
    }

    @ApiOperation("查询二级Tab页")
    @PostMapping("/v2/query/tabs")
    public Response<List<FareItemSumConfigTabVo>> selectV2ForTabs(@RequestBody FareItemSumConfigV2Dto dto) {
        try {
            List<FareItemSumConfigTabVo> classForTabs = fareItemSumConfigV2Service.getClassForTabs(dto);
            return Response.createSuccess(classForTabs);
        } catch (Exception e) {
            log.error("selectV2ForTabs error", e);
        }
        return Response.createError();
    }

    /**
     * 查询钱包流水汇总
     * 1、司机APP-个人信息-钱包-收支明细: 按分类和日期查询 约定一次查7天  返回7天的日汇总列表
     * 2、司机APP-个人信息-收入-收支分析-收入趋势：  按固定分类(YJ1、YJ2)和日期查询（1天时间范围，7天时间范围,30天时间范围）    返回最多30天的日汇总列表
     * @param queryDto  classCode 、startDate 、 endDate
     * @return Response<List<PayAccountAnalyseSumV2Vo>>
     */
    @ApiOperation("查询钱包流水汇总by分类一次查7天")
    @PostMapping("/v2/query/daySum")
    public Response<List<PayAccountAnalyseSumV2Vo>> selectV2ForDaySum(@RequestBody PayAccountAnalyseSumFlowQueryDto queryDto) {
        try {
            String driverUuid = RequestContextHelper.getDriverUuid();
            queryDto.setUserId(driverUuid);
            log.info("selectV2ForDaySum driverUuid={}", driverUuid);
            //1 先查汇总
            Response<List<PayAccountAnalyseSumV2Vo>> listResponse =
                    payAccountAnalyseSumV2Service.selectV2ForDayFlow(queryDto);
            return Response.createSuccess(listResponse.getData());
        } catch (Exception e) {
            log.error("selectV2ForDaySum error", e);
        }
        return Response.createError();
    }

    /**
     * 查询钱包流水明细
     * 司机APP-个人信息-钱包:当月流水 一次查7天、前端实现翻页
     * 司机APP-个人信息-收入-收支分析:日分析时 的 当日流水
     * @param queryDto
     * @return
     */
    @ApiOperation("查询钱包流水明细")
    @PostMapping("/v2/query/flowList")
    public Response<PageResult<AccountFlowResVo>> getPayAccountAnalyseFlowList(@RequestBody AccountFlowReqV2Dto queryDto) {
        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        queryDto.setUserId(userId);
        queryDto.setAccountType(accountType);
        log.info("getPayAccountAnalyseFlowList UserId={} accountType={}", userId, accountType);
        //入参校验 UserId  accountType  endDate-startDate < 40天
        if(StringUtils.isBlank(queryDto.getUserId()) || queryDto.getAccountType() == null ){
            return Response.createError("用户ID和类型缺失");
        }
        if(queryDto.getEndDate().getTime() - queryDto.getStartDate().getTime() > 3456000000L){
            return Response.createError("查询时间范围不能超过一个月");
        }

        PayAccountFlowsQueryPageDto queryPageDto = new PayAccountFlowsQueryPageDto();
        queryPageDto.setNeedCount(Boolean.FALSE);
        queryPageDto.setUserId(queryDto.getUserId());
        queryPageDto.setAccountType(queryDto.getAccountType());
        queryPageDto.setStartDate(DateUtils.beginOfDay(queryDto.getStartDate()));
        queryPageDto.setEndDate(DateUtils.endOfDay(queryDto.getEndDate()));
        queryPageDto.setOrderUuid(queryDto.getOrderUuid());

        queryPageDto.setCurrPage(1);
        queryPageDto.setPageSize(1000);
        queryPageDto.setBookTypeList(
                Arrays.asList(BookTypeEnum.DRIVER_SALARY.getType(), BookTypeEnum.TAXI_DRIVER_CLEAR.getType()));

        String classCode = queryDto.getClassCode();
        FareItemSumConfigV2CacheDto cacheDto = LOCAL_CACHE.getIfPresent(FARE_ITEM_SUM_CONFIG_V2_CACHE_KEY);
        if(cacheDto == null){
            cacheDto = fareItemSumConfigV2Service.getFareItemSumConfigV2Cache();
            LOCAL_CACHE.put(FARE_ITEM_SUM_CONFIG_V2_CACHE_KEY, cacheDto);
        }
        Map<String, List<Integer>> classFareItemMap = cacheDto.getClassToFareItemMap();
        List<Integer> fareItemList = classFareItemMap.get(classCode);
        Map<String, FareItemSumConfigV2Dto> sumConfigV2Map = cacheDto.getClassMap();
        FareItemSumConfigV2Dto fareItemSumConfigV2Dto = sumConfigV2Map.get(classCode);

        queryPageDto.setFareItemList(fareItemList);

        if("YJ1".equals(classCode) || "YJ1".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_1));
        }
        if("YJ2".equals(classCode) || "YJ2".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_2));
        }
        if("YJ3".equals(classCode) || "YJ3".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_1, NumConstant.NUM_2));
        }

        Response<PageResult<PayAccountFlowsDto>> flowsRes = unifiedAccountFacade.getPayAccountFlowListPage(queryPageDto);
        //出租车司机 && 订单流水  区分出租车、网约车
        if (null == flowsRes || !flowsRes.isSuccess() || flowsRes.getData() == null || CollectionUtils.isEmpty(
                flowsRes.getData().getList())) {
            log.warn("query account flow list is empty.");
            return Response.createSuccess();
        }
        PageResult<PayAccountFlowsDto> pageResult1 = flowsRes.getData();
        List<AccountFlowResVo> resVos = new ArrayList<>();
        pageResult1.getList().forEach(item -> {
            AccountFlowResVo resVo = new AccountFlowResVo();
            BeanUtil.copyProperties(item, resVo);
            //金额单位转换
            resVo.setAfterBalance(MoneyUtils.fenToYuan(item.getAfterBalance()));
            resVo.setBeforeBalance(MoneyUtils.fenToYuan(item.getBeforeBalance()));
            resVo.setChangedBalance(MoneyUtils.fenToYuan(item.getChangedBalance()));
            resVo.setAfterPrepareBalance(MoneyUtils.fenToYuan(item.getAfterPrepareBalance()));
            resVo.setAfterAvailableBalance(MoneyUtils.fenToYuan(item.getAfterAvailableBalance()));
            resVo.setAfterFreezeBalance(MoneyUtils.fenToYuan(item.getAfterFreezeBalance()));
            //补充业务时间归属日期
            resVo.setDateStr(DateUtil.format(item.getBizTime(), "yyyy-MM-dd"));
            resVos.add(resVo);
        });
        try {
            //查 t_settlement_common_es 补全其他参数
            driverWalletDwySumService.completionDriverSettlement(resVos);
        } catch (Exception e) {
            log.error("elastic search 查询失败", e);
        }
        PageResult<AccountFlowResVo> pageResult2 = new PageResult<>();
        pageResult2.setTotalCount(pageResult1.getTotalCount());
        pageResult2.setPageSize(pageResult1.getPageSize());
        pageResult2.setCurrPage(pageResult1.getCurrPage());
        pageResult2.setHasMore(pageResult1.isHasMore());
        pageResult2.setList(resVos);
        return Response.createSuccess(pageResult2);
    }


    /**
     * 查流水汇总 - 按分类汇总, 日期范围 最大一个月，不兼容3月前历史数据
     * 司机APP-个人信息-收入-收支分析-收入占比
     * @param queryDto 司机ID 、日期、城市
     * @return AnalyseSumV2MapVo 树结构
     */
    @ApiOperation("查询司机流水汇总收入占比")
    @PostMapping("/v2/query/ratioTrend")
    public Response<AnalyseRatioTrendVo> selectV2ForAnalysisRatio(@RequestBody PayAccountAnalyseSumV2QueryDto queryDto) {
        try {
            String driverUuid = RequestContextHelper.getDriverUuid();
            queryDto.setUserId(driverUuid);
            //入参校验 UserId    endDate-startDate < 40天
            if(StringUtils.isBlank(queryDto.getUserId()) ){
                return Response.createError("用户ID和类型缺失");
            }
            if(DateUtils.parseDate(queryDto.getEndDate()).getTime() -DateUtils.parseDate(queryDto.getStartDate()).getTime() > 3456000000L){
                return Response.createError("查询时间范围不能超过一个月");
            }
            return payAccountAnalyseSumV2Service.selectV2ForRatioTrend(queryDto);
        } catch (Exception e) {
            log.error("selectV2ForAnalysisRatio error", e);
        }
        return Response.createError();
    }

    /**
     * 查询钱包流水总笔数
     * 司机APP-个人信息-收入-收支分析: 总笔数
     */
    @ApiOperation("查询钱包流水总笔数")
    @PostMapping("/v2/query/flowCount")
    public Response<Integer> getPayAccountFlowCount(@RequestBody AccountFlowReqV2Dto queryDto) {

        String userId = RequestContextHelper.getDriverUuid();
        Integer accountType = accountNewUtils.getAccountTypeE(userId);
        queryDto.setUserId(userId);
        queryDto.setAccountType(accountType);
        //入参校验 UserId  accountType  endDate-startDate < 40天
        if(StringUtils.isBlank(queryDto.getUserId()) || queryDto.getAccountType() == null ){
            return Response.createError("用户ID和类型缺失");
        }
        if(queryDto.getEndDate().getTime() - queryDto.getStartDate().getTime() > 3456000000L){
            return Response.createError("查询时间范围不能超过一个月");
        }

        log.info("getPayAccountFlowCount driverUuid={} accountType={}", userId, accountType);
        PayAccountFlowsQueryPageDto queryPageDto = new PayAccountFlowsQueryPageDto();
        queryPageDto.setUserId(queryDto.getUserId());
        queryPageDto.setAccountType(queryDto.getAccountType());
        queryPageDto.setStartDate(DateUtils.beginOfDay(queryDto.getStartDate()));
        queryPageDto.setEndDate(DateUtils.endOfDay(queryDto.getEndDate()));

        queryPageDto.setBookTypeList(
                Arrays.asList(BookTypeEnum.DRIVER_SALARY.getType(), BookTypeEnum.TAXI_DRIVER_CLEAR.getType()));

        String classCode = queryDto.getClassCode();
        FareItemSumConfigV2CacheDto cacheDto = fareItemSumConfigV2Service.getFareItemSumConfigV2Cache();
        Map<String, List<Integer>> classFareItemMap = cacheDto.getClassToFareItemMap();
        List<Integer> fareItemList = classFareItemMap.get(classCode);
        Map<String, FareItemSumConfigV2Dto> sumConfigV2Map = cacheDto.getClassMap();
        FareItemSumConfigV2Dto fareItemSumConfigV2Dto = sumConfigV2Map.get(classCode);

        queryPageDto.setFareItemList(fareItemList);

        if("YJ1".equals(classCode) || "YJ1".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_1));
        }
        if("YJ2".equals(classCode) || "YJ2".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_2));
        }
        if("YJ3".equals(classCode) || "YJ3".equals(fareItemSumConfigV2Dto.getParentClassCode())){
            queryPageDto.setChangedTypeList(Lists.newArrayList(NumConstant.NUM_1, NumConstant.NUM_2));
        }

        return unifiedAccountFacade.getPayAccountFlowsCount(queryPageDto);

    }


    /**
     * 查询统计指定费用项汇总
     *
     * @param dto dto
     * @return AccountSumFareItemDto AccountSumFareItemDto
     */
    @ApiOperation("查询统计指定费用项汇总")
    @PostMapping("/v2/getFareItemSum")
    public Response<AccountSumFareItemDto> getFareItemSum(@RequestBody AccountSumFareItemDto dto) {
        //设置司机ID
        dto.setUserId(RequestContextHelper.getDriverUuid());
        PayAccountFlowsQueryDto dto1 = BeanUtils.propertiesCopy(dto, PayAccountFlowsQueryDto.class);
        dto1.setAccountTypeList(
                Arrays.asList(PayAccountTypeEnum.DRIVER_TYPE.getType(), PayAccountTypeEnum.TAXI_TYPE.getType()));
        dto1.setBookTypeList(
                Arrays.asList(BookTypeEnum.DRIVER_SALARY.getType(), BookTypeEnum.TAXI_DRIVER_CLEAR.getType()));
        dto1.setStartDate(DateUtils.beginOfDay(dto.getStartDate()));
        dto1.setEndDate(DateUtils.endOfDay(dto.getEndDate()));
        Response<BigDecimal> response = unifiedAccountFacade.getFlowsMoneySum(dto1);
        if (response == null) {
            return Response.createError("查询异常");
        }
        if (!response.isSuccess() || response.getData() == null) {
            return Response.createError(response.getMsg());
        }
        dto.setSumMoney(response.getData());
        return Response.createSuccess(dto);
    }
}
