package com.t3.ts.pay.center.api.dto.trade;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: PrivilegeDetail
 * @Package com.t3.ts.pay.center.api.dto.trade
 * @date 2022/3/30 21:53
 */
@Getter
@Setter
public class PrivilegeDetail {
    // 最优卡可抵扣金额
    private BigDecimal decutionAmount;

    // 最优权益卡
    private String canUsePrivilege;
}
