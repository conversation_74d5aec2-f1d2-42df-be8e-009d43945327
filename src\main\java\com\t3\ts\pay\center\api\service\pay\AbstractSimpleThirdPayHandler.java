package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.channelmgr.center.constants.NumConstant;
import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelFactory;
import com.t3.ts.pay.center.api.business.PayProxyService;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PayConstants;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.GeneralSettlePayDto;
import com.t3.ts.pay.remote.dto.PaymentDto;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Description: 简单三方支付流程
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:25
 */
@Slf4j
@Component
public abstract class AbstractSimpleThirdPayHandler {

    @Autowired
    private PayProxyService payProxyService;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private PayChannelFactory payChannelFactory;
    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @DubboReference
    private UnifiedService unifiedService;
    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;

    /**
     * get current type
     *
     * @return {@link String}
     */
    public abstract String getType();


    /**
     * check pay way
     *
     * @param context 上下文
     */
    public void checkPayWay(PayContext context) {
        if (context.getPayChannelList().contains(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode())) {
            // 微信小程序支付，获取openid
            Response<String> response = cmbAggHelper.getWMPOpenId(context.getWxCode());
            if (BooleanUtil.isFalse(response.isSuccess())) {
                throw new IllegalStateException("获取微信验证信息失败");
            }
            context.getExtendParam().put("inner_wxOpenId", response.getData());
        }
        List<Integer> aggPayChannelList = null == context.getAggPayChannelList()
                ? new ArrayList<>() : context.getAggPayChannelList();
        if(!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(context.getUserId()).getData()){
        cmbAggHelper.convertAggChannel(context.getPayChannelList(), aggPayChannelList, context.getUserId(), null);
        }
    }


    /**
     * create pay param
     *
     * @param context 上下文
     * @return {@link PaymentDto}
     */
    public PaymentDto createPaymentParam(PayContext context) {
        PaymentDto paymentDto = new PaymentDto();
        paymentDto.setBizType(getReaBizType(context));
        paymentDto.setUserId(context.getUserId());
        paymentDto.setBizId((String) context.getExtendParam().get("inner_bizId"));
        paymentDto.setSettlementId((String) context.getExtendParam().get("inner_settleId"));
        paymentDto.setPaywayEnums(PayWayConvert.getPayWayEnum(context.getPayChannelList()));
        Map<String, Object> params = new HashMap<>(NumConstants.NUM_8_INT);
        params.put("openId", context.getExtendParam().get("inner_wxOpenId"));
        params.put("code", context.getCode());
        params.put("quit_url", context.getExtendParam().get("quit_url"));
        params.put("payReturnUrl", context.getExtendParam().get("payReturnUrl"));
        params.put("grayVersion", context.getGrayVersion());
        params.put("grayBuild", context.getGrayBuild());
        params.put("terminal", context.getTerminal());
        paymentDto.setExtendParams(params);
        return paymentDto;
    }

    /**
     * 获取PayOrderType
     * @param context 上下文
     * @return {@link Integer}
     */
    private Integer getReaBizType(PayContext context) {
        Integer realBizType = (Integer) context.getExtendParam().get("bizType");
        if (null != realBizType) {
            return getMappingBizType(realBizType);
        }
        return getMappingBizType(getBizType(context));
    }
    /**
     * 映射结算类型和支付类型
     *
     * @param bizType 结算类型
     * @return Integer
     */
    public Integer getMappingBizType(Integer bizType) {
        if (bizType == BizType.THANKS.getType()) {
            return EnumPayOrderType.THANKS.getCode();
        } else if (bizType == BizType.CASH_RECHARGE.getType()) {
            return EnumPayOrderType.CASH_RECHARGE.getCode();
        } else if (bizType == EnumPayOrderType.COMPANY_RECHARGE_ONLINE.getCode()) {
            return EnumPayOrderType.COMPANY_RECHARGE_ONLINE.getCode();
        } else {
            Response<GeneralSettlePayDto> resp = unifiedService.getGeneralSettlePayByBizType(bizType,
                    NumConstant.NUM_1);
            if (null != resp && null != resp.getData()) {
                return resp.getData().getPayOrderType();
            }
        }
        return null;
    }

    /**
     * doPay
     *
     * @param dto dto
     * @return {@link Response<String>}
     */
    public Response<String> doPay(PaymentDto dto) {
        // 获取支付信息
        return payProxyService.pay(dto);
    }

    /**
     * get  pay  type
     * pay-center EnumPayOrderType
     *
     * @param context context
     * @return {@link Integer}
     */
    protected abstract Integer getBizType(PayContext context);

    /**
     * route by pay channel
     *
     * @param payInfo 支付信息
     * @param context 上下文
     */
    public void routePayChannel(Response<String> payInfo, PayContext context) {
        int payType = PayUtils.getRealChannelRouting(payInfo,context.getPayChannelList());
        log.info("AbstractSimpleThirdPayHandler 获取支付路由后的支付类型 payType={}", payType);
        //是否免密支付
        boolean isNoSecret = PayConstants.NO_SEC_PAY.equals(String.valueOf(payInfo.getCode()));
        //内部传递对象
        PaymentInfoBo paymentInfoBo = PaymentInfoBo.builder()
                .noSecret(isNoSecret).paymentResp(payInfo).passengerUuid(context.getUserId())
                .passengerMobile(context.getPassengerMobile()).build();
        //支付工厂模式调用
        Response<RechargePayBo> response = payChannelFactory.paymentInfo(payType, paymentInfoBo);
        context.getExtendParam().put("output_response", response);
    }

    /**
     * 支付宝聚合支付封装
     *
     * @param rechargePayBo 返回体
     */
    public void packageAggAliPayBo(RechargePayBo rechargePayBo) {
        // 支付宝聚合支付需要还原支付渠道
        if (null != rechargePayBo.getPayType()
                && EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode() == rechargePayBo.getPayType()) {
            rechargePayBo.setPayType(EnumPayOrderChannel.ALIPAY.getCode());
        }
    }

    /**
     * process return message
     *
     * @param context 上下文
     * @return {@link Response<RechargePayBo>}
     */
    public Response<JSONObject> processResponse(PayContext context) {
        Response<?> response = (Response<?>) context.getExtendParam().get("output_response");
        if (!response.isSuccess() || Objects.isNull(response.getData())) {
            return Response.createError(response.getMsg());
        }
        RechargePayBo rechargePayBo = (RechargePayBo) response.getData();
        rechargePayBo.setSettlementId((String) context.getExtendParam().get("inner_settleId"));
        packageAggAliPayBo(rechargePayBo);
        return Response.createSuccess("获取支付信息成功", JSON.parseObject(JSON.toJSONString(rechargePayBo)));
    }
}
