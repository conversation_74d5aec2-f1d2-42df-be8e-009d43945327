package com.t3.ts.pay.center.api.business.wechat;

import lombok.Data;

/**
 * 代码2会话
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Data
public class Code2Session {
    /**
     * 用户唯一标识
     */
    private String openid;

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 用户在开放平台的唯一标识符
     */
    private String unionid;

    /**
     * 错误码
     * -1	系统繁忙，此时请开发者稍候再试
     * 0	请求成功
     * 40029	code 无效
     * 45011	频率限制，每个用户每分钟100次
     */
    private Integer errcode;

    /**
     * 错误信息
     */
    private String errmsg;
}
