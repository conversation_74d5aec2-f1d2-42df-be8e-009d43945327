package com.t3.ts.pay.center.api.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.member.account.remote.dto.AccountInfoDto;
import com.t3.ts.member.account.remote.service.AccountInfoService;
import com.t3.ts.passenger.center.dto.RouteSerialDto;
import com.t3.ts.passenger.center.service.PassengerCacheService;
import com.t3.ts.pay.center.api.bo.CancelFareBo;
import com.t3.ts.pay.center.api.bo.CashierDeskBaseFareBo;
import com.t3.ts.pay.center.api.bo.DriverInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.common.CommonPaymentBusiness;
import com.t3.ts.pay.center.api.cache.RoutePlanKey;
import com.t3.ts.pay.center.api.config.RequestContextHelper;
import com.t3.ts.pay.center.api.constants.CharteredAppConstants;
import com.t3.ts.pay.center.api.constants.MemberAccountBusinessTypeEnum;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.PayCenterMethodEnum;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.ChartedAdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.dto.chartered.AdvanceChargeReq;
import com.t3.ts.pay.center.api.dto.chartered.BalanceCanPayDetailDto;
import com.t3.ts.pay.center.api.dto.chartered.BalanceDto;
import com.t3.ts.pay.center.api.dto.chartered.CanUseCouponDto;
import com.t3.ts.pay.center.api.dto.chartered.CashierDeskDto;
import com.t3.ts.pay.center.api.dto.chartered.CashierDeskProVo;
import com.t3.ts.pay.center.api.dto.chartered.CouponDetailDto;
import com.t3.ts.pay.center.api.dto.chartered.FareDetailDto;
import com.t3.ts.pay.center.api.dto.chartered.PayDeskInfoVo;
import com.t3.ts.pay.center.api.dto.chartered.PayItem;
import com.t3.ts.pay.center.api.dto.chartered.PayTradeCharteredReq;
import com.t3.ts.pay.center.api.dto.chartered.PayedDto;
import com.t3.ts.pay.center.api.dto.chartered.RoutePaymentReq;
import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.trade.CouponVo;
import com.t3.ts.pay.center.api.dto.trade.PayDeskReq;
import com.t3.ts.pay.center.api.dto.vo.IntegralMsgVO;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.pay.center.api.dto.vo.RechargePayVo;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.center.api.service.impl.CharterRoutePayServiceImpl;
import com.t3.ts.pay.center.api.util.BigDecimalUtils;
import com.t3.ts.pay.center.api.util.ExchangePointsUtils;
import com.t3.ts.pay.center.api.util.HttpRequestUtil;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.UnifiedDto;
import com.t3.ts.pay.remote.dto.marketing.IntegralDeductDto;
import com.t3.ts.pay.remote.dto.marketing.LadderDiscountDto;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.dto.sr.SrOrderDto;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.utils.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

/**
 * 包车相关的乘客支付充值逻辑
 *
 * <AUTHOR>
 * @version v1.0.0
 * @ClassName: CharteredPaymentBusiness
 * @Package com.t3.ts.pay.center.api.business
 * @date 2021/9/15 10:57
 */
@Slf4j
@Component
public class CharteredPaymentBusiness {

    @DubboReference
    private UnifiedService unifiedService;
    @Autowired
    private CommonPaymentBusiness commonPaymentBusiness;
    @Resource
    private RouteBusiness routeBusiness;
    @DubboReference
    private AccountInfoService accountInfoService;
    @Resource
    private T3CacheFactory cacheFactory;
    @Autowired
    private CharterRoutePayServiceImpl charterRoutePayService;
    @DubboReference
    private PassengerCacheService passengerCacheService;
    @DubboReference
    private SrOrderService srOrderService;

    /**
     * 收银台pro-包车
     *
     * @param req req
     * @return Response
     */
    public Response cashierDeskPro(PayTradeCharteredReq req) {
        if (CollectionUtils.isEmpty(req.getPayTypeList())) {
            final int num = -1;
            req.getPayTypeList().add(num);
        }
        CashierDeskProVo cashierDeskProVo = new CashierDeskProVo();
        cashierDeskProVo.setRoutePlanUuid(req.getRoutePlanUuid());

        StopWatch stopWatch = new StopWatch();

        // 司机信息
        DriverInfoBo driverInfoBo = commonPaymentBusiness.getDriverInfo(req.getRoutePlanUuid(), stopWatch);
        if (!RouteStatus.ROUTE_6.getStatus().equals(driverInfoBo.getRouteStatus())) {
            return Response.createError(ResultErrorEnum.ROUTE_PAYED);
        }
        cashierDeskProVo.setDriverName(driverInfoBo.getDriverName());
        cashierDeskProVo.setPlateNum(driverInfoBo.getPlateNum());

        // 获取收银台基础费用
        Response<CashierDeskBaseFareBo> response = getBaseFare(req, stopWatch);
        if (!response.isSuccess()) {
            return response;
        }
        CashierDeskBaseFareBo baseFareBo = response.getData();

        cashierDeskProVo.setPayTypeList(req.getPayTypeList());
        cashierDeskProVo.setAdditionalFee(baseFareBo.getAdditionalFee());
        cashierDeskProVo.setBalanceTotal(String.valueOf(baseFareBo.getBalanceTotal()));
        cashierDeskProVo.setPayDeskType(commonPaymentBusiness.getPayDeskType(baseFareBo.getPrPayed()));

        stopWatch.start("获取积分抵扣");
        // 获取积分抵扣
        PayDeskInfoVo payDeskInfoVo = getDeductPoints(baseFareBo.getIntegralPayed(), baseFareBo.getAdditionalFee(),
                baseFareBo.getRemainPay(), req.getRoutePlanUuid());
        stopWatch.stop();
        cashierDeskProVo.setIntegralDeductFlag(payDeskInfoVo.getIntegralDeductFlag());
        cashierDeskProVo.setIntegralMsg(payDeskInfoVo.getIntegralMsg());
        cashierDeskProVo.setAvailableIntegral(new BigDecimal(payDeskInfoVo.getAvailableIntegral()));
        // 积分可抵扣费用
        BigDecimal deductCost = BigDecimalUtils.bigDecimalOf(payDeskInfoVo.getDeductCost());
        // 余额可支付金额
        BigDecimal balancePayable = baseFareBo.getBalancePayable();
        // 余额信息
        CashierDeskProVo balanceInfo = commonPaymentBusiness.getBalanceInfo(baseFareBo,
                balancePayable, req.getPayTypeList(), deductCost);
        cashierDeskProVo.setBalanceMsg(balanceInfo.getBalanceMsg());
        cashierDeskProVo.setBalanceFlag(balanceInfo.getBalanceFlag());
        cashierDeskProVo.setAvailableBalance(balanceInfo.getAvailableBalance());

        // 是否使用券
        if (baseFareBo.getCouponPay().compareTo(BigDecimal.ZERO) > 0) {
            cashierDeskProVo.setCouponDeductFlag(true);
        }

        cashierDeskProVo.setPayItemList(getPayItemList(req.getRoutePlanUuid(), baseFareBo, stopWatch));
        cashierDeskProVo.setActualFare(baseFareBo.getRemainPay());
        cashierDeskProVo.setFinalFare(commonPaymentBusiness.getFinalFare(baseFareBo,
                req.getPayTypeList(), balancePayable, deductCost));

        log.info(stopWatch.prettyPrint());

        return Response.createSuccess(cashierDeskProVo);
    }

    /**
     * @param routePaymentReq 入参对象封装
     * @param passengerUuid   乘客uuid
     * @param passengerMobile 乘客手机号
     * @return com.t3.ts.result.Response<com.t3.ts.chartered.controller.payment.vo.RechargePayVo>
     * @author: qul
     * @Description: 行程支付
     * @CreateDate:
     */
    public Response<RechargePayVo> routePay(RoutePaymentReq routePaymentReq, String passengerUuid,
                                            String passengerMobile) {
        RoutePayDTO routePayDTO = new RoutePayDTO();
        routePayDTO.setOrderUuid(routePaymentReq.getOrderUuid());
        routePayDTO.setPayTypeList(routePaymentReq.getPayTypeList());
        routePayDTO.setCouponUuid(routePaymentReq.getCouponUuid());
        routePayDTO.setPassengerUuid(passengerUuid);
        routePayDTO.setPassengerMobile(passengerMobile);
        routePayDTO.setBizType(EnumPayOrderType.CHARTERED_ONLINE.getCode());
        // 积分抵扣：通过缓存获取可抵扣积分
        boolean hasIntegralType = routePaymentReq.getPayTypeList().parallelStream().anyMatch(
                payType -> payType.equals(EnumPayOrderChannel.INTEGRAL.getCode()));
        if (hasIntegralType) {
            routePayDTO.setAvailableIntegral(commonPaymentBusiness.deductPoints4Pay(passengerUuid));
        }
        Response<RechargePayBo> routePayResponse = charterRoutePayService.launchRoutePay(routePayDTO);
        if (!routePayResponse.isSuccess()) {
            return Response.createError(routePayResponse.getMsg(), routePayResponse.getCode());
        }
        try {
            cacheFactory.ExpireHash().deleteHash(RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid);
        } catch (Exception e) {
            log.error("ExpireHashService.deleteHash key: {}, field: {}，Error: {}", RoutePlanKey.DEDUCT_POINTS_KEY,
                    passengerUuid, e);
        }
        RechargePayBo rechargePayBo = routePayResponse.getData();
        //返回对象
        RechargePayVo rechargePayVo =
                new RechargePayVo(rechargePayBo.getPayType(), rechargePayBo.isNoSecret(), rechargePayBo.getSdk());
        return Response.createSuccess("获取支付信息成功", rechargePayVo);
    }

    /**
     * @param advanceChargeReq 入参对象预付款
     * @param passengerUuid    乘客uuid
     * @param passengerMobile  乘客手机号
     * @return com.t3.ts.result.Response<com.t3.ts.chartered.controller.payment.vo.RechargePayVo>
     * @author: qul
     * @Description: 充值（预付款）
     * @CreateDate:
     */
    public Response<RechargePayVo> advanceCharge(AdvanceChargeReq advanceChargeReq, String passengerUuid,
                                                 String passengerMobile) {
        //缓存中获取：行程对应的预付款流水号和预付金额
        RouteSerialDto routeSerialDto = new RouteSerialDto();
        routeSerialDto.setRoutePlanId(advanceChargeReq.getOrderUuid());
        Response<RouteSerialDto> routeSerialDtoResponse = passengerCacheService.queryRouteSerial(routeSerialDto);
        if (!routeSerialDtoResponse.isSuccess() || Objects.isNull(routeSerialDtoResponse.getData())) {
            return Response.createError(ResultErrorEnum.PRE_PAY_AMOUNT_TIMEOUT);
        }
        RouteSerialDto responseData = routeSerialDtoResponse.getData();
        final String prePayNo = responseData.getPrePayNo();
        final BigDecimal rechargeAmount = responseData.getRechargeAmount();
        if (StringUtils.isEmpty(prePayNo)) {
            return Response.createError(ResultErrorEnum.PRE_PAY_AMOUNT_TIMEOUT);
        }
        if (Objects.isNull(rechargeAmount)) {
            return Response.createError(ResultErrorEnum.PRE_PAY_AMOUNT_TIMEOUT);
        }
        ChartedAdvanceRechargeDTO chartedAdvanceRechargeDTO = new ChartedAdvanceRechargeDTO();
        chartedAdvanceRechargeDTO.setPassengerUuid(passengerUuid);
        chartedAdvanceRechargeDTO.setPassengerMobile(passengerMobile);
        chartedAdvanceRechargeDTO.setPayType(advanceChargeReq.getPayType());
        // 预付款流水号
        chartedAdvanceRechargeDTO.setPrePayNo(prePayNo);
        // 充值金额
        chartedAdvanceRechargeDTO.setRechargeAmount(rechargeAmount);
        Response<RechargePayBo> rechargePayResponse = charterRoutePayService.advanceRecharge(chartedAdvanceRechargeDTO);
        if (!rechargePayResponse.isSuccess()) {
            return Response.createError(rechargePayResponse.getMsg(), rechargePayResponse.getCode());
        }
        RechargePayBo rechargePayBo = rechargePayResponse.getData();
        //返回对象
        RechargePayVo rechargePayVo =
                new RechargePayVo(rechargePayBo.getPayType(), rechargePayBo.isNoSecret(), rechargePayBo.getSdk());
        return Response.createSuccess("获取预付款支付信息成功", rechargePayVo);
    }

    /**
     * 获取收银台基础费用
     *
     * @param req       req
     * @param stopWatch stopWatch
     * @return CashierDeskBaseFareBo
     */
    private Response<CashierDeskBaseFareBo> getBaseFare(PayTradeCharteredReq req, StopWatch stopWatch) {
        PayDeskReq deskProDto = new PayDeskReq();
        deskProDto.setCouponId(req.getCouponUuid());
        deskProDto.setOrderId(req.getRoutePlanUuid());
        deskProDto.setType("0");
        deskProDto.setUserId(RequestContextHelper.getPassengerUuid());
        deskProDto.setPayWay(commonPaymentBusiness.getPayWay(req.getPayTypeList()));
        deskProDto.setPayChannel(commonPaymentBusiness.getPayChannel(req.getPayTypeList()));
        deskProDto.setUseCouponType(req.getUseCouponType());
        stopWatch.start("com.t3.ts.pay.remote.service.UnifiedService.handle");

        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.pay.paydesk.deskPro");
        dto.setExtendParam(JSON.toJSONString(deskProDto));
        Response response = unifiedService.handle(dto);

        stopWatch.stop();
        log.info("traceId {} com.t3.ts.pay.remote.service.UnifiedService.handle,Request={} , Resposne={}",
                MDC.get(CharteredAppConstants.LOG_TRACE_ID), JSON.toJSONString(deskProDto),
                JSON.toJSONString(response));
        if (!response.isSuccess() || ObjectUtils.isEmpty(response.getData())) {
            return Response.createError("查询收银台信息失败");
        }
        CashierDeskDto cashierDestDto = JSON.parseObject((String) response.getData(), CashierDeskDto.class);
        FareDetailDto fareDetailDto = cashierDestDto.getFareDetail();
        BalanceDto balanceDto = cashierDestDto.getBalance();
        PayedDto payed = cashierDestDto.getPayed();
        BalanceCanPayDetailDto balanceCanPayDetailDto = cashierDestDto.getBalanceCanPayDetail();
        CouponDetailDto couponDetailDto = cashierDestDto.getCouponDetail();

        // 【支付项列表】优惠券抵扣信息
        CouponVo couponVo = new CouponVo();
        if (!ObjectUtils.isEmpty(couponDetailDto) && !CollectionUtils.isEmpty(couponDetailDto.getCanUseCoupon())) {
            List<CanUseCouponDto> canUseCouponDtoList = Lists.newArrayList();
            couponDetailDto.getCanUseCoupon().forEach(var ->
                    canUseCouponDtoList.add(JSON.parseObject(var, CanUseCouponDto.class)));
            if (BigDecimalUtils.bigDecimalOf(couponDetailDto.getDecutionAmount()).compareTo(BigDecimal.ZERO) > 0) {
                couponVo.setCouponCount(canUseCouponDtoList.size());
                couponVo.setCouponId(canUseCouponDtoList.get(0).getCouponId());
                couponVo.setDecutionAmount(BigDecimalUtils.bigDecimalOf(couponDetailDto.getDecutionAmount()));
                String thirdChannel = couponDetailDto.getThirdChannel();
                if (StringUtils.isNotEmpty(thirdChannel)) {
                    List<String> tlist = Arrays.asList(thirdChannel.split(","));
                    couponVo.setThirdChannelList(tlist);
                }
            }
        }

        // 总费用
        BigDecimal totalFare = BigDecimalUtils.bigDecimalOf(fareDetailDto.getRemainPay())
                .add(BigDecimalUtils.bigDecimalOf(payed.getAmountPayed()));
        // 余额已支付
        BigDecimal balancePay = BigDecimalUtils.bigDecimalOf(payed.getBalancePay()).add(
                BigDecimalUtils.bigDecimalOf(payed.getGiftCardPayed())
        );
        // 剩余应付金额
        BigDecimal remainPay = BigDecimalUtils.bigDecimalOf(fareDetailDto.getRemainPay());
        // 扣减优惠券
        if (BigDecimalUtils.bigDecimalValue(couponVo.getDecutionAmount()).compareTo(BigDecimal.ZERO) > 0) {
            remainPay = remainPay.subtract(couponVo.getDecutionAmount());
        }

        CashierDeskBaseFareBo cashierDeskBaseFareBo = new CashierDeskBaseFareBo();
        cashierDeskBaseFareBo.setCouponVo(couponVo);
        cashierDeskBaseFareBo.setBalanceTotal(BigDecimalUtils.bigDecimalOf(balanceDto.getBalanceTotal()));
        cashierDeskBaseFareBo.setAdditionalFee(BigDecimalUtils.bigDecimalOf(fareDetailDto.getServiceFare()));
        cashierDeskBaseFareBo.setRemainPay(remainPay);
        cashierDeskBaseFareBo.setAmountPayed(BigDecimalUtils.bigDecimalOf(payed.getAmountPayed()));
        cashierDeskBaseFareBo.setTotalFare(totalFare);
        cashierDeskBaseFareBo.setPrPayed(BigDecimalUtils.bigDecimalOf(payed.getPrPayed()));
        cashierDeskBaseFareBo.setCouponPay(BigDecimalUtils.bigDecimalOf(payed.getCouponPay()));
        cashierDeskBaseFareBo.setBalancePay(balancePay);
        cashierDeskBaseFareBo.setIntegralPayed(BigDecimalUtils.bigDecimalOf(payed.getIntegralPayed()));
        cashierDeskBaseFareBo.setGiftCardCanPayService(balanceCanPayDetailDto.getGiftCardCanPayService());

// 余额可支付金额
        BigDecimal balanceMoney =
                new BigDecimal(balanceCanPayDetailDto.getGiftCardCanPay())
                        .add(new BigDecimal(balanceCanPayDetailDto.getGiftMoneyCanPay()));
        cashierDeskBaseFareBo.setBalancePayable(balanceMoney);
        log.info("获取收银台基础费用，结果是 {}", JSON.toJSONString(cashierDeskBaseFareBo));
        return Response.createSuccess(cashierDeskBaseFareBo);
    }

    /**
     * 获取积分抵扣
     *
     * @param integralPayed 积分已抵扣
     * @param additionalFee 附加费
     * @param remainPay     剩余应付金额
     * @param routePlanUuid routePlanUuid
     * @return PayDeskInfoVo
     */
    private PayDeskInfoVo getDeductPoints(BigDecimal integralPayed, BigDecimal additionalFee,
                                          BigDecimal remainPay, String routePlanUuid) {

        PayDeskInfoVo payDeskInfoVo = new PayDeskInfoVo();
        if (integralPayed.compareTo(BigDecimal.ZERO) > 0) {
            payDeskInfoVo.setIntegralDeductFlag(false);
            payDeskInfoVo.setAvailableIntegral("0");
        } else {
            // 订单费用
            payDeskInfoVo.setOrderUuid(routePlanUuid);
            BigDecimal orderCost = remainPay.subtract(additionalFee);
            deductPoints(orderCost, payDeskInfoVo, additionalFee);
        }
        log.info("获取积分抵扣结果是 {}", JSON.toJSONString(payDeskInfoVo));
        return payDeskInfoVo;
    }

    /**
     * 计算积分抵扣
     *
     * @param additionalFee 附加费
     * @param orderCost     本次订单费用
     * @param payDeskInfoVo 收银台vo
     * @return 积分抵扣情况
     */
    private Response<PayDeskInfoVo> deductPoints(//NOSONAR
                                                 BigDecimal orderCost,
                                                 PayDeskInfoVo payDeskInfoVo,
                                                 BigDecimal additionalFee) {
        String passengerUuid = RequestContextHelper.getPassengerUuid();
        // 可用积分
        BigDecimal availableIntegral = BigDecimal.ZERO;
        // 抵扣费用
        BigDecimal deductCost = BigDecimal.ZERO;
        payDeskInfoVo.setAvailableIntegral(availableIntegral + "");
        payDeskInfoVo.setDeductCost(deductCost + "");

        // 积分抵扣不允许重复使用
        if (Objects.nonNull(payDeskInfoVo.getIntegralPayed())
                && payDeskInfoVo.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            log.error("已使用积分抵扣金额: {}，不允许重复抵扣", payDeskInfoVo.getIntegralPayed());
            return getUndeductResponse(payDeskInfoVo);
        }

        // 获取积分抵扣规则
        JSONObject baseRuleCfgDto = new JSONObject();
        baseRuleCfgDto.put("businessType", MemberAccountBusinessTypeEnum.BUSINESS_TYPE_CHARTERED.getBusinessType());
        baseRuleCfgDto.put("recordStatus", 1);

        //产品线、业务线改造，入参变动 原businessType废弃，新增 产品线bizType 业务线bizLine
        Response<SrOrderDto> orderDtoResponse =
                srOrderService.querySrOrderByRoutePlanId(payDeskInfoVo.getOrderUuid());
        Integer bizLine = orderDtoResponse.getData().getExpandBizLine();
        Integer bizType = orderDtoResponse.getData().getTypeModule();
        Response<IntegralDeductDto> deductPointsRes = commonPaymentBusiness.getIntegralDeductInfo(bizType, bizLine);
        if (null == deductPointsRes || Boolean.FALSE.equals(deductPointsRes.isSuccess())
                || null == deductPointsRes.getData()) {
            log.info("积分抵扣规则获取失败: {}", JSON.toJSONString(deductPointsRes));
            return getUndeductResponse(payDeskInfoVo);
        }
        IntegralDeductDto integralDeductDTO = deductPointsRes.getData();
        List<LadderDiscountDto> ladderDiscountDtoList = integralDeductDTO.getLadderDiscountDtos();
        if (CollectionUtils.isEmpty(ladderDiscountDtoList)) {
            log.error("无法获取积分抵扣梯度规则");
            return getUndeductResponse(payDeskInfoVo);
        }

        // 计算可抵扣费用
        // 每单最小抵扣金额
        BigDecimal minDeductMoney = integralDeductDTO.getMinDeductMoney();
        // 获取可用梯度规则
        List<LadderDiscountDto> ladderDiscountDtos = ladderDiscountDtoList.parallelStream()
                .filter(ladderDiscount -> orderCost.compareTo(ladderDiscount.getConsumeMoney()) >= 0)
                .sorted(Comparator.comparing(LadderDiscountDto::getConsumeMoney))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ladderDiscountDtos)) {
            LadderDiscountDto ladderDiscountDto = ladderDiscountDtos.get(ladderDiscountDtos.size() - 1);
            // 1. 计算订单费用所在梯度能抵扣的费用
            deductCost = orderCost.multiply(ladderDiscountDto.getDeductCoefficient())
                    .divide(new BigDecimal(NumConstants.NUM_100_INT)).setScale(NumConstants.NUM_2, RoundingMode.DOWN);

            // 2. 比较所在梯度的最大抵扣费用
            if (Objects.nonNull(ladderDiscountDto.getMaxDeductMoney())) {
                deductCost = deductCost.compareTo(ladderDiscountDto.getMaxDeductMoney()) > 0
                        ? ladderDiscountDto.getMaxDeductMoney().setScale(NumConstants.NUM_2, RoundingMode.DOWN)
                        : deductCost;
            }
            log.info("订单费用: {}， 可抵扣订单费用: {}", orderCost, deductCost);
            // 3. 判断抵扣金额是否符合最小抵扣限制
            if (Objects.nonNull(minDeductMoney) && minDeductMoney.compareTo(deductCost) > 0) {
                log.error("积分可抵扣金额: {} 小于最小抵扣限制: {}，无法进行抵扣", deductCost, minDeductMoney);
                return getUndeductResponse(payDeskInfoVo);
            }
        }

        // 获取积分现金兑换比例
        UnifiedDto unifiedDto = new UnifiedDto();
        unifiedDto.setSceneType(PayCenterMethodEnum.PAY_CENTER_METHOD_EXCHANGE_POINTS.getMethod());
        unifiedDto.setChannel(HttpRequestUtil.parseUa());
        Response exchangeRes = unifiedService.handle(unifiedDto);
        log.info("traceId={} >>> 积分现金比例响应结果: {}", MDC.get(CharteredAppConstants.LOG_TRACE_ID),
                JSON.toJSONString(exchangeRes));
        if (Boolean.FALSE.equals(exchangeRes.isSuccess())) {
            log.error("积分现金比例获取失败：" + exchangeRes.getMsg());
            return getUndeductResponse(payDeskInfoVo);
        }
        if (null == exchangeRes.getData()) {
            log.error("未获取到积分现金比例");
            return getUndeductResponse(payDeskInfoVo);
        }

        JSONObject exchangePointsRes = JSON.parseObject(JSON.toJSONString(exchangeRes.getData()));
        //积分兑换比例
        String exchangePoints = exchangePointsRes.getString("value");
        // 获取个人积分
        Response<AccountInfoDto> accountRes = accountInfoService.queryAccountByUserId(null, passengerUuid);
        if (Boolean.FALSE.equals(accountRes.isSuccess()) || Objects.isNull(accountRes.getData())) {
            log.error("获取个人积分失败: " + accountRes.getMsg());
            return getUndeductResponse(payDeskInfoVo);
        }
        BigDecimal personalPoints = accountRes.getData().getTotalAccount();

        // 积分抵扣限制校验
        payDeskInfoVo.setIntegralDeductFlag(true);
        // 最少可抵扣积分
        BigDecimal minPoints = ExchangePointsUtils.changeMoney2Points(minDeductMoney, exchangePoints, RoundingMode.UP);
        // 最小订单费用
        BigDecimal minCost = ladderDiscountDtoList.stream().map(LadderDiscountDto::getConsumeMoney)
                .min(BigDecimal::compareTo).get().setScale(NumConstants.NUM_2, RoundingMode.UP);
        log.info("个人积分: {}，每单最小抵扣金额: {}，最小抵扣积分: {}", personalPoints, minDeductMoney, minPoints);
        // 既有积分限制，也有订单费用限制
        if (personalPoints.compareTo(minPoints) < 0 || CollectionUtils.isEmpty(ladderDiscountDtos)) {
            if (Objects.nonNull(additionalFee) && additionalFee.compareTo(BigDecimal.ZERO) > 0) {
                return getUndeductResponse(payDeskInfoVo);
            }
            IntegralMsgVO integralMsgVO = commonPaymentBusiness.initIntegralMsg(
                    PassengerConstants.DEDUCT_POINTS_POINTS_AND_COST_LIMIT, minCost + "", minPoints.intValue() + "");
            payDeskInfoVo.setIntegralMsg(integralMsgVO);
            return Response.createSuccess(payDeskInfoVo);
        }

        // 换算可抵扣积分
        BigDecimal deductIntegral =
                ExchangePointsUtils.changeMoney2Points(deductCost, exchangePoints, RoundingMode.DOWN);
        // 判断可用积分是否足够抵扣，否则部分抵扣
        availableIntegral = deductIntegral.compareTo(personalPoints) > 0 ? personalPoints : deductIntegral;
        // 通过费用兑换积分有精度问题，通过积分重新计算费用
        deductCost = ExchangePointsUtils.changePoints2Money(availableIntegral, exchangePoints);
        payDeskInfoVo.setAvailableIntegral(availableIntegral.intValue() + "");
        payDeskInfoVo.setDeductCost(deductCost + "");
        // 缓存可抵扣积分和费用，支付时使用
        try {
            cacheFactory.ExpireHash().setExpireHash(
                    RoutePlanKey.DEDUCT_POINTS_KEY, passengerUuid, payDeskInfoVo, NumConstants.NUM_3600);
        } catch (Exception e) {
            log.info("ExpireHashService.setExpireHash key: {}, field: {}, Error: {}", RoutePlanKey.DEDUCT_POINTS_KEY,
                    passengerUuid, e);
        }
        IntegralMsgVO integralMsgVO = commonPaymentBusiness.initIntegralMsg(PassengerConstants.DEDUCT_POINTS_CAN_PAY,
                availableIntegral.intValue() + "", deductCost + "");
        payDeskInfoVo.setIntegralMsg(integralMsgVO);
        return Response.createSuccess(payDeskInfoVo);
    }

    /**
     * 封装不展示T币支付响应
     *
     * @param payDeskInfoVo 收银台vo
     * @return 不展示T币支付响应
     */
    private Response<PayDeskInfoVo> getUndeductResponse(PayDeskInfoVo payDeskInfoVo) {
        payDeskInfoVo.setIntegralDeductFlag(false);
        return Response.createSuccess("获取收银台数据成功", payDeskInfoVo);
    }

    /**
     * 支付项
     *
     * @param routePlanUuid 行程ID
     * @param baseFareBo    基础费用
     * @param stopWatch     stopWatch
     * @return List<PayItem>
     */
    private List<PayItem> getPayItemList(String routePlanUuid, CashierDeskBaseFareBo baseFareBo, StopWatch stopWatch) {
        // 获取取消的费用
        CancelFareBo cancelFareBo = getCancelFare(routePlanUuid, stopWatch);
        List<PayItem> payItemList = Lists.newArrayList();
        // 【支付项明细列表】0:合计费用
        if (cancelFareBo.getCancelFare().compareTo(BigDecimal.ZERO) <= 0) {
            payItemList.add(new PayItem("合计费用", String.valueOf(baseFareBo.getTotalFare()), Boolean.FALSE, 0, null));
        }
        // 【支付项明细列表】1:取消费
        if (cancelFareBo.getCancelFare().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("取消费", String.valueOf(cancelFareBo.getCancelFare()), Boolean.FALSE, 1, null));
        }
        // 【支付项明细列表】2:超时等待费
        if (cancelFareBo.getTimeOutFee().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "超时等待费", String.valueOf(cancelFareBo.getTimeOutFee()), Boolean.FALSE, NumConstants.NUM_2, null));
        }
        // 【支付项明细列表】3:预付款已支付
        if (baseFareBo.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "已预付", "-" + baseFareBo.getPrPayed(), Boolean.FALSE, NumConstants.NUM_3_INT, null));
        }
        // 【支付项明细列表】4:优惠券已抵扣
        if (baseFareBo.getCouponPay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "优惠券已抵扣", "-" + baseFareBo.getCouponPay(), Boolean.FALSE, NumConstants.NUM_4_INT, null));
        } else {
            // 【支付项明细列表】4:优惠券
            payItemList.add(new PayItem("优惠券", null, Boolean.FALSE, NumConstants.NUM_4_INT,
                    baseFareBo.getCouponVo()));
        }
        // 【支付项明细列表】5:余额已支付
        if (baseFareBo.getBalancePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "余额已支付", "-" + baseFareBo.getBalancePay(), Boolean.FALSE, NumConstants.NUM_5, null));
        }
        // 【支付项明细列表】6:T币已抵扣
        if (baseFareBo.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "T币已抵扣", "-" + baseFareBo.getIntegralPayed(), Boolean.FALSE, NumConstants.NUM_6, null));
        }
        // 【支付项明细列表】7:还需支付
        if (baseFareBo.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem(
                    "还需支付", baseFareBo.getRemainPay().toString(), Boolean.FALSE, NumConstants.NUM_7_INT, null));
        }
        return payItemList;
    }

    /**
     * 获取取消的费用
     *
     * @param routePlanUuid routePlanUuid
     * @param stopWatch     stopWatch
     * @return CancelFareBo
     */
    private CancelFareBo getCancelFare(String routePlanUuid, StopWatch stopWatch) {
        RouteFareItemReq req = new RouteFareItemReq();
        req.setRoutePlanUuid(routePlanUuid);

        stopWatch.start("/api/passenger/read/v1/getRouteFareItems");
        Response<PassengerRouteFareItemsVo> routeFareResp = routeBusiness.getPassengerRouteFareItems(req);
        stopWatch.stop();
        log.info("traceId {} /api/passenger/read/v1/getRouteFareItems,Request={} , Resposne={}",
                MDC.get(CharteredAppConstants.LOG_TRACE_ID), JSON.toJSONString(req),
                JSON.toJSONString(routeFareResp));
        PassengerRouteFareItemsVo routeFareItemsDto = routeFareResp.getData();
        // 超时费
        BigDecimal timeOutFee = BigDecimalUtils.bigDecimalValue(routeFareItemsDto.getWaitFare())
                .subtract(BigDecimalUtils.bigDecimalValue(routeFareItemsDto.getDiscountFare()));
        // 取消费
        BigDecimal cancelFare = BigDecimalUtils.bigDecimalValue(routeFareItemsDto.getCancelFare());

        CancelFareBo bo = new CancelFareBo();
        bo.setTimeOutFee(timeOutFee);
        bo.setCancelFare(cancelFare);
        return bo;
    }

}
