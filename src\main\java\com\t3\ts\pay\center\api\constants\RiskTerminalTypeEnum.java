package com.t3.ts.pay.center.api.constants;

import com.t3.ts.utils.StringUtils;
import lombok.Getter;

/**
 * 风控终端类型枚举
 *
 * <AUTHOR>
 * @date 2023/11/1 11:23
 */
@Getter
public enum RiskTerminalTypeEnum {
    ANDROID(1, "android平台", CommonNumConst.STR_GRAV_A),
    IOS(2, "IOS平台", CommonNumConst.STR_GRAV_I),
    WEB(3, "web", CommonNumConst.H5),
    WECHAT_APPLET(4, "微信小程序", CommonNumConst.WX),
    ALI_APPLET(5, "支付宝小程序", CommonNumConst.AX);

    /**
     * 类型代码
     */
    private Integer type;

    /**
     * 类型名称
     */
    private String name;

    /**
     * app版本前缀
     */
    private String grayVersionPrefix;

    /**
     * 实名认证枚举构造
     *
     * @param type 类型代码
     * @param name 类型名称
     */
    RiskTerminalTypeEnum(Integer type, String name, String grayVersionPrefix) {
        this.type = type;
        this.name = name;
        this.grayVersionPrefix = grayVersionPrefix;
    }

    /**
     * 根据版本获取终端类型
     *
     * @param grayVersion 终端版本信息
     * @return 终端类型
     */
    public static Integer getTypeByGrayVersion(String grayVersion) {
        if(StringUtils.isEmpty(grayVersion)) {
            return null;
        }
        for (RiskTerminalTypeEnum riskTerminalTypeEnum : RiskTerminalTypeEnum.values()) {
            if (grayVersion.startsWith(riskTerminalTypeEnum.getGrayVersionPrefix())) {
                return riskTerminalTypeEnum.getType();
            }
        }
        return null;
    }

}
