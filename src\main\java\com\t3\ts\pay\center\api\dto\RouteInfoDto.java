package com.t3.ts.pay.center.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 行程详情
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RouteInfoDto implements Serializable {
    /**
     * 行程ID
     */
    private String routeId;
    /**
     * 行程状态
     */
    private Integer status;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String plateNum;

    /**
     * 是否新能源车
     */
    private Integer isElectricCar;

    /**
     * 司机ID
     */
    private String driverUuid;

    /**
     * 车辆vin
     */
    private String vin;

    /**
     * 车辆ID
     */
    private String carUuid;

    /**
     * 产品线
     */
    private Integer typeModule;

    /**
     * 业务线
     */
    private Integer expandBizLine;

    /**
     * 费用信息
     */
    private Integer fareMethod;

    /**
     * 座位数量
     */
    private Integer seatNum;

    /**
     * 拼成状态
     */
    private Boolean carPoolSuccess;

    /**
     * 行程特征标签列表
     */
    private List<String> labels;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 用车类型 标识用户是个人用户还是企业用户
     */
    private Integer typeEnt;

    /**
     * 乘客UUID
     */
    private String passengerUuid;

    /**
     * 服务模式  表示服务模式类型，如单程、往返等。
     */
    private Integer serviceModel;
    /**
     * 行程的具体类型，如用车、日租等。影响行程安排和费用计算。
     */
    private Integer typeTrip;
    /**
     * 城市code
     */
    private String cityCode;
    /**
     * 来源
     */
    private Integer source;
    /**
     * 起点地址
     */
    private String oriAddress;
    /**
     * 终点地址
     */
    private String destAddress;
}
