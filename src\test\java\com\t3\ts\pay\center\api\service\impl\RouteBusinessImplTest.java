package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.dto.route.RouteBasicDto;
import com.t3.ts.pay.center.api.dto.route.RoutePlanUuidReq;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.pay.center.api.rest.RouteReadRest;
import com.t3.ts.result.PageResult;
import com.t3.ts.result.Response;
import java.util.Arrays;
import java.util.List;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

/**
 * Describer:
 *
 * <AUTHOR>
 * 2022/11/7 13:59
 */
public class RouteBusinessImplTest {
    @Mock
    List<Integer> allStatus;
    @Mock
    RouteReadRest routeReadRest;
    @Mock
    Logger log;
    @InjectMocks
    RouteBusinessImpl routeBusinessImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

//
//    @Test
//    public void testGetPassengerRouteFareItems() throws Exception {
//        when(routeReadRest.getRouteFareItems(any())).thenReturn(null);
//
//        Response<PassengerRouteFareItemsVo> result = routeBusinessImpl
//                .getPassengerRouteFareItems(new RouteFareItemReq("routePlanUuid", Arrays.<String>asList("String")));
//        Assert.assertEquals(null, result);
//    }

    @Test
    public void testGetEditRoutePointByCache() throws Exception {
        when(routeReadRest.getEditRoutePointByCache(any())).thenReturn(null);

        Response result = routeBusinessImpl.getEditRoutePointByCache("routePlanUuid");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testConvertData() throws Exception {
        Response<T> result = routeBusinessImpl.convertData(null, null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testConvertListData() throws Exception {
        Response<List<T>> result = routeBusinessImpl.convertListData(null, null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testConvertPageData() throws Exception {
        Response<PageResult<T>> result = routeBusinessImpl.convertPageData(null, null);
        Assert.assertEquals(null, result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
