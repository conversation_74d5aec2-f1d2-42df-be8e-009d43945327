package com.t3.ts.pay.center.api.business.query.paystatus.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.pay.center.api.business.query.paystatus.PayStatusDto;
import com.t3.ts.pay.center.api.business.query.paystatus.QueryStatusContext;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.dto.PaymentQueryResultDto;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.OfflineCompanyDto;
import com.t3.ts.settlement.centre.service.SettlementRechargeService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 企业充值状态查询
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@Slf4j
@Component
public class CompanyRechargePayStatusHandler implements PayStatusHandler {

    @DubboReference
    private SettlementRechargeService settlementRechargeService;

    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQueryFacade;

    @Override
    public String getType() {
        return String.valueOf(EnumPayOrderType.COMPANY_RECHARGE_ONLINE.getCode());
    }

    @Override
    public Response<JSONObject> handler(QueryStatusContext context) {
        if (StringUtils.isEmpty(context.getSettleId())) {
            throw new IllegalArgumentException("输入参数有误");
        }

        Response<OfflineCompanyDto> response = settlementRechargeService.getOfflineCompanyInfo(context.getSettleId());
        if (null == response || !response.isSuccess() || null == response.getData()) {
            throw new IllegalStateException("结算单不存在");
        }
        context.setUserId(response.getData().getCompanyUuid());

        PayStatusDto result = new PayStatusDto();
        result.setSettleId(context.getSettleId());
        Response<PaymentQueryResultDto> queryRes =
                unifiedPaymentQueryFacade.queryBySettlementId(context.getUserId(), context.getSettleId());
        // 查询失败，设置为支付中
        if (null == queryRes || !queryRes.isSuccess() || null == queryRes.getData()) {
            result.setPayStatus(NumConstants.NUM_3);
        } else {
            result.setPayStatus(checkStatus(queryRes.getData()));
        }
        return Response.createSuccess("", JSON.parseObject(JSON.toJSONString(result)));
    }

    /**
     * 支付状态，1：初始化，2：待（支付）结算，3：（支付）结算中，4：（支付）结算成功
     *
     * @param resultDto 上下文
     * @return {@link Integer}
     */
    private Integer checkStatus(PaymentQueryResultDto resultDto) {
        Integer status = NumConstants.NUM_2;
        if (null != resultDto.getStatus()) {
            if (NumConstants.NUM_3 == resultDto.getStatus()) {
                status = NumConstants.NUM_4;
            } else if (NumConstants.NUM_2 == resultDto.getStatus()) {
                status = NumConstants.NUM_3;
            }
        }
        return status;
    }
}
