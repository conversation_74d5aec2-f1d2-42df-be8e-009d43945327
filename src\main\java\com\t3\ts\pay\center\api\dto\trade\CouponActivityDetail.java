package com.t3.ts.pay.center.api.dto.trade;

import lombok.Data;

import java.io.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * @ClassName CouponActivityDetail.java
 * <AUTHOR>
 * @version 1.0.0
 * @Description TODO
 * @createTime 2021年07月13日 19:23:00
 */
@Data
public class CouponActivityDetail implements Serializable {

    private static final long serialVersionUID = -7654913278603889648L;

    //优惠券活动是否最优  **必反回**
    private Boolean best;

    //优惠券活动ID
    private String couponNewUuid;

    //商品sku
    private String skuCode;

    //商品skuId
    private String skuId;

    //活动名称
    private String activityName;

    //文案信息
    private String desc;

    //优惠券可抵扣金额  元
    private BigDecimal decutionAmount;

    //活动金额 元
    private BigDecimal activityDecutionAmount;

    // 新人升级包价值(差额)
    private BigDecimal upgradeRewardAmount;

    // 券包总额
    private BigDecimal skuAmount;

    // 本单是否可用
    private Boolean sellStatus;


    // 不可用描述
    private String forbidRemark;

    private String forbidTitle;

    // 新人 老客标识
    private String sellType;

    // 券包 券数量
    private String skuNum;

    // 新-券包商品code
    private String skuCodeNew;

    // 新-券包商品uuid
    private String skuUuid;

    // 商品详情跳转链接
    private String spuUrl;


    private Integer sourceType;

    private String activityId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 收银台展示的券包活动文案
     */
    private String paySellPriceDesc;

}
