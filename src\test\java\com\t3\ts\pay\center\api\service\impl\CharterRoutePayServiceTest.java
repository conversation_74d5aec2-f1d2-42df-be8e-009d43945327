package com.t3.ts.pay.center.api.service.impl;

import com.t3.ts.pay.center.api.dto.ChartedAdvanceRechargeDTO;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.service.CharterRoutePayService;
import com.t3.ts.result.Response;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用模块名称<p>
 * 代码描述<p>
 *
 * <AUTHOR>
 * @since 2020/11/30 16:20
 */
public class CharterRoutePayServiceTest {
    @Mock
    Logger log;
    @InjectMocks
    CharterRoutePayService charterRoutePayService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testLaunchRoutePay() {
        RoutePayDTO routePayDTO = new RoutePayDTO();
        routePayDTO.setPassengerUuid("284498cf422740d7a0e70b7d5978e2f9");
        routePayDTO.setSource(1);
        List<Integer> pays = new ArrayList<>();
        pays.add(4);
        routePayDTO.setPayTypeList(pays);
        routePayDTO.setOrderUuid("100000266700450t3go0000208407082");
        routePayDTO.setUseCounponActivity(0);
        Response<?> response = charterRoutePayService.launchRoutePay(routePayDTO);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testGetPayDeskInfo() {
        Response<?> response = charterRoutePayService.getPayDeskInfo("100000266700450t3go0000208407082",
                "284498cf422740d7a0e70b7d5978e2f9");
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void testAdvanceRecharge() {
        ChartedAdvanceRechargeDTO advanceRechargeDTO = new ChartedAdvanceRechargeDTO();
        advanceRechargeDTO.setPassengerUuid("284498cf422740d7a0e70b7d5978e2f9");
        advanceRechargeDTO.setPayType(1);
        advanceRechargeDTO.setPrePayNo("advanceRechargeDTO");
        Response<?> response = charterRoutePayService.advanceRecharge(advanceRechargeDTO);
        Assert.assertTrue(response.isSuccess());
    }
}
