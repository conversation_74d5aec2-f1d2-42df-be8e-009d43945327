package com.t3.ts.pay.center.api.business;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.t3.ts.account.center.dto.AccountSignChannelDto;
import com.t3.ts.account.center.dto.AccountSignParamDto;
import com.t3.ts.account.center.service.AccountSignService;
import com.t3.ts.cache.t3.type.T3CacheFactory;
import com.t3.ts.channelmgr.center.dto.PayChannelReqDto;
import com.t3.ts.channelmgr.center.dto.PayChannelResDto;
import com.t3.ts.channelmgr.center.service.PayChannelTakeOverService;
import com.t3.ts.interactive.exception.ExceptionUtils;
import com.t3.ts.member.account.remote.dto.AccountInfoDto;
import com.t3.ts.member.account.remote.service.AccountInfoService;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.common.CmbAggHelper;
import com.t3.ts.pay.center.api.business.common.CommonPaymentBusiness;
import com.t3.ts.pay.center.api.cache.RoutePlanKey;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.center.api.constants.NumConstants;
import com.t3.ts.pay.center.api.constants.PassengerConstants;
import com.t3.ts.pay.center.api.constants.PayCenterMethodEnum;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.center.api.dto.FareParam;
import com.t3.ts.pay.center.api.dto.RouteInfoDto;
import com.t3.ts.pay.center.api.dto.RoutePayDTO;
import com.t3.ts.pay.center.api.dto.RoutePaymentReq;
import com.t3.ts.pay.center.api.dto.common.CanUseCoupon;
import com.t3.ts.pay.center.api.dto.route.RouteFareItemReq;
import com.t3.ts.pay.center.api.dto.route.RouteOrderQueryReq;
import com.t3.ts.pay.center.api.dto.route.RoutePlanDetailReq;
import com.t3.ts.pay.center.api.dto.sign.SignButtonVo;
import com.t3.ts.pay.center.api.dto.trade.Balance;
import com.t3.ts.pay.center.api.dto.trade.BalanceCanPayDetail;
import com.t3.ts.pay.center.api.dto.trade.ChannelCouponNumVo;
import com.t3.ts.pay.center.api.dto.trade.ChannelListReq;
import com.t3.ts.pay.center.api.dto.trade.CouponActivityDetail;
import com.t3.ts.pay.center.api.dto.trade.CouponDetail;
import com.t3.ts.pay.center.api.dto.trade.CouponVo;
import com.t3.ts.pay.center.api.dto.trade.FareDetail;
import com.t3.ts.pay.center.api.dto.trade.MakeOrderReq;
import com.t3.ts.pay.center.api.dto.trade.PayDeskReq;
import com.t3.ts.pay.center.api.dto.trade.PayDeskRes;
import com.t3.ts.pay.center.api.dto.trade.PayDeskVo;
import com.t3.ts.pay.center.api.dto.trade.PayDeskVoV3;
import com.t3.ts.pay.center.api.dto.trade.PayForOtherDetail;
import com.t3.ts.pay.center.api.dto.trade.PayItem;
import com.t3.ts.pay.center.api.dto.trade.PayTradeReq;
import com.t3.ts.pay.center.api.dto.trade.Payed;
import com.t3.ts.pay.center.api.dto.trade.PrivilegeDetail;
import com.t3.ts.pay.center.api.dto.trade.RouteInfo;
import com.t3.ts.pay.center.api.dto.trade.UnWarriedArriveDetail;
import com.t3.ts.pay.center.api.dto.vo.PassengerRouteFareItemsVo;
import com.t3.ts.pay.center.api.dto.vo.RechargePayVo;
import com.t3.ts.pay.center.api.exception.BusinessException;
import com.t3.ts.pay.center.api.rest.MarketingRest;
import com.t3.ts.pay.center.api.rest.OperationRest;
import com.t3.ts.pay.center.api.rest.PassengerFeignClient;
import com.t3.ts.pay.center.api.rest.RouteReadRest;
import com.t3.ts.pay.center.api.rest.route.RouteDetailClient;
import com.t3.ts.pay.center.api.service.RouteBusiness;
import com.t3.ts.pay.center.api.service.RoutePayService;
import com.t3.ts.pay.center.api.util.AccountUtils;
import com.t3.ts.pay.center.api.util.CommonUtils;
import com.t3.ts.pay.center.api.util.PayUtils;
import com.t3.ts.pay.center.api.util.PayWayConvert;
import com.t3.ts.pay.center.api.util.SecUtils;
import com.t3.ts.pay.common.constant.sign.AccountSignStatusEnum;
import com.t3.ts.pay.common.http.CityCode;
import com.t3.ts.pay.remote.constants.EnumIntegralDeduct;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.constants.EnumPayOrderSubAccount;
import com.t3.ts.pay.remote.constants.EnumPayOrderType;
import com.t3.ts.pay.remote.constants.ExpandBizLineType;
import com.t3.ts.pay.remote.constants.PayForOtherTypeEnum;
import com.t3.ts.pay.remote.constants.PaywayEnum;
import com.t3.ts.pay.remote.dto.AccountPrivilegeDto;
import com.t3.ts.pay.remote.dto.AliPayDto;
import com.t3.ts.pay.remote.dto.PayConfigDto;
import com.t3.ts.pay.remote.dto.PayOrderDto;
import com.t3.ts.pay.remote.dto.PayStatusDto;
import com.t3.ts.pay.remote.dto.UnifiedDto;
import com.t3.ts.pay.remote.dto.marketing.IntegralDeductDto;
import com.t3.ts.pay.remote.dto.marketing.LadderDiscountDto;
import com.t3.ts.pay.remote.service.AliPayService;
import com.t3.ts.pay.remote.service.UnifiedPaymentQueryFacade;
import com.t3.ts.pay.remote.service.UnifiedService;
import com.t3.ts.pay.remote.service.channelRouting.PayChannelRoutingManageService;
import com.t3.ts.result.Response;
import com.t3.ts.route.plan.status.RouteStatus;
import com.t3.ts.settlement.centre.service.SrOrderService;
import com.t3.ts.travel.manager.resource.dto.res.CarDto;
import com.t3.ts.travel.manager.resource.dto.res.DriverResDto;
import com.t3.ts.travel.manager.resource.service.CarService;
import com.t3.ts.travel.manager.resource.service.DriverService;
import com.t3.ts.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;

import static com.t3.ts.pay.center.api.constants.CommonNumConst.FREE_CANCEL_TITLE;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_0;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_1;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_100;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_16;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_2;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_3;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_35;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_4;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_5;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_6;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_60;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_7;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_8;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.NUM_NEGATIVE_1;
import static com.t3.ts.pay.center.api.constants.CommonNumConst.STR_203;
import static com.t3.ts.pay.center.api.constants.NumConstants.COUPON_SHOW;
import static com.t3.ts.pay.center.api.constants.NumConstants.PRIVILEGE_SHOW;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * @Author: YJ
 * @Date: 2019/4/25 10:24
 * @Version: v2
 * @Description: 乘客支付充值逻辑处理
 */
@Slf4j
@Component
public class PaymentBusiness {

    private static final String MY_NJ_SDK_PREFIX = "znmh://pay?";
    @Resource
    private RouteBusiness routeBusiness;
    @DubboReference
    private DriverService driverService;

    @DubboReference
    private CarService carService;
    @DubboReference
    private UnifiedService unifiedService;
    @DubboReference
    private AccountInfoService accountInfoService;
    @Resource
    private T3CacheFactory factory;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private AccountSignService accountSignService;
    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQuery;
    @Autowired
    private CmbAggHelper cmbAggHelper;
    @Autowired
    private RoutePayService routePayService;
    @DubboReference
    private PayChannelTakeOverService payChannelTakeOverService;
    @Autowired
    private OperationRest operationRest;
    @Autowired
    private CommonPaymentBusiness commonPaymentBusiness;
    @Autowired
    private MarketingRest marketingRest;
    @Autowired
    private SecUtils secUtils;

    @DubboReference
    private UnifiedPaymentQueryFacade unifiedPaymentQueryFacade;
    @Autowired
    private RouteDetailClient routeDetailClient;
    @DubboReference
    private AliPayService aliPayService;
    @Autowired
    private PassengerFeignClient passengerFeignClient;
    @DubboReference
    private PayChannelRoutingManageService payChannelRoutingManageService;
    @DubboReference
    private SrOrderService srOrderService;


    /**
     * 支付情况查询
     *
     * @param routePlanUuid 查询请求
     * @return 支付状态
     * {
     * "payStatus":"支付状态，1：初始化，2：待（支付）结算，3：（支付）结算中，4：（支付）结算成功"
     * }
     */
    public Integer payStatusQuery(String routePlanUuid) {
        Response<PayStatusDto> response = unifiedPaymentQuery.payStatusQuery(routePlanUuid);
        if (null != response && response.isSuccess()) {
            PayStatusDto payStatusDto = response.getData();
            if (null != payStatusDto) {
                return payStatusDto.getPayStatus();
            }
        }
        return null;
    }

    /**
     * 路线付款
     * 行程支付
     *
     * @param routePaymentReq 路线付款申请
     * @param passengerUuid   乘客uuid
     * @param passengerMobile 乘客的移动
     * @return {@link Response<RechargePayVo>}
     */
    public Response<RechargePayVo> routePay(RoutePaymentReq routePaymentReq, String passengerUuid,
                                            String passengerMobile) {
        log.info("收银台支付接口: request:{},passengerUuid:{}", JSONUtil.toJsonStr(routePaymentReq), passengerUuid);
        RoutePayDTO routePayDTO = new RoutePayDTO();
        routePayDTO.setTerminal(PayUtils.getTerminal(routePaymentReq.getGrayVersion(), routePaymentReq.getUserChannel()));
        if (routePaymentReq.getPayTypeList().contains(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode())) {
            // 微信小程序支付，获取openid
            Response<String> wmpResponse = cmbAggHelper.getWMPOpenId(routePaymentReq.getCode());
            if (BooleanUtil.isFalse(wmpResponse.isSuccess())) {
                return Response.createError(ResultErrorEnum.WX_MINI_USER_ERROR);
            }
            routePayDTO.setOpenId(wmpResponse.getData());
        }
        if (routePaymentReq.getPayTypeList().contains(EnumPayOrderChannel.ALIPAY_MINI.getCode())) {
            routePayDTO.setAliAuthCode(routePaymentReq.getCode());
        }
        if (routePaymentReq.getPayTypeList().contains(EnumPayOrderChannel.UNION_JS_PAY.getCode())) {
            routePayDTO.setAuthCode(routePaymentReq.getCode());
            routePayDTO.setSubSource(routePaymentReq.getSubSource());
        }
        routePayDTO.setOrderUuid(routePaymentReq.getOrderUuid());
        routePayDTO.setPayTypeList(routePaymentReq.getPayTypeList());
        routePayDTO.setCouponUuid(routePaymentReq.getCouponUuid());
        routePayDTO.setUnWarriedArrive(routePaymentReq.getUnWarriedArrive());
        routePayDTO.setPrivilegeUuid(routePaymentReq.getPrivilegeUuid());
        routePayDTO.setPassengerUuid(passengerUuid);
        routePayDTO.setPassengerMobile(passengerMobile);
        routePayDTO.setBizType(routePaymentReq.getPayOrderType());
        routePayDTO.setSkuCode(routePaymentReq.getSkuCode());
        routePayDTO.setSkuUuid(routePaymentReq.getSkuUuid());
        routePayDTO.setUseCounponActivity(routePaymentReq.getUseCounponActivity());
        routePayDTO.setPayForOtherType(routePaymentReq.getPayForOtherType());
        routePayDTO.setOrderTime(routePaymentReq.getOrderTime());
        routePayDTO.setSellType(routePaymentReq.getSellType());
        // 支付并签约标识
        routePayDTO.setPayAndSign(routePaymentReq.getPayAndSign());
        routePayDTO.setQuitUrl(routePaymentReq.getQuitUrl());
        //识别是否司机推荐乘客-扫赚钱码支付
        Integer payOrderType = routePaymentReq.getPayOrderType();
        if (null != payOrderType && EnumPayOrderType.DRIVER_RECOMMEND_PASSENGER_PAY_ROUTE.getCode() == payOrderType) {
            routePaymentReq.setTaxiScanPay(Boolean.TRUE);
        }
        routePayDTO.setTaxiScanPay(routePaymentReq.getTaxiScanPay());
        //用户是否可用免密
        routePayDTO.setNoSecret(null == routePaymentReq.getNoSecret() ? false : routePaymentReq.getNoSecret());
        //识别用户可用的聚合支付渠道
        if (!payChannelRoutingManageService.channelRouteDeleteOldGraySwitch(passengerUuid).getData()) {
            List<Integer> aggPayType = cmbAggHelper.getCmbAggPayTypeList(routePaymentReq.getGrayBuild(),
                    routePaymentReq.getGrayVersion(), routePaymentReq.getPayAndSign(), routePaymentReq.getSource(),
                    routePaymentReq.getPayOrderType() == null ? 0 : routePaymentReq.getPayOrderType(),
                    routePaymentReq.getPayTypeList(), passengerUuid);
            routePayDTO.setAggPayTypeList(aggPayType);
        }
        routePayDTO.setSource(routePaymentReq.getSource());
        routePayDTO.setGrayBuild(routePaymentReq.getGrayBuild());
        routePayDTO.setGrayVersion(routePaymentReq.getGrayVersion());
        routePayDTO.setUserIp(routePaymentReq.getUserIp());
        Response<RechargePayBo> routePayResponse = routePayService.launchRoutePay(routePayDTO);
        if (!routePayResponse.isSuccess()) {
            if (StringUtils.isNotBlank(routePayResponse.getMsg())) {
                ResultErrorEnum resultErrorEnum = ResultErrorEnum.getResultErrorEnum(routePayResponse.getMsg());
                if (null != resultErrorEnum) {
                    return Response.createError(resultErrorEnum);
                }
            }
            return Response.createError(ResultErrorEnum.PAY_ERROR);
        }
        RechargePayBo rechargePayBo = routePayResponse.getData();
        packageAggPayBo(rechargePayBo, routePaymentReq);
        // 适配我的南京
        packageMyNanJinPay(routePaymentReq, rechargePayBo, passengerUuid);

        //记录用户签约免密滑块点击情况
        saveUserSignExtend(routePaymentReq, passengerUuid);

        return Response.createSuccess("获取支付信息成功", rechargePayBo);
    }

    /**
     * 记录用户签约免密滑块点击情况
     *
     * @param routePaymentReq routePaymentReq
     * @param userId          userId
     */
    private void saveUserSignExtend(RoutePaymentReq routePaymentReq, String userId) {
        if (null != routePaymentReq.getSelWxSign()) {
            SignButtonVo vo = new SignButtonVo();
            vo.setSelSign(routePaymentReq.getSelWxSign());
            vo.setOperateTime(new Date());

            JSONObject json = new JSONObject();
            json.put("identityType", "1");
            json.put("userId", userId);
            json.put("type", PassengerConstants.WXALREADYSIGN);
            json.put("info", vo);
            passengerFeignClient.saveUserExtend(json);
        }
    }

    /**
     * 适配我的南京支付
     *
     * @param routePaymentReq routePaymentReq
     * @param rechargePayBo   rechargePayBo
     * @param userId          userId
     */
    private void packageMyNanJinPay(RoutePaymentReq routePaymentReq, RechargePayBo rechargePayBo, String userId) {
        // 只有渠道是 101- 我的南京  以及 支付方式是2的 才回转换
        if (CommonNumConst.NUM_101 == routePaymentReq.getSource()
                && rechargePayBo.getPayType() != null
                && rechargePayBo.getPayType().equals(CommonNumConst.NUM_2)
                && routePaymentReq.getPayTypeList().contains(CommonNumConst.NUM_2)) {
            try {
                log.info("我的南京-支付结果分装:param:{}", JSONUtil.toJsonStr(rechargePayBo));
                // 查询待支付金额
                Response<PayOrderDto> payOrder = unifiedPaymentQueryFacade.getPayOrderByOrderNo(userId, rechargePayBo.getOrderNo());
                Integer payAmount = NUM_0;
                if (payOrder.getSuccess() && null != payOrder.getData()) {
                    payAmount = payOrder.getData().getSettleAmount();
                }
                // 组装我的南京需要的参数
                cn.hutool.json.JSONObject wxSdk = JSONUtil.parseObj(rechargePayBo.getSdk());
                String sdk = MY_NJ_SDK_PREFIX + "type=wxpay&sign=" + wxSdk.getStr("sign") + "&params="
                        + secUtils.getEncryptParams(wxSdk, rechargePayBo, payAmount);
                rechargePayBo.setSdk(sdk);
            } catch (Exception e) {
                log.info("PaymentBusiness.packageMyNanJinPay fail!");
            }
        }
    }

    /**
     * 支付宝聚合支付封装
     *
     * @param rechargePayBo   返回体
     * @param routePaymentReq routePaymentReq
     */
    private void packageAggPayBo(RechargePayBo rechargePayBo, RoutePaymentReq routePaymentReq) {
        // 支付宝聚合支付需要还原支付渠道
        if (null != rechargePayBo.getPayType()
                && EnumPayOrderChannel.CMB_AGGREGATE_ALI_APP_PAY.getCode() == rechargePayBo.getPayType()) {
            rechargePayBo.setPayType(EnumPayOrderChannel.ALIPAY.getCode());
        }
        // 只有 203 标识灰度版本
        if (EnumPayOrderChannel.CMB_AGGREGATE_WX_UNIFIED_PAY.getCode() == rechargePayBo.getPayType()
                && STR_203.equals(rechargePayBo.getCode())) {
            //微信聚合支付跳转微信小程序有额外的落地页 需要在sdk上额外拼装参数
            try {
                log.info("packageAggPayBo queryRouteInfo");
                RouteInfoDto routeInfo = routeDetailClient.getRouteInfoWeak(routePaymentReq.getOrderUuid());
                String originAddress = routeInfo == null ? "" : routeInfo.getOriAddress();
                String destAddress = routeInfo == null ? "" : routeInfo.getDestAddress();
                String sdk = rechargePayBo.getSdk() + "%26origin%3D" + URLEncoder.encode(originAddress, "UTF-8")
                        + "%26destination%3D" + URLEncoder.encode(destAddress, "UTF-8")
                        + "%26scene%3D" + "ROUTE_PAY";
                rechargePayBo.setSdk(sdk);
                rechargePayBo.setCmbMiniAppId(switchConfig.getCmbAggT3MiniAppId());

            } catch (Exception e) {
                //查询出行异常则不作任何处理
                log.error("查询出行行程信息异常.cause:{}", ExceptionUtils.getFullStackTrace(e));
            }

        }
    }


    /**
     * 获取支付台信息v3
     * 获取收银台数据
     *
     * @param payTradeReq 支付交易请求
     * @param passengerId 乘客id
     * @return {@link Response}
     */
    public Response<PayDeskVoV3> getPayDeskInfoV3(PayTradeReq payTradeReq, String passengerId) {
        PayDeskVoV3 payDeskVoV3 = new PayDeskVoV3();
        payDeskVoV3.setPassengerId(passengerId);
        String orderUuid = payTradeReq.getOrderUuid();
        payDeskVoV3.setOrderUuid(payTradeReq.getOrderUuid());
        log.info("payTradeReq={}", JSON.toJSONString(payTradeReq));
        payDeskVoV3.setIsPayInstant(switchConfig.getIsPayInstant() && getSecretFreeStatus(passengerId));
        // 设置当前行程的支付状态
        payDeskVoV3.setPayStatus(payStatusQuery(orderUuid));
        // 出租车扫码支付
        Boolean taxiScanPay = payTradeReq.getTaxiScanPay();
        Integer payOrderType = payTradeReq.getPayOrderType();
        if (null != payOrderType && EnumPayOrderType.DRIVER_RECOMMEND_PASSENGER_PAY_ROUTE.getCode() == payOrderType) {
            taxiScanPay = true;
        }
        RouteInfoDto routeInfoDto = new RouteInfoDto();
        if (!taxiScanPay) {
            //获取出行行程信息
            routeInfoDto = getRouteDetailDto(orderUuid);
            if (!checkRouteStatus(routeInfoDto)) {
                return Response.createError(ResultErrorEnum.ROUTE_PAYED);
            }
        }
        // 步骤2 车辆信息相关
        setCarInfo(payDeskVoV3, routeInfoDto);
        //拼车信息
        setCarpoolInfo(payDeskVoV3, routeInfoDto);
        // 城市code
        String orgCode = getCityCode(routeInfoDto, payTradeReq);
        //用户选择的收银台
        List<Integer> payTypeList = payTradeReq.getPayTypeList();
        //收银台展示时使用该list
        List<Integer> deskDisplaypayTypeList = new ArrayList<>();
        deskDisplaypayTypeList.addAll(payTypeList);
        log.info("deskDisplaypayTypeList={} payTradeReq.getPayTypeList={}", deskDisplaypayTypeList, payTypeList);
        int balaceStrategy = 0;
        int tMoneyStrategy = 0;
        if (CollectionUtils.isEmpty(payTypeList)) {
            payTypeList = Lists.newArrayList();
        } else if (payTypeList.size() == 1 && payTypeList.contains(NUM_NEGATIVE_1)) {
            balaceStrategy = 1;
            tMoneyStrategy = 1;
            dealRecommendPayWay(payTypeList, deskDisplaypayTypeList, payTradeReq);
        } else {
            balaceStrategy = payTypeList.contains(EnumPayOrderChannel.BALANCE.getCode()) ? NUM_2 : 0;
            tMoneyStrategy = payTypeList.contains(EnumPayOrderChannel.INTEGRAL.getCode()) ? NUM_2 : 0;
        }
        // 支付方式去重
        payTypeList = getUniquePayType(payTypeList, taxiScanPay);
        // 验证微信是否可用，不可用就过滤掉
        dealWxPayType(passengerId, orderUuid, payTypeList, deskDisplaypayTypeList);
        payTypeList = payTypeList.stream().collect(collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparingInt(e -> e))), ArrayList::new));
        List<String> payWay = getPayWay(payTypeList);
        //确定传入的支付方式
        payDeskVoV3.setPayTypeList(payTypeList);
        // 三方支付渠道
        Integer payChannel = payTypeList.stream().filter(
                e -> !Integer.valueOf(EnumPayOrderChannel.BALANCE.getCode()).equals(e) && !Integer.valueOf(
                        EnumPayOrderChannel.INTEGRAL.getCode()).equals(e)).findFirst().orElse(null);
        //调用支付收银台入参
        PayDeskReq payDeskReq = getPayDeskReq(payTradeReq, routeInfoDto, orgCode,
                payWay, payChannel);
        payDeskVoV3.setUseCounponActivity(payTradeReq.getUseCounponActivity());
        payDeskReq.setUserId(passengerId);
        if (taxiScanPay) {
            payDeskReq.setType("2");
            payDeskReq.setTaxiScanPay(taxiScanPay);
        }
        log.info("t3pay.pay.paydesk.deskPro param:{}", payDeskReq);
        // 获取支付收银台
        Response payDeskResp = getPayDeskResponse(payDeskReq);
        if (null == payDeskResp || !payDeskResp.isSuccess() || payDeskResp.getData() == null) {
            return sendResponse(payDeskResp);
        }
        // 权益卡和优惠券的金额转化为元，并返回收银台对象
        PayDeskRes payDeskRes = decutionAmountToDoubleString(payDeskResp);
        log.info("收银台数据payDeskRes:费用明细:{},已支付费用:{},余额可支付明细:{},优惠券明细：{},权益卡明细:{}",
                JSONUtil.toJsonStr(payDeskRes.getFareDetail()), JSONUtil.toJsonStr(payDeskRes.getPayed()),
                JSONUtil.toJsonStr(payDeskRes.getBalanceCanPayDetail()),
                JSONUtil.toJsonStr(payDeskRes.getCouponDetail()), JSONUtil.toJsonStr(payDeskRes.getPrivilegeDetail()));
        // 【支付项列表】优惠券抵扣信息
        CouponVo couponVo = getPayDeskCouponVo(passengerId, orderUuid, orgCode, payChannel, payDeskRes, payDeskReq.getUserChannel());
        payDeskVoV3.setCouponVo(couponVo);
        //购买券包的金额
        BigDecimal activityDecutionAmount = setCouponActivityParam(payDeskVoV3, payDeskRes.getCouponDetail());
        // 省心打相关参数
        setUnWarriedParam(payDeskVoV3, payDeskRes);
        //设置亲友代付标识
        setPayForOtherType(payDeskVoV3, payDeskRes);
        //老年用车无亲友关系，则券套餐不展示
        activityDecutionAmount = getActivityDecutionAmount(payTradeReq, payDeskVoV3, activityDecutionAmount);
        // 订单人是否为当前登录人 为了兼容小程序跨账号
        Boolean isCurrentPassenger = checkIsCurrentPassenger(passengerId, routeInfoDto);
        // 费用信息参数（过渡用）
        FareParam fareParam = getFareParam(payTradeReq, payDeskRes, payDeskVoV3);
        fareParam.setCouponActivityFee(activityDecutionAmount);
        // 【支付项明细列表】
        List<PayItem> payItemList
                = getPayItemList(payDeskVoV3, payDeskRes, payTradeReq, fareParam, isCurrentPassenger, orgCode);
        payDeskVoV3.setPayItemList(payItemList);
        dealFareParam(payDeskVoV3, fareParam);
        //附加费 只能三方支付
        BigDecimal balanceCanPay = fareParam.getActualFare().subtract(fareParam.getAdditionalFee())
                .subtract(fareParam.getLostReturnFee().subtract(fareParam.getCompensationFare()));
        //本金和赠金支持跨城费和节日费
        BigDecimal rechargeInfoCanUseMoney = fareParam.getRechargeCanPay().add(fareParam.getRechargeGiftCanPay());
        //跨城费、远程调度费和节日费由充值本金和充值赠金扣除后 剩余金额
        BigDecimal remainFestAndCrossMoney = fareParam.getFestivalFee().add(fareParam.getCrossCityFee())
                .subtract(rechargeInfoCanUseMoney);
        if (remainFestAndCrossMoney.compareTo(BigDecimal.ZERO) > 0) {
            //说明充值本金和充值赠金扣除后 还有剩余金额 只能三方付
            //当期剩余金额-三方支付的金额  剩下就是余额能够支付的金额
            balanceCanPay = balanceCanPay.subtract(remainFestAndCrossMoney);
        }
        // 余额最终可扣减费用
        BigDecimal availableBalanceFinal = BigDecimal.ZERO;
        //附加费不计算，只要余额大于0，就不能显示
        BigDecimal availableBalanceMin = fareParam.getAvailableBalance().compareTo(balanceCanPay) > 0
                ? balanceCanPay : fareParam.getAvailableBalance();
        if (checkPassengerChoose(payTradeReq, balaceStrategy, balanceCanPay)) {
            availableBalanceFinal = availableBalanceMin;
        }
        // 【支付项目】钱包余额展示文案
        dealBalance(payDeskVoV3, fareParam);
        checkBalanceNoticeMessage(fareParam, payDeskVoV3, routeInfoDto);
        Boolean existAdditionalFee = hasExistAdditionalFee(fareParam);
        deskDisplaypayTypeList = getDeskDisplayPayTypeList(payTradeReq, payDeskVoV3, taxiScanPay,
                deskDisplaypayTypeList, fareParam, existAdditionalFee);
        currentPassengerFare(payDeskVoV3, deskDisplaypayTypeList, isCurrentPassenger, fareParam,
                availableBalanceFinal);
        // 设置免密支付提醒
        payDeskVoV3.setAutoPayTip(getAutoPayTip(orderUuid, passengerId));
        return Response.createSuccess("查询成功", payDeskVoV3);
    }

    public String getAutoPayTip(String routePlanUuid, String userId) {
        Response<String> response = srOrderService.queryAutoPayTipByRoutePlanId(routePlanUuid, userId);
        if (response.isSuccess() && StringUtils.isNotBlank(response.getData())) {
            return response.getData();
        }
        return null;
    }

    /**
     * sendResponse
     *
     * @param payDeskResp 错误信息
     * @return Response
     */
    public Response sendResponse(Response payDeskResp) {
        if (null != payDeskResp && StringUtils.isNotBlank(payDeskResp.getMsg())) {
            ResultErrorEnum resultErrorEnum = ResultErrorEnum.getResultErrorEnum(payDeskResp.getMsg());
            if (null != resultErrorEnum) {
                return Response.createError(resultErrorEnum);
            }
        }
        return Response.createError(ResultErrorEnum.PAY_DESK_INFO_ERROR);
    }

    /**
     * 校验行程状态
     *
     * @param routeInfoDto 行程详情信息
     * @return boolean
     */
    private boolean checkRouteStatus(RouteInfoDto routeInfoDto) {
        if (routeInfoDto == null) {
            return true;
        }
        String status = routeInfoDto.getStatus() == null ? "" : routeInfoDto.getStatus().toString();
        if (RouteStatus.ROUTE_7.getStatus().equals(status)
                || RouteStatus.ROUTE_8.getStatus().equals(status)
                || RouteStatus.ROUTE_9.getStatus().equals(status)) {
            return false;
        }
        return true;
    }

    /**
     * 判断是否当前登录账号
     *
     * @param passengerId  passengerId
     * @param routeInfoDto routePlanDto
     * @return Boolean
     */
    private Boolean checkIsCurrentPassenger(String passengerId, RouteInfoDto routeInfoDto) {
        Boolean isCurrentPassenger = Boolean.TRUE;
        if (null != routeInfoDto) {
            isCurrentPassenger = getCurrtenPassenger(passengerId, routeInfoDto.getPassengerUuid());
        }
        return isCurrentPassenger;
    }

    /**
     * checkPassengerChoose
     *
     * @param payTradeReq    payTradeReq
     * @param balaceStrategy balaceStrategy
     * @param balanceCanPay  balanceCanPay
     * @return boolean
     */
    private boolean checkPassengerChoose(PayTradeReq payTradeReq, int balaceStrategy, BigDecimal balanceCanPay) {
        //bug新增 首次拉起收银台时没带余额支付方式过来,且余额充足时  availableBalanceFinal赋值逻辑走不到
        boolean flag = (Boolean.FALSE.equals(payTradeReq.getPassengerChooseFlag()) && balaceStrategy == 0
                && balanceCanPay.compareTo(BigDecimal.ZERO) > 0)
                || (balaceStrategy != 0 && balanceCanPay.compareTo(BigDecimal.ZERO) > 0);
        return flag;
    }

    /**
     * currentPassengerFare
     *
     * @param payDeskVoV3            payDeskVoV3
     * @param deskDisplaypayTypeList deskDisplaypayTypeList
     * @param isCurrentPassenger     isCurrentPassenger
     * @param fareParam              fareParam
     * @param availableBalanceFinal  availableBalanceFinal
     */
    private void currentPassengerFare(PayDeskVoV3 payDeskVoV3, List<Integer> deskDisplaypayTypeList,
                                      Boolean isCurrentPassenger, FareParam fareParam,
                                      BigDecimal availableBalanceFinal) {
        if (isCurrentPassenger) {
            // 加上购买券包的钱
            fareParam.setActualFare(fareParam.getActualFare().add(fareParam.getCouponActivityFee()));
            // 【支付项目】最终支付费用
            payDeskVoV3.setActualFare(fareParam.getActualFare());
            payDeskVoV3.setFinalFare(fareParam.getActualFare().subtract(availableBalanceFinal));
            //  处理三方支付校验
            setThirdPayTypeToast(payDeskVoV3, fareParam, deskDisplaypayTypeList);
        } else {
            payDeskVoV3.setFinalFare(fareParam.getActualFare());
        }
        log.info("getPayDeskInfoV3 response:{}", payDeskVoV3);
    }

    /**
     * getDeskDisplayPayTypeList
     *
     * @param payTradeReq            payTradeReq
     * @param payDeskVoV3            payDeskVoV3
     * @param taxiScanPay            taxiScanPay
     * @param deskDisplaypayTypeList deskDisplaypayTypeList
     * @param fareParam              fareParam
     * @param existAdditionalFee     existAdditionalFee
     * @return List<Integer>
     */
    private List<Integer> getDeskDisplayPayTypeList(PayTradeReq payTradeReq, PayDeskVoV3 payDeskVoV3,
                                                    Boolean taxiScanPay, List<Integer> deskDisplaypayTypeList,
                                                    FareParam fareParam, Boolean existAdditionalFee) {
        //2、账户中是否余额
        Boolean hasBalance = fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0 ? Boolean.TRUE : Boolean.FALSE;
        //3、账户余额是否满足待支付金额
        Boolean balanceEnough = fareParam.getBalanceTotal().compareTo(fareParam.getActualFare()) >= 0
                ? Boolean.TRUE : Boolean.FALSE;
        log.info("deskDisplaypayTypeList={} existAdditionalFee={} hasBalance={} balanceEnough={} "
                        + "getPassengerChooseFlag={}", deskDisplaypayTypeList, existAdditionalFee, hasBalance,
                balanceEnough, payTradeReq.getPassengerChooseFlag());
        // 当有选择券包时，当做有附加费处理，券包必须有三方支付
        if (payDeskVoV3.getUseCounponActivity() == NUM_1) {
            existAdditionalFee = Boolean.TRUE;
        }
        if (taxiScanPay) {
            // 出租车扫码支付不展示余额支付
            hasBalance = Boolean.FALSE;
        }
        // 待展示的支付方式处理
        deskDisplaypayTypeList = payTypeListProc(deskDisplaypayTypeList, existAdditionalFee, hasBalance, balanceEnough,
                payTradeReq.getPassengerChooseFlag(), payTradeReq.getApplicationType());
        //如果 可支付金额 有优惠券没有打车金 和礼品卡，支付方式就不能有余额支付
        deskDisplaypayTypeList = getBalancePayTypeList(payDeskVoV3, deskDisplaypayTypeList, fareParam);
        log.info("deskDisplaypayTypeList={}", JSONObject.toJSONString(deskDisplaypayTypeList));
        // 挪个位置，解决出租车扫码付有余额不能支付问题
        if (taxiScanPay) {
            // 出租车扫码支付不展示余额支付
            payDeskVoV3.setBalanceFlag(Boolean.FALSE);
            payDeskVoV3.setBalanceMsg("出租车扫码支付不支持余额支付");
        }
        deskDisplaypayTypeList = getDeskDisplayTypeList(payDeskVoV3, deskDisplaypayTypeList, fareParam);
        payDeskVoV3.setPayTypeList(deskDisplaypayTypeList);
        // 【支付项目】收银台类型
        if (fareParam.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV3.setPayDeskType(1);
        }
        return deskDisplaypayTypeList;
    }

    /**
     * checkIntegralPayType
     *
     * @param payTradeReq    payTradeReq
     * @param payDeskVoV3    payDeskVoV3
     * @param tMoneyStrategy tMoneyStrategy
     * @return boolean
     */
    private boolean checkIntegralPayType(PayTradeReq payTradeReq, PayDeskVoV3 payDeskVoV3, int tMoneyStrategy) {
        boolean flag = getBigDecimalValue(payDeskVoV3.getAvailableIntegral()).compareTo(BigDecimal.ZERO) > 0
                && Objects.nonNull(payDeskVoV3.getIntegralDeductFlag()) && payDeskVoV3.getIntegralDeductFlag()
                && ((tMoneyStrategy != 0 && Boolean.TRUE.equals(payTradeReq.getPassengerChooseFlag()))
                || (Boolean.FALSE.equals(payTradeReq.getPassengerChooseFlag()) && payDeskVoV3.getIntegralChecked()));
        return flag;
    }

    /**
     * getDeskDisplayTypeList
     *
     * @param payDeskVoV3            payDeskVoV3
     * @param deskDisplaypayTypeList deskDisplaypayTypeList
     * @param fareParam              fareParam
     * @return List<Integer>
     */
    private List<Integer> getDeskDisplayTypeList(PayDeskVoV3 payDeskVoV3, List<Integer> deskDisplaypayTypeList,
                                                 FareParam fareParam) {
        //积分使用过时 过滤掉
        if (Objects.nonNull(fareParam.getIntegralPayed())
                && fareParam.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            deskDisplaypayTypeList = deskDisplaypayTypeList.stream()
                    .filter(e -> e != EnumPayOrderChannel.INTEGRAL.getCode()).collect(Collectors.toList());
        }
        //如果返回是选择亲友付，则需要抛出三方支付方式
        if (Objects.nonNull(payDeskVoV3.getPayForOtherType())
                && payDeskVoV3.getPayForOtherType().intValue()
                == PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType()) {
            deskDisplaypayTypeList = filterThirdPaymentWay(deskDisplaypayTypeList);
        }
        return deskDisplaypayTypeList;
    }

    /**
     * 判断是否有附加费
     *
     * @param fareParam fareParam
     * @return Boolean
     */
    private Boolean hasExistAdditionalFee(FareParam fareParam) {
        //1、是否包含附加费
        Boolean existAdditionalFee = fareParam.getAdditionalFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE;
        //节日费 同 附加费 都需要三方支付
        existAdditionalFee = existAdditionalFee || (fareParam.getFestivalFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getCrossCityFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getLostReturnFee().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        existAdditionalFee = existAdditionalFee || (fareParam.getCompensationFare().compareTo(BigDecimal.ZERO) > 0
                ? Boolean.TRUE : Boolean.FALSE);
        return existAdditionalFee;
    }

    /**
     * 处理微信支付情况下的推荐支付方式
     *
     * @param payTypeList            payTypeList
     * @param deskDisplaypayTypeList deskDisplaypayTypeList
     * @param payTradeReq            payTradeReq
     */
    private void dealRecommendPayWay(List<Integer> payTypeList, List<Integer> deskDisplaypayTypeList,
                                     PayTradeReq payTradeReq) {
        Integer applicationType = payTradeReq.getApplicationType();
        //如果是微信小程序的收银台，推荐的支付方式只能是微信小程序支付
        if (Objects.nonNull(applicationType) && applicationType.equals(NUM_1)) {
            payTypeList.add(EnumPayOrderChannel.ALIPAY_MINI.getCode());
            deskDisplaypayTypeList.add(EnumPayOrderChannel.ALIPAY_MINI.getCode());
        } else if (Objects.nonNull(applicationType) && applicationType.equals(NUM_2)) {
            payTypeList.add(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());
            deskDisplaypayTypeList.add(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());
        } else {
            //推荐支付方式
            getRecommendPaymentWay(payTypeList, deskDisplaypayTypeList);
        }
    }

    /**
     * 处理返回dto信息
     *
     * @param payDeskVoV3 payDeskVoV3
     * @param fareParam   fareParam
     */
    private void dealFareParam(PayDeskVoV3 payDeskVoV3, FareParam fareParam) {
        // 【支付项目】待支付金额
        payDeskVoV3.setActualFare(fareParam.getActualFare());
        // 【支付项目】附加费
        payDeskVoV3.setAdditionalFee(fareParam.getAdditionalFee());
        payDeskVoV3.setFestivalFee(fareParam.getFestivalFee());
        payDeskVoV3.setCrossCityFee(fareParam.getCrossCityFee());
        payDeskVoV3.setDispatchFee(fareParam.getDispatchFee());
        payDeskVoV3.setLostReturnFee(fareParam.getLostReturnFee());
        payDeskVoV3.setCompensationFare(fareParam.getCompensationFare());
        // 【支付项目】钱包余额
        payDeskVoV3.setBalance(fareParam.getBalanceTotal());
        // 【支付项目】积分（T币）抵扣  若已使用积分抵扣费用，则APP端不展示积分抵扣选项
        payDeskVoV3.setIntegralDeductFlag(Boolean.FALSE);
    }

    /**
     * 券套餐金额处理
     *
     * @param payTradeReq            payTradeReq
     * @param payDeskVoV3            payDeskVoV3
     * @param activityDecutionAmount activityDecutionAmount
     * @return BigDecimal
     */
    private BigDecimal getActivityDecutionAmount(PayTradeReq payTradeReq, PayDeskVoV3 payDeskVoV3,
                                                 BigDecimal activityDecutionAmount) {
        if (Objects.nonNull(payDeskVoV3.getPayForOtherType())
                && PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType() != payDeskVoV3.getPayForOtherType()) {
            activityDecutionAmount = BigDecimal.ZERO;
            // 券套餐为空或者不是最优的情况下设置为0 不购买不展示
            payDeskVoV3.setCouponActivityDetail(null);
            payDeskVoV3.setUseCounponActivity(NUM_0);
        }
        if (checkCouponActivity(payTradeReq)) {
            payDeskVoV3.setUseCounponActivity(NUM_0);
        }
        return activityDecutionAmount;
    }

    /**
     * 支付方式转化
     *
     * @param payTypeList payTypeList
     * @return List<String>
     */
    private List<String> getPayWay(List<Integer> payTypeList) {
        PaywayEnum[] paywayEnum = PayWayConvert.getPayWayEnum(payTypeList);
        List<String> payWay = Lists.newArrayList();
        // 支付方式
        if (null != paywayEnum) {
            for (PaywayEnum anEnum : paywayEnum) {
                if (Objects.nonNull(anEnum)) {
                    payWay.add(anEnum.getCode());
                }
            }
        }
        return payWay;
    }

    /**
     * checkCouponActivity
     *
     * @param payTradeReq payTradeReq
     * @return boolean
     */
    private boolean checkCouponActivity(PayTradeReq payTradeReq) {
        return (null != payTradeReq.getUnWarriedArrive() && NUM_1 == payTradeReq.getUnWarriedArrive())
                || !(null != payTradeReq.getUseCouponType() && "2".equals(payTradeReq.getUseCouponType())
                && null != payTradeReq.getUseCounponActivity()
                && NUM_1 == payTradeReq.getUseCounponActivity());
    }

    /**
     * 权益卡和优惠券的金额转化为元，并返回收银台对象
     *
     * @param payDeskResp payDeskResp
     * @return {@link PayDeskRes}
     */
    private PayDeskRes decutionAmountToDoubleString(Response payDeskResp) {
        JSONObject payDeskRespData = JSON.parseObject((String) payDeskResp.getData());
        JSONObject couponDetailJson = payDeskRespData.getJSONObject("couponDetail");
        if (null != couponDetailJson
                && couponDetailJson.containsKey("decutionAmount")) {
            Object amount = couponDetailJson.get("decutionAmount");
            if (amount instanceof Integer) {
                couponDetailJson.put("decutionAmount", AccountUtils.toDoubleString((Integer) amount));
            }
        }

        JSONObject privilegeDetailJson = payDeskRespData.getJSONObject("privilegeDetail");
        if (null != privilegeDetailJson
                && privilegeDetailJson.containsKey("decutionAmount")) {
            Object amount = privilegeDetailJson.get("decutionAmount");
            if (amount instanceof Integer) {
                privilegeDetailJson.put("decutionAmount", AccountUtils.toDoubleString((Integer) amount));
            }
        }

        PayDeskRes payDeskRes = JSON.parseObject(payDeskRespData.toString(), PayDeskRes.class);
        return payDeskRes;
    }

    /**
     * 对资产方式显示的处理
     *
     * @param payDeskVoV3            支付桌子vo v3
     * @param deskDisplaypayTypeList 桌子displaypay类型列表
     * @param fareParam              费用参数
     * @return {@link List<Integer>}
     */
    private List<Integer> getBalancePayTypeList(PayDeskVoV3 payDeskVoV3, List<Integer> deskDisplaypayTypeList,
                                                FareParam fareParam) {
        //如果余额总金额大于0，且余额或优惠券都未支付   永远展示，
        boolean flag = fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0
                && fareParam.getCouponPay().compareTo(BigDecimal.ZERO) == 0
                && fareParam.getAssetPay().compareTo(BigDecimal.ZERO) == 0
                && fareParam.getUnWarriedArrivePay().compareTo(BigDecimal.ZERO) == 0;
        if (flag) {
            payDeskVoV3.setBalanceFlag(true);
        }
        flag = (fareParam.getRechargeGift().compareTo(BigDecimal.ZERO) > 0
                || fareParam.getRechargeCash().compareTo(BigDecimal.ZERO) > 0) && fareParam.getProductLine() == 1;
        if (flag) {
            payDeskVoV3.setBalanceMsg("出租车产品不支持使用现金余额");
        }
        flag = fareParam.getIsSupportPersonRecharge() && (fareParam.getRechargeGift().compareTo(BigDecimal.ZERO) > 0
                || fareParam.getRechargeCash().compareTo(BigDecimal.ZERO) > 0);
        if (flag) {
            payDeskVoV3.setBalanceMsg("资产扣款顺序：现金余额>打车金>礼品卡，现金余额用尽后再使用打车金，以此类推。");
        }
        payDeskVoV3.setRechargeFlag(NumConstants.NUM_0);
        return deskDisplaypayTypeList;
    }

    /**
     * 查询支付收银台
     *
     * @param payDeskReq 收银台请求
     * @return 查询支付收银台
     */
    private Response getPayDeskResponse(PayDeskReq payDeskReq) {
        try {
            UnifiedDto dto = new UnifiedDto();
            dto.setSceneType("t3pay.pay.paydesk.deskPro");
            dto.setExtendParam(JSON.toJSONString(payDeskReq));
            Response payDeskResp = unifiedService.handle(dto);
            log.info("获取支付收银台:{}", JSON.toJSONString(payDeskResp));
            return payDeskResp;
        } catch (Exception e) {
            log.error("getPayDeskResponse error", e);
        }
        return Response.createError();
    }

    /**
     * 处理余额
     *
     * @param payDeskVoV3 收银台
     * @param fareParam   费用
     */
    private void dealBalance(PayDeskVoV3 payDeskVoV3, FareParam fareParam) {
        Response<PayConfigDto> payConfigResponse = unifiedService.getPayConfig();
        if (payConfigResponse.isSuccess() && Objects.nonNull(payConfigResponse)
                && Objects.nonNull(fareParam.getProductLine()) && Objects.nonNull(fareParam.getExpandBizLine())) {
            PayConfigDto payConfigDto = payConfigResponse.getData();
            //根据标识判断是否支持个人充值业务
            boolean flag = payConfigDto.getPersonRechargeFlag()
                    && CommonUtils.isContentValue(fareParam.getExpandBizLine(), payConfigDto.getExpandBizLines())
                    && CommonUtils.isContentValue(fareParam.getProductLine(), payConfigDto.getProductLines());
            if (flag) {
                fareParam.setIsSupportPersonRecharge(Boolean.TRUE);
            }
            //说明支持充值业务 不支持叠加 只有充值本金和充值赠金 任一个有钱才显示此文案
            flag = fareParam.getIsSupportPersonRecharge()
                    && (fareParam.getRechargeGiftCanPay().compareTo(BigDecimal.ZERO) > 0
                    || fareParam.getRechargeCanPay().compareTo(BigDecimal.ZERO) > 0);
            if (flag) {
                bulidBanalanceMsg(payDeskVoV3, fareParam,
                        "资产扣款顺序：现金余额>打车金>礼品卡，现金余额用尽后再使用打车金，以此类推。");
                return;
            }
        }
        if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV3.setBalanceFlag(Boolean.TRUE);
        } else if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) <= 0) {
            payDeskVoV3.setBalanceMsg("余额不足");
        }
    }

    /**
     * 建造资产标签描述
     *
     * @param payDeskVoV3 支付桌子vo v3
     * @param fareParam   费用参数
     * @param balanceMsg  平衡味精
     */
    private void bulidBanalanceMsg(PayDeskVoV3 payDeskVoV3, FareParam fareParam, String balanceMsg) {
        if (fareParam.getBalanceTotal().compareTo(BigDecimal.ZERO) > 0
                && fareParam.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0) {
            payDeskVoV3.setBalanceFlag(Boolean.TRUE);
            payDeskVoV3.setBalanceMsg(balanceMsg);
        } else {
            payDeskVoV3.setBalanceMsg("余额不足");
        }
    }

    /**
     * 检查余额提醒通知消息
     *
     * @param param        参数
     * @param deskReturn   收银台返回参数
     * @param routeInfoDto dto路线计划
     */
    private void checkBalanceNoticeMessage(FareParam param, PayDeskVoV3 deskReturn,
                                           RouteInfoDto routeInfoDto) {
        //余额可支付金额
        BigDecimal availableBalance = param.getAvailableBalance();
        BigDecimal orderFareToPay = param.getOrderFareToPay().add(param.getDispatchFee());
        BigDecimal balanceTotal = param.getBalanceTotal();
        //待支付订单费是否大于0
        boolean orderMoreZero = param.getOrderFareToPay().compareTo(BigDecimal.ZERO) > 0;
        //总余额是否大于0
        boolean balanceTotalMoreZero = balanceTotal.compareTo(BigDecimal.ZERO) > 0;
        //礼品卡余额大于0
        boolean giftCardMoreZero = param.getGiftCard().compareTo(BigDecimal.ZERO) > 0;
        //可用余额大于0
        boolean availableBalanceMoreZero = param.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0;
        //可用打车金>0
        boolean giftMoneyCanPayMoreZero = param.getGiftMoneyCanPay().compareTo(BigDecimal.ZERO) > 0;
        //优惠券或者省心打可以支付金额
        BigDecimal couponOrUnWorryCanPay = BigDecimal.ZERO
                .add(deskReturn.getUnWarriedArriveChecked() ? param.getUnWorryCanPay() : BigDecimal.ZERO)
                .add(deskReturn.getCouponChecked() ? param.getCouponCanPay() : BigDecimal.ZERO);
        //账户内可用余额＜需支付金额 & 可用余额＜总余额 & 总余额＞0  可用打车金金额>0
        //优惠券、省心打(用户选择)、或两者一起能全额抵扣时，不展示这段提示
        if (availableBalance.compareTo(orderFareToPay) < 0 && availableBalance.compareTo(balanceTotal) < 0
                && balanceTotalMoreZero && orderMoreZero && couponOrUnWorryCanPay.compareTo(orderFareToPay) < 0
                && giftMoneyCanPayMoreZero) {
            deskReturn.setBalanceMsg("打车金单日限额50元，仅可支付行程费。");
        }
        //待支付订单费大于0 && 总余额大于零 && 余额可支付等于0  ##并且选择了余额  余额按钮置灰
        if (orderMoreZero && balanceTotalMoreZero && availableBalance.compareTo(BigDecimal.ZERO) < 1
                && deskReturn.getPayTypeList().contains(EnumPayOrderChannel.BALANCE.getCode())) {
            deskReturn.setBalanceFlag(Boolean.FALSE);
        }
        if (null != routeInfoDto) {
            boolean isTaxi = routeInfoDto.getTypeModule() != null && routeInfoDto.getTypeModule() == NUM_1;
            boolean isOnePrice = routeInfoDto.getFareMethod() != null && routeInfoDto.getFareMethod() == NUM_3;
            //出租车非一口价订单不能使用礼品卡
            if (isTaxi && !isOnePrice && giftCardMoreZero && orderMoreZero) {
                if (availableBalanceMoreZero) {
                    //如果可用打车金大于0，则给出提示
                    deskReturn.setBalanceMsg(String.format("出租车打表计价订单不支持使用礼品卡，本单可用%s元打车金",
                            param.getAvailableBalance().toPlainString()));
                } else {
                    deskReturn.setBalanceMsg("出租车打表计价订单不支持使用礼品卡");
                }
            }
        }
    }

    /**
     * 获取行程费用
     *
     * @param orderUuid 行程id
     * @return 行程费用
     */
    public PassengerRouteFareItemsVo getRouteFareItems(String orderUuid, RouteInfo routeInfo) {
        RouteFareItemReq req = new RouteFareItemReq();
        req.setRoutePlanUuid(orderUuid);
        if (null != routeInfo && null != routeInfo.getOrderReceiveTarget() && routeInfo.getOrderReceiveTarget() == NUM_2) {
            req.setConsumerType(NUM_2);
        }
        Response<PassengerRouteFareItemsVo> routeFareResp = routeBusiness.getPassengerRouteFareItems(req);
        if (!routeFareResp.isSuccess() || Objects.isNull(routeFareResp.getData())) {
            log.error("getRouteFareItems 获取行程费用失败");
            return null;
        }
        PassengerRouteFareItemsVo routeFareItemsVo = routeFareResp.getData();
        log.info("行程费用 PassengerRouteFareItemsVo：{}", routeFareItemsVo);
        return routeFareItemsVo;
    }

    /**
     * 设置券套餐相关的参数
     *
     * @param payDeskVoV3  收银台返回的数据
     * @param couponDetail 优惠券信息
     * @return 券套餐金额
     */
    private BigDecimal setCouponActivityParam(PayDeskVoV3 payDeskVoV3, CouponDetail couponDetail) {
        BigDecimal activityDecutionAmount = BigDecimal.ZERO;
        if (null != couponDetail) {
            CouponActivityDetail couponActivityDetail = couponDetail.getCouponActivityDetail();
            if (null != couponActivityDetail && null != couponActivityDetail.getBest()
                    && couponActivityDetail.getBest()) {
                payDeskVoV3.setCouponActivityDetail(couponActivityDetail);
                // 购买券包的金额
                boolean flag = null != payDeskVoV3.getUseCounponActivity()
                        && (NUM_1 == payDeskVoV3.getUseCounponActivity() || NUM_2 == payDeskVoV3
                        .getUseCounponActivity()) && couponActivityDetail != null;
                if (flag) {
                    activityDecutionAmount = getBigDecimalValue(couponActivityDetail.getActivityDecutionAmount());
                }
            } else {
                // 券套餐为空或者不是最优的情况下设置为0 不购买不展示
                payDeskVoV3.setCouponActivityDetail(null);
                payDeskVoV3.setUseCounponActivity(NUM_0);
            }
        }
        return activityDecutionAmount;
    }

    /**
     * 获取支付参数
     *
     * @param payTradeReq  收银台接口请求参数
     * @param routeInfoDto 行程信息
     * @param orgCode      城市code
     * @param payWay       支付方式
     * @param payChannel   支付渠道
     * @return 支付后台收银台请求参数
     */
    private PayDeskReq getPayDeskReq(PayTradeReq payTradeReq, RouteInfoDto routeInfoDto, String orgCode,
                                     List<String> payWay, Integer payChannel) {
        PayDeskReq payDeskReq = new PayDeskReq();
        payDeskReq.setCouponId(payTradeReq.getCouponUuid());
        payDeskReq.setOrderId(payTradeReq.getOrderUuid());
        // 收银台类型默认值 0
        dealPayDeskType(routeInfoDto, payDeskReq);
        // 设置收银台查询的用户为，该行程的乘客，注意不是当前登录的用户，因为存在跨账号支付问题
        if (null != routeInfoDto) {
            payDeskReq.setUserId(routeInfoDto.getPassengerUuid());
        }
        payDeskReq.setCityCode(orgCode);
        // 支付方式
        payDeskReq.setPayWay(payWay);
        // 支付渠道
        payDeskReq.setPayChannel(payChannel);
        // 是否使用优惠券
        payDeskReq.setUseCouponType(payTradeReq.getUseCouponType());
        // 是否使用省心打
        if (null == payTradeReq.getUnWarriedArrive()) {
            payTradeReq.setUnWarriedArrive(NUM_0);
        }
        payDeskReq.setUnWarriedArrive(payTradeReq.getUnWarriedArrive());
        // 设置权益卡参数
        payDeskReq.setPrivilegeUuid(payTradeReq.getPrivilegeUuid());
        payDeskReq.setUsePrivilegeType(payTradeReq.getUsePrivilegeType());
        if (null == payTradeReq.getUseCounponActivity()) {
            payTradeReq.setUseCounponActivity(NUM_0);
        }
        //如果不是老年用车就默认 为null
        if (null != routeInfoDto) {
            if (Objects.isNull(routeInfoDto.getServiceModel()) || routeInfoDto.getServiceModel() != NUM_2) {
                payTradeReq.setPayForOtherType(PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType());
            } else if (Objects.isNull(payTradeReq.getPayForOtherType())) {
                payTradeReq.setPayForOtherType(PayForOtherTypeEnum.NOT_OLD_MAN.getPayForOtherType());
            }
        }

        payDeskReq.setPayForOtherType(payTradeReq.getPayForOtherType());
        payDeskReq.setCouponActivityFlag(payTradeReq.getUseCounponActivity());
        // 渠道来源
        payDeskReq.setUserChannel(Integer.valueOf(PayUtils.getTerminal(payTradeReq.getGrayVersion())));
        return payDeskReq;
    }

    /**
     * 省心打相关参数
     *
     * @param payDeskVoV3 返回收银参数
     * @param payDeskRes  支付后台返回的收银台参数
     */
    private void setUnWarriedParam(PayDeskVoV3 payDeskVoV3, PayDeskRes payDeskRes) {
        UnWarriedArriveDetail unWarriedArriveDetail = payDeskRes.getUnWarriedArriveDetail();
        if (null != unWarriedArriveDetail) {
            payDeskVoV3.setUnWarriedArriveDetail(unWarriedArriveDetail);
            BigDecimal unWarriedArriveDisAmount = unWarriedArriveDetail.getUnWarriedArriveDisAmount();
            if (payDeskVoV3.getCouponActivityDetail() != null) {
                //券包中的券可抵扣金额
                BigDecimal decutionAmount = payDeskVoV3.getCouponActivityDetail().getDecutionAmount();
                //券套餐没有省心打优惠时 不展示
                if (getBigDecimalValue(decutionAmount).compareTo(getBigDecimalValue(unWarriedArriveDisAmount)) <= 0) {
                    // 券套餐为空或者不是最优的情况下设置为0 不购买不展示
                    payDeskVoV3.setCouponActivityDetail(null);
                    payDeskVoV3.setUseCounponActivity(NUM_0);
                }
            }
        }
    }

    /**
     * 设置亲友代付标识
     *
     * @param payDeskVoV3 支付桌子vo v3
     * @param payDeskRes  支付的办公桌res
     */
    private void setPayForOtherType(PayDeskVoV3 payDeskVoV3, PayDeskRes payDeskRes) {
        PayForOtherDetail payForOtherDetail = payDeskRes.getPayForOtherDetail();
        if (Objects.nonNull(payForOtherDetail)) {
            payDeskVoV3.setPayForOtherType(payForOtherDetail.getPayForOtherType());
            payDeskVoV3.setDisableForOtherDesc(payForOtherDetail.getDisableForOtherDesc());
        }
    }

    /**
     * 获取城市code
     *
     * @param routeInfoDto 参数
     * @param payTradeReq  参数
     * @return 城市code
     */
    private String getCityCode(RouteInfoDto routeInfoDto, PayTradeReq payTradeReq) {
        String orgCode = "";
        if (null != routeInfoDto) {
            orgCode = CityCode.convertCode(routeInfoDto.getAreaCode());
        } else {
            orgCode = CityCode.convertCode(payTradeReq.getAdCode());
        }
        return orgCode;
    }

    /**
     * 车辆信息相关
     *
     * @param payDeskVoV3  参数
     * @param routeInfoDto 参数
     */
    private void setCarInfo(PayDeskVoV3 payDeskVoV3, RouteInfoDto routeInfoDto) {
        if (null == routeInfoDto) {
            return;
        }
        // 查出行获取车辆信息
        String driverName = routeInfoDto.getDriverName();
        String plateNum = routeInfoDto.getPlateNum();

        // 司机姓名
        if (StringUtils.isNotBlank(driverName)) {
            payDeskVoV3.setDriverName(driverName.charAt(0) + PassengerConstants.SHIFU);
        } else {
            payDeskVoV3.setDriverName(getDriverName(routeInfoDto.getDriverUuid()));
        }

        // 车牌
        if (StringUtils.isNotBlank(plateNum)) {
            payDeskVoV3.setPlateNum(plateNum);
        } else {
            if (switchConfig.getQryCar()) {
                JSONObject jsonObject = operationRest.qryCarInfoByVin(routeInfoDto.getVin());
                if (null != jsonObject) {
                    payDeskVoV3.setPlateNum(jsonObject.getString("plateNum"));
                }
            } else {
                try {
                    Response<CarDto> carDtoResponse = carService.findByUuid(routeInfoDto.getCarUuid());
                    if (carDtoResponse.isSuccess() && Objects.nonNull(carDtoResponse.getData())) {
                        payDeskVoV3.setPlateNum(carDtoResponse.getData().getPlateNum());
                    }
                } catch (Exception e) {
                    log.error("查询车牌信息异常.{}", ExceptionUtils.getFullStackTrace(e));
                }
            }
        }
    }

    /**
     * 设置拼车信息
     *
     * @param payDeskVoV3  收银台返回信息
     * @param routeInfoDto 行程基础信息
     */
    private void setCarpoolInfo(PayDeskVoV3 payDeskVoV3, RouteInfoDto routeInfoDto) {
        if (null == routeInfoDto || null == routeInfoDto.getExpandBizLine()
                || !ExpandBizLineType.CARPOOL.getTypeMode().equals(routeInfoDto.getExpandBizLine())) {
            return;
        }
        log.info("查询拼车结果:carpoolSuccess:{}.seatNum:{}.routePlanUuid:{}.",
                routeInfoDto.getCarPoolSuccess(), routeInfoDto.getSeatNum(), routeInfoDto.getRouteId());
        String carPoolNotice = PayUtils.parseCarPoolNotice(routeInfoDto);
        payDeskVoV3.setAdditionalHint(carPoolNotice);
    }

    /**
     * 支付方式去重
     *
     * @param payTypeList 参数
     * @param taxiScanPay 是否是出租车扫码支付
     * @return 出参
     */
    private List<Integer> getUniquePayType(List<Integer> payTypeList, Boolean taxiScanPay) {
        //默认初始收银台且非扫码支付 勾选余额，其他场景不勾选收银台  最终决议不自动勾选余额
        payTypeList = payTypeList.stream().filter(e -> !Integer.valueOf(NUM_NEGATIVE_1).equals(e))
                .collect(Collectors.toList());
        return payTypeList;
    }

    /**
     * 处理微信支付方式，如果微信不可用，需要去掉该支付方式
     *
     * @param passengerId            乘客id
     * @param orderUuid              行程id
     * @param payTypeList            支付方式
     * @param deskDisplaypayTypeList 展示的支付方式
     * @return 成功或失败
     */
    private boolean dealWxPayType(String passengerId, String orderUuid, List<Integer> payTypeList,
                                  List<Integer> deskDisplaypayTypeList) {
        try {
            if (!CollectionUtils.isEmpty(payTypeList)
                    && payTypeList.contains(Integer.valueOf(EnumPayOrderChannel.WEIXIN.getCode()))
                    && deskDisplaypayTypeList.contains(Integer.valueOf(EnumPayOrderChannel.WEIXIN.getCode()))) {
                Response<Boolean> isWxOpenResp = unifiedPaymentQuery.payDeskWeChatPayAvailable(passengerId, orderUuid);
                if (!isWxOpenResp.isSuccess() || Objects.isNull(isWxOpenResp.getData())) {
                    return true;
                }
                if (!isWxOpenResp.getData()) {
                    removeWechatType(payTypeList);
                    log.info("行程id{}, 移除微信后的支付类型列表是{}", orderUuid, JSON.toJSONString(payTypeList));
                    removeWechatType(deskDisplaypayTypeList);
                    // 将 微信支付移除后 再推荐一种第三方支付方式
                    getRecommendPaymentWay(payTypeList, deskDisplaypayTypeList);
                }
            }
        } catch (Exception e) {
            log.error("dealWxPayType error", e);
        }
        return false;
    }

    /**
     * 处理收银台支付类型
     *
     * @param routeInfoDto 行程信息
     * @param payDeskReq   支付收银台请求信息
     */
    private void dealPayDeskType(RouteInfoDto routeInfoDto, PayDeskReq payDeskReq) {
        if (null == routeInfoDto) {
            return;
        }
        Integer typeModule = routeInfoDto.getTypeModule();
        // 行程类型（个人/企业）：1 个人;2 企业
        Integer typeEnt = routeInfoDto.getTypeEnt();
        if (null != typeEnt && NUM_2 == typeEnt) {
            //企业行程类型
            payDeskReq.setType("1");
        } else {
            boolean flag = typeModule != null && (typeModule == NUM_2 || typeModule == NUM_4
                    || typeModule == NUM_6 || typeModule == NUM_7 || typeModule == NUM_8);
            if (typeModule != null && typeModule == NUM_1) {
                //出租车
                payDeskReq.setType("2");
            } else if (flag) {
                //快享、专享、惠享
                payDeskReq.setType(NumConstants.STR_0);
            }
        }
    }

    /**
     * 判断当前账户是否是乘客本人登录
     *
     * @param passengerId          登录账号
     * @param planDtoPassengerUuid 行程乘客id
     * @return 当前账户是否是乘客本人登录
     */
    private Boolean getCurrtenPassenger(String passengerId, String planDtoPassengerUuid) {
        Boolean isCurrentPassenger = Boolean.FALSE;
        if (passengerId.equals(planDtoPassengerUuid)) {
            isCurrentPassenger = Boolean.TRUE;
        } else {
            log.info("current login passengerId：{}，route plan userId：{}", passengerId, planDtoPassengerUuid);
        }
        return isCurrentPassenger;
    }

    /**
     * 处理三方支付校验
     *
     * @param payDeskVoV3 收银台信息
     * @param fareParam   费用参数
     * @param payList     支付方式
     */
    private void setThirdPayTypeToast(PayDeskVoV3 payDeskVoV3, FareParam fareParam, List<Integer> payList) {
        if (CollectionUtils.isEmpty(payList)) {
            return;
        }
        List<Integer> thirdPayType = payList.stream()
                .filter(e -> e != EnumPayOrderChannel.INTEGRAL.getCode()
                        && e != EnumPayOrderChannel.UNWARRIED_ARRIVE.getCode()
                        && e != EnumPayOrderChannel.BALANCE.getCode()
                        && e != EnumPayOrderChannel.COUPON.getCode()).collect(Collectors.toList());
        boolean flag = payDeskVoV3.getFinalFare().compareTo(BigDecimal.ZERO) > 0
                && CollectionUtils.isEmpty(thirdPayType)
                && switchConfig.getThirdPayTypeToast()
                && (Objects.isNull(payDeskVoV3.getPayForOtherType())
                || payDeskVoV3.getPayForOtherType().intValue()
                != PayForOtherTypeEnum.NEED_PAY_FOR_OTHER.getPayForOtherType());
        if (flag) {
            payDeskVoV3.setThirdPayTypeToast(Boolean.TRUE);
            //无三方支付 && 只有订单费待支付 && 0 < 余额可支付 < 订单费  给余额提示
            if (payList.contains(EnumPayOrderChannel.BALANCE.getCode()) && onlyOrderFareToPay(fareParam)
                    && balanceCanPayMoreThanZeroLessThanOrderFare(fareParam)) {
                payDeskVoV3.setThirdPayTypeToastMsg("可用余额不足以支付车费，您还需要选择第三方支付");
            } else {
                payDeskVoV3.setThirdPayTypeToastMsg(switchConfig.getThirdPayTypeToastMsg());
            }
        }
    }

    /**
     * 余额可以支付超过0并且小于订单费
     *
     * @param fareParam 费用参数
     * @return boolean
     */
    public boolean balanceCanPayMoreThanZeroLessThanOrderFare(FareParam fareParam) {
        return fareParam.getAvailableBalance().compareTo(BigDecimal.ZERO) > 0
                && fareParam.getAvailableBalance().compareTo(fareParam.getOrderFareToPay()
                .add(fareParam.getDispatchFee())) < 0;
    }

    /**
     * 只有订单费待支付
     *
     * @param fareParam 费用参数
     * @return boolean
     */
    public boolean onlyOrderFareToPay(FareParam fareParam) {
        boolean noCrossCityFee = fareParam.getCrossCityFee().compareTo(BigDecimal.ZERO) < 1;
        boolean noFestivalFee = fareParam.getFestivalFee().compareTo(BigDecimal.ZERO) < 1;
        boolean noAdditionalFee = fareParam.getAdditionalFee().compareTo(BigDecimal.ZERO) < 1;
        boolean noCouponActivityFee = fareParam.getCouponActivityFee().compareTo(BigDecimal.ZERO) < 1;
        boolean hasOrderFee = fareParam.getOrderFareToPay().compareTo(BigDecimal.ZERO) > 0;
        boolean noDispatchFee = fareParam.getDispatchFee().compareTo(BigDecimal.ZERO) < 1;
        boolean noLostReturnFee = fareParam.getLostReturnFee().compareTo(BigDecimal.ZERO) < 1;
        boolean noCompensationFare = fareParam.getCompensationFare().compareTo(BigDecimal.ZERO) < 1;
        return noCrossCityFee && noFestivalFee && noAdditionalFee && noCouponActivityFee && hasOrderFee
                && noDispatchFee && noLostReturnFee && noCompensationFare;
    }

    /**
     * 获取收银台相关的费用信息
     *
     * @param payTradeReq 收银台请求对象
     * @param payDeskRes  支付收银台返回信息
     * @param payDeskVoV3 收银台相关的
     * @return 费用信息
     */
    private FareParam getFareParam(PayTradeReq payTradeReq, PayDeskRes payDeskRes, PayDeskVoV3 payDeskVoV3) {
        String useCouponType = payTradeReq.getUseCouponType();
        Integer unWarriedArrive = payTradeReq.getUnWarriedArrive();
        String usePrivilegeType = payTradeReq.getUsePrivilegeType();
        // 【收银台数据对象】
        FareDetail deskFareDetail = payDeskRes.getFareDetail();
        log.info("getFareParam().deskFareDetail:{}", JSONObject.toJSONString(deskFareDetail));
        Payed payed = payDeskRes.getPayed();
        Balance balance = payDeskRes.getBalance();
        RouteInfo routeInfo = payDeskRes.getRouteInfo();
        BalanceCanPayDetail balanceCanPayDetail = payDeskRes.getBalanceCanPayDetail();
        CouponVo couponVo = payDeskVoV3.getCouponVo();
        PrivilegeDetail privilegeDetail = payDeskRes.getPrivilegeDetail();
        UnWarriedArriveDetail unWarried = payDeskVoV3.getUnWarriedArriveDetail();
        CouponActivityDetail couponActivityDetail = payDeskVoV3.getCouponActivityDetail();
        Integer useCounponActivity = payDeskVoV3.getUseCounponActivity();
        // 不返回券套餐时，券套餐设置为不使用
        if (couponActivityDetail == null) {
            payDeskVoV3.setUseCounponActivity(NUM_0);
        }
        // 待支付的钱
        BigDecimal remainPay = getBigDecimalValue(deskFareDetail.getRemainPay());
        // 如果乘客选择使用券包的券 还减掉券包中优惠券可抵扣的金额
        if (null != useCounponActivity && NUM_1 == useCounponActivity && couponActivityDetail != null) {
            remainPay = remainPay.subtract(getBigDecimalValue(couponActivityDetail.getDecutionAmount()));
        }
        FareParam fareParam = new FareParam();
        if (Objects.nonNull(deskFareDetail)) {
            BigDecimal decutionAmount =
                    couponVo == null ? BigDecimal.ZERO : getBigDecimalValue(couponVo.getDecutionAmount());
            //设置优惠券可抵扣金额（用户选择优惠券、或者系统评估到可用优惠券时可能大于0，不代表实际就会使用）
            fareParam.setCouponCanPay(decutionAmount);
            BigDecimal unWarriedDisAmount =
                    unWarried == null ? BigDecimal.ZERO : getBigDecimalValue(unWarried.getUnWarriedArriveDisAmount());
            //设置省心打可支付金额（用户选择省心打、或者系统评估最优组合时可能不大于0）
            fareParam.setUnWorryCanPay(unWarriedDisAmount);
            if (null != couponVo && null != unWarried) {
                if (StringUtils.isNotBlank(useCouponType) && !NumConstants.STR_0.equals(useCouponType)) {
                    // 乘客选择了优惠券
                    //第一次加载收银台时，app请求参数是同时选择了优惠券和省心打
                    if (null != unWarriedArrive && NUM_1 == unWarriedArrive) {
                        // 乘客选择省心打
                        if (unWarriedDisAmount.compareTo(decutionAmount) > 0) {
                            // 省心打更优惠
                            fareParam.setActualFare(getBigDecimalValue(remainPay).subtract(unWarriedDisAmount));
                        } else {
                            //优惠券的优惠更大
                            fareParam.setActualFare(getBigDecimalValue(remainPay).subtract(decutionAmount));
                        }
                    } else {
                        // 乘客没选择或者没开通省心打，只能选择优惠券
                        fareParam.setActualFare(getBigDecimalValue(remainPay).subtract(decutionAmount));
                    }
                } else {
                    //乘客没选择优惠券
                    if (null != unWarriedArrive && NUM_1 == unWarriedArrive) {
                        // 但是乘客选择省心打
                        fareParam.setActualFare(getBigDecimalValue(remainPay).subtract(unWarriedDisAmount));
                    } else {
                        // 乘客也没有选择省心打
                        fareParam.setActualFare(getBigDecimalValue(remainPay));
                    }
                }
            }
            // 乘客没开通省心打
            if (couponVo != null && unWarried == null) {
                //选择优惠券
                if (StringUtils.isNotBlank(useCouponType) && !NumConstants.STR_0.equals(useCouponType)
                        && decutionAmount.compareTo(BigDecimal.ZERO) > 0) {
                    fareParam.setActualFare(getBigDecimalValue(remainPay).subtract(decutionAmount));
                } else {
                    // 没选择优惠券
                    fareParam.setActualFare(getBigDecimalValue(remainPay));
                }
            }
            // 权益卡抵扣需要在优惠券之后
            privilegeFareParam(usePrivilegeType, privilegeDetail, remainPay, fareParam);
            //待支付附加费
            fareParam.setAdditionalFee(getBigDecimalValue(deskFareDetail.getServiceFare()));
            fareParam.setFestivalFee(getBigDecimalValue(deskFareDetail.getFestivalFare()));
            fareParam.setCrossCityFee(getBigDecimalValue(deskFareDetail.getCrossCityFare()));
            fareParam.setDispatchFee(getBigDecimalValue(deskFareDetail.getDispatchFare()));
            fareParam.setLostReturnFee(getBigDecimalValue(deskFareDetail.getLostReturnFare()));
            fareParam.setCompensationFare(getBigDecimalValue(deskFareDetail.getCompensationFare()));
            //待支付订单费
            fareParam.setOrderFareToPay(getBigDecimalValue(deskFareDetail.getOrderFare()));
            //待支付总费用
            fareParam.setTotalFare(getBigDecimalValue(deskFareDetail.getRemainPay()));
            if (Objects.nonNull(payed)) {
                fareParam.setTotalFare(fareParam.getTotalFare().add(getBigDecimalValue(payed.getAmountPayed())));
            }
            if (StringUtils.isNotBlank(deskFareDetail.getCancelFare())) {
                fareParam.setCancelFare(BigDecimal.valueOf(Double.valueOf(deskFareDetail.getCancelFare())));
            }
        }
        if (Objects.nonNull(balance)) {
            //钱包打车金总余额
            balanceFareParam(balance, fareParam);
        }
        if (Objects.nonNull(balanceCanPayDetail)) {
            fareParam.setAvailableBalance(getBigDecimalValue(balanceCanPayDetail.getGiftCardCanPay()).add(
                    getBigDecimalValue(balanceCanPayDetail.getGiftMoneyCanPay()))
                    .add(getBigDecimalValue(balanceCanPayDetail.getRechargeMoneyCanPay()))
                    .add(getBigDecimalValue(balanceCanPayDetail.getRechargeGiftMoneyCanPay())));
            fareParam.setGiftCardCanPayService(balanceCanPayDetail.getGiftCardCanPayService());
            fareParam.setGiftCardCanPayFestival(balanceCanPayDetail.getGiftCardCanPayFestival());
            fareParam.setGiftCardCanPayCrossCity(balanceCanPayDetail.getGiftCardCanPayCrossCity());
            fareParam.setGiftCardCanPayDispatch(balanceCanPayDetail.getGiftCardCanPayDispatch());
            fareParam.setRechargeCanPay(getBigDecimalValue(balanceCanPayDetail.getRechargeMoneyCanPay()));
            fareParam.setRechargeGiftCanPay(getBigDecimalValue(balanceCanPayDetail.getRechargeGiftMoneyCanPay()));
            fareParam.setGiftMoneyCanPay(getBigDecimalValue(balanceCanPayDetail.getGiftMoneyCanPay()));
            //礼品卡可支付金额
            fareParam.setGiftCardCanPay(getBigDecimalValue(balanceCanPayDetail.getGiftCardCanPay()));
            boolean isRechargeCanPay = fareParam.getRechargeCanPay().compareTo(BigDecimal.ZERO) > 0
                    || fareParam.getRechargeGiftCanPay().compareTo(BigDecimal.ZERO) > 0;
        }
        if (Objects.nonNull(payed)) {
            fareParam.setUnWarriedArrivePay(getBigDecimalValue(payed.getUnWarriedArrivePay()));
            fareParam.setIntegralPayed(getBigDecimalValue(payed.getIntegralPayed()));
            fareParam.setPrPayed(getBigDecimalValue(payed.getPrPayed()));
            fareParam.setCouponPay(getBigDecimalValue(payed.getCouponPay()));
            fareParam.setPrivilegePay(getBigDecimalValue(payed.getPrivilegePay()));
            fareParam.setBalancePay(getBigDecimalValue(payed.getBalancePay())
                    .add(getBigDecimalValue(payed.getGiftCardPayed())));
            fareParam.setAssetPay(getBigDecimalValue(payed.getRechargeCashPay())
                    .add(getBigDecimalValue(payed.getRechargeGiftPay()))
                    .add(getBigDecimalValue(payed.getGiftCashPay()))
                    .add(getBigDecimalValue(payed.getGiftCardPayed())));
            fareParam.setRechargeCashPay(getBigDecimalValue(payed.getRechargeCashPay()));
            fareParam.setRechargeGiftPay(getBigDecimalValue(payed.getRechargeGiftPay()));
            fareParam.setGiftCashPay(getBigDecimalValue(payed.getGiftCashPay()));
            // 企业支付金额
            BigDecimal companyPayedAmount = getBigDecimalValue(payed.getCompanyPayedAmount());
            BigDecimal companyCouponAmount = getBigDecimalValue(payed.getCompanyCouponAmount());
            BigDecimal enterprisePay = companyPayedAmount.subtract(companyCouponAmount);
            fareParam.setEnterprisePay(enterprisePay);
            fareParam.setCompanyCouponAmount(companyCouponAmount);

        }
        if (Objects.nonNull(routeInfo)) {
            fareParam.setExpandBizLine(routeInfo.getExpandBizLine());
            fareParam.setProductLine(routeInfo.getProductLine());
        }
        return fareParam;
    }

    /**
     * 余额费用详情
     *
     * @param balance   balance
     * @param fareParam fareParam
     */
    private void balanceFareParam(Balance balance, FareParam fareParam) {
        fareParam.setGiftCurrency(getBigDecimalValue(balance.getGiftCurrency()));
        fareParam.setBalanceTotal(getBigDecimalValue(balance.getBalanceTotal()));
        //钱包礼品卡总余额
        fareParam.setGiftCard(getBigDecimalValue(balance.getGiftCard()));
        fareParam.setRechargeGift(getBigDecimalValue(balance.getRechargeGift()));
        fareParam.setRechargeCash(getBigDecimalValue(balance.getRechargeCash()));
        fareParam.setGiftCash(getBigDecimalValue(balance.getGiftCash()));
        fareParam.setRechargeFlag(balance.getRechargeFlag());
    }

    /**
     * 权益卡费用详情
     *
     * @param usePrivilegeType usePrivilegeType
     * @param privilegeDetail  privilegeDetail
     * @param remainPay        remainPay
     * @param fareParam        fareParam
     */
    private void privilegeFareParam(String usePrivilegeType, PrivilegeDetail privilegeDetail,
                                    BigDecimal remainPay, FareParam fareParam) {
        if (null != privilegeDetail && StringUtils.isNotBlank(usePrivilegeType)
                && !"0".equals(usePrivilegeType)) {
            BigDecimal privilegeDecutionAmount = getBigDecimalValue(privilegeDetail.getDecutionAmount());
            fareParam.setPrivilegeCanPay(privilegeDecutionAmount);
            BigDecimal subtract;
            if (null == fareParam.getActualFare()) {
                subtract = getBigDecimalValue(remainPay).subtract(privilegeDecutionAmount);
            } else {
                subtract = fareParam.getActualFare().subtract(privilegeDecutionAmount);
            }
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                subtract = BigDecimal.ZERO;
            }
            fareParam.setActualFare(subtract);
        }
    }

    /**
     * 获取【支付项列表】优惠券抵扣信息
     *
     * @param passengerId 乘客id
     * @param orderUuid   行程id
     * @param orgCode     城市id
     * @param payChannel  支付渠道类型
     * @param payDeskRes  支付收银台返回信息
     * @param useChannel  渠道
     * @return 优惠券信息
     */
    private CouponVo getPayDeskCouponVo(String passengerId, String orderUuid, String orgCode,
                                        Integer payChannel, PayDeskRes payDeskRes, Integer useChannel) {
        CouponVo couponVo = new CouponVo();
        List<CanUseCoupon> canUseCouponList = Lists.newArrayList();
        CouponDetail couponDetail = payDeskRes.getCouponDetail();
        if (Objects.nonNull(payDeskRes.getCouponDetail())) {
            List<String> canUseCouponStrList = payDeskRes.getCouponDetail().getCanUseCoupon();
            if (CollectionUtils.isNotEmpty(canUseCouponStrList)) {
                for (String str : canUseCouponStrList) {
                    CanUseCoupon canUseCoupon = JSON.parseObject(str, CanUseCoupon.class);
                    canUseCouponList.add(canUseCoupon);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(canUseCouponList)) {
            if (Objects.nonNull(couponDetail)
                    && getBigDecimalValue(couponDetail.getDecutionAmount()).compareTo(BigDecimal.ZERO) > 0) {
                couponVo.setCouponCount(canUseCouponList.size());
                couponVo.setCouponId(canUseCouponList.get(0).getCouponId());
                couponVo.setDecutionAmount(couponDetail.getDecutionAmount());
                String thirdChannel = couponDetail.getThirdChannel();
                couponVo.setThirdChannel(thirdChannel);
                if (StringUtils.isNotEmpty(thirdChannel)) {
                    couponVo.setThirdChannelList(Lists.newArrayList(thirdChannel.split(",")));
                }
                couponVo.setItemSubType(canUseCouponList.get(0).getTerminal());
            }
        }
        // 获取可用渠道券数量
        Integer availableCouponCnt = getAvailableCouponCnt(orgCode, orderUuid, passengerId, payChannel, useChannel);
        couponVo.setCouponCount(availableCouponCnt);
        return couponVo;
    }

    /**
     * 获取支付明细列表
     *
     * @param payDeskVoV3        收银台信息
     * @param payDeskRes         pay-center返回收银台信息
     * @param payTradeReq        请求参数
     * @param fareParam          支付费用明细
     * @param isCurrentPassenger 是否是本人
     * @param orgCode            orgCode
     * @return 支付明细项
     */
    private List<PayItem> getPayItemList(PayDeskVoV3 payDeskVoV3, PayDeskRes payDeskRes, PayTradeReq payTradeReq,
                                         FareParam fareParam, Boolean isCurrentPassenger, String orgCode) {
        List<PayItem> payItemList = new ArrayList<>();

        PayItem totalPayItem = new PayItem("合计费用", fareParam.getTotalFare().toString(),
                Boolean.FALSE, NUM_0, 0, null);
        BigDecimal additionalFee = getBigDecimalValue(fareParam.getAdditionalFee());
        BigDecimal festivalFee = getBigDecimalValue(fareParam.getFestivalFee());
        BigDecimal lostReturnFee = getBigDecimalValue(fareParam.getLostReturnFee());
        BigDecimal compensationFare = getBigDecimalValue(fareParam.getCompensationFare());
        if (additionalFee.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + additionalFee.toPlainString() + "元附加费");
        } else if (festivalFee.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + festivalFee.toPlainString() + "元节日服务费");
        } else if (lostReturnFee.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + lostReturnFee.toPlainString() + "元物品遗失返还费");
        } else if (compensationFare.compareTo(BigDecimal.ZERO) > NUM_0) {
            totalPayItem.setItemSubTitle("含" + compensationFare.toPlainString() + "元赔偿金");
        }
        payItemList.add(totalPayItem);

        // 【支付项明细列表】1:企业支付
        if (fareParam.getEnterprisePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("企业支付", "-" + fareParam.getEnterprisePay(), Boolean.FALSE, 1, null));
        }

        Payed payed = payDeskRes.getPayed();
        boolean rechargePayed = false;
        //如果充值金(含充值赠金)已支付则优惠券不能使用，直接隐优惠券选项
        if (getBigDecimalValue(payed.getRechargeCashPay()).compareTo(BigDecimal.ZERO) > 0
                || getBigDecimalValue(payed.getRechargeGiftPay()).compareTo(BigDecimal.ZERO) > 0) {
            rechargePayed = true;
        }
        // 【支付项明细列表】3:优惠券已抵扣
        if (isCurrentPassenger && !rechargePayed) {
            if (fareParam.getCouponPay().compareTo(BigDecimal.ZERO) > 0
                    || fareParam.getCompanyCouponAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal couponPayed;
                if (fareParam.getCouponPay().compareTo(BigDecimal.ZERO) > 0) {
                    couponPayed = fareParam.getCouponPay();
                } else {
                    couponPayed = fareParam.getCompanyCouponAmount();
                }
                payDeskVoV3.setCouponDeductFlag(Boolean.TRUE);
                payItemList.add(new PayItem("优惠券已抵扣", "-" + couponPayed,
                        Boolean.FALSE, NUM_1, NUM_2, null));
            } else if (fareParam.getUnWarriedArrivePay().compareTo(BigDecimal.ZERO) > 0) {
                payDeskVoV3.setUnWarriedArriveDeductFlag(Boolean.TRUE);
                payItemList.add(new PayItem("省心打优惠已抵扣", "-" + fareParam.getUnWarriedArrivePay(),
                        Boolean.FALSE, NUM_2, NUM_2, null));
            } else {
                unWarriedCoupon(payDeskVoV3, payTradeReq, fareParam, payItemList, orgCode);
            }
        }

        // 【支付项明细列表】3:权益卡已支付
        if (isCurrentPassenger) {
            privilegePayItem(payDeskVoV3, payDeskRes, payTradeReq, fareParam, payItemList, orgCode);
        }

        // 【支付项明细列表】4:预付款已支付
        if (fareParam.getPrPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("已预付", "-" + fareParam.getPrPayed(),
                    Boolean.FALSE, NUM_4, null));
        }
        // 【支付项明细列表】5:余额已支付
        if (fareParam.getBalancePay().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("余额已支付", "-" + fareParam.getBalancePay(),
                    Boolean.FALSE, NUM_5, null));
        }
        // 【支付项明细列表】6:T币已抵扣
        if (fareParam.getIntegralPayed().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("T币已抵扣", "-" + fareParam.getIntegralPayed(),
                    Boolean.FALSE, NUM_6, null));
        }
        // 【支付项明细列表】7:还需支付
        if (fareParam.getActualFare().compareTo(BigDecimal.ZERO) > 0) {
            payItemList.add(new PayItem("还需支付", fareParam.getActualFare().toString(),
                    Boolean.FALSE, NUM_7, null));
        }
        return payItemList;
    }

    /**
     * 权益卡费用详情
     *
     * @param payDeskVoV3 payDeskVoV3
     * @param payDeskRes  payDeskRes
     * @param payTradeReq payTradeReq
     * @param fareParam   fareParam
     * @param payItemList payItemList
     * @param orgCode     orgCode
     */
    private void privilegePayItem(PayDeskVoV3 payDeskVoV3, PayDeskRes payDeskRes, PayTradeReq payTradeReq,
                                  FareParam fareParam, List<PayItem> payItemList, String orgCode) {
        String usePrivilegeType = payTradeReq.getUsePrivilegeType();
        PrivilegeDetail privilegeDetail = payDeskRes.getPrivilegeDetail();
        String bestPrivilege = privilegeDetail.getCanUsePrivilege();
        // 权益卡已抵扣
        if (fareParam.getPrivilegePay().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal privilegePayed = fareParam.getPrivilegePay();
            payDeskVoV3.setPrivilegeDeductFlag(Boolean.TRUE);
            payItemList.add(new PayItem("权益卡已抵扣", "-" + privilegePayed,
                    Boolean.FALSE, NUM_3, NUM_3, null));
        } else if (StringUtils.isNotBlank(usePrivilegeType)
                && !"0".equals(usePrivilegeType)) {
            // 选择使用权益卡
            BigDecimal privilegeDecutionAmount = getBigDecimalValue(privilegeDetail.getDecutionAmount());
            // 有可用权益卡才显示
            if (privilegeDecutionAmount.compareTo(BigDecimal.ZERO) > 0) {
                payDeskVoV3.setPrivilegeChecked(Boolean.TRUE);
                CouponVo couponVo = new CouponVo();
                if (StringUtils.isNotEmpty(bestPrivilege)) {
                    AccountPrivilegeDto privilege = JSON.parseObject(bestPrivilege, AccountPrivilegeDto.class);
                    couponVo.setDecutionAmount(privilegeDecutionAmount);
                    couponVo.setCouponId(privilege.getCardUuid());
                    couponVo.setCouponCount(privilege.getAvailableCount());
                    Integer privilegeSubType = privilege.getPrivilegeSubType();
                    if (privilegeSubType != null && privilege.getUseChannel() != null
                            && privilege.getPrivilegeSubType().equals(NUM_1)
                            && privilege.getUseChannel().equals(NUM_1)) {
                        // 判断是否需要展示
                        JSONObject resp = marketingRest.showAppGuide(payDeskVoV3.getPassengerId(), orgCode);
                        if (resp != null && StringUtils.isNotBlank(resp.getString(PRIVILEGE_SHOW))
                                && resp.getBoolean(PRIVILEGE_SHOW)) {
                            couponVo.setItemSubTitle(FREE_CANCEL_TITLE + privilege.getPrivilegeName());
                        }
                    }
                }
                payItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
            }
        } else if (compareGrayBuild(payTradeReq.getGrayBuild(), NumConstants.STR_470)
                && StringUtils.isNotEmpty(bestPrivilege)) {
            //兼容老版本，老版本不返回权益卡费用项
            // 不使用权益卡但是有可用卡的情况，不使用也要返回支付列表
            payDeskVoV3.setPrivilegeChecked(Boolean.FALSE);
            CouponVo couponVo = new CouponVo();
            payItemList.add(new PayItem("权益卡", null, Boolean.FALSE, NUM_3, NUM_3, couponVo));
        }
    }

    /**
     * unWarriedCoupon
     *
     * @param payDeskVoV3 payDeskVoV3
     * @param payTradeReq payTradeReq
     * @param fareParam   fareParam
     * @param payItemList payItemList
     * @param orgCode     orgCode
     */
    private void unWarriedCoupon(PayDeskVoV3 payDeskVoV3, PayTradeReq payTradeReq, FareParam fareParam,
                                 List<PayItem> payItemList, String orgCode) {
        log.info("PaymentBusiness.unWarriedCoupon:{}", JSONUtil.toJsonStr(payDeskVoV3));
        String useCouponType = payTradeReq.getUseCouponType();
        Integer unWarriedArrive = payTradeReq.getUnWarriedArrive();
        // 【支付项明细列表】3:优惠券
        CouponVo couponVo = payDeskVoV3.getCouponVo();
        couponVo.setCannotUseCouponFlag(fareParam.getCannotUseCouponFlag());
        if (fareParam.getCannotUseCouponFlag()) {
            couponVo.setCannotUseCouponMsg("现金余额不支持叠加优惠券");
        }
        UnWarriedArriveDetail unWarried = payDeskVoV3.getUnWarriedArriveDetail();
        //1:购买券套餐并且使用 2:只购买
        Integer useCounponActivity = payDeskVoV3.getUseCounponActivity();
        CouponActivityDetail couponActivityDetail = payDeskVoV3.getCouponActivityDetail();

        // 专享判断
        if (StringUtils.isNotBlank(couponVo.getItemSubType())
                && couponVo.getItemSubType().equals(NumConstants.STR_1)) {
            JSONObject resp = marketingRest.showAppGuide(payDeskVoV3.getPassengerId(), orgCode);
            if (resp != null && StringUtils.isNotBlank(resp.getString(PRIVILEGE_SHOW))
                    && resp.getBoolean(COUPON_SHOW)) {
                couponVo.setItemSubTitle("APP专享");
            }
        }
        String unWarriedSubTiltle = "";
        if (null != couponVo && null != unWarried) {
            BigDecimal decutionAmount = getBigDecimalValue(couponVo.getDecutionAmount());
            BigDecimal unWarriedDisAmount = getBigDecimalValue(unWarried.getUnWarriedArriveDisAmount());
            // 乘客选择了优惠券
            if (StringUtils.isNotBlank(useCouponType) && !NumConstants.STR_0.equals(useCouponType)) {
                //第一次加载收银台时，app请求参数是同时选择了优惠券和省心打
                if (null != unWarriedArrive && NUM_1 == unWarriedArrive) {
                    // 乘客选择省心打
                    if (unWarriedDisAmount.compareTo(decutionAmount) >= 0) {
                        // 省心打更优惠
                        payDeskVoV3.setUnWarriedArriveChecked(Boolean.TRUE);
                        payDeskVoV3.setCouponChecked(Boolean.FALSE);
                        // 乘客选择省心打时，如果省心打更优惠真是文案
                        BigDecimal subtract = unWarriedDisAmount.subtract(decutionAmount);
                        unWarried.setSubCouponAmount(subtract);
                        unWarriedSubTiltle = "已为您选择省心打优惠，比用券节省" + subtract.toPlainString() + "元";
                    } else {
                        //优惠券的优惠更大时，如果是券套餐的券
                        if (null != couponActivityDetail && NUM_1 == useCounponActivity) {
                            payDeskVoV3.setCouponChecked(Boolean.FALSE);
                        } else {
                            payDeskVoV3.setCouponChecked(Boolean.TRUE);
                        }
                        unWarriedSubTiltle = "不可与优惠券叠加使用";
                    }
                } else {
                    // 乘客没选择或者没开通省心打，选择自有优惠券,优惠券金额大于0的时候选择优惠券
                    // 乘客选择自有优惠券，说明肯定没选择券包里的券
                    if (decutionAmount.compareTo(BigDecimal.ZERO) > 0) {
                        payDeskVoV3.setCouponChecked(Boolean.TRUE);
                    }
                    unWarriedSubTiltle = "不可与优惠券叠加使用";
                }
            } else {
                //乘客没选择优惠券
                if (null != unWarriedArrive && NUM_1 == unWarriedArrive) {
                    // 乘客选择省心打
                    payDeskVoV3.setUnWarriedArriveChecked(Boolean.TRUE);
                    payDeskVoV3.setCouponChecked(Boolean.FALSE);
                    if (unWarriedDisAmount.compareTo(decutionAmount) >= 0) {
                        // 乘客选择省心打时，如果省心打更优惠副标题文案
                        BigDecimal subtract = unWarriedDisAmount.subtract(decutionAmount);
                        unWarried.setSubCouponAmount(subtract);
                        unWarriedSubTiltle = "已为您选择省心打优惠，比用券节省" + subtract.toPlainString() + "元";
                    }
                } else {
                    // 乘客也没有选择省心打，
                    payDeskVoV3.setUnWarriedArriveChecked(Boolean.FALSE);
                    payDeskVoV3.setCouponChecked(Boolean.FALSE);
                    unWarriedSubTiltle = "不可与优惠券叠加使用";
                }
            }

            // 如果省心打更优的话，优惠券展示位仍然只展示用户账户下的优惠券信息
            if (null != couponActivityDetail && NUM_1 == useCounponActivity) {
                payDeskVoV3.setCouponChecked(Boolean.TRUE);
                couponVo.setCouponId(couponActivityDetail.getCouponNewUuid());
                couponVo.setDecutionAmount(couponActivityDetail.getDecutionAmount());
            }
            payItemList.add(new PayItem("优惠券", null, Boolean.FALSE, NUM_1, NUM_2, couponVo));
            //省心打可抵扣金额大于0时才展示
            if (unWarriedDisAmount.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, BigDecimal> unWarriedMap = new HashMap<>(NUM_16);
                unWarriedMap.put("decutionAmount", unWarried.getUnWarriedArriveDisAmount());
                PayItem unWarriedPayItem = new PayItem("省心打优惠", null,
                        Boolean.FALSE, NUM_2, NUM_2, unWarriedMap);
                unWarriedPayItem.setItemSubTitle(unWarriedSubTiltle);
                payItemList.add(unWarriedPayItem);
            } else {
                payDeskVoV3.setUnWarriedArriveChecked(Boolean.FALSE);
            }
        }
        if (couponVo != null && unWarried == null) {
            log.info("couponVo:{}, useCouponType:{}", JSONUtil.toJsonStr(couponVo), useCouponType);
            //useCouponType和unWarriedArrive 都是使用状态时表示是收银台首次拉起
            if (null != useCouponType && !NumConstants.STR_0.equals(useCouponType)) {
                payDeskVoV3.setCouponChecked(Boolean.TRUE);
                payItemList.add(new PayItem("优惠券", null, Boolean.FALSE, NUM_1, NUM_2, couponVo));
            } else {
                // 如果省心打更优的话，优惠券展示位仍然只展示用户账户下的优惠券信息
                if (null != couponActivityDetail && NUM_1 == useCounponActivity) {
                    payDeskVoV3.setCouponChecked(Boolean.TRUE);
                    couponVo.setCouponId(couponActivityDetail.getCouponNewUuid());
                    couponVo.setDecutionAmount(couponActivityDetail.getDecutionAmount());
                }
                payItemList.add(new PayItem("优惠券", null, Boolean.FALSE, NUM_1, NUM_2, couponVo));
            }
        }
    }

    /**
     * 获取行程信息
     *
     * @param orderUuid 行程id
     * @return 行程信息
     */
    public RouteInfoDto getRouteDetailDto(String orderUuid) {
        log.info("getRouteDetailDto orderUuid: {}", orderUuid);
        RouteInfoDto routeInfo = routeDetailClient.getRouteInfoWithFareWeak(orderUuid);
        if (null == routeInfo) {
            log.error("getRouteDetailDto error resp is empty");
//            throw new BusinessException(ResultErrorEnum.ROUTE_INFO_ERROR);
            return null;
        }
        String routeStatus = routeInfo.getStatus() == null ? "" : routeInfo.getStatus().toString();
        if (RouteStatus.ROUTE_7.getStatus().equals(routeStatus)
                || RouteStatus.ROUTE_8.getStatus().equals(routeStatus)
                || RouteStatus.ROUTE_9.getStatus().equals(routeStatus)) {
            return routeInfo;
        }
        if (!RouteStatus.ROUTE_6.getStatus().equals(routeStatus)) {
            throw new BusinessException(ResultErrorEnum.ROUTE_PAYED);
        }
        return routeInfo;
    }


    /**
     * 付款类型列表过程
     * 付款类型列表无秘密
     *
     * @param existAdditionalFee     存在额外费用
     * @param hasBalance             是否有余额
     * @param balanceEnough          余额充足够支付费用
     * @param deskDisplaypayTypeList 桌面显示付款类型列表
     * @param passengerChooseFlag    乘客选择标志
     * @param applicationType        小程序标记
     * @return {@link List<Integer>}
     */
    public List<Integer> payTypeListProc(List<Integer> deskDisplaypayTypeList, Boolean existAdditionalFee,
                                         Boolean hasBalance, Boolean balanceEnough, Boolean passengerChooseFlag,
                                         Integer applicationType) {
        //包含附加费
        if (existAdditionalFee) {
            //有余额
            if (hasBalance) {
                //最后决定不主动选择余额支付
                //小程序请求时使用该字段
                deskDisplaypayTypeList = miniPayTypeListProc(applicationType, deskDisplaypayTypeList);
            }
        } else {
            //有余额
            if (hasBalance) {
                if (Boolean.FALSE.equals(passengerChooseFlag)) {
                    if (balanceEnough) {
                        //不添加渠道，同时不勾选余额
                        log.debug("初始化加载收银台，不默认勾选余额，余额足够支付也不处理");
                    } else { //微信小程序 新增
                        //小程序请求时使用该字段
                        deskDisplaypayTypeList = miniPayTypeListProc(applicationType, deskDisplaypayTypeList);
                    }
                }
            } else {
                //账户无余额
                //有历史支付方式--返回历史支付方式
                //无历史支付方式--返回推荐支付方式
                //这部分处理逻辑在收银台入口处已经填入 防止重复接口调用，提高性能
                //小程序请求时使用该字段
                deskDisplaypayTypeList = miniPayTypeListProc(applicationType, deskDisplaypayTypeList);
            }
        }
        // 目前微信小程序只会有  积分、余额、微信 三种支付方式  过滤掉快享可能推荐除微信之外的支付方式
        if (Objects.nonNull(applicationType) && applicationType.equals(NUM_2)) {
            deskDisplaypayTypeList = deskDisplaypayTypeList.stream()
                    .filter(e -> e == EnumPayOrderChannel.WECHATMINIPROGRAM.getCode()
                            || e == EnumPayOrderChannel.BALANCE.getCode()
                            || e == EnumPayOrderChannel.INTEGRAL.getCode()).collect(Collectors.toList());
        }
        deskDisplaypayTypeList = deskDisplaypayTypeList.stream().distinct().
                filter(e -> e != NUM_NEGATIVE_1).collect(Collectors.toList());
        List<Integer> displaypayTypeList = new ArrayList<>(Arrays.asList(new Integer[deskDisplaypayTypeList.size()]));
        Collections.copy(displaypayTypeList, deskDisplaypayTypeList);
        return displaypayTypeList;
    }

    /**
     * 迷你支付类型列表程序
     *
     * @param applicationType        应用程序类型
     * @param deskDisplaypayTypeList 桌面显示付款类型列表
     * @return {@link List<Integer>}
     */
    private List<Integer> miniPayTypeListProc(Integer applicationType, List<Integer> deskDisplaypayTypeList) {
        if (Objects.nonNull(applicationType) && applicationType.equals(NUM_2)) {
            if (!deskDisplaypayTypeList.contains(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode())) {
                deskDisplaypayTypeList.add(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());
            }
            // 目前微信小程序只会有  积分、余额、微信 三种支付方式
            deskDisplaypayTypeList = deskDisplaypayTypeList.stream()
                    .filter(e -> e == EnumPayOrderChannel.WECHATMINIPROGRAM.getCode()
                            || e == EnumPayOrderChannel.BALANCE.getCode()
                            || e == EnumPayOrderChannel.INTEGRAL.getCode()).collect(Collectors.toList());
        }
        List<Integer> displaypayTypeList = new ArrayList<>(Arrays.asList(new Integer[deskDisplaypayTypeList.size()]));
        Collections.copy(displaypayTypeList, deskDisplaypayTypeList);
        return displaypayTypeList;
    }

    /**
     * 删除微信类型
     *
     * @param payTypeList 付款类型列表
     * @return {@link List<Integer>}
     */
    private List<Integer> removeWechatType(List<Integer> payTypeList) {
        Iterator<Integer> iterator = payTypeList.iterator();
        while (iterator.hasNext()) {
            Integer next = iterator.next();
            if (Integer.valueOf(EnumPayOrderChannel.WEIXIN.getCode()).equals(next)) {
                iterator.remove();
            }
        }
        return payTypeList;
    }

    /**
     * 获得推荐的付款方式
     *
     * @param payTypeList            付款类型列表
     * @param deskDisplaypayTypeList 桌面显示付款类型列表
     */
    private void getRecommendPaymentWay(List<Integer> payTypeList, List<Integer> deskDisplaypayTypeList) {
        Response<List<PayChannelResDto>> payChannelListRes = payChannelTakeOverService.getPayChannelListForApp(
                new PayChannelReqDto());
        if (!payChannelListRes.isSuccess() || CollectionUtils.isEmpty(payChannelListRes.getData())) {
            log.info("获取支付类型列表失败");
        }
        PayChannelResDto payChannelResDto = payChannelListRes.getData().get(0);
        payTypeList.add(payChannelResDto.getPayChannel());
        //将推荐的第三方支付保存
        deskDisplaypayTypeList.add(payChannelResDto.getPayChannel());
    }

    /**
     * 过滤器第三方付款方式
     *
     * @param payTypeList 付款类型列表
     * @return {@link List<Integer>} 桌面显示付款类型列表
     */
    public List<Integer> filterThirdPaymentWay(List<Integer> payTypeList) {
        PayChannelReqDto payChannelReqDto = new PayChannelReqDto();
        payChannelReqDto.setQuickPass(true);
        Response<List<PayChannelResDto>> payChannelListRes = payChannelTakeOverService.getPayChannelListForApp(
                payChannelReqDto);
        if (!payChannelListRes.isSuccess() || CollectionUtils.isEmpty(payChannelListRes.getData())) {
            log.error("获取支付类型列表失败");
            return new ArrayList<>();
        }
        List<Integer> payWayList = payChannelListRes.getData().stream()
                .map(PayChannelResDto::getPayChannel).collect(Collectors.toList());
        List<Integer> deskDisplaypayTypeList = payTypeList.stream().
                filter(payType -> !payWayList.contains(payType)).collect(Collectors.toList());
        return deskDisplaypayTypeList;
    }

    /**
     * 获取免密开通状态  任何一种支付方式开通免密后就表示该乘客已开通免密
     *
     * @param passengerUuid 乘客uuid
     * @return {@link Response}
     * @throws Exception 异常
     */
    public Boolean getSecretFreeStatus(String passengerUuid) {
        try {
            AccountSignParamDto queryDto = new AccountSignParamDto();
            queryDto.setUserId(passengerUuid);
            Response<List<AccountSignChannelDto>> response = accountSignService.getSignChannelStatus(queryDto);
            if (null != response && response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                if (atLeastOneSign(response.getData())) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("getSecretFreeStatus error", e);
        }
        return Boolean.FALSE;

    }

    /**
     * 判断是否至少有一条签约记录
     *
     * @param list 签约记录
     * @return boolean
     */
    private boolean atLeastOneSign(List<AccountSignChannelDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (AccountSignChannelDto dto : list) {
            if (AccountSignStatusEnum.SIGN_STATUS_SUCCESS.getCode() == dto.getDensityFree()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取渠道券数量
     *
     * @param cityCode      城市
     * @param orderUuid     行程id
     * @param passengerUuid 乘客id
     * @param payChannel    渠道类型
     * @param useChannel    useChannel
     * @return 渠道券数量
     */
    private Integer getAvailableCouponCnt(String cityCode, String orderUuid, String passengerUuid, Integer payChannel,
                                          Integer useChannel) {
        log.info("获取渠道券数量getAvailableCouponCnt，cityCode:{},orderUuid:{},passengerUuid:{},payChannel:{},useChannel:{}",
                cityCode, orderUuid, passengerUuid, payChannel, useChannel);
        if (StringUtils.isBlank(cityCode) || StringUtils.isBlank(orderUuid) || StringUtils.isBlank(passengerUuid)) {
            return NUM_0;
        }
        // 获取可用优惠券数量
        ChannelListReq channelListReq = new ChannelListReq();
        channelListReq.setJourneyId(orderUuid);
        channelListReq.setUserId(passengerUuid);
        // 获取选中的第三方渠道
        channelListReq.setUseChannel(NumConstants.STR_0 + (Objects.nonNull(payChannel) ? "," + payChannel : ""));
        channelListReq.setCityCode(cityCode);
        channelListReq.setTerminal(PayUtils.changeForCoupon(useChannel));
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.pay.query.coupon.channelCouponNum");
        dto.setExtendParam(JSON.toJSONString(channelListReq));
        Response response = unifiedService.handle(dto);
        if (Objects.nonNull(response) && Objects.nonNull(response.getData())) {
            ChannelCouponNumVo channelCouponNumVo = JSON.parseObject((String) response.getData(),
                    ChannelCouponNumVo.class);
            if (Objects.nonNull(channelCouponNumVo)) {
                return channelCouponNumVo.getCanUseNum();
            }
        }
        return NUM_0;
    }

    /**
     * 获取较大的十进制值
     *
     * @param bigDecimal 大小数
     * @return {@link BigDecimal}
     */
    private BigDecimal getBigDecimalValue(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return BigDecimal.ZERO;
        }
        return bigDecimal;
    }

    /**
     * 是否当前版本是否大于等于指定版本
     *
     * @param b1 待比对版本
     * @param b2 指定版本
     * @return boolean true:大于等于 false:小于
     */
    private boolean compareGrayBuild(String b1, String b2) {
        Integer grayBuild = 0;
        if (StringUtils.isNotBlank(b1)) {
            grayBuild = Integer.parseInt(b1);
        }
        if (grayBuild >= Integer.parseInt(b2)) {
            return true;
        }
        return false;
    }


    /**
     * 获得完整的响应
     *
     * @param payDeskVo         支付台语音
     * @param integralDeductDTO 积分扣除数据到
     * @param minDeductMoney    最低扣款
     * @param minDeductPoint    最小扣点
     * @param availableIntegral 可用积分
     * @return {@link Response}
     */
    private Response getIntegralResponse(PayDeskVo payDeskVo, IntegralDeductDto integralDeductDTO,
                                         BigDecimal minDeductMoney,
                                         BigDecimal minDeductPoint, BigDecimal availableIntegral) {
        BigDecimal remainAmount = payDeskVo.getActualFare().subtract(payDeskVo.getAdditionalFee())
                .subtract(payDeskVo.getFestivalFee()).subtract(payDeskVo.getCrossCityFee());
        //获取符合条件的积分抵扣规则（先按金额倒序排，取小于待支付金额中最大的规则）
        Optional<LadderDiscountDto> optional =
                integralDeductDTO.getLadderDiscountDtos().stream()
                        .sorted(Comparator.comparing(
                                LadderDiscountDto::getConsumeMoney).reversed())
                        .filter(item -> item.getConsumeMoney().compareTo(
                                remainAmount) <= 0).findFirst();
        LadderDiscountDto discountDto = optional.isPresent()
                ? optional.get()
                : null;
        if (Objects.nonNull(discountDto) && availableIntegral.compareTo(minDeductPoint) >= 0) {
            //discountDto不为空 说明存在匹配的抵扣规则
            //计算对应规则的折扣金额
            BigDecimal discountAmount = remainAmount.multiply(discountDto.getDeductCoefficient())
                    .divide(new BigDecimal(NUM_100), NUM_2, BigDecimal.ROUND_DOWN);
            if (discountAmount.compareTo(minDeductMoney) >= 0
                    && discountAmount.compareTo(discountDto.getMaxDeductMoney()) < 0) {
                //计算后的折扣金额大于最小抵扣金额且小于最大抵扣金额，抵扣金额为计算后的折扣金额
                payDeskVo.setDeductCost(discountAmount);
            }
            if (discountAmount.compareTo(discountDto.getMaxDeductMoney()) >= 0) {
                //计算后的折扣金额大于最大抵扣金额，抵扣金额为最大抵扣金额
                payDeskVo.setDeductCost(discountDto.getMaxDeductMoney());
            }
            if (discountAmount.compareTo(minDeductMoney) < 0) {
                //折扣金额小于最低抵扣金额限制，则APP端不展示抵扣规则文案
                payDeskVo.setIntegralDeductFlag(false);
                return Response.createSuccess(payDeskVo);
            }
            Map<String, Object> integralMsg = new HashMap<>(NUM_4);
            integralMsg.put("msg", "行程费用满%s元，T币满%s可用");
            integralMsg.put("fullMsg",
                    String.format("行程费用满%s元，T币满%s可用", discountDto.getConsumeMoney(), minDeductPoint));
            integralMsg.put("key1", discountDto.getConsumeMoney());
            integralMsg.put("key2", minDeductPoint);
            payDeskVo.setIntegralMsg(integralMsg);
        } else {
            boolean flag = (Objects.nonNull(payDeskVo.getAdditionalFee())
                    && payDeskVo.getAdditionalFee().compareTo(BigDecimal.ZERO) > 0)
                    || (Objects.nonNull(payDeskVo.getFestivalFee())
                    && payDeskVo.getFestivalFee().compareTo(BigDecimal.ZERO) > 0)
                    || (Objects.nonNull(payDeskVo.getCrossCityFee())
                    && payDeskVo.getCrossCityFee().compareTo(BigDecimal.ZERO) > 0);
            if (flag) {
                //此种情况若存在附加费，则APP端不展示积分抵扣选项（因为用户收银台页面不知道具体附件费金额）
                payDeskVo.setIntegralDeductFlag(false);
                return Response.createSuccess(payDeskVo);
            }
            //discountDto为空 说明没有对应匹配的抵扣规则，但该情况APP端仍需展示抵扣规则文案
            BigDecimal minConsumeMoney = integralDeductDTO.getLadderDiscountDtos().stream()
                    .map(LadderDiscountDto::getConsumeMoney).min(
                            BigDecimal::compareTo).get();
            Map<String, Object> integralMsg = new HashMap<>(NUM_4);
            integralMsg.put("msg", "行程费用满%s元，T币满%s可用");
            integralMsg.put("fullMsg", String.format("行程费用满%s元，T币满%s可用", minConsumeMoney, minDeductPoint));
            integralMsg.put("key1", minConsumeMoney);
            integralMsg.put("key2", minDeductPoint);
            payDeskVo.setIntegralMsg(integralMsg);
            return Response.createSuccess(payDeskVo);
        }
        return null;
    }

    /**
     * 获取驱动程序名称
     * 获取司机显示的姓名
     *
     * @param actualDriverUuid 司机id
     * @return 司机显示姓名
     */
    private String getDriverName(String actualDriverUuid) {
        // 获取司机信息
        try {
            Response<DriverResDto> driverResp = driverService.findDriverByUuid(actualDriverUuid);
            if (!driverResp.isSuccess() || Objects.isNull(driverResp.getData())
                    || StringUtils.isBlank(driverResp.getData().getName())) {
                return PassengerConstants.SIJI_SHIFU;
            } else {
                return driverResp.getData().getName().charAt(0) + PassengerConstants.SHIFU;
            }
        } catch (Exception e) {
            log.error("查询司机信息失败{}", ExceptionUtils.getFullStackTrace(e));
        }
        return "";
    }

    /**
     * 查询行程可以积分个数缓存
     *
     * @param orderUuid 行程UUID
     * @return num
     */
    private int getIntegralNum(String orderUuid) {
        try {
            Integer integralNum = factory.ExpireString()
                    .getValue(RoutePlanKey.DEDUCT_INTEGRAL + orderUuid, Integer.class);
            if (integralNum == null) {
                integralNum = Integer.valueOf(NUM_0);
            }
            return integralNum;

        } catch (Exception e) {
            log.info("缓存中获取积分异常", e);
        }
        return NUM_0;
    }

    /**
     * 聚合支付小程序下单
     *
     * @param makeOrderReq 下单请求参数
     * @return Response
     */
    public Response makeOrder(MakeOrderReq makeOrderReq) {
        if (StringUtils.isBlank(makeOrderReq.getUserCode()) || StringUtils.isBlank(makeOrderReq.getPayString())) {
            return Response.createError("参数错误");
        }
        UnifiedDto dto = new UnifiedDto();
        dto.setSceneType("t3pay.trade.agg.wx.makeOrder");
        dto.setExtendParam(JSON.toJSONString(makeOrderReq));
        Response resp = unifiedService.handle(dto);
        if (ObjectUtil.isNotNull(resp)) {
            if (resp.getSuccess()) {
                return Response.createSuccess("下单成功", resp.getData());
            }
            if (StringUtils.isNotBlank(resp.getMsg())) {
                ResultErrorEnum resultErrorEnum = ResultErrorEnum.getResultErrorEnum(resp.getMsg());
                if (null != resultErrorEnum) {
                    return Response.createError(resultErrorEnum);
                }
                return Response.createError("下单失败");
            }
        }
        return Response.createError("下单失败");
    }

    /**
     * 根据订单查询行程Id
     *
     * @param routeOrderQueryReq req
     * @return com.t3.ts.result.Response
     */
    public Response queryRoutePlanUuid(RouteOrderQueryReq routeOrderQueryReq) {
        if (StringUtils.isBlank(routeOrderQueryReq.getOrderNo())
                || StringUtils.isBlank(routeOrderQueryReq.getUserId())) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR);
        }
        Response<PayOrderDto> payOrderRsp = unifiedPaymentQuery.getPayOrderByOrderNo(routeOrderQueryReq.getUserId(),
                routeOrderQueryReq.getOrderNo());
        if (null == payOrderRsp || null == payOrderRsp.getData()) {
            return Response.createError(ResultErrorEnum.NONE_PERMISSION);
        }
        return Response.createSuccess("success", payOrderRsp.getData().getJourneyId());
    }

    /**
     * 根据行程单号查询蚂蚁森林能量
     *
     * @param routePlanUuid 查询请求
     * @param userId        请求
     * @return {@link Response}
     */
    public Response queryAliEcoRecycle(String routePlanUuid, String userId) {
        if (StringUtils.isBlank(routePlanUuid) || StringUtils.isBlank(userId)) {
            return Response.createError(ResultErrorEnum.PARAM_NULL_ERROR);
        }

        AliPayDto aliPayDto = new AliPayDto();
        aliPayDto.setOrderNo(routePlanUuid);
        return aliPayService.queryAliEcoRecycle(aliPayDto);
    }

    /**
     * 判断是否支付宝支付
     *
     * @param payOrderSubAccount payOrderSubAccount
     * @return boolean
     */
    private boolean checkAliPay(Integer payOrderSubAccount) {
        return payOrderSubAccount != null
                && (payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_NOPWD_PAY)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY_H5)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY_JSAPI)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY_PC)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY_MINIPROGRAM)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ALI_PAY_TRANS)
                || payOrderSubAccount.equals(EnumPayOrderSubAccount.ZHIMA_SCORE));
    }

}
