package com.t3.ts.pay.center.api.dto.wallet;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/1/4 9:44
 */
@Getter
@Setter
public class AmountWalletVo {

    /**
     * 余额
     */
    private String balance;

    /**
     * 打车金
     */
    private String give;
    /**
     * 7天内即将失效打车金
     */
    private String giveToExpire;

    /**
     * 礼品卡
     */
    private String gift;

    /**
     * 预付款金额
     */
    private String prePayCash;

    /**
     * 现金
     */
    private String cash;
    /**
     * 现金
     */
    private String rechargeCash;
    /**
     * 现金
     */
    private String rechargeCashGive;

    /**
     * 积分
     */
    private String integral;

    /**
     * 顺风车车主收益
     */
    private String pickrideProfit;
    /**
     * 打车金是否启用开关
     */
    private Boolean giveFareOpen;

    /**
     * 现金总额= 赠金+现金
     */
    private String totalCashAndGive;

    /**
     * 权益卡数量
     */
    private String rightsCount;

    /**
     * 充值开关
     */
    private Boolean rechargeSwitch;

    /**
     * 乘推乘
     */
    private JSONObject recommendPassenger;

    /**
     * tip内容
     */
    private String tipContent;

    /**
     *  首页侧边栏显示控制开关
     */
    private Boolean sidebarSwitch;

    /**
     * 省钱中心显示控制开关
     */
    private Boolean saveMoneySwitch;
    /**
     * 借钱额度
     */
    private QuotaDto quota;
}
