package com.t3.ts.pay.center.api.dto.wallet;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AccountFlowDto.java
 * @Description TODO
 * @createTime 2021年08月02日 19:26:00
 */
@Data
public class AccountFlowDto implements Serializable {

    private static final long serialVersionUID = -5889898269742435758L;

    private String createTime;

    private Integer payChannel;

    private Integer changedType;

    private BigDecimal changedBalance;

    private String changedTypeName;

    private Integer flowType;

}
