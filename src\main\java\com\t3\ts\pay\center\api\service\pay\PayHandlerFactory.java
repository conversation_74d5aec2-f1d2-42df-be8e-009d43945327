package com.t3.ts.pay.center.api.service.pay;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: //模块目的、功能描述
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 16:49
 */
@Component
public class PayHandlerFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<String, PayHandler> PAY_SERVICE_MAP = new HashMap<>(8);

    /**
     * 得到处理程序
     *
     * @param type 类型
     * @return {@link PayHandler}
     */
    public PayHandler getHandler(String type) {
        return PAY_SERVICE_MAP.get(type);
    }

    @Override
    public void afterPropertiesSet() {
        appContext.getBeansOfType(PayHandler.class).values().forEach(t -> PAY_SERVICE_MAP.put(t.getType(), t));
    }

    private ApplicationContext appContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        this.appContext = context;
    }
}
