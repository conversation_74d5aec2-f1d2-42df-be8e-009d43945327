package com.t3.ts.pay.center.api.config;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.t3.ts.context.ContextUtil;
import com.t3.ts.finance.center.util.NumberConstants;
import com.t3.ts.passenger.dto.PassengerMultiDto;
import com.t3.ts.passenger.service.PassengerService;
import com.t3.ts.pay.center.api.config.interceptor.DriverTokenInterceptor;
import com.t3.ts.pay.center.api.config.interceptor.PassengerTokenInterceptor;
import com.t3.ts.pay.common.http.driver.DriverInfoService;
import com.t3.ts.pay.common.http.driver.dto.req.DriverReqDto;
import com.t3.ts.pay.common.http.driver.dto.res.DriverResDto;
import com.t3.ts.result.Response;
import com.t3.ts.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
public class RequestContextHelperInstead {

    private static final String THIRD_SOURCE = "1";

    private static final String HITCH_CAR_SOURCE = "2";

    @DubboReference
    private PassengerService passengerService;

    @Autowired
    private DriverInfoService driverInfoService;

    public String getPassengerUuid() {
        return Assert.notEmpty(ContextUtil.getUserId(), "乘客uuid为空");
    }

    public String getPassengerMobile() {
        String passengerMobile = PassengerTokenInterceptor.THREAD_LOCAL_PASSENGER_MOBILE.get();
        if (StrUtil.isEmpty(passengerMobile)) {
            Response<PassengerMultiDto> response = passengerService.getPassengerById(getPassengerUuid());
            if (null != response && null != response.getData()) {
                passengerMobile = response.getData().getMobile();
                PassengerTokenInterceptor.THREAD_LOCAL_PASSENGER_MOBILE.set(passengerMobile);
            }
        }
        return passengerMobile;
    }

    public String getDriverUuid() {
        return Assert.notEmpty(ContextUtil.getUserId(), "司机uuid为空");
    }

    public String getDriverName() {
        return getLoginDriver().getName();
    }

    public String getDriverIdCard() {
        return getLoginDriver().getIdCard();
    }

    public String getDriverMobile() {
        return getLoginDriver().getMobile();
    }

    public DriverResDto getLoginDriver() {
        DriverResDto driverResDto = DriverTokenInterceptor.THREAD_LOCAL_DRIVER.get();
        if (null == driverResDto) {
            driverResDto = getDriver();
            DriverTokenInterceptor.THREAD_LOCAL_DRIVER.set(driverResDto);
        }
        return driverResDto;
    }

    /**
     * getDriver
     *
     * @return DriverResDto
     */
    private DriverResDto getDriver() {
        HttpServletRequest request = getRequest();
        String source = request.getHeader("source");

        DriverReqDto driverReqDto = new DriverReqDto();
        driverReqDto.setDriverId(getDriverUuid());
        //端内顺风车司机
        if (StringUtils.isNotBlank(source) && HITCH_CAR_SOURCE.equals(source)) {
            driverReqDto.setDriverSource(NumberConstants.NUMBER_2);
        }else {
            driverReqDto.setDriverSource(NumberConstants.NUMBER_0);
        }
        Response<DriverResDto> response = driverInfoService.queryCacheDriverInfo(driverReqDto);
        if (null != response && null != response.getData()) {
            return response.getData();
        }
        //查询未投运司机信息
        response = driverInfoService.findDriverInfoByDriverId(driverReqDto);
        if (null != response && response.isSuccess() && null != response.getData()) {
            return response.getData();
        }
        return new DriverResDto();
    }

    /**
     * get请求
     *
     * @return {@link HttpServletRequest}
     */
    private HttpServletRequest getRequest() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == attrs) {
            return null;
        }
        return attrs.getRequest();
    }

}
