package com.t3.ts.pay.center.api.business.pay.channel;

import com.t3.ts.pay.center.api.bo.PaymentInfoBo;
import com.t3.ts.pay.center.api.bo.RechargePayBo;
import com.t3.ts.pay.center.api.business.PayChannelService;
import com.t3.ts.pay.center.api.constants.ResultErrorEnum;
import com.t3.ts.pay.remote.constants.EnumPayOrderChannel;
import com.t3.ts.pay.remote.dto.WxPayDto;
import com.t3.ts.pay.remote.service.WxPayService;
import com.t3.ts.result.Response;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 支付信息
 *
 * <AUTHOR>
 * @date 2019.9.29
 */
@Component("wxAppletPayServiceImpl")
public class WxAppletPayServiceImpl implements PayChannelService {

    @DubboReference
    private WxPayService wxPayService;

    @Override
    public Response<RechargePayBo> paymentInfo(PaymentInfoBo paymentInfoBo) {

        Response paymentResp = paymentInfoBo.getPaymentResp();
        if (paymentResp == null) {
            return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR);
        }

        RechargePayBo rechargePayVo = new RechargePayBo();
        rechargePayVo.setNoSecret(paymentInfoBo.isNoSecret());
        rechargePayVo.setPayType(EnumPayOrderChannel.WECHATMINIPROGRAM.getCode());

        //免密支付
        if (paymentInfoBo.isNoSecret()) {
            // 调用免密支付接口
            WxPayDto dto = new WxPayDto();
            dto.setOrderNo(paymentResp.getMsg());
            dto.setUserId(paymentInfoBo.getPassengerUuid());
            Response response = wxPayService.noPwdPay(dto);
            rechargePayVo.setSdk(response.getMsg());
            if (!response.isSuccess()) {
                rechargePayVo.setSdk("微信小程序免密支付失败");
                return Response.createError(ResultErrorEnum.UNIFIED_PAY_INFO_ERROR, rechargePayVo);
            }

        } else {
            rechargePayVo.setSdk(String.valueOf(paymentResp.getData()));
        }
        return Response.createSuccess(rechargePayVo);

    }
}
