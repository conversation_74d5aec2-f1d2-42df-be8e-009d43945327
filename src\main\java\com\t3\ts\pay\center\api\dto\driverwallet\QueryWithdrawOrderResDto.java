package com.t3.ts.pay.center.api.dto.driverwallet;

import lombok.Data;

import java.util.Date;

@Data
public class QueryWithdrawOrderResDto {

    /**
     * "accountId": 1415940221119279124,
     * "accountType": 3,               // 账户类型
     * "applyTime": *************,     // 申请时间
     * "bankName": "中信银行",
     * "batchUuid": "****************",
     * "cardNo": "****************",    // 银行卡号
     * "cardType": 1,
     * "city": "",
     * "createTime": *************,
     * "orderUuid": "20210716173543333696",
     * "province": "",
     * "remark": "司机提现",
     * "updateTime": *************,
     * "userId": "93eb872afda746e4b616692e45151e3c",
     * "userName": "王一",
     * "withdrawAmount": 200,            // 提现金额
     * "withdrawStatus": 8               //  8成功，9失败，其他都是处理中
     */
    private Long accountId;
    private Integer accountType;
    private Date applyTime;
    private String bankName;
    private String batchUuid;
    private String cardNo;
    private Integer cardType;
    private String city;
    private Date createTime;
    private String orderUuid;
    private String province;
    private String remark;
    private Date updateTime;
    private String userId;
    private String userName;
    private String withdrawAmount;
    /**
     * status 8成功，9失败，其他都是处理中
     */
    private Integer withdrawStatus;
    private Date withdrawTime;
}
