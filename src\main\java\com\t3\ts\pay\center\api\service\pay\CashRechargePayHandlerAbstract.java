package com.t3.ts.pay.center.api.service.pay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.t3.ts.account.center.dto.AccountDto;
import com.t3.ts.account.center.dto.PersonSuperviseAccountDto;
import com.t3.ts.account.center.service.AccountService;
import com.t3.ts.account.center.service.PersonSuperviseService;
import com.t3.ts.pay.center.api.config.SwitchConfig;
import com.t3.ts.pay.center.api.exception.BizExceptionUtil;
import com.t3.ts.result.Response;
import com.t3.ts.settlement.centre.dto.SettlementGeneralDto;
import com.t3.ts.settlement.centre.enums.BizType;
import com.t3.ts.settlement.centre.service.SettlementGeneralService;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.t3.ts.pay.center.api.exception.BizExceptionEnum.ACCOUNT_STATUS_ERROR;
import static com.t3.ts.pay.center.api.exception.BizExceptionEnum.PARAM_ERROR;

/**
 * Description: 简单三方支付流程
 *
 * <AUTHOR> <EMAIL>
 * Date:     2021/7/27/0027 17:25
 */
@Slf4j
@Component
public class CashRechargePayHandlerAbstract extends AbstractSimpleThirdPayHandler implements PayHandler {

    @DubboReference
    private SettlementGeneralService settlementGeneralService;
    @DubboReference
    private AccountService accountService;
    @Autowired
    private SwitchConfig switchConfig;
    @DubboReference
    private PersonSuperviseService personSuperviseService;

    /**
     * get current type
     *
     * @return {@link String}
     */
    @Override
    public String getType() {
        return "personalRecharge";
    }

    /**
     * 参数校验
     *
     * @param context 上下文
     */
    @Override
    public void checkParam(PayContext context) {
        PayHandler.super.checkParam(context);
        JSONObject json = JSON.parseObject(context.getMessage());
        context.setAmount(json.getBigDecimal("amount"));
        final Object settleId = json.get("settleId");
        final Object orderId = json.get("orderId");
        if (ObjectUtil.isNull(settleId) && ObjectUtil.isNull(orderId)) {
            BizExceptionUtil.create(PARAM_ERROR);
        }
        context.getExtendParam().put("inner_settleId", settleId);
        context.getExtendParam().put("inner_orderId", orderId);
        Response<AccountDto> acctResp = accountService.simpleGetAccount(context.getUserId());
        if (acctResp.getSuccess() && null != acctResp.getData()) {
            AccountDto accountDto = acctResp.getData();
            //超过金额上限不让充值
            if (accountDto.getAccountCash() + yuan2fen(context.getAmount()) > switchConfig.getCashRechargeLimit()) {
                BizExceptionUtil.create("充值金额超限");
            }
            PersonSuperviseAccountDto dto = new PersonSuperviseAccountDto();
            dto.setUserId(accountDto.getUserId());
            dto.setAccountType(accountDto.getAccountType());
            //判断是否已开户成功
            Response response = personSuperviseService.querySuperviseAccountInfo(dto);
            if (!response.getSuccess() || null == response.getData()) {
                BizExceptionUtil.create(ACCOUNT_STATUS_ERROR);
            }
        } else {
            BizExceptionUtil.create("账户信息错误");
        }
    }

    /**
     * 元转分 int型
     *
     * @param yuan yuan
     * @return Integer
     */
    private Integer yuan2fen(BigDecimal yuan) {
        return yuan.multiply(new BigDecimal("100")).intValue();
    }

    /**
     * 查询 settlement 充值结算单
     *
     * @param context 上下文
     */
    @Override
    public void checkSettlement(PayContext context) {
        SettlementGeneralDto dto = new SettlementGeneralDto();
        dto.setBizType(getBizType(context));
        dto.setSettleUuid((String) context.getExtendParam().get("inner_settleId"));
        dto.setOrderUuid((String) context.getExtendParam().get("inner_orderId"));
        final Response<List<SettlementGeneralDto>> listResponse = settlementGeneralService.selectSettlement(dto);
        if (!listResponse.isSuccess() || CollectionUtil.isEmpty(listResponse.getData())) {
            throw new IllegalStateException("结算单不存在");
        }
        context.getExtendParam().put("inner_bizId", listResponse.getData().get(0).getOrderUuid());
    }


    /**
     * get  pay  type
     * pay-center EnumPayOrderType
     *
     * @return {@link Integer}
     */
    @Override
    public Integer getBizType(PayContext context) {
        return BizType.CASH_RECHARGE.getType();
    }

}
