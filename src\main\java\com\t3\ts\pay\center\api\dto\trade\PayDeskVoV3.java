package com.t3.ts.pay.center.api.dto.trade;

import com.t3.ts.pay.center.api.constants.CommonNumConst;
import com.t3.ts.pay.remote.dto.PayConfigDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/7/18 17:49
 * @des 1.0
 */

@Data
public class PayDeskVoV3 {

    // 用戶Id
    private String passengerId;

    // 行程id
    private String orderUuid;

    // 支付状态，1：初始化，2：待（支付）结算，3：（支付）结算中，4：（支付）结算成功
    private Integer payStatus;

    // 司机姓名
    private String driverName;

    // 车牌号
    private String plateNum;

    /**
     * 待支付费用
     */
    private BigDecimal actualFare;

    /**
     * 最终需要支付费用
     */
    private BigDecimal finalFare;

    /**
     * 附加费
     */
    private BigDecimal additionalFee;

    /**
     * 节日费
     */
    private BigDecimal festivalFee;

    private BigDecimal crossCityFee;
    private BigDecimal dispatchFee;
    private BigDecimal lostReturnFee;
    private BigDecimal compensationFare;

    /**
     * 钱包余额
     */
    private BigDecimal balance;

    /**
     * app是否展示校验三方支付方式必选提示
     */
    private Boolean thirdPayTypeToast = Boolean.FALSE;

    /**
     * app展示校验三方支付方式必选提示信息
     */
    private String thirdPayTypeToastMsg;

    /**
     * 余额提示文案(附加费)
     * 1、不显示：没有附加费
     * 2、没有可用于支付的余额：当前账号余额包含赠送币、礼品卡，不可使用于支付，需通过第三方支付
     * 3、有可用支付的余额：当前账号余额中包含赠送币/礼品卡，余额支付xx.xx元，剩余费用需要通过第三方支付
     */
    private String balanceMsg;

    /**
     * 余额开关
     */
    private Boolean balanceFlag = Boolean.FALSE;

    /**
     * 余额可支付费用 <= 0  余额条目展示灰色
     */
    private BigDecimal availableBalance;

    /**
     * 是否可以积分抵扣(是否展示积分抵扣UI)
     */
    private Boolean integralDeductFlag = Boolean.FALSE;

    /**
     * 可用积分>0：打开开关
     */
    private BigDecimal availableIntegral;

    /**
     * 积分抵扣限制提示
     * T币抵扣
     * 1、满足抵扣条件：展示 eg：可用46T币，抵￥1.38元
     * 2、不满足抵扣条件：展示可用条件  eg：行程费用满xx元，T币满多少元可用
     */
    private Map<String, Object> integralMsg;

    /**
     * 积分抵扣项
     */
    private Map<String, Object> integralMessage;

    /**
     * 支付项
     * 合计费用  0.01元
     * 企业支付  0.01元
     * 动态折扣  0.01元
     * 取消费  0.01元
     * 超时等待费  0.01元
     * 已预付  0.01元
     * 优惠券  0.01元  可以跳转到优惠券列表界面
     * 优惠券已抵扣  -0.01元
     * 余额已支付  -0.01元
     * T币已抵扣  -0.01元
     * 还需支付  0.01元
     */
    private List<PayItem> payItemList;

    /**
     * 最优优惠券
     */
    private CanUseCoupon canUseCoupon;

    /**
     * 优惠券
     */
    private CouponVo couponVo;

    /**
     * 支付渠道
     */
    private List<Integer> payTypeList;

    /*省心打折扣明细*/
    private UnWarriedArriveDetail unWarriedArriveDetail;

    /*券包明细*/
    private CouponActivityDetail couponActivityDetail;

    /**
     * 使用券套餐里面的优惠券 1表示使用
     */
    @ApiModelProperty(value = "1表示购买券套餐并使用券，2表示只购买券套餐", required = false)
    private Integer useCounponActivity;

    /**
     * 省心打折扣是否已抵扣
     */
    private Boolean unWarriedArriveDeductFlag = Boolean.FALSE;

    /**
     * 省心打折扣是是否选中
     */
    private Boolean unWarriedArriveChecked = Boolean.FALSE;


    /**
     * 优惠券是否选中
     */
    private Boolean couponChecked = Boolean.FALSE;

    /**
     * 1:尾款支付
     * 2:正常支付
     */
    private Integer payDeskType = CommonNumConst.NUM_2;

    /**
     * 优惠券是否已抵扣
     */
    private Boolean couponDeductFlag = Boolean.FALSE;

    /**
     * 是否开启立即支付 FALSE--非立即支付（默认值） TRUE--立即支付
     */
    private Boolean isPayInstant = Boolean.FALSE;
    /**
     * 亲友支付标识 0 表示不是老年用车 ,1 不使用亲友付   2 使用亲友支付  3 亲友付选项为不可选择
     */
    private Integer payForOtherType;
    /**
     * 不可以用亲友付原因
     */
    private String disableForOtherDesc;

    /**
     * 是否开启T币抵扣  端上无用，端上用的payTypeList.contains(37) && integralDeductFlag=true
     */
    private Boolean integralChecked = Boolean.FALSE;
    //paycenter配置
    private PayConfigDto payConfigDto;
    /**
     * 充值余额标识   =1 说明充值余额》0
     */
    private Integer rechargeFlag = 0;
    /**
     * 收银台额外提示信息 （目前用于展示拼车结果）
     */
    private String additionalHint ;

    /**
     * 权益卡是否选中
     */
    private Boolean privilegeChecked = Boolean.FALSE;

    /**
     * 权益卡是否已抵扣
     */
    private Boolean privilegeDeductFlag = Boolean.FALSE;
    /**
     * 起始地
     */
    private String origin;
    /**
     * 目的地
     */
    private String destination;

    /**
     * 免密支付提示文案
     */
    private String autoPayTip;
}
