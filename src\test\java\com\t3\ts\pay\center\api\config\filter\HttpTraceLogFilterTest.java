package com.t3.ts.pay.center.api.config.filter;

import org.apache.commons.logging.Log;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.springframework.core.env.Environment;

import javax.servlet.FilterConfig;
import javax.servlet.ServletContext;
import java.util.Set;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HttpTraceLogFilterTest.java
 * @Description TODO
 * @createTime 2020年11月24日 19:18:00
 */
public class HttpTraceLogFilterTest {
    @Mock
    Logger log;
    @Mock
    Log logger;
    @Mock
    Environment environment;
    @Mock
    ServletContext servletContext;
    @Mock
    FilterConfig filterConfig;
    @Mock
    Set<String> requiredProperties;
    @InjectMocks
    HttpTraceLogFilter httpTraceLogFilter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDoFilterInternal() throws Exception {
        httpTraceLogFilter.doFilterInternal(null, null, null);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme